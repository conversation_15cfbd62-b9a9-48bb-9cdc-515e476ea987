{"id": 4896696031890359153, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c1, c0, and c2 are at l0; c4 is at l1. The goal is to reach a state where the following facts hold: Car c3 is at location l0, Car c4 is at location l0, Car c1 is at location l0, Car c0 is at location l1, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c2 l0) (debark c2 l0) (board c2 l0) (sail l0 l1) (board c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (sail l1 l0)\"?", "answer": 4, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": -4447316279887451134, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c9, c8, c1, c6, c0, and c2 are at l0; c7, c5, and c4 are at l1. The goal is to reach a state where the following facts hold: Car c3 is at location l1, Car c9 is at location l1, Car c4 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c8 is at location l0, Car c5 is at location l0, Car c0 is at location l0, and Car c6 is at location l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c0 l0) (debark c0 l0) (board c9 l0) (sail l0 l1) (debark c9 l1) (board c5 l1) (sail l0 l1) (debark c5 l0) (board c2 l0) (sail l0 l1) (debark c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c3 l0) (sail l0 l1) (debark c3 l1) (board c7 l1) (sail l1 l0) (debark c7 l0)\"?", "answer": 6, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 3468551644281451113, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 3 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c0 l1) (sail l1 l2) (sail l2 l1) (sail l1 l0) (debark c0 l0) (board c0 l0) (sail l2 l1) (sail l0 l1) (board c1 l1) (sail l1 l2) (debark c1 l2)\"?", "answer": 6, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l0) (at c1 l2)))\n)"}
{"id": -6291233197210223159, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c1, c0, and c2 are at l0; c4 is at l1. The goal is to reach a state where the following facts hold: Car c3 is at location l0, Car c4 is at location l0, Car c1 is at location l0, Car c0 is at location l1, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c2 l0) (sail l0 l1) (debark c2 l1) (board c2 l1) (debark c2 l1) (sail l1 l0) (debark c1 l1) (sail l0 l1) (debark c0 l1) (board c2 l1) (debark c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0)\"?", "answer": 6, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 7630252500073167255, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l1 and Car c1 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c0 l0) (debark c0 l0) (board c1 l0) (sail l0 l1) (debark c1 l1) (sail l1 l0) (board c0 l0) (debark c0 l0) (board c0 l1) (debark c0 l0) (board c0 l0) (debark c0 l0) (board c0 l0) (sail l0 l1) (debark c0 l1)\"?", "answer": 8, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l1)))\n)"}
{"id": 3264153577772858774, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1, p0, and p3 are at l1-2, p2 is at l1-0, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p0 is at l0-2, and p1 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p0 t1 l1-2) (LOAD-TRUCK p3 t1 l1-2) (LOAD-TRUCK p3 t1 l1-2) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p1 t1 l1-1) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (UNLOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p2 t1 l1-2) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (UNLOAD-TRUCK p0 t0 l0-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1)\"?", "answer": 2, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-2) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": 3429519911232464068, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-1, l2-2, and l2-0 are in c2; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1 and p1 are at l1-2, t2 is at l2-2, t0 is at l0-2, p0 and a0 are at l2-0, p3 is at l2-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p1 is at l2-0, p2 is at l2-2, p3 is at l1-2, and p0 is at l2-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p1 t1 l1-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (LOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (UNLOAD-TRUCK p2 t0 l0-0) (DRIVE-TRUCK t2 l2-2 l2-0 c2) (LOAD-TRUCK p0 t2 l2-0) (DRIVE-TRUCK t2 l2-0 l2-1 c2) (UNLOAD-TRUCK p0 t2 l2-1) (LOAD-TRUCK p3 t2 l2-1) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p1 t1 l1-0) (FLY-AIRPLANE a0 l2-0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p3 t2 l2-0) (FLY-AIRPLANE a0 l1-0 l2-0) (UNLOAD-AIRPLANE p1 a0 l2-0) (LOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p2 t2 l2-0) (LOAD-AIRPLANE p3 a0 l2-0) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p3 t1 l1-2)\"?", "answer": 21, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 - package t0 t1 t2 - truck)\n    (:init (at a0 l2-0) (at p0 l2-0) (at p1 l1-2) (at p2 l0-1) (at p3 l2-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l2-1) (at p1 l2-0) (at p2 l2-2) (at p3 l1-2)))\n)"}
{"id": 6085821344967771329, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1 is at l1-2, p0 and a0 are at l0-0, p1 is at l1-1, t0 and p3 are at l0-2, p2 is at l0-1. The goal is to reach a state where the following facts hold: p0 is at l1-1, p2 is at l1-0, p3 is at l1-2, and p1 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(DRIVE-TRUCK t1 l1-2 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p3 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p0 a0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p3 t1 l1-1) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (LOAD-AIRPLANE p3 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p0 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (UNLOAD-AIRPLANE p2 a0 l1-0)\"?", "answer": 7, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-0) (at p1 l1-1) (at p2 l0-1) (at p3 l0-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": -5837180489821515199, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1, p0, and p3 are at l1-2, p2 is at l1-0, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p0 is at l0-2, and p1 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p0 t1 l1-2) (LOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p1 t1 l1-1) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (UNLOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p2 t1 l1-2) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (UNLOAD-TRUCK p0 t0 l0-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1)\"?", "answer": 21, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-2) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": 6626038777387080313, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1, p0, and p3 are at l1-2, p2 is at l1-0, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p0 is at l0-2, and p1 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck which is in location ?loc-from in city ?city to another location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(UNLOAD-TRUCK p0 t1 l1-1) (LOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p1 t1 l1-1) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (UNLOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p2 t1 l1-2) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (UNLOAD-TRUCK p0 t0 l0-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2)\"?", "answer": 0, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-2) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": -6677423721181968423, "group": "validation_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_3. The following block(s) is stacked on top of another block: block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_1 and The block block_1 is currently situated above the block block_3. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the first inapplicable action in the next sequence of actions: \"(unstack block_2 block_1) (put-down block_2) (pick-up block_1) (stack block_1 block_1) (unstack block_1 block_2) (stack block_1 block_3) (unstack block_1 block_3) (stack block_1 block_3) (pick-up block_2) (put-down block_2) (pick-up block_2) (stack block_2 block_1)\"?", "answer": 3, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_2) (clear block_3) (handempty) (on block_2 block_1) (ontable block_1) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_1)))\n)"}
{"id": 2447176438515928342, "group": "validation_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1, block_2, and block_3. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_2. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick-up block_1) (stack block_1 block_3) (unstack block_1 block_3) (stack block_1 block_3) (unstack block_1 block_3) (stack block_1 block_3) (pick-up block_1) (stack block_1 block_3) (unstack block_1 block_3) (stack block_1 block_3) (unstack block_1 block_3) (put-down block_1) (pick-up block_2) (stack block_2 block_1)\"?", "answer": 6, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_1) (clear block_2) (clear block_3) (handempty) (ontable block_1) (ontable block_2) (ontable block_3))\n    (:goal (on block_2 block_1))\n)"}
{"id": 2186936177690406548, "group": "validation_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_4 is on block_1, block_5 is on block_2, and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_1 is on top of block block_3, and The block block_5 is currently situated under the block block_4. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the first inapplicable action in the next sequence of actions: \"(unstack block_4 block_5) (put-down block_3) (unstack block_5 block_2) (stack block_5 block_3) (unstack block_5 block_3) (put-down block_5) (unstack block_4 block_1) (stack block_4 block_5) (pick-up block_2) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_4) (pick-up block_1) (stack block_1 block_3)\"?", "answer": 0, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (handempty) (on block_3 block_5) (on block_4 block_1) (on block_5 block_2) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -336007196084587760, "group": "validation_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3 and The block block_2 is currently situated under the block block_1. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the first inapplicable action in the next sequence of actions: \"(unstack block_3 block_2) (stack block_3 block_1) (pick-up block_2) (put-down block_2) (unstack block_3 block_1) (stack block_3 block_1) (pick-up block_2) (unstack block_2 block_2) (unstack block_3 block_1) (put-down block_3) (pick-up block_1) (stack block_1 block_2) (pick-up block_3) (stack block_3 block_1)\"?", "answer": 7, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_1) (clear block_3) (handempty) (on block_3 block_2) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_2) (on block_3 block_1)))\n)"}
{"id": -533591439954511992, "group": "validation_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_4 is on block_1, block_5 is on block_2, and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_1 is on top of block block_3, and The block block_5 is currently situated under the block block_4. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the first inapplicable action in the next sequence of actions: \"(unstack block_3 block_5) (stack block_3 block_5) (unstack block_3 block_5) (put-down block_3) (unstack block_5 block_2) (put-down block_5) (unstack block_4 block_1) (stack block_5 block_2) (pick-up block_2) (stack block_2 block_4) (pick-up block_1) (stack block_1 block_2) (unstack block_1 block_2) (stack block_1 block_3)\"?", "answer": 7, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (handempty) (on block_3 block_5) (on block_4 block_1) (on block_5 block_2) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -5796649381081259088, "group": "validation_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f0-1f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-4f location and Key key0-1 is at f4-2f location. The available actions are: (unlock ?curpos ?lockpos ?key ?shape) - use the key ?key of shape ?shape to unlock the place ?lockpos from the current position ?curpos, (move ?curpos ?nextpos) - travel from the current position ?curpos to the next position ?nextpos, (pickup ?curpos ?key) - acquire the key ?key from the place ?curpos, (pickup-and-loose ?curpos ?newkey ?oldkey) - pick up key ?newkey at current position place ?curpos and loose key ?oldkey being held, and (putdown ?curpos ?key) - place the key ?key at the current position place ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-1f f1-1f) (move f1-1f f0-1f) (pickup f0-1f key0-1) (move f0-1f f1-1f) (move f1-1f f2-1f) (move f2-1f f3-1f) (move f3-1f f3-2f) (move f3-2f f4-2f) (putdown f4-2f key0-1) (move f1-4f f1-3f) (move f4-3f f4-4f) (pickup f4-4f key0-0) (move f4-4f f3-4f) (putdown f3-4f key0-0) (pickup f3-4f key0-0) (move f3-4f f2-4f) (putdown f2-4f key0-0)\"?", "answer": 9, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-4f) (at key0-1 f0-1f) (at-robot f2-1f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (locked f0-3f) (locked f4-0f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-4f) (at key0-1 f4-2f)))\n)"}
{"id": 1890385883188749975, "group": "validation_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  \nCurrently, the robot is at position f0-3f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock, f2-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-0 is at position f4-1f. Key key0-2 is at position f1-2f. Key key0-1 is at position f3-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f1-0f location, Key key0-2 is at f1-1f location, and Key key0-1 is at f2-4f location. The available actions are: (unlock ?curpos ?lockpos ?key ?shape) - use the key ?key of shape ?shape to unlock the place ?lockpos from the current position ?curpos, (move ?curpos ?nextpos) - move from place ?curpos to place ?nextpos, (pickup ?curpos ?key) - retrieve the key ?key from its current position ?curpos, (pickup-and-loose ?curpos ?newkey ?oldkey) - pick up the key ?newkey at the current position place ?curpos and loose the key ?oldkey being held, and (putdown ?curpos ?key) - place the key ?key at the current position place ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f0-3f f1-3f) (move f1-3f f2-3f) (move f2-3f f3-3f) (pickup f3-3f key0-1) (move f3-3f f3-4f) (move f3-4f f2-4f) (putdown f2-4f key0-1) (move f2-4f f2-3f) (unlock f2-3f f2-2f key0-2 shape0) (move f1-3f f1-2f) (pickup f1-2f key0-2) (move f1-2f f1-1f) (unlock f1-1f f2-1f key0-2 shape0) (unlock f1-1f f0-1f key0-2 shape0) (putdown f1-1f key0-2) (move f1-1f f2-1f) (move f2-1f f3-1f) (move f3-1f f4-1f) (pickup f4-1f key0-0) (move f4-1f f3-1f) (move f3-1f f2-1f) (move f2-1f f1-1f) (move f1-1f f1-0f) (putdown f1-0f key0-0)\"?", "answer": 8, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-1f) (at key0-1 f3-3f) (at key0-2 f1-2f) (at-robot f0-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f0-1f shape0) (lock-shape f2-1f shape0) (lock-shape f2-2f shape0) (locked f0-1f) (locked f2-1f) (locked f2-2f) (open f0-0f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f1-0f) (at key0-1 f2-4f) (at key0-2 f1-1f)))\n)"}
{"id": 92778731062853893, "group": "validation_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f3-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-1 is at position f1-3f. Key key0-0 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-3f location and Key key0-0 is at f2-0f location. The available actions are: (unlock ?curpos ?lockpos ?key ?shape) - unlock the place ?lockpos with key ?key of shape ?shape from the current position place ?curpos, (move ?curpos ?nextpos) - move from place ?curpos to place ?nextpos, (pickup ?curpos ?key) - acquire the key ?key from the place ?curpos, (pickup-and-loose ?curpos ?newkey ?oldkey) - pick up the key ?newkey from the current position ?curpos and loose the key ?oldkey which is being held, and (putdown ?curpos ?key) - place the key ?key at the current position ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(putdown f4-0f key0-1) (move f3-2f f2-2f) (pickup f2-2f key0-0) (move f2-2f f2-1f) (putdown f2-1f key0-0) (pickup f2-1f key0-0) (unlock f2-1f f2-0f key0-0 shape0) (move f2-1f f2-0f) (putdown f2-0f key0-0) (move f2-0f f1-0f)\"?", "answer": 0, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f2-2f) (at key0-1 f1-3f) (at-robot f3-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f2-0f shape0) (lock-shape f4-2f shape0) (locked f2-0f) (locked f4-2f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-0f) (at key0-1 f1-3f)))\n)"}
{"id": -5527389115633353713, "group": "validation_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f3-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-1 is at position f1-3f. Key key0-0 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-3f location and Key key0-0 is at f2-0f location. The available actions are: (unlock ?curpos ?lockpos ?key ?shape) - unlock the place ?lockpos with key ?key of shape ?shape from the current position place ?curpos, (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos, (pickup ?curpos ?key) - acquire the key ?key from the place ?curpos, (pickup-and-loose ?curpos ?newkey ?oldkey) - pick up the key ?newkey from the current position ?curpos and loose the key ?oldkey which is being held, and (putdown ?curpos ?key) - put down the key ?key at the current position ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f3-3f f2-3f) (putdown f0-4f key0-1) (pickup f2-2f key0-0) (move f2-2f f2-1f) (unlock f2-1f f2-0f key0-0 shape0) (move f2-1f f2-0f) (move f2-0f f3-0f) (move f3-0f f2-0f) (putdown f2-0f key0-0)\"?", "answer": 1, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f2-2f) (at key0-1 f1-3f) (at-robot f3-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f2-0f shape0) (lock-shape f4-2f shape0) (locked f2-0f) (locked f4-2f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-0f) (at key0-1 f1-3f)))\n)"}
{"id": -2101435201820776746, "group": "validation_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f0-1f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-4f location and Key key0-1 is at f4-2f location. The available actions are: (unlock ?curpos ?lockpos ?key ?shape) - unlock the place ?lockpos with key ?key of shape ?shape from the current position place ?curpos, (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos, (pickup ?curpos ?key) - retrieve the key ?key from its current position ?curpos, (pickup-and-loose ?curpos ?newkey ?oldkey) - pick up the key ?newkey from the current position ?curpos and loose the key ?oldkey which is being held, and (putdown ?curpos ?key) - place the key ?key at the current position place ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-1f f1-1f) (move f3-3f f4-3f) (pickup f0-1f key0-1) (move f0-1f f1-1f) (move f1-1f f2-1f) (move f2-1f f3-1f) (move f3-1f f3-2f) (move f3-2f f4-2f) (putdown f4-2f key0-1) (move f4-2f f4-3f) (move f4-3f f4-4f) (pickup f4-4f key0-0) (move f4-4f f3-4f) (move f3-4f f2-4f) (putdown f2-4f key0-0) (move f2-4f f3-4f) (move f3-4f f2-4f)\"?", "answer": 1, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-4f) (at key0-1 f0-1f) (at-robot f2-1f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (locked f0-3f) (locked f4-0f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-4f) (at key0-1 f4-2f)))\n)"}
{"id": -8682376828094741291, "group": "validation_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, and tile_8 is to the right of tile_7. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_4 is down from tile_7, tile_1 is down from tile_4, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_6 and holding color black and robot robot1 is at tile_8 and holding color white; tile_4, tile_9, tile_3, tile_1, tile_7, tile_2, and tile_5 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_5 is painted in black color, Tile tile_7 is painted in black color, and Tile tile_6 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move the robot ?r from tile ?x to the right tile ?y, and (left ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to its left.", "question": "What is the first inapplicable action in the next sequence of actions: \"(paint-up robot2 tile_9 tile_6 black) (down robot2 tile_6 tile_3) (change-color robot1 white black) (left robot1 tile_8 tile_7) (down robot1 tile_7 tile_4) (paint-up robot1 tile_7 tile_4 black) (down robot1 tile_4 tile_1) (change-color robot1 black white) (paint-up robot1 tile_4 tile_1 white) (right robot1 tile_1 tile_2) (up robot1 tile_2 tile_5) (change-color robot2 black white) (paint-up robot2 tile_6 tile_3 white) (paint-up robot1 tile_8 tile_5 white) (down robot1 tile_5 tile_2) (change-color robot1 white black) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (paint-down robot1 tile_2 tile_5 white) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black) (change-color robot2 black white) (paint-up robot1 tile_5 tile_2 black) (change-color robot2 white black) (change-color robot2 black white) (change-color robot2 white black)\"?", "answer": 62, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_6) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 7799832864796445863, "group": "validation_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, and tile_8 is to the right of tile_7. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_4 is down from tile_7, tile_1 is down from tile_4, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_9 and holding color white; tile_4, tile_8, tile_7, tile_3, tile_1, tile_6, and tile_5 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_5 is painted in black color, Tile tile_7 is painted in black color, and Tile tile_6 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from tile ?x to the right tile ?y, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(left robot1 tile_9 tile_8) (left robot2 tile_2 tile_1) (up robot2 tile_1 tile_4) (paint-up robot2 tile_7 tile_4 black) (right robot2 tile_4 tile_5) (right robot2 tile_5 tile_6) (paint-up robot2 tile_9 tile_6 black) (down robot2 tile_6 tile_3) (change-color robot2 black white) (down robot1 tile_8 tile_5) (paint-up robot2 tile_6 tile_3 white) (left robot2 tile_3 tile_2) (left robot2 tile_2 tile_1) (paint-up robot2 tile_4 tile_1 white) (paint-up robot1 tile_8 tile_5 white) (down robot1 tile_5 tile_2) (change-color robot1 white black) (right robot1 tile_2 tile_3) (right robot2 tile_1 tile_2) (change-color robot2 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (right robot1 tile_8 tile_9) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (change-color robot1 white black) (change-color robot1 black white) (paint-up robot2 tile_5 tile_2 black) (change-color robot1 white black)\"?", "answer": 37, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -4324032614135910875, "group": "validation_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, and tile_8 is to the right of tile_7. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_4 is down from tile_7, tile_1 is down from tile_4, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_9 and holding color white; tile_4, tile_8, tile_7, tile_3, tile_1, tile_6, and tile_5 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_5 is painted in black color, Tile tile_7 is painted in black color, and Tile tile_6 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from tile ?x to the right tile ?y, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(left robot1 tile_9 tile_8) (down robot1 tile_8 tile_5) (paint-up robot1 tile_8 tile_5 white) (change-color robot1 white black) (right robot1 tile_5 tile_6) (paint-up robot1 tile_9 tile_6 black) (change-color robot1 black white) (down robot1 tile_6 tile_3) (paint-up robot2 tile_5 tile_2 black) (left robot2 tile_2 tile_1) (paint-up robot1 tile_6 tile_3 white) (up robot2 tile_1 tile_4) (paint-up robot2 tile_7 tile_4 black) (down robot2 tile_4 tile_1) (change-color robot2 black white) (paint-up robot1 tile_5 tile_2 white)\"?", "answer": 15, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -3839881170886649051, "group": "validation_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_3 is down from tile_6, tile_9 is down from tile_12, tile_6 is down from tile_9, tile_4 is down from tile_7, tile_1 is down from tile_4, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_11 and holding color black and robot robot1 is at tile_6 and holding color white; tile_12, tile_4, tile_8, tile_9, tile_10, tile_3, tile_1, tile_7, tile_2, and tile_5 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_10 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_5 is painted in black color, Tile tile_7 is painted in black color, and Tile tile_6 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(up robot1 tile_6 tile_9) (paint-up robot1 tile_12 tile_9 white) (down robot2 tile_11 tile_8) (paint-up robot2 tile_11 tile_8 black) (left robot2 tile_8 tile_7) (left robot1 tile_9 tile_8) (down robot2 tile_7 tile_4) (left robot1 tile_8 tile_7) (paint-up robot1 tile_10 tile_7 white) (right robot1 tile_7 tile_8) (paint-up robot2 tile_7 tile_4 black) (right robot2 tile_4 tile_5) (paint-up robot1 tile_5 tile_2 black) (paint-up robot2 tile_9 tile_6 black) (down robot2 tile_6 tile_3) (change-color robot2 black white) (paint-up robot2 tile_6 tile_3 white) (left robot2 tile_3 tile_2) (left robot2 tile_2 tile_1) (paint-up robot2 tile_4 tile_1 white) (down robot1 tile_8 tile_5) (paint-up robot1 tile_8 tile_5 white) (down robot1 tile_5 tile_2) (change-color robot1 white black) (paint-up robot1 tile_5 tile_2 black)\"?", "answer": 12, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_11) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 8226068209230748309, "group": "validation_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, and tile_8 is to the right of tile_7. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_4 is down from tile_7, tile_1 is down from tile_4, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_9 and holding color white; tile_4, tile_8, tile_7, tile_3, tile_1, tile_6, and tile_5 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_5 is painted in black color, Tile tile_7 is painted in black color, and Tile tile_6 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to its left.", "question": "What is the first inapplicable action in the next sequence of actions: \"(down robot1 tile_9 tile_6) (left robot1 tile_6 tile_5) (paint-up robot1 tile_8 tile_5 white) (change-color robot1 white black) (up robot2 tile_2 tile_5) (paint-up robot1 tile_9 tile_6 black) (left robot1 tile_6 tile_5) (left robot1 tile_5 tile_4) (paint-up robot2 tile_5 tile_2 black) (paint-up robot1 tile_7 tile_4 black) (down robot1 tile_4 tile_1) (right robot2 tile_2 tile_3) (change-color robot2 black white) (paint-up robot2 tile_6 tile_3 white) (change-color robot1 black white) (paint-up robot1 tile_4 tile_1 white) (right robot1 tile_1 tile_2)\"?", "answer": 4, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 5070745387097520946, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 2 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 and ball3 are at room2, ball1 and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is at room1 location, Ball ball1 is at room1 location, Ball ball2 is at room1 location, and Ball ball3 is in room room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room1 room2) (pick robot1 ball3 room2 left1) (drop robot1 ball3 room2 left1) (pick robot1 ball4 room2 right1) (pick robot1 ball3 room2 left1) (move robot1 room2 room1) (drop robot1 ball3 room1 left1) (drop robot1 ball4 room1 right1) (pick robot1 ball4 room2 left1)\"?", "answer": 8, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room2) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room1) (at ball4 room1)))\n)"}
{"id": -5291605621537774136, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 2 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 and ball3 are at room2, ball1 and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is in room room1, Ball ball2 is at room1 location, and Ball ball3 is in room room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room1 room2) (pick robot1 ball3 room2 left1) (pick robot1 ball4 room2 right1) (pick robot1 ball4 room2 left1) (drop robot1 ball3 room1 left1) (drop robot1 ball4 room1 right1) (move robot1 room1 room2) (move robot1 room2 room1) (move robot1 room1 room2)\"?", "answer": 3, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room2) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room1) (at ball4 room1)))\n)"}
{"id": -8978321776887898105, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball3 is at room3, ball1, ball4, and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is in room room3, Ball ball1 is at room3 location, Ball ball2 is in room room2, and Ball ball3 is in room room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room3 room1) (pick robot1 ball1 room1 left1) (pick robot1 ball2 room1 right1) (move robot1 room1 room2) (drop robot1 ball2 room2 right1) (move robot1 room2 room1) (pick robot1 ball4 room1 right1) (drop robot1 ball2 room1 right1) (move robot1 room2 room3) (drop robot1 ball1 room3 left1) (drop robot1 ball4 room3 right1)\"?", "answer": 7, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": 299556045967411851, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2 and both grippers are free. Additionally, ball4 is at room3, ball1 and ball3 are at room2, ball2 is at room1. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball4 is in room room1, Ball ball1 is at room1 location, and Ball ball2 is in room room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball1 room2 right1) (move robot1 room2 room3) (pick robot1 ball4 room3 left1) (move robot1 room3 room1) (pick robot1 ball2 room1 left1) (pick robot1 ball4 room1 left1) (drop robot1 ball4 room1 left1) (drop robot1 ball1 room1 right1) (move robot1 room1 room2)\"?", "answer": 4, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room3) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 1964512194049385639, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1 and ball2 are at room3, ball4 is at room6, ball3 is at room5. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is in room room1, Ball ball2 is at room2 location, and Ball ball3 is at room3 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball1 room3 left1) (pick robot1 ball2 room3 right1) (move robot1 room3 room4) (move robot1 room4 room2) (drop robot1 ball2 room2 right1) (move robot1 room2 room6) (move robot1 room5 room2) (move robot1 room6 room1) (drop robot1 ball1 room1 left1) (drop robot1 ball4 room1 right1) (move robot1 room1 room5) (pick robot1 ball3 room5 right1) (move robot1 room5 room3) (drop robot1 ball3 room3 right1)\"?", "answer": 6, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room3) (at ball2 room3) (at ball3 room5) (at ball4 room6) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 4116588710586017227, "group": "validation_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective4. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint0. Objective objective2 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2 and waypoint1. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store1 and store0 are empty.  The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint2;, Image objective0 was communicated in mode high_res, Image objective2 was communicated in mode high_res, Rock data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;. The available actions are: (navigate ?x ?y ?z) - move the rover ?x from waypoint ?y to waypoint ?z, (sample_soil ?x ?s ?p) - collect a soil sample from waypoint ?p using rover ?x and deposit it in the store ?s, (sample_rock ?x ?s ?p) - sample a rock at waypoint ?p using rover ?x, then store it in the storage unit ?s, (drop ?x ?y) - unload the store ?y from the rover ?x, (calibrate ?r ?i ?t ?w) - calibrate the camera ?i on the rover ?r for the objective ?t at the waypoint ?w, (take_image ?r ?p ?o ?i ?m) - take an image of the objective ?o in mode ?m using the camera ?i on the rover ?r from the waypoint ?p, (communicate_soil_data ?r ?l ?p ?x ?y) - transmit soil data from rover ?r at waypoint ?x to the lander ?l at waypoint ?y, regarding the soil analysis of waypoint ?p, (communicate_rock_data ?r ?l ?p ?x ?y) - communicate rock data from rover ?r at waypoint ?x about waypoint ?p to lander ?l at waypoint ?y, and (communicate_image_data ?r ?l ?o ?m ?x ?y) - communicate the image data of target ?o in mode ?m from rover ?r at waypoint ?x to lander ?l at waypoint ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(navigate rover1 waypoint1 waypoint2) (sample_rock rover0 store0 waypoint1) (navigate rover0 waypoint1 waypoint2) (communicate_rock_data rover0 general waypoint1 waypoint2 waypoint1) (sample_soil rover1 store1 waypoint2) (communicate_soil_data rover1 general waypoint2 waypoint2 waypoint1) (calibrate rover1 camera1 objective0 waypoint2) (take_image rover1 waypoint2 objective0 camera1 high_res) (communicate_image_data rover1 general objective0 high_res waypoint2 waypoint1) (calibrate rover1 camera1 objective0 waypoint2) (navigate rover1 waypoint2 waypoint1) (navigate rover1 waypoint1 waypoint0) (take_image rover1 waypoint0 objective2 camera1 high_res) (communicate_image_data rover1 general objective2 high_res waypoint0 waypoint1) (drop rover0 store0) (take_image rover1 waypoint0 objective2 camera0 low_res) (communicate_rock_data rover0 general waypoint2 waypoint2 waypoint1) (drop rover1 store1) (sample_soil rover1 store1 waypoint0) (communicate_soil_data rover1 general waypoint0 waypoint0 waypoint1) (navigate rover1 waypoint0 waypoint1) (navigate rover1 waypoint1 waypoint2) (calibrate rover1 camera1 objective0 waypoint2) (take_image rover1 waypoint2 objective0 camera1 high_res)\"?", "answer": 15, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-5-2-5-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 objective3 objective4 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint1) (at rover1 waypoint1) (at_lander general waypoint1) (at_rock_sample waypoint1) (at_rock_sample waypoint2) (at_soil_sample waypoint0) (at_soil_sample waypoint2) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective4) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1) (visible_from objective2 waypoint0) (visible_from objective3 waypoint0) (visible_from objective4 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_soil_data waypoint2) (communicated_rock_data waypoint2) (communicated_rock_data waypoint1) (communicated_image_data objective2 high_res) (communicated_image_data objective0 high_res)))\n)"}
{"id": 5488206533017081116, "group": "validation_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store1 and store0 are empty.  The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint2;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Image objective0 was communicated in mode low_res, Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;. The available actions are: (navigate ?x ?y ?z) - move the rover ?x from waypoint ?y to waypoint ?z, (sample_soil ?x ?s ?p) - sample soil at waypoint ?p with rover ?x and store in the store ?s, (sample_rock ?x ?s ?p) - sample a rock at waypoint ?p using rover ?x, then store it in the storage unit ?s, (drop ?x ?y) - drop store ?y of rover ?x, (calibrate ?r ?i ?t ?w) - calibrate the camera ?i on the rover ?r for the objective ?t at the waypoint ?w, (take_image ?r ?p ?o ?i ?m) - take an image of objective ?o in mode ?m using camera ?i on rover ?r from waypoint ?p, (communicate_soil_data ?r ?l ?p ?x ?y) - communicate soil data from rover ?r at waypoint ?x with soil analysis of waypoint ?p to lander ?l at waypoint ?y, (communicate_rock_data ?r ?l ?p ?x ?y) - communicate rock data from rover ?r at waypoint ?x about waypoint ?p to lander ?l at waypoint ?y, and (communicate_image_data ?r ?l ?o ?m ?x ?y) - communicate the image data of target ?o in mode ?m from rover ?r at waypoint ?x to lander ?l at waypoint ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(sample_rock rover1 store1 waypoint2) (communicate_rock_data rover1 general waypoint2 waypoint2 waypoint1) (sample_soil rover0 store0 waypoint2) (communicate_soil_data rover0 general waypoint2 waypoint2 waypoint1) (navigate rover0 waypoint2 waypoint1) (calibrate rover0 camera1 objective1 waypoint1) (take_image rover0 waypoint1 objective1 camera1 high_res) (navigate rover1 waypoint2 waypoint0) (calibrate rover0 camera1 objective1 waypoint1) (take_image rover0 waypoint1 objective1 camera1 low_res) (calibrate rover0 camera1 objective1 waypoint1) (take_image rover0 waypoint1 objective0 camera1 low_res) (navigate rover0 waypoint1 waypoint2) (communicate_image_data rover0 general objective0 low_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 high_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1) (navigate rover0 waypoint2 waypoint0) (drop rover0 store0) (sample_soil rover0 store0 waypoint0) (communicate_soil_data rover0 general waypoint0 waypoint0 waypoint1) (drop rover0 store0) (drop rover1 store1) (navigate rover1 waypoint2 waypoint0) (communicate_rock_data rover1 general waypoint0 waypoint0 waypoint1) (drop rover1 store1)\"?", "answer": 22, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2022)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint2) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_rock_sample waypoint2) (at_soil_sample waypoint0) (at_soil_sample waypoint2) (available rover0) (available rover1) (calibration_target camera0 objective2) (calibration_target camera1 objective1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (on_board camera0 rover1) (on_board camera1 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 low_res) (supports camera1 high_res) (supports camera1 low_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint1) (visible_from objective2 waypoint1))\n    (:goal (and (communicated_soil_data waypoint2) (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_image_data objective0 low_res) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": 9171608096940618752, "group": "validation_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Store(s) store1 and store0 are empty.  The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Soil data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;. The available actions are: (navigate ?x ?y ?z) - navigate with rover ?x to waypoint ?z from waypoint ?y, (sample_soil ?x ?s ?p) - collect a soil sample from waypoint ?p using rover ?x and deposit it in the store ?s, (sample_rock ?x ?s ?p) - sample rock at waypoint ?p with rover ?x and store in store ?s, (drop ?x ?y) - drop the content from store ?y of the rover ?x, (calibrate ?r ?i ?t ?w) - calibrate camera ?i on rover ?r for objective ?t at waypoint ?w, (take_image ?r ?p ?o ?i ?m) - capture an image of objective ?o in mode ?m using the camera ?i on the rover ?r from waypoint ?p, (communicate_soil_data ?r ?l ?p ?x ?y) - transmit soil data from rover ?r at waypoint ?x to the lander ?l at waypoint ?y, regarding the soil analysis of waypoint ?p, (communicate_rock_data ?r ?l ?p ?x ?y) - transmit the rock data from rover ?r at waypoint ?x to the lander ?l at waypoint ?y through waypoint ?p, and (communicate_image_data ?r ?l ?o ?m ?x ?y) - transmit the image data of the objective ?o in mode ?m from the rover ?r at waypoint ?x to the lander ?l at waypoint ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(sample_soil rover1 store1 waypoint1) (navigate rover0 waypoint1 waypoint0) (take_image rover1 waypoint2 objective0 camera1 high_res) (communicate_rock_data rover0 general waypoint0 waypoint0 waypoint1) (calibrate rover0 camera2 objective1 waypoint0) (take_image rover0 waypoint0 objective1 camera2 high_res) (communicate_image_data rover0 general objective1 high_res waypoint0 waypoint1) (navigate rover1 waypoint1 waypoint2) (calibrate rover1 camera1 objective0 waypoint2) (communicate_soil_data rover1 general waypoint1 waypoint2 waypoint1) (calibrate rover1 camera1 objective0 waypoint2) (take_image rover1 waypoint2 objective0 camera1 high_res) (communicate_image_data rover1 general objective0 high_res waypoint2 waypoint1) (drop rover0 store0) (sample_soil rover0 store0 waypoint0) (communicate_soil_data rover0 general waypoint0 waypoint0 waypoint1) (navigate rover0 waypoint0 waypoint1) (navigate rover0 waypoint1 waypoint2) (drop rover0 store0) (sample_rock rover0 store0 waypoint2) (communicate_rock_data rover0 general waypoint2 waypoint2 waypoint1)\"?", "answer": 2, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint1) (at rover1 waypoint1) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_rock_sample waypoint2) (at_soil_sample waypoint0) (at_soil_sample waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_soil_data waypoint1) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_image_data objective1 high_res) (communicated_image_data objective0 high_res)))\n)"}
{"id": -718982515424108062, "group": "validation_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store1 and store0 are empty.  The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint2;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Image objective0 was communicated in mode low_res, Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;. The available actions are: (navigate ?x ?y ?z) - navigate with rover ?x to waypoint ?z from waypoint ?y, (sample_soil ?x ?s ?p) - sample the soil at waypoint ?p with rover ?x and store it in store ?s, (sample_rock ?x ?s ?p) - sample rock at waypoint ?p with rover ?x and store in store ?s, (drop ?x ?y) - drop store ?y of rover ?x, (calibrate ?r ?i ?t ?w) - calibrate the camera ?i on the rover ?r for the objective ?t at the waypoint ?w, (take_image ?r ?p ?o ?i ?m) - take an image of objective ?o in mode ?m using camera ?i on rover ?r from waypoint ?p, (communicate_soil_data ?r ?l ?p ?x ?y) - communicate the soil data from the rover ?r at waypoint ?x with the soil analysis of waypoint ?p to the lander ?l at waypoint ?y, (communicate_rock_data ?r ?l ?p ?x ?y) - communicate the rock data from rover ?r at waypoint ?x to lander ?l at waypoint ?y via waypoint ?p, and (communicate_image_data ?r ?l ?o ?m ?x ?y) - transmit the image data of the objective ?o in mode ?m from the rover ?r at waypoint ?x to the lander ?l at waypoint ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(sample_rock rover1 store1 waypoint2) (communicate_rock_data rover1 general waypoint2 waypoint2 waypoint1) (sample_soil rover0 store0 waypoint2) (communicate_soil_data rover0 general waypoint2 waypoint2 waypoint1) (navigate rover0 waypoint2 waypoint1) (calibrate rover0 camera1 objective1 waypoint1) (take_image rover0 waypoint1 objective1 camera1 high_res) (calibrate rover0 camera1 objective1 waypoint1) (take_image rover0 waypoint1 objective1 camera1 low_res) (calibrate rover0 camera1 objective1 waypoint1) (take_image rover0 waypoint1 objective0 camera1 low_res) (navigate rover0 waypoint1 waypoint2) (communicate_image_data rover0 general objective0 low_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 high_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1) (navigate rover0 waypoint2 waypoint0) (drop rover0 store0) (sample_soil rover0 store0 waypoint0) (communicate_soil_data rover0 general waypoint0 waypoint0 waypoint1) (drop rover0 store0) (sample_rock rover0 store0 waypoint0) (communicate_rock_data rover0 general waypoint0 waypoint0 waypoint1) (navigate rover0 waypoint0 waypoint2) (navigate rover0 waypoint1 waypoint2) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1) (communicate_image_data rover0 general objective1 low_res waypoint2 waypoint1)\"?", "answer": 23, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2022)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint2) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_rock_sample waypoint2) (at_soil_sample waypoint0) (at_soil_sample waypoint2) (available rover0) (available rover1) (calibration_target camera0 objective2) (calibration_target camera1 objective1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (on_board camera0 rover1) (on_board camera1 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 low_res) (supports camera1 high_res) (supports camera1 low_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint1) (visible_from objective2 waypoint1))\n    (:goal (and (communicated_soil_data waypoint2) (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_image_data objective0 low_res) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": 6112105464435490609, "group": "validation_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Store(s) store1 and store0 are empty.  The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Soil data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;. The available actions are: (navigate ?x ?y ?z) - navigate with rover ?x to waypoint ?z from waypoint ?y, (sample_soil ?x ?s ?p) - use the rover ?x to collect soil samples at waypoint ?p and store them in the store ?s, (sample_rock ?x ?s ?p) - collect a rock sample from waypoint ?p using rover ?x and store it in store ?s, (drop ?x ?y) - drop from store ?y of rover ?x, (calibrate ?r ?i ?t ?w) - calibrate the camera ?i on rover ?r for the objective ?t at the waypoint ?w, (take_image ?r ?p ?o ?i ?m) - take a picture of the objective ?o in mode ?m using the camera ?i mounted on the rover ?r from the waypoint ?p, (communicate_soil_data ?r ?l ?p ?x ?y) - transmit soil data from rover ?r located at waypoint ?x to lander ?l located at waypoint ?y using soil analysis of waypoint ?p, (communicate_rock_data ?r ?l ?p ?x ?y) - transmit the rock data from rover ?r at waypoint ?x to the lander ?l at waypoint ?y through waypoint ?p, and (communicate_image_data ?r ?l ?o ?m ?x ?y) - communicate the image data of target ?o in mode ?m from rover ?r at waypoint ?x to lander ?l at waypoint ?y.", "question": "What is the first inapplicable action in the next sequence of actions: \"(calibrate rover1 camera0 objective0 waypoint2) (navigate rover0 waypoint1 waypoint0) (sample_rock rover0 store0 waypoint0) (communicate_rock_data rover0 general waypoint0 waypoint0 waypoint1) (calibrate rover0 camera2 objective1 waypoint0) (take_image rover0 waypoint0 objective1 camera2 high_res) (communicate_image_data rover0 general objective1 high_res waypoint0 waypoint1) (communicate_image_data rover0 general objective1 high_res waypoint0 waypoint1) (navigate rover1 waypoint1 waypoint0) (communicate_soil_data rover1 general waypoint1 waypoint0 waypoint1) (calibrate rover0 camera2 objective1 waypoint0) (navigate rover0 waypoint0 waypoint1) (navigate rover0 waypoint1 waypoint2) (take_image rover0 waypoint2 objective0 camera2 high_res) (communicate_image_data rover0 general objective0 high_res waypoint2 waypoint1) (drop rover0 store0) (sample_rock rover0 store0 waypoint2) (communicate_rock_data rover0 general waypoint2 waypoint2 waypoint1) (drop rover1 store1) (sample_soil rover1 store1 waypoint0) (communicate_soil_data rover1 general waypoint0 waypoint0 waypoint1)\"?", "answer": 0, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint1) (at rover1 waypoint1) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_rock_sample waypoint2) (at_soil_sample waypoint0) (at_soil_sample waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_soil_data waypoint1) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_image_data objective1 high_res) (communicated_image_data objective0 high_res)))\n)"}
{"id": 6168951149728630819, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y0, and loc-x0-y3. \nCurrently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - go to ?nextpos from ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y3 loc-x2-y3)\"?", "answer": 8, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-3-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 - place)\n    (:init (at-robot loc-x3-y2) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2)))\n)"}
{"id": -6860106509883848266, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move from place ?curpos to place ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x0-y2 loc-x0-y3) (move loc-x3-y1 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x2-y3)\"?", "answer": 7, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 712407344306479888, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. \nCurrently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x2-y3) (move loc-x2-y3 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x2-y0) (move loc-x1-y0 loc-x0-y0)\"?", "answer": 15, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x0-y3) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -4346974138748284013, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move from ?curpos to ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y1 loc-x3-y1) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0)\"?", "answer": 3, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -7762072371428479018, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - Please move to ?nextpos position from ?curpos position.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1)\"?", "answer": 8, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
