#!/usr/bin/env python3
"""
Script to organize benchmark data by domain and task.
Reads JSONL files and separates them based on domain keywords found in the context field.
"""

import json
import os
import glob
from pathlib import Path

# Domain keywords mapping
DOMAIN_KEYWORDS = {
    'ferry': 'this is a ferry domain',
    'logistics': 'there are several cities',
    'blocksworld': 'this is a blocksworld domain',
    'grid': 'a robot is in a grid',
    'floortile': 'a set of robots use different colors to paint patterns',
    'grippers': 'this is a grippers domain',
    'rovers': 'this is a rovers domain',
    'visitall': 'this is a visitall domain',
    'depot': 'this is a depot domain',
    'goldminer': 'a robotic arm is in a grid and can only move to locations that are connected to its current location',
    'satellite': 'this domain consists of satellite(s) equipped',
    'swap': 'this is a swap domain where',
    'alfworld': 'this is an alfworld'
}

def identify_domain(context):
    """Identify domain based on context content."""
    context_lower = context.lower()

    for domain, keyword in DOMAIN_KEYWORDS.items():
        if keyword in context_lower:
            return domain

    return 'unknown'

def extract_task_name(file_path):
    """Extract task name from file path."""
    # Get the parent directory name which contains the task info
    parent_dir = Path(file_path).parent.name

    # Remove 'acp_' prefix if present
    if parent_dir.startswith('acp_'):
        task_name = parent_dir[4:]  # Remove 'acp_' prefix
    else:
        task_name = parent_dir

    return task_name

def process_jsonl_file(file_path, domain_data_accumulator):
    """Process a single JSONL file and accumulate data by domain."""
    print(f"Processing: {file_path}")

    task_name = extract_task_name(file_path)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                    context = data.get('context', '')
                    domain = identify_domain(context)

                    # Create nested structure: domain -> task -> data_list
                    if domain not in domain_data_accumulator:
                        domain_data_accumulator[domain] = {}

                    if task_name not in domain_data_accumulator[domain]:
                        domain_data_accumulator[domain][task_name] = []

                    domain_data_accumulator[domain][task_name].append(data)

                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON in {file_path} at line {line_num}: {e}")
                    continue

    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return

def write_organized_data(domain_data_accumulator, output_dir):
    """Write the accumulated domain data to organized files."""
    for domain, tasks in domain_data_accumulator.items():
        for task_name, data_list in tasks.items():
            if not data_list:
                continue

            output_filename = f"{domain}-{task_name}.jsonl"
            output_path = os.path.join(output_dir, output_filename)

            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    for item in data_list:
                        f.write(json.dumps(item, ensure_ascii=False) + '\n')

                print(f"Created: {output_filename} with {len(data_list)} samples")

            except Exception as e:
                print(f"Error writing to {output_path}: {e}")

def main():
    """Main function to process all JSONL files in the benchmark directory."""
    benchmark_dir = "benchmark/ACPBench"
    output_dir = "benchmark/organized_data"

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Find all JSONL files in the benchmark directory
    jsonl_files = []
    for root, _, files in os.walk(benchmark_dir):
        for file in files:
            if file.endswith('.jsonl'):
                jsonl_files.append(os.path.join(root, file))

    print(f"Found {len(jsonl_files)} JSONL files to process")

    # Accumulate all data first
    domain_data_accumulator = {}

    # Process each file
    for file_path in sorted(jsonl_files):
        process_jsonl_file(file_path, domain_data_accumulator)

    # Write all organized data
    print(f"\nWriting organized data to: {output_dir}")
    write_organized_data(domain_data_accumulator, output_dir)

    print(f"\nProcessing complete! Organized data saved to: {output_dir}")

    # Print summary
    output_files = glob.glob(os.path.join(output_dir, "*.jsonl"))
    print(f"Created {len(output_files)} organized files:")
    for file_path in sorted(output_files):
        filename = os.path.basename(file_path)
        print(f"  {filename}")

if __name__ == "__main__":
    main()
