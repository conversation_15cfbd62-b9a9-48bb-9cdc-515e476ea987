{"id": 6812576411098478670, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, michelle is assigned zebra, xena is assigned slinky, vic is assigned whale, alice is assigned iceskates, carol is assigned frisbee, dave is assigned guitar, zoe is assigned quadcopter, and heidi is assigned necklace. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? xena is assigned frisbee", "answer": "no"}
{"id": -2290004044155730302, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, bob, liam, quentin, vic, and xena. There are 6 items/roles: pliers, nibbler, wrench, sander, ratchet, and knead. Currently, vic is assigned wrench, quentin is assigned ratchet, xena is assigned sander, liam is assigned nibbler, bob is assigned pliers, and frank is assigned knead. The goal is to reach a state where the following facts hold: xena is assigned wrench, quentin is assigned knead, bob is assigned nibbler, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? quentin is assigned sander", "answer": "no"}
{"id": -5434473652666980808, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, xena is assigned zebra, alice is assigned necklace, carol is assigned whale, zoe is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, vic is assigned slinky, and dave is assigned iceskates. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? vic is assigned necklace", "answer": "yes"}
{"id": -5516021290527302588, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, bob, liam, quentin, vic, and xena. There are 6 items/roles: pliers, nibbler, wrench, sander, ratchet, and knead. Currently, bob is assigned nibbler, xena is assigned knead, quentin is assigned ratchet, liam is assigned sander, frank is assigned wrench, and vic is assigned pliers. The goal is to reach a state where the following facts hold: xena is assigned wrench, quentin is assigned knead, bob is assigned nibbler, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? quentin is assigned sander", "answer": "no"}
{"id": 2049834357869796001, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, xena is assigned whale, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, vic is assigned slinky, dave is assigned iceskates, and zoe is assigned necklace. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? vic is assigned necklace", "answer": "yes"}
{"id": 6229289268775576392, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, ted, alice, bob, kevin, dave, and xena. There are 7 items/roles: valerian, yam, parsnip, mushroom, quince, leek, and ulluco. Currently, heidi is assigned quince, kevin is assigned mushroom, dave is assigned parsnip, xena is assigned leek, bob is assigned valerian, ted is assigned ulluco, and alice is assigned yam. The goal is to reach a state where the following facts hold: kevin is assigned yam, bob is assigned parsnip, heidi is assigned mushroom, ted is assigned leek, xena is assigned quince, dave is assigned ulluco, and alice is assigned valerian.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? heidi is assigned mushroom", "answer": "yes"}
{"id": 2212203000903997920, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, xena is assigned slinky, vic is assigned guitar, carol is assigned quadcopter, heidi is assigned zebra, zoe is assigned whale, michelle is assigned necklace, dave is assigned iceskates, and alice is assigned frisbee. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? vic is assigned necklace", "answer": "yes"}
{"id": -5124016585761956714, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, bob, liam, quentin, vic, and xena. There are 6 items/roles: pliers, nibbler, wrench, sander, ratchet, and knead. Currently, frank is assigned nibbler, xena is assigned wrench, quentin is assigned sander, liam is assigned knead, bob is assigned pliers, and vic is assigned ratchet. The goal is to reach a state where the following facts hold: xena is assigned wrench, quentin is assigned knead, bob is assigned nibbler, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? bob is assigned nibbler", "answer": "yes"}
{"id": -7356083227961421425, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, michelle is assigned frisbee, vic is assigned quadcopter, xena is assigned slinky, alice is assigned whale, zoe is assigned zebra, dave is assigned iceskates, carol is assigned guitar, and heidi is assigned necklace. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? michelle is assigned quadcopter", "answer": "yes"}
{"id": 3735483598263633190, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: bob, steve, zoe, and vic. There are 4 items/roles: book04, book02, book03, and book01. Currently, vic is assigned book04, bob is assigned book01, steve is assigned book03, and zoe is assigned book02. The goal is to reach a state where the following facts hold: vic is assigned book04, bob is assigned book02, zoe is assigned book01, and steve is assigned book03.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? bob is assigned book03", "answer": "no"}
