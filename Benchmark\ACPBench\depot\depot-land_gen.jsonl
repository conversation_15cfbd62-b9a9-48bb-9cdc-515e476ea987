{"id": 1426324260687767671, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 4 depots, 6 hoists, 3 crates, 2 trucks, numbered consecutively. Currently, pallet2, pallet5, crate0, pallet0, pallet4, and pallet1 are clear; hoist5, hoist3, hoist2, hoist4, and hoist0 are available; truck1 is at depot3, pallet3 is at depot3, hoist5 is at distributor1, pallet4 is at distributor0, pallet1 is at depot1, hoist2 is at depot2, crate0 is at depot3, truck0 is at depot1, hoist4 is at distributor0, hoist0 is at depot0, pallet2 is at depot2, hoist3 is at depot3, pallet5 is at distributor1, pallet0 is at depot0, and hoist1 is at depot1; crate0 is on pallet3; crate1 is in truck0; hoist1 is lifting crate2. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear pallet3)", "(lifting hoist0 crate2)", "(on crate1 pallet0)", "(on crate0 pallet5)", "(at truck1 depot2)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate0 crate1)", "(at crate0 depot1)", "(in crate0 truck1)", "(at truck1 distributor0)", "(at crate2 depot2)", "(on crate1 pallet1)", "(on crate2 crate1)", "(on crate0 crate0)", "(lifting hoist3 crate2)", "(on crate1 pallet2)", "(at truck1 depot1)", "(at crate2 depot3)", "(lifting hoist5 crate1)", "(lifting hoist2 crate0)", "(in crate2 truck0)", "(at truck0 depot0)", "(at crate1 depot2)", "(lifting hoist2 crate1)", "(at truck0 depot2)", "(at crate0 depot0)", "(lifting hoist2 crate2)", "(in crate0 truck0)", "(lifting hoist0 crate1)", "(on crate2 pallet3)", "(at crate0 distributor1)", "(at crate2 depot1)", "(on crate2 crate2)", "(on crate0 crate2)", "(at truck0 depot3)", "(on crate1 pallet5)", "(in crate1 truck1)", "(on crate2 pallet1)", "(lifting hoist1 crate1)", "(lifting hoist5 crate0)", "(at crate0 distributor0)", "(at crate0 depot2)", "(on crate0 pallet2)", "(on crate2 pallet2)", "(on crate1 crate0)", "(lifting hoist4 crate0)", "(on crate1 pallet3)", "(at crate1 depot3)", "(at crate2 distributor0)", "(at crate1 distributor1)", "(at truck0 distributor1)", "(at truck0 distributor0)", "(on crate2 pallet0)", "(on crate2 crate0)", "(at crate1 depot1)", "(on crate1 crate1)", "(on crate0 pallet4)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at crate2 depot0)", "(at truck1 distributor1)", "(at crate1 distributor0)", "(clear crate1)", "(at crate2 distributor1)", "(at truck1 depot0)", "(lifting hoist0 crate0)", "(in crate2 truck1)", "(lifting hoist5 crate2)", "(on crate2 pallet5)"], "yes": ["(lifting hoist1 crate0)", "(clear crate2)", "(lifting hoist4 crate2)", "(available hoist1)", "(lifting hoist3 crate0)", "(lifting hoist4 crate1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot1) (at truck1 depot3) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (in crate1 truck0) (lifting hoist1 crate2) (on crate0 pallet3))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -6942716640831271883, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 pallets, 2 distributors, 3 depots, 5 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate0, pallet0, and pallet4 are clear; hoist3, hoist2, hoist4, hoist0, and hoist1 are available; crate0 is at depot1, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, hoist0 is at depot0, pallet2 is at depot2, truck0 is at distributor1, pallet4 is at distributor1, hoist3 is at distributor0, pallet3 is at distributor0, pallet0 is at depot0, and hoist1 is at depot1; crate0 is on pallet1; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate0 is on pallet1 and crate1 is on pallet2. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on crate1 pallet0)", "(at truck1 depot2)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate0 crate1)", "(in crate0 truck1)", "(on crate0 crate0)", "(at truck1 depot1)", "(on crate1 pallet1)", "(lifting hoist4 crate1)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(at truck0 depot0)", "(at crate1 depot2)", "(at truck0 depot2)", "(lifting hoist1 crate0)", "(at crate0 depot0)", "(in crate0 truck0)", "(lifting hoist0 crate1)", "(at truck0 depot1)", "(at crate0 distributor1)", "(in crate1 truck1)", "(at crate0 distributor0)", "(on crate1 crate0)", "(lifting hoist1 crate1)", "(lifting hoist4 crate0)", "(at crate0 depot2)", "(clear pallet1)", "(on crate1 pallet3)", "(on crate0 pallet2)", "(at crate1 distributor1)", "(lifting hoist3 crate0)", "(at truck0 distributor0)", "(on crate0 pallet4)", "(at crate1 depot1)", "(on crate1 crate1)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at truck1 distributor1)", "(at crate1 distributor0)", "(clear crate1)", "(at truck1 depot0)", "(lifting hoist0 crate0)"], "yes": ["(lifting hoist2 crate1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 distributor1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (clear crate0) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (in crate1 truck0) (on crate0 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": -7156513161656193077, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 10 depots, 12 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, pallet11, pallet5, pallet8, pallet0, pallet4, pallet1, pallet6, pallet7, pallet10, and crate1 are clear; hoist5, hoist8, hoist10, hoist3, hoist2, hoist6, hoist4, hoist7, hoist0, hoist1, and hoist9 are available; pallet4 is at depot4, hoist7 is at depot7, pallet3 is at depot3, pallet9 is at depot9, hoist5 is at depot5, pallet7 is at depot7, pallet1 is at depot1, hoist9 is at depot9, hoist6 is at depot6, hoist11 is at distributor1, hoist2 is at depot2, hoist8 is at depot8, pallet11 is at distributor1, pallet5 is at depot5, pallet6 is at depot6, crate1 is at depot9, hoist0 is at depot0, pallet2 is at depot2, pallet8 is at depot8, truck0 is at distributor1, hoist3 is at depot3, hoist10 is at distributor0, hoist4 is at depot4, pallet0 is at depot0, pallet10 is at distributor0, truck1 is at depot4, and hoist1 is at depot1; crate1 is on pallet9; hoist11 is lifting crate0. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at truck1 depot3)", "(on crate0 pallet1)", "(on crate0 pallet9)", "(at crate1 depot5)", "(on crate1 pallet0)", "(on crate0 pallet10)", "(on crate1 pallet4)", "(on crate0 crate1)", "(at truck0 depot0)", "(lifting hoist2 crate1)", "(at truck0 depot4)", "(at truck0 depot7)", "(lifting hoist7 crate1)", "(at crate0 distributor1)", "(at truck0 depot3)", "(at truck0 depot9)", "(at crate0 depot7)", "(lifting hoist6 crate0)", "(at crate0 depot9)", "(clear pallet9)", "(on crate0 pallet6)", "(at crate1 depot1)", "(on crate1 crate1)", "(lifting hoist3 crate1)", "(on crate1 pallet11)", "(at truck0 depot8)", "(lifting hoist9 crate0)", "(on crate0 pallet5)", "(on crate0 pallet0)", "(on crate1 pallet7)", "(at crate0 depot1)", "(at crate0 depot6)", "(on crate1 pallet8)", "(on crate1 pallet1)", "(at truck1 depot8)", "(at truck1 distributor0)", "(on crate1 pallet2)", "(at truck1 depot9)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(lifting hoist11 crate1)", "(at truck0 depot2)", "(at crate0 depot0)", "(in crate0 truck0)", "(clear crate0)", "(lifting hoist1 crate1)", "(at crate0 depot2)", "(on crate0 pallet2)", "(at crate1 depot3)", "(at crate0 depot5)", "(at truck0 depot5)", "(at crate1 depot0)", "(at truck1 distributor1)", "(at truck1 depot0)", "(at crate1 depot4)", "(at truck0 depot6)", "(on crate0 pallet8)", "(in crate0 truck1)", "(lifting hoist10 crate0)", "(lifting hoist4 crate1)", "(at crate1 depot7)", "(at crate0 depot8)", "(lifting hoist5 crate1)", "(at crate1 depot2)", "(lifting hoist1 crate0)", "(lifting hoist6 crate1)", "(at truck0 depot1)", "(on crate1 pallet5)", "(in crate1 truck1)", "(at truck1 depot5)", "(on crate1 pallet6)", "(at crate1 depot8)", "(lifting hoist7 crate0)", "(lifting hoist10 crate1)", "(lifting hoist9 crate1)", "(at crate1 distributor1)", "(at truck0 distributor0)", "(lifting hoist3 crate0)", "(on crate0 pallet4)", "(at truck1 depot7)", "(at crate1 distributor0)", "(in crate1 truck0)", "(on crate1 pallet10)", "(lifting hoist0 crate0)", "(at truck1 depot2)", "(at truck1 depot6)", "(on crate0 crate0)", "(at truck1 depot1)", "(lifting hoist8 crate0)", "(at crate0 depot4)", "(lifting hoist8 crate1)", "(at crate0 depot3)", "(lifting hoist0 crate1)", "(lifting hoist5 crate0)", "(at crate0 distributor0)", "(on crate1 crate0)", "(lifting hoist4 crate0)", "(on crate1 pallet3)", "(on crate0 pallet7)", "(at crate1 depot6)"], "yes": ["(available hoist11)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot9) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 distributor1) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (lifting hoist11 crate0) (on crate1 pallet9))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 7275217234086006669, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 4 depots, 6 hoists, 3 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, pallet5, crate0, crate2, and pallet0 are clear; hoist5, hoist3, hoist2, hoist0, and hoist1 are available; pallet3 is at depot3, hoist5 is at distributor1, truck1 is at depot2, crate0 is at depot1, pallet4 is at distributor0, pallet1 is at depot1, truck0 is at depot2, hoist2 is at depot2, crate2 is at distributor0, hoist0 is at depot0, hoist4 is at distributor0, pallet2 is at depot2, hoist3 is at depot3, pallet5 is at distributor1, pallet0 is at depot0, and hoist1 is at depot1; crate0 is on pallet1 and crate2 is on pallet4; hoist4 is lifting crate1. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at truck1 depot3)", "(lifting hoist0 crate2)", "(on crate1 pallet0)", "(on crate0 pallet5)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate0 crate1)", "(lifting hoist4 crate2)", "(in crate0 truck1)", "(at crate2 depot2)", "(at truck1 distributor0)", "(on crate1 pallet1)", "(on crate2 crate1)", "(on crate0 crate0)", "(at truck1 depot1)", "(on crate1 pallet2)", "(lifting hoist3 crate2)", "(at crate2 depot3)", "(lifting hoist5 crate1)", "(lifting hoist2 crate0)", "(in crate2 truck0)", "(on crate0 pallet3)", "(at truck0 depot0)", "(at crate1 depot2)", "(lifting hoist2 crate1)", "(lifting hoist2 crate2)", "(at crate0 depot0)", "(lifting hoist1 crate0)", "(in crate0 truck0)", "(at crate0 depot3)", "(lifting hoist0 crate1)", "(on crate2 pallet3)", "(at truck0 depot1)", "(at crate0 distributor1)", "(at crate2 depot1)", "(on crate2 crate2)", "(on crate0 crate2)", "(clear pallet4)", "(at truck0 depot3)", "(on crate1 pallet5)", "(in crate1 truck1)", "(on crate2 pallet1)", "(lifting hoist1 crate1)", "(lifting hoist5 crate0)", "(at crate0 distributor0)", "(at crate0 depot2)", "(on crate0 pallet2)", "(on crate2 pallet2)", "(clear pallet1)", "(on crate1 crate0)", "(lifting hoist4 crate0)", "(at crate1 depot3)", "(on crate1 pallet3)", "(at crate1 distributor1)", "(at truck0 distributor1)", "(at truck0 distributor0)", "(lifting hoist3 crate0)", "(on crate2 pallet0)", "(lifting hoist1 crate2)", "(on crate2 crate0)", "(at crate1 depot1)", "(on crate1 crate1)", "(on crate0 pallet4)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at crate2 depot0)", "(at truck1 distributor1)", "(at crate1 distributor0)", "(in crate1 truck0)", "(clear crate1)", "(at crate2 distributor1)", "(at truck1 depot0)", "(lifting hoist0 crate0)", "(in crate2 truck1)", "(lifting hoist5 crate2)", "(on crate2 pallet5)"], "yes": ["(available hoist4)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at crate2 distributor0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot2) (at truck1 depot2) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist5) (clear crate0) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet5) (lifting hoist4 crate1) (on crate0 pallet1) (on crate2 pallet4))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 358982951644516688, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 pallets, 2 distributors, 7 depots, 9 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet2, pallet8, pallet5, crate0, pallet0, pallet4, pallet6, pallet7, and crate1 are clear; hoist5, hoist8, hoist3, hoist2, hoist6, hoist4, hoist7, hoist0, and hoist1 are available; pallet4 is at depot4, pallet3 is at depot3, crate0 is at depot1, hoist7 is at distributor0, hoist5 is at depot5, pallet8 is at distributor1, hoist8 is at distributor1, pallet1 is at depot1, hoist6 is at depot6, hoist2 is at depot2, pallet5 is at depot5, pallet7 is at distributor0, pallet6 is at depot6, truck0 is at depot3, hoist0 is at depot0, pallet2 is at depot2, crate1 is at depot3, hoist3 is at depot3, hoist4 is at depot4, truck1 is at distributor1, pallet0 is at depot0, and hoist1 is at depot1; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear pallet3)", "(at truck1 depot3)", "(at crate1 depot4)", "(at crate1 depot5)", "(on crate1 pallet0)", "(on crate0 pallet5)", "(at truck0 depot6)", "(at truck1 depot2)", "(at truck1 depot6)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate1 pallet7)", "(on crate0 crate1)", "(at crate0 depot6)", "(in crate0 truck1)", "(on crate1 pallet8)", "(on crate1 pallet1)", "(at truck1 distributor0)", "(on crate0 crate0)", "(lifting hoist4 crate1)", "(at truck1 depot1)", "(on crate1 pallet2)", "(lifting hoist5 crate1)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(at crate0 depot4)", "(at truck0 depot0)", "(at crate1 depot2)", "(lifting hoist2 crate1)", "(at truck0 depot2)", "(at truck0 depot4)", "(at crate0 depot0)", "(lifting hoist6 crate1)", "(in crate0 truck0)", "(lifting hoist8 crate1)", "(at crate0 depot3)", "(lifting hoist0 crate1)", "(at truck1 depot4)", "(at truck0 depot1)", "(lifting hoist7 crate1)", "(at crate0 distributor1)", "(on crate1 pallet5)", "(in crate1 truck1)", "(lifting hoist5 crate0)", "(at crate0 distributor0)", "(lifting hoist1 crate1)", "(on crate1 crate0)", "(lifting hoist4 crate0)", "(lifting hoist6 crate0)", "(at crate0 depot2)", "(on crate0 pallet2)", "(at truck1 depot5)", "(on crate1 pallet6)", "(clear pallet1)", "(lifting hoist7 crate0)", "(at crate1 distributor1)", "(at crate0 depot5)", "(on crate0 pallet7)", "(at truck0 distributor1)", "(at truck0 distributor0)", "(lifting hoist3 crate0)", "(at crate1 depot6)", "(on crate0 pallet6)", "(at truck0 depot5)", "(at crate1 depot1)", "(on crate1 crate1)", "(on crate0 pallet4)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at crate1 distributor0)", "(at truck1 depot0)", "(lifting hoist0 crate0)", "(in crate1 truck0)"], "yes": ["(lifting hoist8 crate0)", "(lifting hoist1 crate0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot3) (at truck1 distributor1) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (on crate0 pallet1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -8990698019899809677, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 pallets, 2 distributors, 2 depots, 4 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist3, hoist2, and hoist1 are available; hoist3 is at distributor1, pallet1 is at depot1, pallet2 is at distributor0, hoist2 is at distributor0, hoist0 is at depot0, truck0 is at distributor0, pallet0 is at depot0, pallet3 is at distributor1, truck1 is at depot0, and hoist1 is at depot1; crate1 is in truck1; hoist0 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on crate0 pallet1)", "(on crate0 pallet0)", "(at crate0 depot1)", "(in crate0 truck1)", "(at truck1 distributor0)", "(on crate0 crate0)", "(at truck1 depot1)", "(on crate1 pallet1)", "(on crate1 pallet2)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(at truck0 depot0)", "(lifting hoist2 crate1)", "(lifting hoist1 crate0)", "(at crate0 depot0)", "(in crate0 truck0)", "(at truck0 depot1)", "(clear crate0)", "(at crate0 distributor1)", "(at crate0 distributor0)", "(on crate1 crate0)", "(lifting hoist1 crate1)", "(on crate0 pallet2)", "(on crate1 pallet3)", "(at crate1 distributor1)", "(at truck0 distributor1)", "(lifting hoist3 crate0)", "(at crate1 depot1)", "(on crate1 crate1)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at truck1 distributor1)", "(at crate1 distributor0)", "(in crate1 truck0)"], "yes": ["(clear crate1)", "(lifting hoist0 crate1)", "(available hoist0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 - hoist pallet0 pallet1 pallet2 pallet3 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist1) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate1 truck1) (lifting hoist0 crate0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 1835194445795146358, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 pallets, 2 distributors, 7 depots, 9 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, pallet8, pallet5, pallet0, pallet4, pallet1, pallet6, and pallet7 are clear; hoist5, hoist8, hoist2, hoist6, hoist4, hoist7, hoist0, and hoist1 are available; pallet4 is at depot4, pallet3 is at depot3, hoist7 is at distributor0, hoist5 is at depot5, pallet8 is at distributor1, hoist8 is at distributor1, pallet1 is at depot1, truck0 is at depot0, hoist6 is at depot6, hoist2 is at depot2, pallet5 is at depot5, pallet7 is at distributor0, pallet6 is at depot6, hoist0 is at depot0, pallet2 is at depot2, hoist3 is at depot3, hoist4 is at depot4, truck1 is at distributor1, pallet0 is at depot0, and hoist1 is at depot1; crate0 is in truck1; hoist3 is lifting crate1. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at truck1 depot3)", "(on crate0 pallet1)", "(at crate1 depot4)", "(at crate1 depot5)", "(on crate1 pallet0)", "(on crate0 pallet5)", "(at truck0 depot6)", "(at truck1 depot2)", "(at truck1 depot6)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate1 pallet7)", "(on crate0 crate1)", "(at crate0 depot1)", "(at crate0 depot6)", "(on crate1 pallet8)", "(on crate1 pallet1)", "(at truck1 distributor0)", "(on crate0 crate0)", "(lifting hoist4 crate1)", "(at truck1 depot1)", "(on crate1 pallet2)", "(lifting hoist5 crate1)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(at crate0 depot4)", "(at crate1 depot2)", "(lifting hoist2 crate1)", "(at truck0 depot2)", "(at truck0 depot4)", "(at crate0 depot0)", "(lifting hoist6 crate1)", "(in crate0 truck0)", "(lifting hoist1 crate0)", "(lifting hoist8 crate1)", "(at crate0 depot3)", "(at truck1 depot4)", "(at truck0 depot1)", "(lifting hoist0 crate1)", "(clear crate0)", "(lifting hoist7 crate1)", "(at crate0 distributor1)", "(at truck0 depot3)", "(on crate1 pallet5)", "(in crate1 truck1)", "(lifting hoist5 crate0)", "(lifting hoist1 crate1)", "(at crate0 distributor0)", "(on crate1 crate0)", "(lifting hoist6 crate0)", "(at crate0 depot2)", "(on crate0 pallet2)", "(at truck1 depot5)", "(on crate1 pallet6)", "(lifting hoist4 crate0)", "(at crate1 depot3)", "(lifting hoist7 crate0)", "(at crate1 distributor1)", "(at crate0 depot5)", "(on crate0 pallet7)", "(at truck0 distributor1)", "(at truck0 distributor0)", "(lifting hoist3 crate0)", "(at crate1 depot6)", "(on crate0 pallet6)", "(at truck0 depot5)", "(at crate1 depot1)", "(on crate1 crate1)", "(on crate0 pallet4)", "(at crate1 depot0)", "(at crate1 distributor0)", "(clear crate1)", "(at truck1 depot0)", "(lifting hoist0 crate0)", "(in crate1 truck0)"], "yes": ["(lifting hoist8 crate0)", "(available hoist3)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot0) (at truck1 distributor1) (available hoist0) (available hoist1) (available hoist2) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (in crate0 truck1) (lifting hoist3 crate1))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": 4387902420462591794, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 10 depots, 12 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, pallet11, pallet5, pallet8, pallet0, pallet4, pallet1, pallet6, pallet7, pallet10, and crate1 are clear; hoist5, hoist8, hoist10, hoist3, hoist2, hoist6, hoist4, hoist7, hoist0, hoist1, and hoist9 are available; pallet4 is at depot4, hoist7 is at depot7, pallet3 is at depot3, pallet9 is at depot9, hoist5 is at depot5, pallet7 is at depot7, pallet1 is at depot1, hoist9 is at depot9, hoist6 is at depot6, hoist11 is at distributor1, truck0 is at depot2, hoist2 is at depot2, hoist8 is at depot8, pallet11 is at distributor1, pallet5 is at depot5, pallet6 is at depot6, crate1 is at depot9, hoist0 is at depot0, pallet2 is at depot2, pallet8 is at depot8, hoist3 is at depot3, hoist10 is at distributor0, hoist4 is at depot4, pallet0 is at depot0, pallet10 is at distributor0, truck1 is at depot4, and hoist1 is at depot1; crate1 is on pallet9; hoist11 is lifting crate0. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at truck1 depot3)", "(on crate0 pallet1)", "(on crate0 pallet9)", "(at crate1 depot5)", "(on crate1 pallet0)", "(on crate0 pallet10)", "(on crate1 pallet4)", "(on crate0 crate1)", "(at truck0 depot0)", "(lifting hoist2 crate1)", "(at truck0 depot4)", "(at truck0 depot7)", "(lifting hoist7 crate1)", "(at crate0 distributor1)", "(at truck0 depot3)", "(at truck0 depot9)", "(at crate0 depot7)", "(lifting hoist6 crate0)", "(at crate0 depot9)", "(clear pallet9)", "(on crate0 pallet6)", "(at crate1 depot1)", "(on crate1 crate1)", "(lifting hoist3 crate1)", "(on crate1 pallet11)", "(at truck0 depot8)", "(lifting hoist9 crate0)", "(on crate0 pallet5)", "(on crate0 pallet0)", "(on crate1 pallet7)", "(at crate0 depot1)", "(at crate0 depot6)", "(on crate1 pallet8)", "(on crate1 pallet1)", "(at truck1 depot8)", "(at truck1 distributor0)", "(on crate1 pallet2)", "(at truck1 depot9)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(lifting hoist11 crate1)", "(at crate0 depot0)", "(in crate0 truck0)", "(clear crate0)", "(lifting hoist1 crate1)", "(at crate0 depot2)", "(on crate0 pallet2)", "(at crate1 depot3)", "(at crate0 depot5)", "(at truck0 depot5)", "(at crate1 depot0)", "(at truck1 distributor1)", "(at truck1 depot0)", "(at crate1 depot4)", "(at truck0 depot6)", "(on crate0 pallet8)", "(in crate0 truck1)", "(lifting hoist10 crate0)", "(lifting hoist4 crate1)", "(at crate1 depot7)", "(at crate0 depot8)", "(lifting hoist5 crate1)", "(at crate1 depot2)", "(lifting hoist1 crate0)", "(lifting hoist6 crate1)", "(at truck0 depot1)", "(on crate1 pallet5)", "(in crate1 truck1)", "(at truck1 depot5)", "(on crate1 pallet6)", "(at crate1 depot8)", "(lifting hoist7 crate0)", "(lifting hoist10 crate1)", "(lifting hoist9 crate1)", "(at crate1 distributor1)", "(at truck0 distributor0)", "(lifting hoist3 crate0)", "(on crate0 pallet4)", "(at truck1 depot7)", "(at crate1 distributor0)", "(in crate1 truck0)", "(on crate1 pallet10)", "(lifting hoist0 crate0)", "(at truck1 depot2)", "(at truck1 depot6)", "(on crate0 crate0)", "(at truck1 depot1)", "(lifting hoist8 crate0)", "(at crate0 depot4)", "(lifting hoist8 crate1)", "(at crate0 depot3)", "(lifting hoist0 crate1)", "(lifting hoist5 crate0)", "(at crate0 distributor0)", "(on crate1 crate0)", "(lifting hoist4 crate0)", "(on crate1 pallet3)", "(on crate0 pallet7)", "(at truck0 distributor1)", "(at crate1 depot6)"], "yes": ["(available hoist11)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot9) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot2) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (lifting hoist11 crate0) (on crate1 pallet9))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": -228639061071469172, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 pallets, 2 distributors, 3 depots, 5 hoists, 2 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate0, pallet0, and pallet4 are clear; hoist3, hoist2, hoist4, and hoist0 are available; crate0 is at depot1, truck1 is at depot1, hoist4 is at distributor1, pallet1 is at depot1, truck0 is at depot0, hoist2 is at depot2, hoist0 is at depot0, pallet2 is at depot2, pallet4 is at distributor1, hoist3 is at distributor0, pallet3 is at distributor0, pallet0 is at depot0, and hoist1 is at depot1; crate0 is on pallet1; hoist1 is lifting crate1. The goal is to reach a state where the following facts hold: crate0 is on pallet1 and crate1 is on pallet2. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on crate1 pallet0)", "(at truck1 depot2)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate0 crate1)", "(in crate0 truck1)", "(on crate0 crate0)", "(at truck1 distributor0)", "(on crate1 pallet1)", "(lifting hoist4 crate1)", "(lifting hoist2 crate0)", "(on crate0 pallet3)", "(at crate1 depot2)", "(at truck0 depot2)", "(lifting hoist1 crate0)", "(at crate0 depot0)", "(in crate0 truck0)", "(lifting hoist0 crate1)", "(at truck0 depot1)", "(at crate0 distributor1)", "(at crate0 distributor0)", "(in crate1 truck1)", "(on crate1 crate0)", "(lifting hoist4 crate0)", "(at crate0 depot2)", "(clear pallet1)", "(on crate1 pallet3)", "(on crate0 pallet2)", "(at crate1 distributor1)", "(at truck0 distributor1)", "(lifting hoist3 crate0)", "(at truck0 distributor0)", "(on crate0 pallet4)", "(at crate1 depot1)", "(on crate1 crate1)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at truck1 distributor1)", "(at crate1 distributor0)", "(clear crate1)", "(at truck1 depot0)", "(lifting hoist0 crate0)", "(in crate1 truck0)"], "yes": ["(lifting hoist2 crate1)", "(available hoist1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot0) (at truck1 depot1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (clear crate0) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (lifting hoist1 crate1) (on crate0 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": -7646733026948603141, "group": "landmarks_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 4 depots, 6 hoists, 3 crates, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, pallet5, crate0, crate2, and pallet0 are clear; hoist5, hoist3, hoist2, hoist4, hoist0, and hoist1 are available; pallet3 is at depot3, hoist5 is at distributor1, crate0 is at depot1, pallet4 is at distributor0, pallet1 is at depot1, hoist2 is at depot2, truck0 is at depot3, crate2 is at distributor0, hoist0 is at depot0, hoist4 is at distributor0, pallet2 is at depot2, hoist3 is at depot3, pallet5 is at distributor1, truck1 is at distributor1, pallet0 is at depot0, and hoist1 is at depot1; crate0 is on pallet1 and crate2 is on pallet4; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at truck1 depot3)", "(lifting hoist0 crate2)", "(on crate1 pallet0)", "(on crate0 pallet5)", "(at truck1 depot2)", "(on crate0 pallet0)", "(on crate1 pallet4)", "(on crate0 crate1)", "(lifting hoist4 crate2)", "(in crate0 truck1)", "(at crate2 depot2)", "(at truck1 distributor0)", "(on crate1 pallet1)", "(on crate2 crate1)", "(on crate0 crate0)", "(at truck1 depot1)", "(on crate1 pallet2)", "(lifting hoist3 crate2)", "(at crate2 depot3)", "(lifting hoist5 crate1)", "(lifting hoist2 crate0)", "(in crate2 truck0)", "(on crate0 pallet3)", "(at truck0 depot0)", "(at crate1 depot2)", "(lifting hoist2 crate1)", "(at truck0 depot2)", "(at crate0 depot0)", "(lifting hoist1 crate0)", "(lifting hoist2 crate2)", "(in crate0 truck0)", "(at crate0 depot3)", "(lifting hoist0 crate1)", "(on crate2 pallet3)", "(at truck0 depot1)", "(at crate0 distributor1)", "(at crate2 depot1)", "(on crate2 crate2)", "(on crate0 crate2)", "(clear pallet4)", "(on crate1 pallet5)", "(in crate1 truck1)", "(lifting hoist5 crate0)", "(on crate2 pallet1)", "(lifting hoist1 crate1)", "(at crate0 distributor0)", "(on crate1 crate0)", "(at crate0 depot2)", "(on crate0 pallet2)", "(on crate2 pallet2)", "(clear pallet1)", "(lifting hoist4 crate0)", "(on crate1 pallet3)", "(at crate1 depot3)", "(at crate1 distributor1)", "(at truck0 distributor1)", "(lifting hoist3 crate0)", "(at truck0 distributor0)", "(on crate2 pallet0)", "(lifting hoist1 crate2)", "(on crate2 crate0)", "(at crate1 depot1)", "(on crate1 crate1)", "(on crate0 pallet4)", "(at crate1 depot0)", "(lifting hoist3 crate1)", "(at crate2 depot0)", "(at crate1 distributor0)", "(clear crate1)", "(at crate2 distributor1)", "(at truck1 depot0)", "(lifting hoist0 crate0)", "(in crate2 truck1)", "(lifting hoist5 crate2)", "(on crate2 pallet5)"], "yes": ["(lifting hoist4 crate1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at crate2 distributor0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot3) (at truck1 distributor1) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet5) (in crate1 truck0) (on crate0 pallet1) (on crate2 pallet4))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
