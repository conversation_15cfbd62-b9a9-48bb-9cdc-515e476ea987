{"id": -5324534174348056188, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. bed1 is at location13. shelf1 is at location20. shelf5 is at location22. drawer3 is at location17. drawer4 and drawer5 are at location12. safe1 is at location6. desk2 is at location10. drawer1 is at location21. garbagecan1 is at location2. laundryhamper1 is at location8. shelf3 is at location11. desk1 is at location3. shelf2 is at location25. shelf4 is at location23. drawer2 is at location18. drawer6 is at location1.  Currently, the objects are at locations as follows. blinds2 is at location15. cellphone3 is at location12. laptop1, laptop2, pillow1, cellphone1, and pillow2 are at location13. mirror1 is at location19. keychain2 and keychain1 are at location6. bowl3 is at location24. basketball1 is at location7. window2 is at location4. cd1, alarmclock1, mug1, bowl1, and pencil1 are at location3. pencil3, cellphone2, mug2, cd3, and pen1 are at location10. alarmclock3, bowl2, and desklamp1 are at location23. blinds1 is at location16. creditcard1 and pencil2 are at location22. window1 is at location5. alarmclock2 is at location11. laundryhamperlid1 is at location8. chair2 is at location26. cd2 is at location2. lightswitch1 is at location14. chair1 is at location21. baseballbat1 is at location9. agent agent1 is at location location25. The objects are in/on receptacle as follows. mug2, cd3, alarmclock3, cellphone2, bowl2, desklamp1, pencil3, and pen1 are on desk2. laptop2, pillow2, laptop1, pillow1, and cellphone1 are in bed1. mug1, pencil1, alarmclock1, cd1, and bowl1 are on desk1. alarmclock2 is on shelf3. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. pencil2 and creditcard1 are on shelf5. bowl3 is on shelf6. cellphone3 is in drawer5. drawer3, drawer1, drawer6, and safe1 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object book1. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? keychain2 is in drawer3", "answer": "no"}
{"id": -1959964052239426071, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. stoveknob2 and stoveknob3 are at location12. dishsponge1 and fork1 are at location27. vase1 and dishsponge2 are at location13. egg1, plate2, potato1, bowl1, mug1, tomato1, and egg2 are at location10. glassbottle3, potato3, and apple2 are at location8. cellphone1, lettuce1, lettuce2, plate1, peppershaker1, apple1, knife1, and cellphone2 are at location2. bread1, houseplant1, glassbottle1, creditcard1, papertowelroll1, knife2, spoon2, spatula1, cellphone3, plate3, and butterknife1 are at location3. spatula2, potato2, sink1, glassbottle2, and spatula3 are at location6. bowl2 is at location29. soapbottle1 is at location4. vase2 is at location17. window1 is at location30. stoveknob4 is at location7. soapbottle2 and statue1 are at location26. pan1, spoon1, and mug2 are at location11. chair2 is at location1. lightswitch1 is at location25. window2 is at location28. saltshaker1 is at location15. stoveknob1 is at location18. chair1 is at location23. peppershaker3 is at location16. cup1 is at location24. peppershaker2 is at location20. pot1 is at location5. agent agent1 is at location location19. The objects are in/on receptacle as follows. knife2, cellphone3, butterknife1, papertowelroll1, houseplant1, glassbottle1, creditcard1, spatula1, bread1, plate3, and spoon2 are on countertop3. potato1, bowl1, tomato1, mug1, egg2, plate2, and egg1 are in fridge1. pan1 is on stoveburner2. cellphone1, cellphone2, lettuce2, peppershaker1, knife1, apple1, lettuce1, and plate1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. spoon1 and pan1 are on countertop2. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. vase2 is on shelf1. pot1 is on stoveburner1. statue1 and soapbottle2 are on shelf3. cup1 is in microwave1. mug2 is in coffeemachine1. spatula3, potato2, glassbottle2, and spatula2 are in sinkbasin1. saltshaker1 is in drawer2. peppershaker2 is in drawer1. bowl2 is on shelf2. dishsponge2 and vase1 are in cabinet5. soapbottle1 is in cabinet6. pan1 is on stoveburner4. cellphone2 is on plate1. pot1 is on stoveburner3. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? soapbottle2 is in cabinet4", "answer": "no"}
{"id": -3122691734874038778, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. apple2 and egg1 are at location8. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location6. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg1 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. egg2 is hot. Nothing has been validated. agent1 is holding object egg2. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? plate1 is at location17", "answer": "no"}
{"id": -2867874079787021419, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. bed1 is at location13. shelf1 is at location20. shelf5 is at location22. drawer3 is at location17. drawer4 and drawer5 are at location12. safe1 is at location6. desk2 is at location10. drawer1 is at location21. garbagecan1 is at location2. laundryhamper1 is at location8. shelf3 is at location11. desk1 is at location3. shelf2 is at location25. shelf4 is at location23. drawer2 is at location18. drawer6 is at location1.  Currently, the objects are at locations as follows. blinds2 is at location15. cellphone3 is at location12. laptop1, book1, laptop2, pillow1, cellphone1, and pillow2 are at location13. mirror1 is at location19. keychain2 and keychain1 are at location6. bowl3 is at location24. basketball1 is at location7. window2 is at location4. cd1, alarmclock1, mug1, bowl1, and pencil1 are at location3. pencil3, cellphone2, mug2, cd3, and pen1 are at location10. alarmclock3, bowl2, and desklamp1 are at location23. blinds1 is at location16. creditcard1 and pencil2 are at location22. window1 is at location5. alarmclock2 is at location11. laundryhamperlid1 is at location8. chair2 is at location26. cd2 is at location2. lightswitch1 is at location14. chair1 is at location21. baseballbat1 is at location9. agent agent1 is at location location2. The objects are in/on receptacle as follows. mug2, cd3, alarmclock3, cellphone2, bowl2, desklamp1, pencil3, and pen1 are on desk2. laptop2, book1, pillow2, laptop1, pillow1, and cellphone1 are in bed1. mug1, pencil1, alarmclock1, cd1, and bowl1 are on desk1. alarmclock2 is on shelf3. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. pencil2 and creditcard1 are on shelf5. bowl3 is on shelf6. cellphone3 is in drawer5. drawer3, drawer1, drawer6, and safe1 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? agent1 is holding object book1", "answer": "yes"}
{"id": 5615063566305886031, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. egg2 and apple2 are at location8. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location10. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg2 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. egg1 is hot. Nothing has been validated. agent1 is holding object egg1. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? saltshaker1 is at location19", "answer": "no"}
{"id": 5851631183972330216, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. egg2 and apple2 are at location8. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location26. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg2 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. Nothing has been validated. agent1 is holding object egg1. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? spatula2 is at location3", "answer": "no"}
{"id": -1169280393445361824, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. egg2, apple2, and egg1 are at location8. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location8. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2, egg2, and egg1 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? agent agent1 is at location location24", "answer": "yes"}
{"id": -8803000159183432939, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. apple2 and egg1 are at location8. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location19. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg1 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. Nothing has been validated. agent1 is holding object egg2. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? agent agent1 is at location location24", "answer": "yes"}
{"id": -6177815315037371298, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. sinkbasin2 is at location6. cabinet2 is at location11. cabinet4 is at location15. cabinet3 is at location8. toiletpaperhanger1 is at location10. handtowelholder2 is at location17. handtowelholder1 is at location18. garbagecan1 is at location2. towelholder1 is at location5. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. sinkbasin1 is at location16.  Currently, the objects are at locations as follows. toiletpaper1 and soapbar1 are at location7. handtowel1 is at location18. scrubbrush1 and plunger1 are at location10. cloth1 and candle1 are at location3. spraybottle3 is at location2. showerdoor1 is at location1. spraybottle2 and soapbottle1 are at location15. handtowel2 is at location17. sink2 is at location14. towel1 and showerglass1 are at location5. sink1 is at location12. soapbottle2 and spraybottle1 are at location4. mirror1 is at location9. toiletpaper2 is at location8. lightswitch1 is at location13. cloth2 is at location11. agent agent1 is at location location3. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. cloth2 is in cabinet2. cloth1 and candle1 are on countertop1. towel1 is on towelholder1. toiletpaper1 and soapbar1 are in toilet1. soapbottle2 and spraybottle1 are in cabinet1. spraybottle2 and soapbottle1 are in cabinet4. toiletpaper2 is in cabinet3. spraybottle3 is in garbagecan1. handtowel1 is on handtowelholder1. cabinet2, cabinet4, cabinet1, and cabinet3 are closed. Nothing has been validated. agent1 is holding object soapbar2. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? cloth1 is at location7", "answer": "no"}
{"id": -306709117252800840, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. stoveknob2 and stoveknob3 are at location12. dishsponge1 and fork1 are at location27. vase1 and dishsponge2 are at location13. egg1, plate2, potato1, bowl1, mug1, tomato1, and egg2 are at location10. glassbottle3, potato3, and apple2 are at location8. cellphone1, lettuce1, lettuce2, plate1, peppershaker1, apple1, knife1, and cellphone2 are at location2. bread1, houseplant1, glassbottle1, creditcard1, papertowelroll1, knife2, spoon2, spatula1, cellphone3, plate3, and butterknife1 are at location3. spatula2, potato2, sink1, glassbottle2, and spatula3 are at location6. bowl2 is at location29. soapbottle1 is at location4. vase2 is at location17. window1 is at location30. stoveknob4 is at location7. soapbottle2 and statue1 are at location26. pan1 and spoon1 are at location11. chair2 is at location1. lightswitch1 is at location25. window2 is at location28. saltshaker1 is at location15. stoveknob1 is at location18. chair1 is at location23. peppershaker3 is at location16. cup1 is at location24. peppershaker2 is at location20. pot1 is at location5. agent agent1 is at location location19. The objects are in/on receptacle as follows. knife2, cellphone3, butterknife1, papertowelroll1, houseplant1, glassbottle1, creditcard1, spatula1, bread1, plate3, and spoon2 are on countertop3. potato1, bowl1, tomato1, mug1, egg2, plate2, and egg1 are in fridge1. pan1 is on stoveburner2. cellphone1, cellphone2, lettuce2, peppershaker1, knife1, apple1, lettuce1, and plate1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. spoon1 and pan1 are on countertop2. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. vase2 is on shelf1. pot1 is on stoveburner1. statue1 and soapbottle2 are on shelf3. cup1 is in microwave1. spatula3, potato2, glassbottle2, and spatula2 are in sinkbasin1. saltshaker1 is in drawer2. peppershaker2 is in drawer1. bowl2 is on shelf2. dishsponge2 and vase1 are in cabinet5. soapbottle1 is in cabinet6. pan1 is on stoveburner4. cellphone2 is on plate1. pot1 is on stoveburner3. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? glassbottle1 is at location6", "answer": "no"}
