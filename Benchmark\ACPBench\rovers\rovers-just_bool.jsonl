{"id": -1176293359104985967, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample the rock at waypoint waypoint0 with rover rover1 and store it in store store1, communicate rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on rover rover0 for the objective objective0 at the waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint1, capture an image of objective objective0 in mode colour using the camera camera1 on the rover rover1 from waypoint waypoint1, move the rover rover0 from waypoint waypoint4 to waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, communicate soil data from rover rover0 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, drop from store store1 of rover rover1, sample the rock at waypoint waypoint1 with rover rover1 and store it in store store1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3 via waypoint waypoint1, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: move the rover rover1 from waypoint waypoint2 to waypoint waypoint0?", "answer": "yes"}
{"id": -8183749649213245858, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take a picture of the objective objective1 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, collect a rock sample from waypoint waypoint1 using rover rover0 and store it in store store0, empty the store store0 from rover rover0, sample soil at waypoint waypoint1 with rover rover0 and store in store store0, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate rock data from rover rover0 at waypoint waypoint2 about waypoint waypoint1 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint1, communicate image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1?", "answer": "yes"}
{"id": -6445788600385362326, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera2 can be calibrated on objective1. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective4 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective3 is visible from waypoint2. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0, waypoint1, and waypoint2. Soil can be sampled at the following location(s): waypoint2 and waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint2;, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, Image objective3 was communicated in mode high_res, Image objective0 was communicated in mode low_res, Image objective6 was communicated in mode low_res, Rock data was communicated from waypoint waypoint2;, Rock data was communicated from waypoint waypoint1;, and Image objective4 was communicated in mode low_res.", "question": "Given the plan: \"adjust the camera camera2 on the rover rover1 for the objective objective1 at waypoint waypoint2, take an image of the objective objective3 in mode high_res using the camera camera2 on the rover rover1 from the waypoint waypoint2, transmit the image data of the objective objective3 in mode high_res from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1, sample a rock at waypoint waypoint2 using rover rover1, then store it in the storage unit store1, communicate the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 via waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, adjust the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint2, sample soil at waypoint waypoint2 with rover rover0 and store in the store store0, communicate the soil data from rover rover0 at waypoint waypoint2 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint1, drop store store0 of rover rover0, take an image of the objective objective0 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint0, transmit the image data of the objective objective0 in mode low_res from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint1, sample soil at waypoint waypoint1 with rover rover0 and store in the store store0, adjust the camera camera0 on the rover rover0 for the objective objective4 at waypoint waypoint1, take an image of the objective objective0 in mode colour using the camera camera0 on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, transmit the image data of the objective objective0 in mode colour from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, drop store store1 of rover rover1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample a rock at waypoint waypoint0 using rover rover1, then store it in the storage unit store1, communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2, drop store store1 of rover rover1, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, sample a rock at waypoint waypoint1 using rover rover1, then store it in the storage unit store1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, communicate the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 via waypoint waypoint1, adjust the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, take an image of the objective objective6 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint0, transmit the image data of the objective objective6 in mode low_res from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, adjust the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint1, take an image of the objective objective4 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint0, transmit the image data of the objective objective4 in mode low_res from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate the soil data from rover rover0 at waypoint waypoint2 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint1?", "answer": "yes"}
{"id": 8481620696822644480, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, adjust the camera camera2 on the rover rover1 for the objective objective0 at waypoint waypoint0, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, communicate the soil data from the rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint1, sample the rock at waypoint waypoint0 with rover rover1 and store it in store store1, navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2, communicate rock data from rover rover1 at waypoint waypoint2 about waypoint waypoint0 to lander general at waypoint waypoint1, capture an image of the objective objective1 in mode colour with the camera camera2 on the rover rover1 at waypoint waypoint2, communicate image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, adjust the camera camera2 on the rover rover1 for the objective objective0 at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0?", "answer": "no"}
{"id": 1463742592245775874, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, calibrate the camera camera0 on rover rover0 for the objective objective1 at the waypoint waypoint2, take an image of the objective objective1 in mode colour using the camera camera0 on the rover rover0 from the waypoint waypoint2, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the image data of the objective objective1 in mode colour from the rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, communicate rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1?", "answer": "yes"}
{"id": -1776480360619521463, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"navigate the rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take an image of objective objective0 in mode low_res using camera camera1 on rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, sample a rock at waypoint waypoint1 using rover rover0, then store it in the storage unit store0, empty the store store0 from rover rover0, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take an image of objective objective1 in mode low_res using camera camera1 on rover rover0 from waypoint waypoint1, sample soil at waypoint waypoint1 with rover rover0 and store in store store0, navigate the rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover0 at waypoint waypoint2 about waypoint waypoint1 to lander general at waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of target objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate the rover rover0 from waypoint waypoint2 to waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1?", "answer": "yes"}
{"id": 843876286158284239, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on rover rover0 for the objective objective0 at the waypoint waypoint1, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint0, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint4, take an image of the objective objective1 in mode low_res using the camera camera0 on the rover rover0 from the waypoint waypoint4, sample the rock at waypoint waypoint0 with rover rover1 and store it in store store1, transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint0 as a relay, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, take an image of the objective objective0 in mode low_res using the camera camera1 on the rover rover1 from the waypoint waypoint1, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint1, take an image of the objective objective0 in mode colour using the camera camera1 on the rover rover1 from the waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, unload the store store1 from the rover rover1, sample the rock at waypoint waypoint1 with rover rover1 and store it in store store1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 using waypoint waypoint1 as a relay, navigate with rover rover0 from waypoint waypoint4 to waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, sample the soil at waypoint waypoint0 with rover rover0 and store it in store store0, transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint3 using soil analysis of waypoint waypoint0, transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint3 using soil analysis of waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint3 using soil analysis of waypoint waypoint0?", "answer": "yes"}
{"id": 1762989238429457397, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, collect a rock sample from waypoint waypoint0 using rover rover1 and store it in store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 through waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1, adjust the camera camera0 on the rover rover0 for the objective objective0 at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint4, take a picture of the objective objective1 in mode low_res using the camera camera0 mounted on the rover rover0 from the waypoint waypoint4, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover1 from the waypoint waypoint1, adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint1, take a picture of the objective objective0 in mode colour using the camera camera1 mounted on the rover rover1 from the waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, if storey is full, drop store store1 of rover rover1, collect a rock sample from waypoint waypoint1 using rover rover1 and store it in store store1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 through waypoint waypoint1, move the rover rover0 from waypoint waypoint4 to waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, sample soil at waypoint waypoint0 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, regarding the soil analysis of waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3?", "answer": "no"}
{"id": 529173937884219804, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover0 and store in store store0, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, calibrate the camera camera0 on the rover rover0 for the objective objective1 at the waypoint waypoint2, capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2, transmit image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, transmit image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: calibrate the camera camera0 on the rover rover0 for the objective objective1 at the waypoint waypoint2?", "answer": "no"}
{"id": -915539420950664861, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint2, sample soil at waypoint waypoint0 with rover rover0 and store in store store0, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, communicate soil data from rover rover0 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1, adjust the camera camera0 on the rover rover0 for the objective objective0 at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, move the rover rover0 from waypoint waypoint4 to waypoint waypoint1, adjust the camera camera0 on the rover rover0 for the objective objective0 at waypoint waypoint1, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, communicate rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint0, capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, drop the content from store store1 of the rover rover1, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, sample rock at waypoint waypoint1 with rover rover1 and store in store store1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3 via waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3?", "answer": "yes"}
{"id": -823323299480448130, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera0 on the rover rover1 for the objective objective0 at waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint0, use the rover rover1 to collect soil samples at waypoint waypoint0 and store them in the store store1, transmit soil data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, calibrate the camera camera2 on the rover rover0 for the objective objective2 at waypoint waypoint0, sample a rock at waypoint waypoint0 using rover rover0, then store it in the storage unit store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, transmit image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, empty the store store1 from rover rover1, move the rover rover1 from waypoint waypoint0 to waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: move the rover rover1 from waypoint waypoint0 to waypoint waypoint1?", "answer": "yes"}
{"id": 3434323998818943333, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Image objective1 was communicated in mode high_res, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"sample a rock at waypoint waypoint2 using rover rover1, then store it in the storage unit store1, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, sample soil at waypoint waypoint2 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at the waypoint waypoint1, take an image of the objective objective1 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at the waypoint waypoint1, take an image of the objective objective1 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at the waypoint waypoint1, take an image of the objective objective0 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0, empty the store store0 from rover rover0, sample soil at waypoint waypoint0 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, empty the store store0 from rover rover0, sample a rock at waypoint waypoint0 using rover rover0, then store it in the storage unit store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: take an image of the objective objective1 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint1?", "answer": "no"}
{"id": -1094315416979394159, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate camera camera1 on rover rover1 for objective objective0 at waypoint waypoint2, calibrate camera camera0 on rover rover1 for objective objective0 at waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover1 and store in store store1, communicate the soil data from rover rover1 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective2 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0?", "answer": "yes"}
{"id": -4740218684046217943, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint1;, Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover0 for the objective objective0 at the waypoint waypoint0, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint0, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera2 on rover rover1 for the objective objective0 at the waypoint waypoint1, collect a rock sample from waypoint waypoint1 using rover rover1 and store it in store store1, empty the store store1 from rover rover1, sample soil at waypoint waypoint1 with rover rover1 and store in store store1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, take a picture of the objective objective1 in mode colour using the camera camera2 mounted on the rover rover1 from the waypoint waypoint2, communicate the image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0, communicate the soil data from rover rover1 at waypoint waypoint0 with the soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, communicate rock data from rover rover1 at waypoint waypoint0 about waypoint waypoint1 to lander general at waypoint waypoint1, communicate the soil data from rover rover1 at waypoint waypoint0 with the soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: sample soil at waypoint waypoint1 with rover rover1 and store in store store1?", "answer": "no"}
{"id": 3953279470711986231, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint1, collect a sample from the waypoint waypoint2 using the rover rover0 and store it in the store store0, capture an image of the objective objective0 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint2, navigate with rover rover0 to waypoint waypoint1 from waypoint waypoint2, navigate with rover rover0 to waypoint waypoint0 from waypoint waypoint1, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, collect a soil sample from waypoint waypoint1 using rover rover1 and deposit it in the store store1, navigate with rover rover1 to waypoint waypoint2 from waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint2 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, drop the content from store store0 of the rover rover0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint2, communicate image data of objective objective0 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, drop the content from store store0 of the rover rover0, collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0, communicate soil data from rover rover0 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: capture an image of the objective objective0 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint2?", "answer": "no"}
