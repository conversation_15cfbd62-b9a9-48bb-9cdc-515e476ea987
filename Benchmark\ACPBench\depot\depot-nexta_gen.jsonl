{"id": -9106428117608857098, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 2 trucks, 2 distributors, 2 crates, 2 depots, 4 pallets, numbered consecutively. Currently, pallet3, pallet2, pallet1, and pallet0 are clear; hoist3, hoist1, and hoist2 are available; pallet3 is at distributor1, hoist2 is at distributor0, pallet2 is at distributor0, pallet0 is at depot0, hoist0 is at depot0, truck1 is at depot0, hoist1 is at depot1, truck0 is at distributor0, hoist3 is at distributor1, and pallet1 is at depot1; crate0 is in truck0; hoist0 is lifting crate1. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck1 depot0 depot1)", "(drive truck1 depot0 depot0)", "(drive truck0 distributor0 distributor0)", "(drive truck1 depot0 distributor0)", "(unload hoist2 crate0 truck0 distributor0)", "(drive truck0 distributor0 distributor1)"], "no": ["(load hoist0 crate1 truck1 depot0)", "(drive truck1 depot0 distributor1)", "(drive truck0 distributor0 depot1)"], "opt": "4", "yes": ["(drop hoist0 crate1 pallet0 depot0)", "(drive truck0 distributor0 depot0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist1) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate0 truck0) (lifting hoist0 crate1))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -5302354778148001544, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 2 trucks, 2 distributors, 2 crates, 2 depots, 4 pallets, numbered consecutively. Currently, pallet3, pallet2, pallet1, and pallet0 are clear; hoist3, hoist1, and hoist2 are available; pallet3 is at distributor1, hoist2 is at distributor0, pallet2 is at distributor0, pallet0 is at depot0, hoist0 is at depot0, truck1 is at depot1, hoist1 is at depot1, truck0 is at depot1, hoist3 is at distributor1, and pallet1 is at depot1; crate0 is in truck0; hoist0 is lifting crate1. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck0 depot1 distributor0)", "(drive truck1 depot1 distributor0)", "(drive truck1 depot1 depot0)", "(drive truck0 depot1 distributor1)", "(unload hoist1 crate0 truck0 depot1)", "(drive truck0 depot1 depot0)"], "no": ["(drive truck0 depot1 depot1)", "(drive truck1 depot1 distributor1)", "(drive truck1 depot1 depot1)"], "opt": "4", "yes": ["(drop hoist0 crate1 pallet0 depot0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 depot1) (at truck1 depot1) (available hoist1) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate0 truck0) (lifting hoist0 crate1))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 5710200239869579634, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 trucks, 2 distributors, 2 crates, 10 depots, 12 pallets, numbered consecutively. Currently, pallet8, pallet10, pallet3, pallet11, pallet2, pallet4, pallet7, pallet1, pallet5, crate1, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist1, hoist5, hoist10, hoist7, hoist2, hoist9, hoist6, hoist11, and hoist8 are available; pallet5 is at depot5, pallet2 is at depot2, truck1 is at distributor1, hoist10 is at distributor0, hoist2 is at depot2, pallet3 is at depot3, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, hoist7 is at depot7, hoist11 is at distributor1, pallet10 is at distributor0, crate1 is at depot9, pallet11 is at distributor1, pallet8 is at depot8, truck0 is at depot0, hoist6 is at depot6, hoist1 is at depot1, pallet7 is at depot7, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist8 is at depot8, hoist3 is at depot3, hoist9 is at depot9, pallet1 is at depot1, and pallet9 is at depot9; crate1 is on pallet9; crate0 is in truck1. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck0 depot0 depot0)", "(drive truck1 distributor1 depot0)", "(drive truck0 depot0 depot6)", "(drive truck0 depot0 depot2)", "(drive truck1 distributor1 depot5)", "(drive truck0 depot0 depot9)", "(drive truck1 distributor1 distributor0)", "(lift hoist9 crate1 pallet9 depot9)", "(drive truck0 depot0 depot3)", "(drive truck1 distributor1 depot6)", "(drive truck1 distributor1 depot7)", "(drive truck0 depot0 distributor0)", "(drive truck0 depot0 depot7)", "(drive truck1 distributor1 depot3)", "(drive truck1 distributor1 distributor1)", "(drive truck1 distributor1 depot1)", "(drive truck0 depot0 depot4)", "(drive truck1 distributor1 depot9)", "(drive truck0 depot0 depot8)", "(drive truck0 depot0 distributor1)", "(drive truck1 distributor1 depot2)", "(drive truck0 depot0 depot1)"], "no": ["(drive truck1 distributor1 depot4)", "(drive truck1 distributor1 depot8)", "(drive truck0 depot0 depot5)"], "opt": "2", "yes": ["(unload hoist11 crate0 truck1 distributor1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot9) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 distributor1) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (in crate0 truck1) (on crate1 pallet9))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 4581510132996848904, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 trucks, 2 distributors, 2 crates, 7 depots, 9 pallets, numbered consecutively. Currently, pallet8, pallet2, pallet4, crate0, pallet7, pallet5, crate1, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist1, hoist5, hoist7, hoist2, hoist6, and hoist8 are available; pallet5 is at depot5, pallet2 is at depot2, hoist8 is at distributor1, crate0 is at depot1, hoist2 is at depot2, pallet7 is at distributor0, pallet3 is at depot3, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, truck1 is at depot2, hoist7 is at distributor0, pallet8 is at distributor1, truck0 is at depot0, hoist6 is at depot6, hoist1 is at depot1, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist3 is at depot3, pallet1 is at depot1, and crate1 is at depot3; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - use hoist ?x to load crate ?y into truck ?z at place ?p, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(lift hoist3 crate1 pallet3 depot3)", "(drive truck0 depot0 depot0)", "(drive truck0 depot0 depot6)", "(drive truck0 depot0 depot2)", "(drive truck1 depot2 depot6)", "(lift hoist1 crate0 pallet1 depot1)", "(drive truck1 depot2 distributor0)", "(drive truck1 depot2 depot2)", "(drive truck1 depot2 depot3)", "(drive truck1 depot2 depot4)", "(drive truck1 depot2 distributor1)", "(drive truck0 depot0 distributor0)", "(drive truck1 depot2 depot5)", "(drive truck0 depot0 distributor1)", "(drive truck0 depot0 depot5)"], "no": ["(drive truck1 depot2 depot0)", "(drive truck0 depot0 depot4)", "(drive truck0 depot0 depot3)"], "opt": "6", "yes": ["(drive truck1 depot2 depot1)", "(drive truck0 depot0 depot1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot0) (at truck1 depot2) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (on crate0 pallet1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": 35416995220207564, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 trucks, 2 distributors, 2 crates, 10 depots, 12 pallets, numbered consecutively. Currently, pallet8, pallet10, pallet3, pallet11, pallet2, pallet4, pallet7, pallet1, pallet5, pallet9, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist1, hoist5, hoist10, hoist7, hoist2, hoist9, hoist6, and hoist11 are available; pallet5 is at depot5, pallet2 is at depot2, hoist10 is at distributor0, hoist2 is at depot2, truck1 is at depot9, pallet3 is at depot3, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, hoist7 is at depot7, hoist11 is at distributor1, pallet10 is at distributor0, pallet11 is at distributor1, pallet8 is at depot8, truck0 is at depot0, hoist6 is at depot6, hoist1 is at depot1, pallet7 is at depot7, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist8 is at depot8, hoist3 is at depot3, hoist9 is at depot9, pallet1 is at depot1, and pallet9 is at depot9; crate1 is in truck1; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck0 depot0 depot0)", "(drive truck0 depot0 depot6)", "(drive truck0 depot0 depot2)", "(drive truck1 depot9 distributor0)", "(drive truck1 depot9 depot6)", "(drive truck1 depot9 depot1)", "(drop hoist8 crate0 pallet8 depot8)", "(drive truck1 depot9 depot7)", "(drive truck1 depot9 depot4)", "(drive truck0 depot0 depot3)", "(drive truck1 depot9 depot3)", "(drive truck1 depot9 depot0)", "(drive truck0 depot0 distributor0)", "(drive truck1 depot9 depot9)", "(drive truck0 depot0 depot7)", "(drive truck0 depot0 depot4)", "(drive truck1 depot9 depot2)", "(drive truck1 depot9 distributor1)", "(drive truck1 depot9 depot5)", "(drive truck0 depot0 depot5)", "(drive truck0 depot0 depot1)"], "no": ["(drive truck1 depot9 depot8)", "(drive truck0 depot0 distributor1)", "(drive truck0 depot0 depot9)"], "opt": "7", "yes": ["(drive truck0 depot0 depot8)", "(unload hoist9 crate1 truck1 depot9)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 depot9) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist9) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (clear pallet9) (in crate1 truck1) (lifting hoist8 crate0))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": -595010623912576882, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 trucks, 2 distributors, 2 crates, 7 depots, 9 pallets, numbered consecutively. Currently, pallet8, pallet2, pallet4, pallet7, pallet1, pallet5, crate1, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist5, hoist7, hoist2, hoist6, and hoist8 are available; pallet5 is at depot5, pallet2 is at depot2, truck0 is at depot2, hoist8 is at distributor1, hoist2 is at depot2, pallet7 is at distributor0, pallet3 is at depot3, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, hoist7 is at distributor0, truck1 is at depot1, pallet8 is at distributor1, hoist6 is at depot6, hoist1 is at depot1, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist3 is at depot3, pallet1 is at depot1, and crate1 is at depot3; crate1 is on pallet3; hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck1 depot1 depot2)", "(drive truck1 depot1 distributor1)", "(drop hoist1 crate0 pallet1 depot1)", "(drive truck0 depot2 depot6)", "(drive truck1 depot1 depot6)", "(drive truck0 depot2 depot2)", "(drive truck0 depot2 depot0)", "(drive truck0 depot2 depot5)", "(drive truck1 depot1 distributor0)", "(drive truck1 depot1 depot3)", "(drive truck1 depot1 depot5)", "(drive truck1 depot1 depot0)", "(drive truck0 depot2 depot4)", "(drive truck0 depot2 distributor1)", "(drive truck0 depot2 depot3)", "(drive truck0 depot2 depot1)", "(drive truck1 depot1 depot1)"], "no": ["(lift hoist3 crate1 pallet3 depot3)", "(drive truck0 depot2 distributor0)", "(drive truck1 depot1 depot4)"], "opt": "4", "yes": ["(load hoist1 crate0 truck1 depot1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot2) (at truck1 depot1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (lifting hoist1 crate0) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -5110553970098147870, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 trucks, 2 distributors, 2 crates, 10 depots, 12 pallets, numbered consecutively. Currently, pallet8, pallet10, pallet3, pallet11, pallet2, pallet4, pallet7, pallet1, pallet5, pallet9, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist1, hoist5, hoist10, hoist7, hoist2, hoist9, hoist6, and hoist11 are available; pallet5 is at depot5, pallet2 is at depot2, hoist10 is at distributor0, hoist2 is at depot2, truck1 is at depot8, pallet3 is at depot3, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, hoist7 is at depot7, hoist11 is at distributor1, pallet10 is at distributor0, pallet11 is at distributor1, pallet8 is at depot8, truck0 is at depot0, hoist6 is at depot6, hoist1 is at depot1, pallet7 is at depot7, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist8 is at depot8, hoist3 is at depot3, hoist9 is at depot9, pallet1 is at depot1, and pallet9 is at depot9; crate1 is in truck1; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck0 depot0 depot0)", "(drive truck0 depot0 depot6)", "(drive truck0 depot0 depot2)", "(drive truck1 depot8 distributor0)", "(drive truck0 depot0 depot9)", "(drive truck1 depot8 depot5)", "(drop hoist8 crate0 pallet8 depot8)", "(drive truck1 depot8 depot7)", "(drive truck1 depot8 depot8)", "(drive truck0 depot0 depot3)", "(drive truck1 depot8 depot3)", "(drive truck0 depot0 distributor0)", "(drive truck1 depot8 depot9)", "(drive truck0 depot0 depot7)", "(drive truck1 depot8 depot2)", "(drive truck1 depot8 depot0)", "(drive truck0 depot0 depot4)", "(drive truck1 depot8 distributor1)", "(drive truck1 depot8 depot6)", "(drive truck0 depot0 depot8)", "(drive truck0 depot0 distributor1)", "(drive truck0 depot0 depot5)"], "no": ["(drive truck1 depot8 depot1)", "(drive truck1 depot8 depot4)", "(drive truck0 depot0 depot1)"], "opt": "7", "yes": ["(load hoist8 crate0 truck1 depot8)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 depot8) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist9) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (clear pallet9) (in crate1 truck1) (lifting hoist8 crate0))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 8480066786191752438, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 trucks, 2 distributors, 2 crates, 7 depots, 9 pallets, numbered consecutively. Currently, pallet8, pallet2, pallet4, crate0, pallet7, pallet5, crate1, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist1, hoist5, hoist7, hoist2, hoist6, and hoist8 are available; pallet5 is at depot5, pallet2 is at depot2, hoist8 is at distributor1, crate0 is at depot1, hoist2 is at depot2, pallet7 is at distributor0, pallet3 is at depot3, truck1 is at depot6, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, hoist7 is at distributor0, pallet8 is at distributor1, hoist6 is at depot6, hoist1 is at depot1, truck0 is at depot5, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist3 is at depot3, pallet1 is at depot1, and crate1 is at depot3; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(lift hoist3 crate1 pallet3 depot3)", "(drive truck0 depot5 depot2)", "(drive truck1 depot6 distributor0)", "(lift hoist1 crate0 pallet1 depot1)", "(drive truck0 depot5 depot5)", "(drive truck0 depot5 distributor0)", "(drive truck1 depot6 depot0)", "(drive truck0 depot5 depot4)", "(drive truck0 depot5 depot0)", "(drive truck0 depot5 depot3)", "(drive truck1 depot6 depot4)", "(drive truck1 depot6 depot3)", "(drive truck0 depot5 depot6)", "(drive truck1 depot6 depot6)", "(drive truck0 depot5 distributor1)"], "no": ["(drive truck1 depot6 distributor1)", "(drive truck1 depot6 depot5)", "(drive truck1 depot6 depot2)"], "opt": "6", "yes": ["(drive truck0 depot5 depot1)", "(drive truck1 depot6 depot1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot5) (at truck1 depot6) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (on crate0 pallet1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -406884763016528998, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 trucks, 2 distributors, 2 crates, 10 depots, 12 pallets, numbered consecutively. Currently, pallet10, pallet3, pallet11, pallet2, pallet4, crate0, pallet7, pallet1, pallet5, crate1, pallet0, and pallet6 are clear; hoist0, hoist4, hoist3, hoist1, hoist5, hoist10, hoist7, hoist2, hoist9, hoist6, hoist11, and hoist8 are available; pallet5 is at depot5, pallet2 is at depot2, hoist10 is at distributor0, hoist2 is at depot2, truck0 is at depot3, pallet3 is at depot3, hoist4 is at depot4, pallet0 is at depot0, hoist0 is at depot0, crate0 is at depot8, hoist7 is at depot7, hoist11 is at distributor1, pallet10 is at distributor0, truck1 is at depot0, crate1 is at depot9, pallet11 is at distributor1, pallet8 is at depot8, hoist6 is at depot6, hoist1 is at depot1, pallet7 is at depot7, hoist5 is at depot5, pallet6 is at depot6, pallet4 is at depot4, hoist8 is at depot8, hoist3 is at depot3, hoist9 is at depot9, pallet1 is at depot1, and pallet9 is at depot9; crate0 is on pallet8 and crate1 is on pallet9. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck1 depot0 depot4)", "(drive truck1 depot0 depot7)", "(drive truck1 depot0 depot9)", "(drive truck0 depot3 distributor1)", "(lift hoist9 crate1 pallet9 depot9)", "(drive truck1 depot0 distributor1)", "(drive truck0 depot3 depot6)", "(drive truck0 depot3 distributor0)", "(drive truck0 depot3 depot7)", "(drive truck0 depot3 depot0)", "(drive truck0 depot3 depot2)", "(drive truck0 depot3 depot1)", "(drive truck0 depot3 depot4)", "(drive truck1 depot0 depot0)", "(drive truck1 depot0 depot6)", "(drive truck0 depot3 depot3)", "(drive truck1 depot0 depot2)", "(drive truck1 depot0 depot3)", "(drive truck1 depot0 distributor0)", "(lift hoist8 crate0 pallet8 depot8)", "(drive truck0 depot3 depot5)"], "no": ["(drive truck1 depot0 depot1)", "(drive truck0 depot3 depot9)", "(drive truck1 depot0 depot5)"], "opt": "6", "yes": ["(drive truck1 depot0 depot8)", "(drive truck0 depot3 depot8)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot8) (at crate1 depot9) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot3) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate0) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (on crate0 pallet8) (on crate1 pallet9))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 401185114724532633, "group": "goal_closer_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 2 trucks, 2 distributors, 2 crates, 2 depots, 4 pallets, numbered consecutively. Currently, pallet3, pallet2, pallet1, and crate1 are clear; hoist3, hoist1, and hoist2 are available; pallet3 is at distributor1, hoist2 is at distributor0, pallet2 is at distributor0, crate1 is at depot0, truck1 is at distributor1, pallet0 is at depot0, hoist0 is at depot0, hoist1 is at depot1, hoist3 is at distributor1, pallet1 is at depot1, and truck0 is at distributor1; crate1 is on pallet0; hoist0 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drive truck1 distributor1 depot0)", "(drive truck1 distributor1 distributor1)", "(drive truck1 distributor1 depot1)", "(drive truck0 distributor1 distributor0)", "(drive truck0 distributor1 depot0)"], "no": ["(drive truck1 distributor1 distributor0)", "(drive truck0 distributor1 depot1)", "(drive truck0 distributor1 distributor1)"], "opt": "1", "yes": ["(drop hoist0 crate0 crate1 depot0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor1) (at truck1 distributor1) (available hoist1) (available hoist2) (available hoist3) (clear crate1) (clear pallet1) (clear pallet2) (clear pallet3) (lifting hoist0 crate0) (on crate1 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
