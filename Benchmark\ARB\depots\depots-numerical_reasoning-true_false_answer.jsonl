{"question_id": "1c7f6d75-b419-4fab-9269-49c64a74fa75", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2 and crate2 is dropped on pallet5 at distributor1 by hoist5 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 10?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, then truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 then travels from distributor1 to distributor2, and finally, hoist5 drops crate2 onto pallet5 at distributor1 to reach the current state. Is it True or False that the number of actions in the sequence that led to the current state is equal to 10?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is situated at depot3, crate2 is located at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is situated at distributor1, crate3 is empty, crate3 is positioned on pallet5, truck2 is located at depot1, pallet3 is located at depot3, truck1 is situated at distributor0, pallet6 is located at distributor2, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is available, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is situated at distributor2, hoist6 is available for use, pallet0 is located at depot0, pallet1 is situated at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is situated at distributor1 and truck0 is situated at depot1."}
{"question_id": "d7b62272-466a-4504-8ade-39616d2aa952", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 26? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: hoist0 moves crate2 from pallet0 at depot0 to achieve the current state. In this state, does the number of valid properties of the state that do not involve negations equal 26? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is placed on pallet4. Crate1 is located at depot2, clear of any crates, and is positioned on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on top of pallet0. Crate3 is located at distributor2, with no crates on top of it, and is placed on pallet5. The location of hoist4 is distributor1. Hoist0 is available and situated at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and located at depot2. Hoist3 is available for work and situated at distributor0. Hoist4 is accessible, and hoist5 is also accessible and located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "f5d12d5e-2927-42ad-b37f-40af43cb58b3", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet3 at depot3 by hoist3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, hoist3 loads crate1 into truck2 at depot3, hoist3 unloads crate0 from truck2 at depot3, from depot3, truck2 is driven to distributor0, crate3 is unloaded by hoist4 from truck2 at distributor0, hoist3 drops crate0 on pallet3 at depot3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 42? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2 at depot0. Next, truck2 is driven from depot0 to distributor1. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3, and at distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, while unloading crate2 from truck2. Truck2 then proceeds from distributor1 to distributor2. At distributor1, hoist5 places crate2 on pallet5, and at distributor2, hoist6 lifts crate0 from pallet6 and loads it into truck2. Truck2 is then driven from distributor2 to depot3, where hoist3 loads crate1 into truck2 and unloads crate0 from truck2. From depot3, truck2 is driven to distributor0, where hoist4 unloads crate3 from truck2. Finally, hoist3 places crate0 on pallet3 at depot3, and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 42? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for use, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is located at distributor1 and truck0 is stationed at depot1."}
{"question_id": "0238f835-1683-42ce-bc36-64ef3cc0a110", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, truck1 is driven to depot1 from depot0, hoist1 unloads crate2 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, at depot2, hoist2 lifts crate3 off pallet2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, at distributor3, hoist6 drops crate3 on pallet6, crate1 is lifted from crate0 at distributor2 by hoist5, crate1 is loaded by hoist5 into truck0 at distributor2, hoist5 lifts crate0 from pallet5 at distributor2, at distributor2, hoist5 loads crate0 into truck0, hoist5 unloads crate1 from truck0 at distributor2, from distributor2, truck0 is driven to distributor0, crate0 is unloaded by hoist3 from truck0 at distributor0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck1, after which truck1 is driven back to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which is then driven to distributor3, where hoist6 unloads crate3 and drops it on pallet6. At distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, also lifting crate0 from pallet5 and loading it into truck0, but then unloads crate1 from truck0. Truck0 is then driven to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "1c51c408-83b2-4722-b753-43af611e9f42", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck1 at depot0, truck1 is driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, truck2 is driven from depot2 to distributor3, hoist6 unloads crate3 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, hoist5 lifts crate1 from crate0 at distributor2, at distributor2, hoist5 loads crate1 into truck0, crate0 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate0 into truck0 at distributor2, crate1 is unloaded by hoist5 from truck0 at distributor2, from distributor2, truck0 is driven to distributor0, hoist3 unloads crate0 from truck0 at distributor0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 5148? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0, loads it into truck1, and then truck1 is driven back to depot1. At depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, loads it into truck2, and truck2 is driven to distributor3. Upon arrival, hoist6 unloads crate3 from truck2 and drops it on pallet6. At distributor2, hoist5 lifts crate1 from crate0, loads it into truck0, lifts crate0 from pallet5, and loads it into truck0 as well. However, crate1 is then unloaded from truck0 by hoist5. Truck0 is then driven to distributor0, where hoist3 unloads crate0 and drops it on pallet3, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 5148? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "dd9cc784-1c6a-47be-b148-bf7c85babd67", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, at depot1, hoist1 drops crate2 on pallet1, hoist2 lifts crate3 from pallet2 at depot2, at depot2, hoist2 loads crate3 into truck2, truck2 is driven to distributor3 from depot2, hoist6 unloads crate3 from truck2 at distributor3, at distributor3, hoist6 drops crate3 on pallet6, hoist5 lifts crate1 from crate0 at distributor2, crate1 is loaded by hoist5 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, at distributor2, hoist5 loads crate0 into truck0, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, at distributor0, hoist3 unloads crate0 from truck0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 5614? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck1, which is then driven back to depot1. At depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven to distributor3. At distributor3, hoist6 unloads crate3 from truck2 and places it on pallet6. At distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, then lifts crate0 from pallet5 and loads it into truck0 as well. However, crate1 is then unloaded from truck0, and truck0 is driven to distributor0. At distributor0, hoist3 unloads crate0 from truck0 and places it on pallet3, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 5614? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "dc3f767f-fcae-498b-90fa-71fe198d4892", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, at depot3, hoist3 lifts crate1 off pallet3, crate3 is lifted from pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1, crate2 is dropped on pallet5 at distributor1 by hoist5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, at depot3, hoist3 unloads crate0 from truck2, truck2 is driven from depot3 to distributor0, at distributor0, hoist4 unloads crate3 from truck2, crate0 is dropped on pallet3 at depot3 by hoist3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 38? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck2, then truck2 proceeds to distributor1. At depot3, hoist3 lifts crate1 from pallet3, while at distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, and unloads crate2 from truck2. Truck2 then heads to distributor2, where crate2 is placed on pallet5 by hoist5. At distributor2, hoist6 lifts crate0 from pallet6 and loads it into truck2, which then travels to depot3. At depot3, hoist3 loads crate1 into truck2 and unloads crate0 from truck2. Truck2 then drives to distributor0, where hoist4 unloads crate3 from truck2. Finally, hoist3 places crate0 on pallet3 at depot3, and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 38? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is located at depot0, pallet1 is situated at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is situated at distributor1 and truck0 is stationed at depot1."}
{"question_id": "4bf2db26-f9c8-49fb-ad74-5ed096aca95c", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven from depot2 to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4 and at distributor1, hoist4 loads crate0 into truck2 to reach the current state. In this state, is the number of inexecutable actions equal to 3873? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first removes crate2 from pallet0 and then loads it into truck2, after which truck2 travels from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 off pallet2 and loads it into truck0, which then drives from depot2 to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. At distributor1, hoist4 lifts crate0 off pallet4 and loads it into truck2, resulting in the current state. In this state, is the number of inexecutable actions equal to 3873? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is placed on pallet4. Crate1 is located at depot2, clear of any crates, and is positioned on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is placed on pallet5. The location of hoist4 is distributor1. Hoist0 is available and situated at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and located at depot2. Hoist3 is available for work and situated at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "0d4f563b-325b-4c58-b2ff-355f33db9e55", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, hoist3 lifts crate1 from pallet3 at depot3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2 and crate2 is dropped on pallet5 at distributor1 by hoist5 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 193? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is moved from depot1 to depot0, then hoist0 at depot0 lifts crate2 from pallet0, loads it into truck2, and truck2 proceeds to distributor1. At distributor1, hoist3 lifts crate1 from pallet3 at depot3, while hoist5 lifts crate3 off pallet5 and loads it into truck2. Subsequently, hoist5 unloads crate2 from truck2 and truck2 is driven to distributor2. Meanwhile, crate2 is placed on pallet5 at distributor1 by hoist5, resulting in the current state. In this state, is the number of valid properties involving negations equal to 193? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is located at distributor1 and truck0 is stationed at depot1."}
{"question_id": "2bd7c0f8-b4fb-4d68-a1ca-c3f576fd5c49", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 210? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 210? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear of any crates and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "718ea4d5-94b2-4a46-a8b1-d05d1366520a", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, hoist4 lifts crate0 from pallet4 at distributor1, crate0 is loaded by hoist4 into truck2 at distributor1, hoist5 lifts crate3 from pallet5 at distributor2, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven to distributor1 from distributor2, hoist4 unloads crate3 from truck1 at distributor1, hoist4 drops crate3 on pallet4 at distributor1, at distributor1, hoist4 unloads crate2 from truck2, from distributor1, truck2 is driven to depot1, crate0 is unloaded by hoist1 from truck2 at depot1 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 29? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2 at depot0, then truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2, after which truck0 is driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1. Truck1 is then driven from distributor2 to distributor1, where hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, at distributor1, hoist4 unloads crate2 from truck2, and truck2 is driven from distributor1 to depot1. At depot1, hoist1 unloads crate0 from truck2 and places it on pallet1, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 29? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "1f03052e-d64e-44e1-959e-d0940c41d888", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, hoist6 drops crate3 on pallet6 at distributor3, at distributor2, hoist5 lifts crate1 off crate0, hoist5 loads crate1 into truck0 at distributor2, at distributor2, hoist5 lifts crate0 off pallet5, at distributor2, hoist5 loads crate0 into truck0, at distributor2, hoist5 unloads crate1 from truck0, from distributor2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate0 from truck0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 212? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, where hoist0 lifts crate2 off pallet0 and loads it into truck1, then truck1 proceeds to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which then drives to distributor3, where hoist6 unloads crate3 and drops it on pallet6. At distributor2, hoist5 lifts crate1 off crate0 and loads it into truck0, then lifts crate0 off pallet5 and loads it into truck0 as well. However, hoist5 then unloads crate1 from truck0. Subsequently, truck0 drives to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 212? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "f8d88db5-07d0-4ba6-bc27-b1d92114ff78", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, truck1 is driven from depot0 to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, hoist2 loads crate3 into truck2 at depot2, from depot2, truck2 is driven to distributor3, hoist6 unloads crate3 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, hoist5 lifts crate1 from crate0 at distributor2, hoist5 loads crate1 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, crate0 is loaded by hoist5 into truck0 at distributor2, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, crate0 is unloaded by hoist3 from truck0 at distributor0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 249? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck1; truck1 then proceeds from depot0 to depot1, where crate2 is unloaded by hoist1 and placed on pallet1. At depot2, hoist2 lifts crate3 from pallet2, loads it into truck2, and then truck2 is driven to distributor3; at distributor3, hoist6 unloads crate3 from truck2 and drops it on pallet6. Meanwhile, at distributor2, hoist5 lifts crate1 from crate0, loads it into truck0, lifts crate0 from pallet5, and loads it into truck0 as well; crate1 is then unloaded from truck0 by hoist5. Subsequently, truck0 is driven from distributor2 to distributor0, where crate0 is unloaded by hoist3 and placed on pallet3. The question is, in the resulting state, does the number of valid properties involving negations equal 249? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "438b206d-4bcf-4870-a668-8cb53148695a", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, at distributor1, hoist4 lifts crate0 off pallet4, hoist4 loads crate0 into truck2 at distributor1, crate3 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven to distributor1 from distributor2, hoist4 unloads crate3 from truck1 at distributor1, hoist4 drops crate3 on pallet4 at distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, at depot1, hoist1 unloads crate0 from truck2 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 145? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which is then driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. At distributor0, crate1 is unloaded from truck0 by hoist3 and placed on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. Upon arrival, hoist4 unloads crate3 from truck1 and places it on pallet4, and also unloads crate2 from truck2. Truck2 is then driven from distributor1 to depot1, where hoist1 unloads crate0 and places it on pallet1, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 145? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "99f1de2d-806a-4f71-a7d4-f1cd50e991d8", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, crate3 is lifted from crate2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, crate2 is lifted from crate1 at depot2 by hoist2, hoist2 loads crate2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven to distributor0 from depot2, hoist3 lifts crate0 from pallet3 at distributor0 and at distributor0, hoist3 loads crate0 into truck2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 175? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot0 to depot2, then at depot2, hoist2 lifts crate3 from crate2 and loads it into truck2, next hoist2 lifts crate2 from crate1 and loads it into truck2, after that hoist2 lifts crate1 from pallet2 and loads it into truck2, subsequently truck2 is driven from depot2 to distributor0, and finally at distributor0, hoist3 lifts crate0 from pallet3 and loads it into truck2 to achieve the current state. In this state, is the number of valid properties of the state that involve negations equal to 175? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 can be found. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "dc939eb8-7230-40a5-995e-06d925d3bccd", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1, crate0 is loaded by hoist4 into truck2 at distributor1, at distributor2, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, crate3 is unloaded by hoist4 from truck1 at distributor1, hoist4 drops crate3 on pallet4 at distributor1, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven from distributor1 to depot1, hoist1 unloads crate0 from truck2 at depot1 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, is the number of inexecutable actions equal to 3815? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "To reach the current state from the initial condition, the following sequence of actions is executed: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, then truck2 is driven to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4, then unloads crate2 from truck2. Truck2 is then driven to depot1, where hoist1 unloads crate0 and places it on pallet1. Given this sequence of actions, is the number of inexecutable actions equal to 3815? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "b1304df5-8855-43fe-8517-3b50d2d4fcbd", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 45? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: truck1 is moved from depot1 to depot0 to achieve the current state. In this state, does the count of valid properties that do not include negations equal 45? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "c4e101fc-25ce-455a-9d20-7e4bd4f81852", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 213? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0 to achieve the current state. In this state, does the number of valid properties involving negations equal 213? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "e6c0b5b6-8632-45ef-9b06-a93b6515d319", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 39? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0 to achieve the current state. In this state, does the number of valid properties without negations equal 39? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is also clear of any crates and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "592243c9-3c54-4c69-9582-5889c4344b68", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 192? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: hoist0 moves crate2 from pallet0 at depot0 to achieve the current state. In this state, does the number of valid properties of the state that involve negations equal 192? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "f2629170-afee-4f9d-b6cd-4b74e8eea567", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and crate3 is unloaded by hoist6 from truck2 at distributor3 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 6528? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 picks up crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1 and places it on pallet1, meanwhile, at depot2, hoist2 lifts crate3 from pallet2, hoist2 loads crate3 into truck2 at depot2, truck2 is then driven from depot2 to distributor3, and finally, hoist6 unloads crate3 from truck2 at distributor3, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 6528? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and is positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, is clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is home to hoist1. Distributor1 houses hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and is situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear of any crates and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "b0fe4ed1-c3aa-43a5-b958-1c9e9ee91872", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, hoist2 lifts crate3 from pallet2 at depot2, at depot2, hoist2 loads crate3 into truck2, truck2 is driven from depot2 to distributor3 and crate3 is unloaded by hoist6 from truck2 at distributor3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 37? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 picks up crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, and hoist1 places crate2 on pallet1 at depot1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2, loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3, and hoist6 unloads crate3 from truck2 at distributor3, resulting in the current state. In this state, does the number of valid properties that do not involve negations equal 37? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "fae88cb9-7b83-4ebf-bd39-fbd8b721a3f7", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, crate3 is lifted from crate2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor0, at distributor0, hoist3 lifts crate0 off pallet3 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 28? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from depot0, truck2 travels to depot2, where hoist2 lifts crate3 from crate2 and loads it into truck2, then lifts crate2 from crate1 and loads it into truck2, and finally lifts crate1 from pallet2 and loads it into truck2; subsequently, truck2 is driven from depot2 to distributor0, where hoist3 unloads crate0 from pallet3 and loads it into truck2, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 28? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 can be found. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "41847c04-de7a-407f-9337-ab3efdc1dd2f", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, at depot2, hoist2 lifts crate1 off pallet2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven from depot2 to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4 and at distributor1, hoist4 loads crate0 into truck2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, then truck2 is driven from depot0 to distributor1. At depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. Meanwhile, at distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 33? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and situated at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is located at depot2 and is available for work. Hoist3 is situated at distributor0 and is available for work. Both hoist4 and hoist5 are accessible, with hoist5 located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and is located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and is situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "4d880d8f-8385-434a-aba2-3955d291f072", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, at depot2, hoist2 lifts crate3 off crate2, at depot2, hoist2 loads crate3 into truck2, at depot2, hoist2 lifts crate2 off crate1, at depot2, hoist2 loads crate2 into truck2, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 169? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is executed: truck2 travels from depot0 to depot2, then at depot2, hoist2 unloads crate3 from crate2, followed by loading crate3 into truck2, then unloads crate2 from crate1 and loads crate2 into truck2, next lifts crate1 from pallet2 and loads crate1 into truck2, after which truck2 travels from depot2 to distributor0. At distributor0, hoist3 unloads crate0 from pallet3 and loads crate0 into truck2, resulting in the current state. In this state, does the total number of valid properties (including both affirmative and negated properties) equal 169? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 is located. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates, while pallet2 is located at depot2. Pallet3 is situated at distributor0, pallet4 is clear and located at distributor1, and pallet5 is clear and located at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "e092b120-0641-4bd4-998d-81ba7e229097", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven to depot1 from depot0, at depot1, hoist1 unloads crate2 from truck1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3 and hoist6 unloads crate3 from truck2 at distributor3 to reach the current state. In this state, is the number of executable actions equal to 23? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1, then truck1 returns to depot1, and at depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, hoist2 at depot2 lifts crate3 from pallet2, loads it into truck2, and truck2 is driven to distributor3, where hoist6 unloads crate3 from truck2, resulting in the current state. In this state, is the number of executable actions equal to 23? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is home to hoist1. Distributor1 houses hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear of any crates and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "cac8cca7-b477-449b-a65f-55f5fcf63ca5", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 210? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: truck2 travels from depot1 to depot0 to attain the current state. In this state, does the count of valid state properties involving negations equal 210? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is located at depot0, pallet1 is situated at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is situated at distributor1 and truck0 is stationed at depot1."}
{"question_id": "158ea750-12a3-45c8-8ddd-af4efc618af5", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, crate3 is lifted from pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2 and crate2 is dropped on pallet5 at distributor1 by hoist5 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 213? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot1 to depot0, hoist0 picks up crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck2 at depot0, truck2 proceeds from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 then drives from distributor1 to distributor2, and hoist5 places crate2 on pallet5 at distributor1 to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 213? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for work, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available, hoist2 is available, hoist2 is situated at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is located at depot0, pallet1 is at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is at distributor1 and truck0 is stationed at depot1."}
{"question_id": "33f57714-8eca-482f-87dd-438bb3e624a6", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck0 at depot2, from depot2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, crate3 is lifted from pallet5 at distributor2 by hoist5, crate3 is loaded by hoist5 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, crate3 is unloaded by hoist4 from truck1 at distributor1, crate3 is dropped on pallet4 at distributor1 by hoist4, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven from distributor1 to depot1, at depot1, hoist1 unloads crate0 from truck2 and hoist1 drops crate0 on pallet1 at depot1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 173? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, after which truck2 is driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1. Truck1 is then driven from distributor2 to distributor1, where hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, hoist4 unloads crate2 from truck2 at distributor1, and truck2 is driven from distributor1 to depot1. Finally, at depot1, hoist1 unloads crate0 from truck2 and places it on pallet1, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 173? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "04f39c43-23bf-4981-8d77-e9cf7caadd5a", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 171? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following steps are taken: truck2 travels from depot0 to depot2 to achieve the current state. In this state, does the count of valid state properties involving negations equal 171? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is placed on pallet3. Crate1 is located at depot2 and is positioned on top of pallet2. Crate2 is also at depot2, and it is situated underneath crate1. Crate3 is positioned at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 is situated. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "2926285f-73a1-4928-b25f-bf14b7cba88e", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven to distributor0 from depot2, crate1 is unloaded by hoist3 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, crate0 is lifted from pallet4 at distributor1 by hoist4, crate0 is loaded by hoist4 into truck2 at distributor1, hoist5 lifts crate3 from pallet5 at distributor2, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven to distributor1 from distributor2, at distributor1, hoist4 unloads crate3 from truck1, at distributor1, hoist4 drops crate3 on pallet4, crate2 is unloaded by hoist4 from truck2 at distributor1, from distributor1, truck2 is driven to depot1, crate0 is unloaded by hoist1 from truck2 at depot1 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 206? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0. Next, truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2, which is then driven to distributor0 from depot2. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. Additionally, hoist5 lifts crate3 from pallet5 at distributor2 and loads it into truck1, which is then driven to distributor1. Upon arrival, hoist4 unloads crate3 from truck1 and places it on pallet4 at distributor1. Furthermore, hoist4 unloads crate2 from truck2 at distributor1, and truck2 is then driven to depot1. Finally, hoist1 unloads crate0 from truck2 at depot1 and drops it on pallet1. The question is, in this resulting state, does the total number of valid properties (including both affirmative and negated properties) equal 206? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 is accessible, and hoist5 is also accessible, with the latter being situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "596b9c2c-aeb3-4d70-a084-26d36497363c", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, at depot2, hoist2 lifts crate3 off crate2, hoist2 loads crate3 into truck2 at depot2, at depot2, hoist2 lifts crate2 off crate1, at depot2, hoist2 loads crate2 into truck2, at depot2, hoist2 lifts crate1 off pallet2, hoist2 loads crate1 into truck2 at depot2, from depot2, truck2 is driven to distributor0, hoist3 lifts crate0 from pallet3 at distributor0, crate0 is loaded by hoist3 into truck2 at distributor0, at distributor0, hoist3 unloads crate1 from truck2, from distributor0, truck2 is driven to distributor1, crate2 is unloaded by hoist4 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, hoist5 unloads crate3 from truck2 at distributor2, at distributor0, hoist3 drops crate1 on pallet3, at distributor1, hoist4 drops crate2 on pallet4, crate3 is dropped on pallet5 at distributor2 by hoist5 and at distributor2, hoist5 unloads crate0 from truck2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 205? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot0 to depot2, where hoist2 unloads crate3 from crate2 and loads it into truck2, then hoist2 unloads crate2 from crate1 and loads it into truck2, and finally hoist2 unloads crate1 from pallet2 and loads it into truck2. Subsequently, truck2 is driven from depot2 to distributor0, where hoist3 unloads crate0 from pallet3 and loads it into truck2, and then unloads crate1 from truck2. Next, truck2 is driven from distributor0 to distributor1, where hoist4 unloads crate2 from truck2, and then from distributor1 to distributor2, where hoist5 unloads crate3 from truck2. After that, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, and hoist5 drops crate3 on pallet5 at distributor2. Finally, hoist5 unloads crate0 from truck2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 205? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned on top of crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 is located. Hoist0 is situated at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "5b362dd5-db28-4e11-9766-bb2a1ae73bad", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven to distributor0 from depot2, crate1 is unloaded by hoist3 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, at distributor1, hoist4 lifts crate0 off pallet4 and hoist4 loads crate0 into truck2 at distributor1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 179? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0, after which truck2 is driven to distributor1 from depot0. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2, and then truck0 is driven to distributor0 from depot2. At distributor0, crate1 is unloaded from truck0 by hoist3 and then dropped onto pallet3 by hoist3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 179? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and situated at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and located at depot2. Hoist3 is available for work and situated at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "7fdc8475-042b-4d75-96dc-c72ccb9992b5", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, crate1 is lifted from crate0 at distributor2 by hoist5, crate1 is loaded by hoist5 into truck0 at distributor2, hoist5 lifts crate0 from pallet5 at distributor2, crate0 is loaded by hoist5 into truck0 at distributor2, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, hoist3 unloads crate0 from truck0 at distributor0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 37? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "To reach the current state from the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck1; truck1 then proceeds from depot0 to depot1, where hoist1 unloads crate2 and places it on pallet1; meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven to distributor3; at distributor3, hoist6 unloads crate3 from truck2 and drops it on pallet6; at distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, then lifts crate0 from pallet5 and loads it into truck0 as well; after unloading crate1 from truck0, truck0 is driven to distributor0, where hoist3 unloads crate0 and drops it on pallet3. In this state, is the number of valid properties that do not involve negations equal to 37? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is located at distributor2, is positioned on top of crate0 and has no other crates on it. Crate2 is found at depot0 and is clear of any other crates. Crate3, located at depot2, is also clear of any crates and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3, situated at distributor0, and hoist4 are available for work. Hoist5, located at distributor2, and hoist6 are also available.\n\nPallet0, which is located at depot0, has crate2 on it. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3, situated at distributor0, and pallet4, located at distributor1, are both clear of any crates. Pallet6, situated at distributor3, is also clear of any crates. Lastly, truck0 is located at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "c374d9bb-2f2b-4765-8e3d-5a59a44b354d", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, at depot3, hoist3 lifts crate1 off pallet3, hoist5 lifts crate3 from pallet5 at distributor1, crate3 is loaded by hoist5 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven from distributor1 to distributor2 and hoist5 drops crate2 on pallet5 at distributor1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 28? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 moves from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck2 at depot0, truck2 is then driven from depot0 to distributor1, at depot3, hoist3 lifts crate1 off pallet3, at distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, hoist5 unloads crate2 from truck2 at distributor1, truck2 is then driven from distributor1 to distributor2, and finally, hoist5 places crate2 on pallet5 at distributor1, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 28? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for use, pallet0 is located at depot0, pallet1 is situated at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is situated at distributor1 and truck0 is stationed at depot1."}
{"question_id": "cf7f8ad5-7b5e-4ad3-a1b7-a7457377d132", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven from depot2 to distributor0, hoist3 unloads crate1 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, is the number of inexecutable actions equal to 3815? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following sequence of actions takes place: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0. Next, truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2. Then, truck0 is driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, resulting in the current state. In this state, is the number of inexecutable actions equal to 3815? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "cb8190eb-c64d-4d29-9073-71c170b85b66", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, at depot2, hoist2 lifts crate3 off crate2, hoist2 loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot0 to depot2, then at depot2, hoist2 unloads crate3 from crate2 and loads it into truck2, next hoist2 unloads crate2 from crate1 and loads it into truck2, followed by unloading crate1 from pallet2 and loading it into truck2, after which truck2 is driven from depot2 to distributor0. Upon arrival at distributor0, hoist3 unloads crate0 from pallet3 and loads it into truck2, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 31? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 can be found. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "c0a6d3b7-369c-453d-ab31-5e038aeb71e9", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven from depot2 to distributor0, hoist3 unloads crate1 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4 and hoist4 loads crate0 into truck2 at distributor1 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 246? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which then travels from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, resulting in the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 246? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "b2f97c3a-d599-4ba5-9911-697a07ae5e7e", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven from depot2 to distributor0, at distributor0, hoist3 unloads crate1 from truck0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4, at distributor1, hoist4 loads crate0 into truck2, hoist5 lifts crate3 from pallet5 at distributor2, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, at distributor1, hoist4 unloads crate3 from truck1, hoist4 drops crate3 on pallet4 at distributor1, hoist4 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to depot1, hoist1 unloads crate0 from truck2 at depot1 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, is the number of inexecutable actions equal to 4032? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0. Next, truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2, which is then driven to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4, and then loads it into truck2. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. Upon arrival, hoist4 unloads crate3 from truck1 and places it on pallet4 at distributor1. Additionally, hoist4 unloads crate2 from truck2 at distributor1. Truck2 is then driven from distributor1 to depot1, where hoist1 unloads crate0 from truck2 and places it on pallet1, resulting in the current state. In this state, is the number of inexecutable actions equal to 4032? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is placed on pallet4. Crate1 is located at depot2, clear of any crates, and is positioned on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is placed on pallet5. The location of hoist4 is distributor1. Hoist0 is available and situated at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and located at depot2. Hoist3 is available for work and situated at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "38f36de1-00f7-4c61-8669-a715eb71bdc7", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven from depot2 to distributor0, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, at distributor1, hoist4 lifts crate0 off pallet4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 173? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, after which truck2 travels from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which then drives from depot2 to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, resulting in the current state. In this state, is the number of valid properties that include negations equal to 173? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 is accessible, and hoist5 is also accessible, with the latter being located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "f8304a3a-7fe8-4001-9b68-b913683bf0f6", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 235? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0 to achieve the current state. In this state, does the total number of valid properties (including both affirmative and negated properties) equal 235? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and is positioned on top of crate0. Crate2 can be found at depot0, and it is clear of any other crates. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, depot1 houses hoist1, distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and is situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, pallet3 is clear and situated at distributor0, pallet4 is clear of any crates and located at distributor1, and pallet6 is clear of any crates and situated at distributor3. Lastly, truck0 is located at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "6f07a1e1-a9bb-496f-baa9-a310905cf88d", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet3 at depot3 by hoist3, at distributor1, hoist5 lifts crate3 off pallet5, crate3 is loaded by hoist5 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven from distributor1 to distributor2, at distributor1, hoist5 drops crate2 on pallet5, hoist6 lifts crate0 from pallet6 at distributor2, at distributor2, hoist6 loads crate0 into truck2, truck2 is driven to depot3 from distributor2, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2 at depot3, from depot3, truck2 is driven to distributor0, hoist4 unloads crate3 from truck2 at distributor0, hoist3 drops crate0 on pallet3 at depot3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 211? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck2 at depot0, truck2 proceeds to distributor1 from depot0, hoist3 lifts crate1 from pallet3 at depot3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is then driven to distributor2 from distributor1, at distributor1, hoist5 places crate2 on pallet5, hoist6 lifts crate0 from pallet6 at distributor2, at distributor2, hoist6 loads crate0 into truck2, truck2 is then driven to depot3 from distributor2, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2 at depot3, truck2 then travels from depot3 to distributor0, hoist4 unloads crate3 from truck2 at distributor0, hoist3 places crate0 on pallet3 at depot3 and hoist4 places crate3 on pallet4 at distributor0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 211? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is located at distributor1 and truck0 is stationed at depot1."}
{"question_id": "63f42444-37c9-4b8d-bbbd-48e4da0adac0", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, hoist2 loads crate3 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 222? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 is driven from depot1 to depot0, where hoist0 removes crate2 from pallet0 and then loads it into truck1, after which truck1 is driven back to depot1, and at depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 off pallet2, loads it into truck2, and then truck2 is driven to distributor3, where hoist6 unloads crate3 from truck2, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 222? True or False", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3, situated at distributor0, and hoist4 are available for work. Hoist5, located at distributor2, and hoist6 are also available.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is at depot2, while pallet3 is clear and located at distributor0. Pallet4, clear of any crates, is situated at distributor1, and pallet6, also clear, is located at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
