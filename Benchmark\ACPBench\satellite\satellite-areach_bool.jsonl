{"id": 697982829725650112, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): star0, groundstation5, planet8, planet7, groundstation3, star2, star9, groundstation1, groundstation4, star10, groundstation6. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument6 supports image of mode infrared1 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to planet7. Satellite satellite1 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite1, satellite5. Following instruments are powered on: instrument3. Following instruments are calibrated: instrument3. A thermograph2 mode image of target planet7 is available. A infrared1 mode image of target planet8 is available. A infrared1 mode image of target star9 is available. A infrared1 mode image of target star10 is available. ", "question": "Is it possible to transition to a state where the action \"point the satellite satellite0 to direction star9 instead of planet7\" can be applied?", "answer": "yes"}
{"id": -6521562304411901465, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, groundstation3, star2, planet5, phenomenon6, groundstation1, groundstation4. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to planet5. Satellite satellite1 is pointing to phenomenon6. Satellite satellite0 is pointing to groundstation4. Power is available on the following satellite(s): satellite0, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Is it possible to transition to a state where the action \"fire laser in direction planet5 in mode infrared1 using the instrument instrument4 on satellite satellite2\" can be applied?", "answer": "no"}
{"id": -8904592331809701451, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): star0, groundstation5, planet8, planet7, groundstation3, star2, star9, groundstation1, groundstation4, star10, groundstation6. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument6 supports image of mode infrared1 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Satellite satellite1 is pointing to groundstation6. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite1, satellite5. Following instruments are powered on: instrument3. Following instruments are calibrated: instrument3. A infrared1 mode image of target planet8 is available. A infrared1 mode image of target star9 is available. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite4 from star10 to groundstation4\" can be applied?", "answer": "no"}
{"id": 4224778746955172081, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, star3, star1, phenomenon5, groundstation4, groundstation2. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite4 has following instruments onboard: instrument11, instrument9, instrument10. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite3 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to groundstation0. Satellite satellite2 is pointing to phenomenon5. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite6, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image1 mode image of target star6 is available. A image0 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the action \"turn the satellite satellite2 from direction star3 to direction star6\" can be applied?", "answer": "yes"}
{"id": 4936761361611780016, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): star0, groundstation5, planet8, planet7, groundstation3, star2, star9, groundstation1, groundstation4, star10, groundstation6. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument6 supports image of mode infrared1 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star0. Satellite satellite1 is pointing to groundstation4. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Power is available on the following satellite(s): satellite3, satellite1. Following instruments are powered on: instrument10, instrument5, instrument7, instrument0. ", "question": "Is it possible to transition to a state where the action \"turn satellite satellite5 to point from star10 direction to groundstation4\" can be applied?", "answer": "yes"}
{"id": 2069740558730661775, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, star3, star1, phenomenon5, groundstation4, groundstation2. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite4 has following instruments onboard: instrument11, instrument9, instrument10. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite2 is pointing to groundstation2. Satellite satellite3 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to star6. Power is available on the following satellite(s): satellite4, satellite0, satellite2, satellite3, satellite1, satellite5. Following instruments are powered on: instrument16. ", "question": "Is it possible to transition to a state where the action \"fix instrument instrument1 on the satellite satellite0\" can be applied?", "answer": "no"}
{"id": 7216992357351929206, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, star3, star1, phenomenon5, groundstation4, groundstation2. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite4 has following instruments onboard: instrument11, instrument9, instrument10. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite4 is pointing to star3. Satellite satellite3 is pointing to groundstation0. Satellite satellite5 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Satellite satellite0 is pointing to star1. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to phenomenon5. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite6, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image2 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the action \"transfer weight on satellite satellite2 from section star1 to section star3\" can be applied?", "answer": "no"}
{"id": -5030808179310385287, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, groundstation3, star2, planet5, phenomenon6, groundstation1, groundstation4. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite1 is pointing to groundstation3. Satellite satellite2 is pointing to phenomenon6. Power is available on the following satellite(s): satellite0, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite1 from star2 to groundstation1\" can be applied?", "answer": "no"}
{"id": 1677785867634412520, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, groundstation3, star2, planet5, groundstation1, star4, planet6. There are 3 image mode(s): infrared2, thermograph0, image1. There are 16 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument12 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3.  Currently, Satellite satellite8 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite0 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite9 is pointing to star4. Satellite satellite3 is pointing to planet5. Satellite satellite6 is pointing to star4. Satellite satellite5 is pointing to groundstation3. Satellite satellite7 is pointing to star2. Power is available on the following satellite(s): satellite0, satellite7, satellite6, satellite8, satellite1, satellite5, satellite4, satellite2, satellite9. Following instruments are powered on: instrument5. Following instruments are calibrated: instrument5. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite9 from star4 to groundstation0\" can be applied?", "answer": "no"}
{"id": 3060792481324172375, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, groundstation3, star2, planet5, phenomenon6, groundstation1, groundstation4. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Satellite satellite1 is pointing to star0. Power is available on the following satellite(s): satellite0, satellite2, satellite1. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite2 from groundstation3 to groundstation1\" can be applied?", "answer": "no"}
