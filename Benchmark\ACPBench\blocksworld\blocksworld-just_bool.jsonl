{"id": 1851874060262167436, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_5 is on top of block block_1, The block block_5 is currently situated under the block block_2, The block block_4 is on top of block block_2, and The block block_1 is on top of block block_3.", "question": "Given the plan: \"unstack object block_4 from object block_3, put down object block_4, pick up the object block_4 from the table, put down object block_4, unstack object block_3 from object block_2, put down object block_3, unstack object block_2 from object block_1, stack the object block_2 on top of the object block_4, pick up the object block_1 from the table, stack the object block_1 on top of the object block_3, pick up the object block_5 from the table, stack the object block_5 on top of the object block_2, unstack object block_5 from object block_2, stack the object block_5 on top of the object block_1, unstack object block_2 from object block_4, stack the object block_2 on top of the object block_5, pick up the object block_4 from the table, stack the object block_4 on top of the object block_2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: stack the object block_5 on top of the object block_2 and unstack object block_5 from object block_2?", "answer": "yes"}
{"id": -7634464818431618385, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_5, The block block_2 is on top of block block_5, The block block_2 is currently situated under the block block_4, and The block block_1 is on top of block block_3.", "question": "Given the plan: \"remove the object block_4 from on top of the object block_3, put down object block_4, remove the object block_3 from on top of the object block_2, stack the object block_3 on top of the object block_5, remove the object block_3 from on top of the object block_5, put down object block_3, remove the object block_2 from on top of the object block_1, stack the object block_2 on top of the object block_4, collect the object block_1 from the table, stack the object block_1 on top of the object block_3, collect the object block_5 from the table, stack the object block_5 on top of the object block_1, remove the object block_2 from on top of the object block_4, stack the object block_2 on top of the object block_5, collect the object block_4 from the table, stack the object block_4 on top of the object block_2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: stack the object block_3 on top of the object block_5 and remove the object block_3 from on top of the object block_5?", "answer": "yes"}
{"id": 7246690288083552689, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is on top of block block_1.", "question": "Given the plan: \"remove the object block_3 from on top of the object block_2, place the object block_3 on the table, remove the object block_2 from on top of the object block_1, stack the object block_2 on top of the object block_3, remove the object block_2 from on top of the object block_3, place the object block_2 on the table, remove block_3 from table, place the object block_3 on the table, remove block_3 from table, stack the object block_3 on top of the object block_1, remove block_2 from table, stack the object block_2 on top of the object block_3\"; can the following action be removed from this plan and still have a valid plan: stack the object block_2 on top of the object block_3?", "answer": "yes"}
{"id": 1447913331789251765, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_1, block_6, block_19, block_7, and block_14. The following block(s) are stacked on top of another block: block_20 is on block_8, block_16 is on block_20, block_15 is on block_16, block_17 is on block_3, block_11 is on block_1, block_3 is on block_13, block_10 is on block_19, block_13 is on block_4, block_12 is on block_17, block_2 is on block_18, block_5 is on block_14, block_4 is on block_11, block_9 is on block_15, and block_8 is on block_7. The goal is to reach a state where the following facts hold: The block block_16 is on top of block block_11, The block block_12 is currently situated under the block block_17, The block block_4 is currently situated above the block block_6, The block block_1 is on top of block block_17, The block block_2 is on top of block block_14, The block block_5 is on top of block block_1, The block block_19 is currently situated above the block block_16, The block block_3 is currently situated above the block block_13, The block block_18 is on top of block block_5, The block block_8 is currently situated under the block block_11, The block block_15 is currently situated above the block block_9, The block block_8 is currently situated above the block block_4, The block block_7 is on top of block block_10, The block block_7 is currently situated under the block block_12, The block block_9 is currently situated above the block block_2, and The block block_14 is on top of block block_20.", "question": "Given the plan: \"unstack the object block_10 from the object block_19, put down object block_10, unstack the object block_9 from the object block_15, stack the object block_9 on top of the object block_2, unstack the object block_12 from the object block_17, stack the object block_12 on top of the object block_10, unstack the object block_15 from the object block_16, stack the object block_15 on top of the object block_9, unstack the object block_16 from the object block_20, stack the object block_16 on top of the object block_15, unstack the object block_20 from the object block_8, put down object block_20, unstack the object block_17 from the object block_3, stack the object block_17 on top of the object block_12, remove block_19 from table, stack the object block_19 on top of the object block_16, unstack the object block_5 from the object block_14, stack the object block_5 on top of the object block_6, remove block_14 from table, stack the object block_14 on top of the object block_20, unstack the object block_3 from the object block_13, stack the object block_3 on top of the object block_19, unstack the object block_13 from the object block_4, put down object block_13, unstack the object block_4 from the object block_11, stack the object block_4 on top of the object block_8, unstack the object block_11 from the object block_1, stack the object block_11 on top of the object block_13, remove block_1 from table, stack the object block_1 on top of the object block_17, unstack the object block_5 from the object block_6, stack the object block_5 on top of the object block_1, unstack the object block_4 from the object block_8, stack the object block_4 on top of the object block_6, unstack the object block_8 from the object block_7, stack the object block_8 on top of the object block_4, unstack the object block_11 from the object block_13, stack the object block_11 on top of the object block_8, unstack the object block_3 from the object block_19, stack the object block_3 on top of the object block_13, unstack the object block_5 from the object block_1, stack the object block_5 on top of the object block_11, unstack the object block_1 from the object block_17, stack the object block_1 on top of the object block_19, unstack the object block_17 from the object block_12, stack the object block_17 on top of the object block_1, unstack the object block_12 from the object block_10, stack the object block_12 on top of the object block_14, remove block_7 from table, stack the object block_7 on top of the object block_10, unstack the object block_12 from the object block_14, stack the object block_12 on top of the object block_7, unstack the object block_17 from the object block_1, stack the object block_17 on top of the object block_12, unstack the object block_1 from the object block_19, stack the object block_1 on top of the object block_17, unstack the object block_5 from the object block_11, stack the object block_5 on top of the object block_1, unstack the object block_19 from the object block_16, stack the object block_19 on top of the object block_14, unstack the object block_16 from the object block_15, stack the object block_16 on top of the object block_11, unstack the object block_19 from the object block_14, stack the object block_19 on top of the object block_16, unstack the object block_15 from the object block_9, stack the object block_15 on top of the object block_14, unstack the object block_9 from the object block_2, stack the object block_9 on top of the object block_15, unstack the object block_2 from the object block_18, stack the object block_2 on top of the object block_3, remove block_18 from table, stack the object block_18 on top of the object block_5, unstack the object block_9 from the object block_15, stack the object block_9 on top of the object block_19, unstack the object block_15 from the object block_14, stack the object block_15 on top of the object block_9, unstack the object block_2 from the object block_3, stack the object block_2 on top of the object block_14, unstack the object block_15 from the object block_9, stack the object block_15 on top of the object block_18, unstack the object block_9 from the object block_19, stack the object block_9 on top of the object block_19, unstack the object block_9 from the object block_19, stack the object block_9 on top of the object block_2, unstack the object block_15 from the object block_18, stack the object block_15 on top of the object block_9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: unstack the object block_9 from the object block_19 and stack the object block_9 on top of the object block_19?", "answer": "yes"}
{"id": 1191412835998500791, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_5, The block block_2 is currently situated above the block block_5, The block block_4 is on top of block block_2, and The block block_3 is currently situated under the block block_1.", "question": "Given the plan: \"unstack object block_4 from object block_3, put down the object block_4, unstack object block_3 from object block_2, stack object block_3 on top of object block_5, unstack object block_3 from object block_5, put down the object block_3, unstack object block_2 from object block_1, stack object block_2 on top of object block_4, pick up the object block_1 from the table, stack object block_1 on top of object block_3, pick up the object block_5 from the table, stack object block_5 on top of object block_1, unstack object block_2 from object block_4, stack object block_2 on top of object block_5, pick up the object block_4 from the table, stack object block_4 on top of object block_2\"; can the following action be removed from this plan and still have a valid plan: pick up the object block_5 from the table?", "answer": "no"}
{"id": 5081501847287293561, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_1, block_6, block_19, block_7, and block_14. The following block(s) are stacked on top of another block: block_20 is on block_8, block_16 is on block_20, block_15 is on block_16, block_17 is on block_3, block_11 is on block_1, block_3 is on block_13, block_10 is on block_19, block_13 is on block_4, block_12 is on block_17, block_2 is on block_18, block_5 is on block_14, block_4 is on block_11, block_9 is on block_15, and block_8 is on block_7. The goal is to reach a state where the following facts hold: The block block_16 is currently situated above the block block_11, The block block_17 is currently situated above the block block_12, The block block_4 is on top of block block_6, The block block_1 is currently situated above the block block_17, The block block_2 is on top of block block_14, The block block_5 is currently situated above the block block_1, The block block_19 is currently situated above the block block_16, The block block_13 is currently situated under the block block_3, The block block_18 is on top of block block_5, The block block_8 is currently situated under the block block_11, The block block_15 is on top of block block_9, The block block_8 is currently situated above the block block_4, The block block_10 is currently situated under the block block_7, The block block_7 is currently situated under the block block_12, The block block_2 is currently situated under the block block_9, and The block block_20 is currently situated under the block block_14.", "question": "Given the plan: \"remove the object block_10 from on top of the object block_19, put down object block_10, remove the object block_9 from on top of the object block_15, stack object block_9 on top of object block_2, remove the object block_12 from on top of the object block_17, stack object block_12 on top of object block_10, remove the object block_15 from on top of the object block_16, stack object block_15 on top of object block_9, remove the object block_16 from on top of the object block_20, stack object block_16 on top of object block_15, remove the object block_20 from on top of the object block_8, put down object block_20, remove the object block_17 from on top of the object block_3, stack object block_17 on top of object block_12, pick up object block_19 from the table, stack object block_19 on top of object block_16, remove the object block_5 from on top of the object block_14, stack object block_5 on top of object block_6, pick up object block_14 from the table, stack object block_14 on top of object block_20, remove the object block_3 from on top of the object block_13, stack object block_3 on top of object block_19, remove the object block_13 from on top of the object block_4, put down object block_13, remove the object block_4 from on top of the object block_11, stack object block_4 on top of object block_8, remove the object block_11 from on top of the object block_1, stack object block_11 on top of object block_13, pick up object block_1 from the table, stack object block_1 on top of object block_17, remove the object block_5 from on top of the object block_6, stack object block_5 on top of object block_1, remove the object block_4 from on top of the object block_8, stack object block_4 on top of object block_6, remove the object block_8 from on top of the object block_7, stack object block_8 on top of object block_4, remove the object block_11 from on top of the object block_13, stack object block_11 on top of object block_8, remove the object block_3 from on top of the object block_19, stack object block_3 on top of object block_13, remove the object block_5 from on top of the object block_1, stack object block_5 on top of object block_11, remove the object block_1 from on top of the object block_17, stack object block_1 on top of object block_19, remove the object block_17 from on top of the object block_12, stack object block_17 on top of object block_1, remove the object block_12 from on top of the object block_10, stack object block_12 on top of object block_14, pick up object block_7 from the table, stack object block_7 on top of object block_10, remove the object block_12 from on top of the object block_14, stack object block_12 on top of object block_7, remove the object block_17 from on top of the object block_1, stack object block_17 on top of object block_12, remove the object block_1 from on top of the object block_19, stack object block_1 on top of object block_17, remove the object block_5 from on top of the object block_11, stack object block_5 on top of object block_1, remove the object block_19 from on top of the object block_16, stack object block_19 on top of object block_14, remove the object block_16 from on top of the object block_15, stack object block_16 on top of object block_11, remove the object block_19 from on top of the object block_14, stack object block_19 on top of object block_16, remove the object block_15 from on top of the object block_9, stack object block_15 on top of object block_14, remove the object block_9 from on top of the object block_2, stack object block_9 on top of object block_15, remove the object block_2 from on top of the object block_18, stack object block_2 on top of object block_19, pick up object block_18 from the table, put down object block_18, pick up object block_18 from the table, put down object block_18, pick up object block_18 from the table, stack object block_18 on top of object block_5, remove the object block_9 from on top of the object block_15, stack object block_9 on top of object block_18, remove the object block_15 from on top of the object block_14, stack object block_15 on top of object block_9, remove the object block_2 from on top of the object block_19, stack object block_2 on top of object block_14, remove the object block_15 from on top of the object block_9, stack object block_15 on top of object block_19, remove the object block_9 from on top of the object block_18, stack object block_9 on top of object block_2, remove the object block_15 from on top of the object block_19, stack object block_15 on top of object block_9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: put down object block_18 and pick up object block_18 from the table?", "answer": "yes"}
{"id": -1593190598297143480, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3.", "question": "Given the plan: \"unstack the object block_3 from the object block_2, stack object block_3 on top of object block_2, unstack the object block_3 from the object block_2, put down object block_3, unstack the object block_2 from the object block_1, put down object block_2, collect the object block_3 from the table, stack object block_3 on top of object block_1, unstack the object block_3 from the object block_1, put down object block_3, collect the object block_3 from the table, stack object block_3 on top of object block_1\"; can the following action be removed from this plan and still have a valid plan: stack object block_3 on top of object block_2?", "answer": "no"}
{"id": 6888605824248685564, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_1, block_6, block_19, block_7, and block_14. The following block(s) are stacked on top of another block: block_20 is on block_8, block_16 is on block_20, block_15 is on block_16, block_17 is on block_3, block_11 is on block_1, block_3 is on block_13, block_10 is on block_19, block_13 is on block_4, block_12 is on block_17, block_2 is on block_18, block_5 is on block_14, block_4 is on block_11, block_9 is on block_15, and block_8 is on block_7. The goal is to reach a state where the following facts hold: The block block_11 is currently situated under the block block_16, The block block_17 is currently situated above the block block_12, The block block_4 is on top of block block_6, The block block_1 is currently situated above the block block_17, The block block_14 is currently situated under the block block_2, The block block_1 is currently situated under the block block_5, The block block_16 is currently situated under the block block_19, The block block_3 is currently situated above the block block_13, The block block_18 is on top of block block_5, The block block_11 is currently situated above the block block_8, The block block_9 is currently situated under the block block_15, The block block_8 is on top of block block_4, The block block_7 is on top of block block_10, The block block_12 is on top of block block_7, The block block_9 is currently situated above the block block_2, and The block block_14 is on top of block block_20.", "question": "Given the plan: \"remove the object block_9 from on top of the object block_15, place the object block_9 on top of the object block_2, remove the object block_15 from on top of the object block_16, place the object block_15 on the table, remove the object block_12 from on top of the object block_17, place the object block_12 on the table, remove the object block_16 from on top of the object block_20, place the object block_16 on the table, remove the object block_17 from on top of the object block_3, place the object block_17 on the table, remove the object block_5 from on top of the object block_14, place the object block_5 on top of the object block_17, remove the object block_5 from on top of the object block_17, place the object block_5 on the table, remove the object block_10 from on top of the object block_19, place the object block_10 on the table, remove the object block_20 from on top of the object block_8, place the object block_20 on the table, remove block_15 from table, place the object block_15 on top of the object block_9, remove block_14 from table, place the object block_14 on top of the object block_20, remove block_17 from table, place the object block_17 on top of the object block_12, remove block_19 from table, place the object block_19 on top of the object block_16, remove the object block_8 from on top of the object block_7, place the object block_8 on the table, remove block_7 from table, place the object block_7 on top of the object block_10, remove the object block_17 from on top of the object block_12, place the object block_17 on the table, remove block_12 from table, place the object block_12 on top of the object block_7, remove block_17 from table, place the object block_17 on top of the object block_12, remove the object block_3 from on top of the object block_13, place the object block_3 on the table, remove the object block_13 from on top of the object block_4, place the object block_13 on the table, remove block_3 from table, place the object block_3 on top of the object block_13, remove the object block_4 from on top of the object block_11, place the object block_4 on top of the object block_6, remove the object block_11 from on top of the object block_1, place the object block_11 on top of the object block_8, remove block_1 from table, place the object block_1 on top of the object block_17, remove block_5 from table, place the object block_5 on top of the object block_1, remove the object block_11 from on top of the object block_8, place the object block_11 on the table, remove block_8 from table, place the object block_8 on top of the object block_4, remove block_11 from table, place the object block_11 on top of the object block_8, remove the object block_19 from on top of the object block_16, place the object block_19 on the table, remove block_16 from table, place the object block_16 on top of the object block_11, remove block_19 from table, place the object block_19 on top of the object block_16, remove the object block_15 from on top of the object block_9, place the object block_15 on the table, remove the object block_9 from on top of the object block_2, place the object block_9 on the table, remove block_15 from table, place the object block_15 on top of the object block_9, remove the object block_2 from on top of the object block_18, place the object block_2 on top of the object block_14, remove block_18 from table, place the object block_18 on top of the object block_5, remove the object block_15 from on top of the object block_9, place the object block_15 on the table, remove block_9 from table, place the object block_9 on top of the object block_2, remove block_15 from table, place the object block_15 on top of the object block_9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object block_5 on top of the object block_17 and remove the object block_5 from on top of the object block_17?", "answer": "yes"}
{"id": -4024253297516936144, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_5, The block block_2 is currently situated above the block block_5, The block block_4 is on top of block block_2, and The block block_1 is on top of block block_3.", "question": "Given the plan: \"remove the object block_4 from on top of the object block_3, place the object block_4 on the table, remove the object block_3 from on top of the object block_2, place the object block_3 on the table, remove the object block_2 from on top of the object block_1, place the object block_2 on the table, pick up object block_1 from the table, place the object block_1 on top of the object block_4, remove the object block_1 from on top of the object block_4, place the object block_1 on top of the object block_3, pick up object block_5 from the table, place the object block_5 on top of the object block_1, pick up object block_2 from the table, place the object block_2 on top of the object block_5, pick up object block_4 from the table, place the object block_4 on top of the object block_2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object block_1 on top of the object block_4 and remove the object block_1 from on top of the object block_4?", "answer": "yes"}
{"id": -3966675097644208513, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is currently situated above the block block_1.", "question": "Given the plan: \"unstack the object block_3 from the object block_2, put down object block_3, unstack the object block_2 from the object block_1, put down object block_2, pick up object block_2 from the table, put down object block_2, pick up object block_1 from the table, place the object block_1 on top of the object block_2, pick up object block_3 from the table, place the object block_3 on top of the object block_1, unstack the object block_3 from the object block_1, place the object block_3 on top of the object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object block_3 on top of the object block_1 and unstack the object block_3 from the object block_1?", "answer": "yes"}
{"id": 6805655646912598736, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_3. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_2.", "question": "Given the plan: \"pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, stack object block_2 on top of object block_1, unstack object block_2 from object block_1, put down the object block_2, pick up object block_2 from the table, stack object block_2 on top of object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: put down the object block_2 and pick up object block_2 from the table?", "answer": "yes"}
{"id": -3969747564111391627, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_3. The following block(s) is stacked on top of another block: block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_1 and The block block_3 is currently situated under the block block_1.", "question": "Given the plan: \"collect the object block_3 from the table, put down object block_3, collect the object block_3 from the table, put down object block_3, remove the object block_2 from on top of the object block_1, put down object block_2, collect the object block_1 from the table, place the object block_1 on top of the object block_3, remove the object block_1 from on top of the object block_3, place the object block_1 on top of the object block_3, collect the object block_2 from the table, place the object block_2 on top of the object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: remove the object block_1 from on top of the object block_3 and place the object block_1 on top of the object block_3?", "answer": "yes"}
{"id": 4208848912703692972, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_2 and The block block_3 is currently situated above the block block_1.", "question": "Given the plan: \"collect the object block_1 from the table, put down the object block_1, unstack the object block_3 from the object block_2, put down the object block_3, collect the object block_1 from the table, stack the object block_1 on top of the object block_2, unstack the object block_1 from the object block_2, put down the object block_1, collect the object block_2 from the table, put down the object block_2, collect the object block_1 from the table, stack the object block_1 on top of the object block_2, collect the object block_3 from the table, stack the object block_3 on top of the object block_1\"; can the following action be removed from this plan and still have a valid plan: put down the object block_3?", "answer": "no"}
{"id": 2208528741058528525, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_3. The goal is to reach a state where the following facts hold: The block block_2 is currently situated above the block block_1.", "question": "Given the plan: \"collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, place the object block_2 on top of the object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: collect the object block_2 from the table and put down object block_2?", "answer": "yes"}
{"id": -1638613008874701049, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_3. The following block(s) is stacked on top of another block: block_2 is on block_3. The goal is to reach a state where the following facts hold: The block block_2 is currently situated under the block block_1.", "question": "Given the plan: \"remove the object block_2 from on top of the object block_3, put down object block_2, remove block_1 from table, stack object block_1 on top of object block_2, remove block_3 from table\"; can the following action be removed from this plan and still have a valid plan: remove block_1 from table?", "answer": "no"}
