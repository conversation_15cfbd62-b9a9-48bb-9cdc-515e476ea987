{"id": 9049837613513031125, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y0, loc-x1-y1, loc-x2-y1, and loc-x3-y0. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y2", "answer": "yes"}
{"id": 3523378070750313112, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x3-y1, loc-x2-y1, loc-x2-y2, loc-x3-y3, loc-x1-y4, loc-x0-y1, loc-x2-y4, loc-x1-y3, loc-x1-y0, loc-x3-y2, loc-x3-y4, loc-x0-y2, loc-x0-y0, loc-x1-y2, loc-x1-y1, loc-x0-y4, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y4 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x0-y0", "answer": "no"}
{"id": -3588711518378217174, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x1-y0, loc-x3-y1, loc-x2-y1, loc-x3-y2, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x2-y3 has been visited", "answer": "yes"}
{"id": 4380079466326278459, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x0-y0.The following places have been visited: loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x1-y2, and loc-x1-y1. The goal is to reach a state where the following facts hold: Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y4 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x0-y3 has been visited", "answer": "yes"}
{"id": 3587444392823179703, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y2, loc-x2-y3, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x2-y1, loc-x3-y2, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y3", "answer": "no"}
{"id": 4991027463510983589, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x2-y1, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x2-y3 has been visited", "answer": "yes"}
{"id": -8173230092299897472, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y0.The following places have been visited: loc-x0-y1, loc-x0-y2, loc-x0-y0, and loc-x1-y0. The goal is to reach a state where the following facts hold: Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y4 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x0-y2", "answer": "no"}
{"id": 2792000296749103800, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y4, loc-x1-y2, and loc-x2-y3. Currently, the robot is in place loc-x2-y4.The following places have been visited: loc-x3-y4, loc-x0-y2, loc-x2-y4, loc-x1-y3, loc-x1-y4, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x3-y2 has been visited", "answer": "yes"}
{"id": -7342651258682572140, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x0-y1, loc-x0-y0, loc-x1-y1, and loc-x1-y0. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x3-y2 has been visited", "answer": "yes"}
{"id": -1383135437718035770, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y1, loc-x2-y3, loc-x0-y0, loc-x2-y0, loc-x1-y1, loc-x1-y0, loc-x3-y3, loc-x3-y1, loc-x2-y1, loc-x3-y2, loc-x3-y0, and loc-x2-y2. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x0-y3 has been visited", "answer": "yes"}
{"id": -4508310008424411529, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x2-y3, loc-x1-y3, loc-x0-y3, loc-x3-y3, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x2-y1 has been visited", "answer": "yes"}
{"id": -1838219358475474055, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x2-y3, loc-x0-y2, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x1-y0, loc-x3-y3, loc-x2-y1, loc-x3-y2, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y2", "answer": "no"}
{"id": 8507800824277109560, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x2-y3, loc-x0-y2, loc-x0-y0, loc-x2-y0, loc-x1-y0, loc-x3-y3, loc-x3-y1, loc-x3-y2, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x0-y0", "answer": "no"}
{"id": 2388437373313835832, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x2-y3, loc-x0-y2, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x3-y2, loc-x1-y3, loc-x1-y0, loc-x3-y3, loc-x2-y1, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x0-y0 has been visited", "answer": "yes"}
{"id": 5944644628503791475, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x1-y2, loc-x1-y1, loc-x1-y0, loc-x2-y1, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y0", "answer": "yes"}
