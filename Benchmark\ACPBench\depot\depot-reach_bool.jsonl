{"id": -2876959616016670438, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, crate1, pallet10, pallet5, pallet1, pallet4, pallet11, pallet8, pallet0, pallet6, pallet7, pallet9, and pallet3 are clear; hoist3, hoist1, hoist7, hoist10, hoist2, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, truck1 is at depot8, hoist9 is at depot9, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, hoist8 is at depot8, pallet4 is at depot4, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, truck0 is at depot0, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, crate1 is at depot2, hoist11 is at distributor1, and hoist3 is at depot3; crate1 is on pallet2; hoist8 is lifting crate0.", "question": "Is it possible to transition to a state where the following holds: hoist10 is at distributor0 and hoist5 is at distributor0?", "answer": "no"}
{"id": 2209055194466017943, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 pallets, 2 distributors, 2 trucks, 4 hoists, 2 depots, 2 crates, numbered consecutively. Currently, crate1, pallet1, pallet2, and pallet3 are clear; hoist3, hoist2, and hoist1 are available; pallet2 is at distributor0, truck1 is at distributor0, hoist0 is at depot0, hoist1 is at depot1, truck0 is at depot0, pallet1 is at depot1, hoist3 is at distributor1, pallet0 is at depot0, crate1 is at depot0, pallet3 is at distributor1, and hoist2 is at distributor0; crate1 is on pallet0; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the following holds: truck1 is at depot0 and truck0 is at depot1?", "answer": "yes"}
{"id": -4265684394652917076, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 2 trucks, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, pallet5, pallet2, pallet4, pallet0, crate0, and pallet3 are clear; hoist3, hoist2, hoist0, hoist5, and hoist1 are available; hoist4 is at distributor0, pallet4 is at distributor0, crate0 is at depot1, hoist5 is at distributor1, pallet3 is at depot3, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot2, pallet1 is at depot1, truck0 is at depot2, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, hoist3 is at depot3, and hoist2 is at depot2; crate0 is on pallet1; crate1 is in truck0; hoist4 is lifting crate2.", "question": "Is it possible to transition to a state where the following holds: hoist3 is at depot0 and hoist3 is at depot3?", "answer": "no"}
{"id": -3417521372616212671, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 2 trucks, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, crate2, pallet5, pallet1, pallet2, pallet0, and pallet3 are clear; hoist3, hoist2, hoist0, hoist5, and hoist1 are available; hoist4 is at distributor0, pallet4 is at distributor0, hoist5 is at distributor1, crate2 is at distributor0, pallet3 is at depot3, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot1, pallet1 is at depot1, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, hoist3 is at depot3, hoist2 is at depot2, and truck0 is at distributor0; crate2 is on pallet4; crate0 is in truck1; hoist4 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: pallet1 is at distributor0?", "answer": "no"}
{"id": 2783800139767511239, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 pallets, 2 distributors, 2 trucks, 9 hoists, 7 depots, 2 crates, numbered consecutively. Currently, pallet5, pallet1, pallet2, pallet4, pallet8, pallet0, pallet6, pallet7, and pallet3 are clear; hoist1, hoist8, hoist7, hoist2, hoist0, hoist5, hoist6, and hoist4 are available; hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, hoist6 is at depot6, pallet4 is at depot4, hoist5 is at depot5, pallet3 is at depot3, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot1, pallet5 is at depot5, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet0 is at depot0, pallet2 is at depot2, truck0 is at depot1, hoist2 is at depot2, hoist7 is at distributor0, and hoist3 is at depot3; crate0 is in truck1; hoist3 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: pallet0 is at depot0 and hoist0 is at depot0?", "answer": "no"}
{"id": -2467461375520176010, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, pallet10, pallet5, pallet1, pallet2, pallet4, pallet11, pallet0, pallet6, pallet7, pallet9, crate0, and pallet3 are clear; hoist3, hoist1, hoist8, hoist7, hoist10, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, hoist8 is at depot8, pallet4 is at depot4, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot2, truck0 is at depot0, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, hoist11 is at distributor1, crate0 is at depot8, and hoist3 is at depot3; crate0 is on pallet8; hoist2 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: pallet9 is at depot9 and truck1 is at depot9?", "answer": "no"}
{"id": -5628398575096707880, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 pallets, 2 distributors, 2 trucks, 4 hoists, 2 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet2, pallet0, and pallet3 are clear; hoist3, hoist2, hoist0, and hoist1 are available; pallet2 is at distributor0, truck1 is at depot0, hoist0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, hoist3 is at distributor1, pallet0 is at depot0, truck0 is at distributor1, pallet3 is at distributor1, and hoist2 is at distributor0; crate0 is in truck1 and crate1 is in truck1.", "question": "Is it possible to transition to a state where the following holds: hoist3 is at depot0?", "answer": "no"}
{"id": 5187376524514580818, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, pallet10, pallet5, pallet1, pallet2, pallet4, pallet11, pallet8, pallet0, pallet6, pallet7, pallet9, and pallet3 are clear; hoist3, hoist1, hoist8, hoist7, hoist10, hoist2, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, hoist9 is at depot9, truck1 is at distributor1, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, pallet4 is at depot4, hoist8 is at depot8, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, truck0 is at depot0, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, hoist11 is at distributor1, and hoist3 is at depot3; crate0 is in truck1 and crate1 is in truck1.", "question": "Is it possible to transition to a state where the following holds: hoist1 is at depot9?", "answer": "no"}
{"id": -6986907514704113462, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, pallet10, pallet5, pallet1, pallet2, pallet4, pallet11, pallet8, pallet0, pallet6, pallet7, pallet9, and pallet3 are clear; hoist3, hoist1, hoist8, hoist7, hoist10, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, pallet4 is at depot4, hoist8 is at depot8, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, truck1 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, hoist11 is at distributor1, truck0 is at distributor1, and hoist3 is at depot3; crate0 is in truck0; hoist2 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: truck0 is at depot2 and truck1 is at depot3?", "answer": "yes"}
{"id": -1901112298191899187, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 pallets, 2 distributors, 2 trucks, 5 hoists, 3 depots, 2 crates, numbered consecutively. Currently, pallet2, pallet4, pallet0, crate0, and pallet3 are clear; hoist3, hoist2, hoist0, hoist4, and hoist1 are available; crate0 is at depot1, truck1 is at depot0, hoist0 is at depot0, hoist1 is at depot1, hoist4 is at distributor1, pallet1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, truck0 is at depot1, pallet3 is at distributor0, hoist2 is at depot2, hoist3 is at distributor0, and pallet4 is at distributor1; crate0 is on pallet1; crate1 is in truck0.", "question": "Is it possible to transition to a state where the following holds: hoist1 is lifting crate1 and truck1 is at depot1?", "answer": "yes"}
