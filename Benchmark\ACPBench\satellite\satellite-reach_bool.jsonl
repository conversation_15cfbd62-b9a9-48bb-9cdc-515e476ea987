{"id": 3105364738225347439, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite6 is pointing to star1. Satellite satellite5 is pointing to phenomenon5. Power is available on the following satellite(s): satellite0, satellite1, satellite6, satellite5, satellite3. Following instruments are powered on: instrument11, instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite3 is pointing to groundstation4?", "answer": "yes"}
{"id": 7507623453296779685, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star4, planet5, star2, groundstation1, groundstation0, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument12 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument4 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument2 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite1 is pointing to groundstation0. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite6 is pointing to groundstation1. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite9 is pointing to star4. Power is available on the following satellite(s): satellite1, satellite2, satellite9, satellite3, satellite8, satellite5, satellite0, satellite4, satellite7. Following instruments are powered on: instrument10. Following instruments are calibrated: instrument10. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite9 is pointing to star4 and Satellite satellite9 is pointing to planet6?", "answer": "no"}
{"id": -58859649782086694, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, phenomenon6, planet5, star2, groundstation1, groundstation4, groundstation3. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation1. Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Power is available on the following satellite(s): satellite0, satellite2, satellite1. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite2 is pointing to groundstation1 and Satellite satellite2 is pointing to planet5?", "answer": "no"}
{"id": -109716664035700185, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star4, planet5, star2, groundstation1, groundstation0, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument12 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument4 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument2 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite3 is pointing to star4. Satellite satellite5 is pointing to groundstation3. Satellite satellite7 is pointing to star2. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite1 is pointing to star2. Satellite satellite6 is pointing to star4. Satellite satellite8 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite9 is pointing to star4. Power is available on the following satellite(s): satellite1, satellite2, satellite9, satellite8, satellite5, satellite0, satellite4, satellite7. Following instruments are powered on: instrument5, instrument10. Following instruments are calibrated: instrument10. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite7 is pointing to groundstation3 and Satellite satellite7 is pointing to planet6?", "answer": "no"}
{"id": 4376933373471435824, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite2 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite2, satellite4, satellite5, satellite3. Following instruments are powered on: instrument16. Following instruments are calibrated: instrument16. A image1 mode image of target star6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to groundstation0 and Satellite satellite1 is pointing to groundstation0?", "answer": "yes"}
{"id": -8179275404747455390, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star4, planet5, star2, groundstation1, groundstation0, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation0. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet5 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to star4?", "answer": "yes"}
{"id": 7188686075881517182, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star6. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite6 is pointing to groundstation0. Satellite satellite5 is pointing to phenomenon5. Power is available on the following satellite(s): satellite0, satellite4, satellite6, satellite5, satellite3. Following instruments are powered on: instrument6, instrument4. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. A image1 mode image of target star6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to star3 and Satellite satellite0 is pointing to groundstation4?", "answer": "no"}
{"id": 38115520750114517, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star6. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite6, satellite4, satellite5, satellite3. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image1 mode image of target star6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to groundstation0 and Satellite satellite0 is pointing to star1?", "answer": "no"}
{"id": -7853614269930853508, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite4, satellite6, satellite5, satellite3. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite3 is pointing to star1?", "answer": "yes"}
{"id": -7241477041063095125, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite2 is pointing to groundstation2. Satellite satellite5 is pointing to groundstation0. Satellite satellite3 is pointing to star3. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite4, satellite6, satellite5, satellite3. Following instruments are powered on: instrument6. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite3 is pointing to groundstation2 and Satellite satellite5 is pointing to phenomenon5?", "answer": "yes"}
