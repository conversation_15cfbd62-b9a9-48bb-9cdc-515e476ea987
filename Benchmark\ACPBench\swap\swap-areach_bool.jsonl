{"id": -5521162125335532902, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, michelle is assigned zebra, alice is assigned quadcopter, carol is assigned guitar, zoe is assigned frisbee, vic is assigned necklace, dave is assigned slinky, heidi is assigned whale, and xena is assigned iceskates.", "question": "Is it possible to transition to a state where the action \"drive michelle and xena from guitar to frisbee\" can be applied?", "answer": "no"}
{"id": 8743683866226050442, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, bob, dave, xena, kevin, ted, and alice. There are 7 items/roles: quince, leek, yam, mushroom, valerian, parsnip, and ulluco. Currently, alice is assigned valerian, kevin is assigned ulluco, heidi is assigned quince, xena is assigned parsnip, dave is assigned leek, ted is assigned mushroom, and bob is assigned yam.", "question": "Is it possible to transition to a state where the action \"marry alice with kevin using parsnip and ulluco\" can be applied?", "answer": "no"}
{"id": -5160240541075683291, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, bob, dave, xena, kevin, ted, and alice. There are 7 items/roles: quince, leek, yam, mushroom, valerian, parsnip, and ulluco. Currently, heidi is assigned mushroom, xena is assigned quince, alice is assigned yam, ted is assigned leek, kevin is assigned parsnip, bob is assigned ulluco, and dave is assigned valerian.", "question": "Is it possible to transition to a state where the action \"marry alice with ted using yam and leek\" can be applied?", "answer": "no"}
{"id": -7207305354013748401, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, xena is assigned frisbee, dave is assigned necklace, michelle is assigned slinky, carol is assigned iceskates, vic is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, and alice is assigned zebra.", "question": "Is it possible to transition to a state where the action \"swap xena with xena, frisbee for guitar\" can be applied?", "answer": "no"}
{"id": -8115253248543848352, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, carol is assigned guitar, vic is assigned zebra, zoe is assigned frisbee, alice is assigned iceskates, dave is assigned slinky, michelle is assigned quadcopter, xena is assigned whale, and heidi is assigned necklace.", "question": "Is it possible to transition to a state where the action \"exchange necklace of vic with iceskates of heidi\" can be applied?", "answer": "yes"}
{"id": 3424125903127121322, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, xena, frank, vic, quentin, and liam. There are 6 items/roles: wrench, knead, ratchet, nibbler, pliers, and sander. Currently, bob is assigned ratchet, frank is assigned wrench, xena is assigned pliers, liam is assigned sander, quentin is assigned knead, and vic is assigned nibbler.", "question": "Is it possible to transition to a state where the action \"exchange ratchet of frank with pliers of frank\" can be applied?", "answer": "no"}
{"id": -8225768601398321772, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, xena, frank, vic, quentin, and liam. There are 6 items/roles: wrench, knead, ratchet, nibbler, pliers, and sander. Currently, xena is assigned nibbler, frank is assigned ratchet, bob is assigned pliers, vic is assigned sander, quentin is assigned knead, and liam is assigned wrench.", "question": "Is it possible to transition to a state where the action \"swap vic with frank, pliers for knead\" can be applied?", "answer": "yes"}
{"id": -1111435978795325206, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, xena, frank, vic, quentin, and liam. There are 6 items/roles: wrench, knead, ratchet, nibbler, pliers, and sander. Currently, vic is assigned wrench, xena is assigned sander, bob is assigned nibbler, liam is assigned ratchet, quentin is assigned knead, and frank is assigned pliers.", "question": "Is it possible to transition to a state where the action \"trade ratchet of quentin for pliers of frank\" can be applied?", "answer": "yes"}
{"id": -4264426084168253139, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, michelle is assigned zebra, xena is assigned quadcopter, zoe is assigned frisbee, alice is assigned iceskates, dave is assigned slinky, vic is assigned guitar, carol is assigned whale, and heidi is assigned necklace.", "question": "Is it possible to transition to a state where the action \"trade whale of vic for necklace of heidi\" can be applied?", "answer": "yes"}
{"id": 6767165740127222054, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, carol is assigned guitar, xena is assigned frisbee, vic is assigned necklace, heidi is assigned iceskates, dave is assigned slinky, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra.", "question": "Is it possible to transition to a state where the action \"drive michelle and vic from quadcopter to frisbee\" can be applied?", "answer": "no"}
