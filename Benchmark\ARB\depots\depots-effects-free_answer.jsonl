{"question_id": "e55a5a22-f094-460e-b22b-8ae2d8f5ca67", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, if crate2 is lifted from pallet0 at depot0 by hoist0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "crate0 is clear of any crates, crate0 is not at depot1, crate0 is not inside truck0, crate0 is not inside truck1, crate0 is not located at depot0, crate0 is not located at depot3, crate0 is not located at distributor0, crate0 is not located at distributor1, crate0 is not on crate0, crate0 is not on crate1, crate0 is not on pallet1, crate0 is not on pallet3, crate0 is not on pallet4, crate0 is not on top of crate2, crate0 is not on top of crate3, crate0 is not on top of pallet0, crate0 is not on top of pallet2, crate0 is on pallet6, crate1 cannot be found located at depot0, crate1 cannot be found located at depot1, crate1 is clear, crate1 is located at depot3, crate1 is not at distributor0, crate1 is not at distributor2, crate1 is not in truck0, crate1 is not inside truck2, crate1 is not located at depot2, crate1 is not located at distributor1, crate1 is not on crate0, crate1 is not on crate1, crate1 is not on pallet5, crate1 is not on pallet6, crate1 is not on top of crate2, crate1 is not on top of crate3, crate1 is not on top of pallet0, crate2 cannot be found located at distributor2, crate2 does not have crate3 on it, crate2 is not at depot0, crate2 is not at distributor0, crate2 is not clear of any crates, crate2 is not located at depot3, crate2 is not located at distributor1, crate2 is not on crate0, crate2 is not on crate2, crate2 is not on pallet6, crate2 is not on top of crate1, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet2, crate2 is not on top of pallet4, crate2 is not on top of pallet5, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor0, crate3 cannot be found located at distributor2, crate3 is at distributor1, crate3 is clear, crate3 is not at depot0, crate3 is not at depot2, crate3 is not in truck1, crate3 is not in truck2, crate3 is not inside truck0, crate3 is not on crate0, crate3 is not on crate1, crate3 is not on crate3, crate3 is not on pallet4, crate3 is not on pallet6, crate3 is not on top of pallet3, depot0 is where hoist3 is not located, depot0 is where pallet4 is not located, depot1 is where crate2 is not located, depot1 is where hoist1 is located, depot1 is where hoist5 is not located, depot1 is where truck0 is located, depot2 is where crate0 is not located, depot2 is where crate2 is not located, depot2 is where hoist1 is not located, depot2 is where hoist2 is located, depot2 is where hoist3 is not located, depot2 is where hoist5 is not located, depot2 is where pallet1 is not located, depot2 is where pallet3 is not located, depot2 is where pallet5 is not located, depot2 is where truck0 is not located, depot3 is where crate3 is not located, depot3 is where hoist5 is not located, depot3 is where pallet3 is located, depot3 is where pallet4 is not located, depot3 is where truck0 is not located, distributor0 is where hoist3 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet4 is located, distributor0 is where pallet5 is not located, distributor0 is where truck1 is located, distributor1 is where hoist0 is not located, distributor1 is where pallet6 is not located, distributor2 is where crate0 is located, distributor2 is where hoist1 is not located, distributor2 is where hoist3 is not located, distributor2 is where pallet2 is not located, distributor2 is where pallet6 is located, hoist0 cannot be found located at depot1, hoist0 cannot be found located at depot2, hoist0 cannot be found located at distributor2, hoist0 is at depot0, hoist0 is lifting crate2, hoist0 is not at depot3, hoist0 is not available for work, hoist0 is not lifting crate1, hoist0 is not lifting crate3, hoist0 is not located at distributor0, hoist0 is not raising crate0, hoist1 cannot be found located at depot3, hoist1 cannot be found located at distributor0, hoist1 is available for work, hoist1 is not at depot0, hoist1 is not elevating crate0, hoist1 is not elevating crate2, hoist1 is not located at distributor1, hoist1 is not raising crate1, hoist1 is not raising crate3, hoist2 cannot be found located at depot0, hoist2 cannot be found located at depot1, hoist2 cannot be found located at depot3, hoist2 is accessible, hoist2 is not at distributor2, hoist2 is not elevating crate2, hoist2 is not lifting crate3, hoist2 is not located at distributor0, hoist2 is not located at distributor1, hoist2 is not raising crate0, hoist2 is not raising crate1, hoist3 is at depot3, hoist3 is available for work, hoist3 is not elevating crate3, hoist3 is not lifting crate1, hoist3 is not located at depot1, hoist3 is not located at distributor1, hoist3 is not raising crate0, hoist3 is not raising crate2, hoist4 can be found located at distributor0, hoist4 cannot be found located at depot2, hoist4 cannot be found located at distributor1, hoist4 is available for work, hoist4 is not at depot0, hoist4 is not at depot1, hoist4 is not at depot3, hoist4 is not elevating crate2, hoist4 is not lifting crate0, hoist4 is not lifting crate3, hoist4 is not located at distributor2, hoist4 is not raising crate1, hoist5 cannot be found located at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist5 is not at depot0, hoist5 is not elevating crate0, hoist5 is not elevating crate1, hoist5 is not lifting crate3, hoist5 is not located at distributor2, hoist5 is not raising crate2, hoist6 cannot be found located at depot0, hoist6 cannot be found located at depot3, hoist6 cannot be found located at distributor0, hoist6 is accessible, hoist6 is located at distributor2, hoist6 is not elevating crate0, hoist6 is not elevating crate1, hoist6 is not elevating crate2, hoist6 is not elevating crate3, hoist6 is not located at depot1, hoist6 is not located at depot2, hoist6 is not located at distributor1, pallet0 can be found located at depot0, pallet0 cannot be found located at depot2, pallet0 does not have crate3 on it, pallet0 is clear of any crates, pallet0 is not at depot3, pallet0 is not at distributor2, pallet0 is not located at depot1, pallet0 is not located at distributor1, pallet1 can be found located at depot1, pallet1 cannot be found located at distributor1, pallet1 cannot be found located at distributor2, pallet1 does not have crate1 on it, pallet1 does not have crate2 on it, pallet1 does not have crate3 on it, pallet1 is clear, pallet1 is not at depot0, pallet1 is not at depot3, pallet1 is not located at distributor0, pallet2 can be found located at depot2, pallet2 does not have crate1 on it, pallet2 does not have crate3 on it, pallet2 is clear of any crates, pallet2 is not at depot3, pallet2 is not located at depot0, pallet2 is not located at depot1, pallet2 is not located at distributor0, pallet2 is not located at distributor1, pallet3 cannot be found located at depot0, pallet3 does not have crate2 on it, pallet3 has crate1 on it, pallet3 is not at depot1, pallet3 is not at distributor1, pallet3 is not clear of any crates, pallet3 is not located at distributor0, pallet3 is not located at distributor2, pallet4 cannot be found located at distributor2, pallet4 does not have crate1 on it, pallet4 is clear, pallet4 is not at depot1, pallet4 is not at depot2, pallet4 is not located at distributor1, pallet5 cannot be found located at distributor2, pallet5 does not have crate0 on it, pallet5 has crate3 on it, pallet5 is located at distributor1, pallet5 is not at depot0, pallet5 is not at depot1, pallet5 is not clear of any crates, pallet5 is not located at depot3, pallet6 cannot be found located at distributor0, pallet6 is not at depot0, pallet6 is not at depot1, pallet6 is not at depot3, pallet6 is not clear, pallet6 is not located at depot2, truck0 cannot be found located at distributor1, truck0 cannot be found located at distributor2, truck0 does not contain crate2, truck0 is not at distributor0, truck0 is not located at depot0, truck1 cannot be found located at depot1, truck1 cannot be found located at distributor1, truck1 cannot be found located at distributor2, truck1 does not contain crate1, truck1 does not contain crate2, truck1 is not at depot0, truck1 is not at depot3, truck1 is not located at depot2, truck2 can be found located at depot0, truck2 cannot be found located at depot3, truck2 cannot be found located at distributor2, truck2 does not contain crate0, truck2 does not contain crate2, truck2 is not at depot2, truck2 is not at distributor0, truck2 is not located at depot1 and truck2 is not located at distributor1", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: truck2 travels from depot1 to depot0 to achieve the current state. In this state, if hoist0 lifts crate2 from pallet0 at depot0, what are all the possible valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, pallet6 has crate0 on it and is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "db127ba5-9f81-487f-8286-bfc8419e19bf", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, crate3 is lifted from crate2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, crate2 is lifted from crate1 at depot2 by hoist2, crate2 is loaded by hoist2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, if hoist3 unloads crate1 from truck2 at distributor0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "crate0 cannot be found located at depot1, crate0 is not at depot0, crate0 is not at distributor1, crate0 is not clear of any crates, crate0 is not in truck0, crate0 is not located at depot2, crate0 is not located at distributor0, crate0 is not located at distributor2, crate0 is not on crate0, crate0 is not on crate1, crate0 is not on pallet3, crate0 is not on top of crate3, crate0 is not on top of pallet0, crate0 is not on top of pallet4, crate1 cannot be found located at distributor1, crate1 does not have crate3 on it, crate1 is not at distributor0, crate1 is not at distributor2, crate1 is not clear of any crates, crate1 is not in truck0, crate1 is not in truck1, crate1 is not inside truck2, crate1 is not located at depot0, crate1 is not located at depot2, crate1 is not on crate0, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet3, crate1 is not on pallet4, crate1 is not on top of crate1, crate1 is not on top of pallet0, crate1 is not on top of pallet5, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor1, crate2 cannot be found located at distributor2, crate2 does not have crate0 on it, crate2 does not have crate1 on it, crate2 is not at depot2, crate2 is not at distributor0, crate2 is not clear, crate2 is not in truck1, crate2 is not located at depot1, crate2 is not on crate1, crate2 is not on crate2, crate2 is not on crate3, crate2 is not on pallet0, crate2 is not on top of crate0, crate2 is not on top of pallet2, crate3 cannot be found located at depot2, crate3 cannot be found located at distributor1, crate3 does not have crate1 on it, crate3 is not clear of any crates, crate3 is not in truck0, crate3 is not located at depot1, crate3 is not located at distributor2, crate3 is not on crate0, crate3 is not on crate2, crate3 is not on pallet1, crate3 is not on pallet2, crate3 is not on pallet4, crate3 is not on top of crate3, depot0 is where crate3 is not located, depot0 is where hoist3 is not located, depot1 is where crate1 is not located, depot1 is where hoist0 is not located, depot1 is where hoist2 is not located, depot1 is where hoist3 is not located, depot1 is where hoist5 is not located, depot1 is where pallet0 is not located, depot1 is where pallet4 is not located, depot1 is where pallet5 is not located, depot1 is where truck2 is not located, depot2 is where hoist3 is not located, depot2 is where pallet4 is not located, distributor0 is where crate3 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist4 is not located, distributor0 is where pallet5 is not located, distributor1 is where hoist2 is not located, distributor1 is where hoist5 is not located, distributor1 is where pallet0 is not located, distributor1 is where truck0 is not located, distributor1 is where truck1 is not located, distributor2 is where hoist2 is not located, distributor2 is where hoist4 is not located, distributor2 is where pallet1 is not located, distributor2 is where pallet3 is not located, distributor2 is where truck1 is not located, distributor2 is where truck2 is not located, hoist0 cannot be found located at depot2, hoist0 cannot be found located at distributor1, hoist0 cannot be found located at distributor2, hoist0 is not at distributor0, hoist0 is not elevating crate1, hoist0 is not elevating crate3, hoist0 is not lifting crate0, hoist0 is not raising crate2, hoist1 is not at depot0, hoist1 is not at depot2, hoist1 is not at distributor1, hoist1 is not elevating crate0, hoist1 is not elevating crate2, hoist1 is not elevating crate3, hoist1 is not lifting crate1, hoist1 is not located at distributor2, hoist2 cannot be found located at distributor0, hoist2 is not at depot0, hoist2 is not lifting crate1, hoist2 is not raising crate0, hoist2 is not raising crate2, hoist2 is not raising crate3, hoist3 is not at distributor1, hoist3 is not at distributor2, hoist3 is not available for work, hoist3 is not elevating crate2, hoist3 is not elevating crate3, hoist3 is not lifting crate0, hoist4 is not at depot1, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not elevating crate3, hoist4 is not lifting crate1, hoist4 is not located at depot0, hoist4 is not located at depot2, hoist5 is not at distributor0, hoist5 is not elevating crate3, hoist5 is not lifting crate0, hoist5 is not lifting crate2, hoist5 is not located at depot0, hoist5 is not located at depot2, hoist5 is not raising crate1, pallet0 cannot be found located at depot2, pallet0 does not have crate3 on it, pallet0 is not at distributor0, pallet0 is not located at distributor2, pallet1 cannot be found located at depot0, pallet1 does not have crate0 on it, pallet1 does not have crate2 on it, pallet1 is not at depot2, pallet1 is not at distributor1, pallet1 is not located at distributor0, pallet2 cannot be found located at depot0, pallet2 does not have crate0 on it, pallet2 is not at depot1, pallet2 is not at distributor0, pallet2 is not at distributor1, pallet2 is not located at distributor2, pallet3 cannot be found located at distributor1, pallet3 does not have crate2 on it, pallet3 does not have crate3 on it, pallet3 is not located at depot0, pallet3 is not located at depot1, pallet3 is not located at depot2, pallet4 cannot be found located at distributor0, pallet4 does not have crate2 on it, pallet4 is not at distributor2, pallet4 is not located at depot0, pallet5 does not have crate0 on it, pallet5 does not have crate2 on it, pallet5 does not have crate3 on it, pallet5 is not at depot0, pallet5 is not located at depot2, pallet5 is not located at distributor1, truck0 cannot be found located at depot0, truck0 cannot be found located at depot2, truck0 cannot be found located at distributor2, truck0 does not contain crate2, truck0 is not located at depot1, truck1 cannot be found located at depot0, truck1 cannot be found located at depot2, truck1 does not contain crate0, truck1 does not contain crate3, truck1 is not located at distributor0, truck2 is not at depot0, truck2 is not at depot2 and truck2 is not located at distributor1", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, hoist2 lifts crate3 from crate2 at depot2, then hoist2 loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, and finally hoist2 loads crate1 into truck2 at depot2, after which truck2 is driven from depot2 to distributor0. At distributor0, hoist3 lifts crate0 from pallet3 and loads it into truck2 to reach the current state. In this state, if hoist3 unloads crate1 from truck2 at distributor0, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the site of hoist3. Hoist0 is available for use and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is also available for work. Hoist4 is positioned at distributor1 and is available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is also empty. Pallet2 is situated at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and is empty, while pallet5 is at distributor2 and empty. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "b48a816b-46af-484d-ba25-4dae09d5fb8d", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven from depot0 to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and hoist6 unloads crate3 from truck2 at distributor3 to reach the current state. In this state, if at distributor3, hoist6 drops crate3 on pallet6, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "crate0 cannot be found located at distributor3, crate0 does not have crate0 on it, crate0 does not have crate2 on it, crate0 does not have crate3 on it, crate0 has crate1 on it, crate0 is not at distributor1, crate0 is not clear of any crates, crate0 is not in truck1, crate0 is not inside truck0, crate0 is not located at depot0, crate0 is not on crate1, crate0 is not on crate2, crate0 is not on pallet2, crate0 is not on top of pallet1, crate0 is not on top of pallet3, crate0 is on pallet5, crate1 can be found located at distributor2, crate1 cannot be found located at distributor0, crate1 does not have crate2 on it, crate1 is clear of any crates, crate1 is not in truck1, crate1 is not inside truck0, crate1 is not inside truck2, crate1 is not located at depot2, crate1 is not located at distributor3, crate1 is not on crate1, crate1 is not on top of crate2, crate1 is not on top of pallet1, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor3, crate2 does not have crate3 on it, crate2 is at depot1, crate2 is clear of any crates, crate2 is not at depot2, crate2 is not inside truck0, crate2 is not located at distributor1, crate2 is not located at distributor2, crate2 is not on crate2, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet3, crate2 is not on top of pallet5, crate2 is on pallet1, crate3 cannot be found located at distributor0, crate3 does not have crate0 on it, crate3 does not have crate1 on it, crate3 does not have crate3 on it, crate3 is clear, crate3 is located at distributor3, crate3 is not at depot1, crate3 is not at depot2, crate3 is not in truck0, crate3 is not in truck2, crate3 is not located at depot0, crate3 is not located at distributor1, crate3 is not on crate1, crate3 is not on pallet3, crate3 is not on pallet5, crate3 is not on top of pallet1, crate3 is not on top of pallet4, crate3 is on pallet6, depot0 is where crate1 is not located, depot0 is where hoist2 is not located, depot0 is where pallet0 is located, depot0 is where pallet2 is not located, depot0 is where pallet5 is not located, depot0 is where truck0 is not located, depot0 is where truck1 is not located, depot0 is where truck2 is not located, depot1 is where crate0 is not located, depot1 is where crate1 is not located, depot1 is where hoist1 is located, depot1 is where hoist2 is not located, depot2 is where crate0 is not located, depot2 is where hoist0 is not located, depot2 is where pallet6 is not located, depot2 is where truck1 is not located, distributor0 is where crate0 is not located, distributor0 is where crate2 is not located, distributor0 is where hoist0 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet1 is not located, distributor0 is where pallet2 is not located, distributor1 is where crate1 is not located, distributor1 is where hoist2 is not located, distributor1 is where pallet0 is not located, distributor1 is where pallet3 is not located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where crate3 is not located, distributor2 is where hoist1 is not located, distributor2 is where hoist3 is not located, distributor2 is where hoist4 is not located, distributor2 is where hoist6 is not located, distributor2 is where pallet3 is not located, distributor2 is where pallet4 is not located, distributor2 is where truck1 is not located, distributor3 is where hoist1 is not located, distributor3 is where pallet2 is not located, distributor3 is where pallet3 is not located, distributor3 is where pallet5 is not located, hoist0 is available for work, hoist0 is located at depot0, hoist0 is not lifting crate0, hoist0 is not lifting crate3, hoist0 is not located at depot1, hoist0 is not located at distributor1, hoist0 is not located at distributor2, hoist0 is not located at distributor3, hoist0 is not raising crate1, hoist0 is not raising crate2, hoist1 cannot be found located at distributor0, hoist1 cannot be found located at distributor1, hoist1 is available for work, hoist1 is not at depot0, hoist1 is not at depot2, hoist1 is not elevating crate0, hoist1 is not elevating crate1, hoist1 is not elevating crate2, hoist1 is not lifting crate3, hoist2 can be found located at depot2, hoist2 cannot be found located at distributor0, hoist2 is accessible, hoist2 is not at distributor3, hoist2 is not elevating crate0, hoist2 is not lifting crate1, hoist2 is not lifting crate2, hoist2 is not lifting crate3, hoist2 is not located at distributor2, hoist3 can be found located at distributor0, hoist3 cannot be found located at depot2, hoist3 cannot be found located at distributor1, hoist3 is accessible, hoist3 is not at depot1, hoist3 is not elevating crate2, hoist3 is not elevating crate3, hoist3 is not lifting crate1, hoist3 is not located at depot0, hoist3 is not located at distributor3, hoist3 is not raising crate0, hoist4 can be found located at distributor1, hoist4 cannot be found located at depot0, hoist4 cannot be found located at distributor0, hoist4 is available for work, hoist4 is not at depot2, hoist4 is not elevating crate2, hoist4 is not lifting crate0, hoist4 is not located at depot1, hoist4 is not located at distributor3, hoist4 is not raising crate1, hoist4 is not raising crate3, hoist5 can be found located at distributor2, hoist5 cannot be found located at depot1, hoist5 cannot be found located at depot2, hoist5 cannot be found located at distributor0, hoist5 is available, hoist5 is not at depot0, hoist5 is not at distributor1, hoist5 is not at distributor3, hoist5 is not elevating crate2, hoist5 is not lifting crate0, hoist5 is not lifting crate1, hoist5 is not lifting crate3, hoist6 cannot be found located at depot1, hoist6 is at distributor3, hoist6 is available, hoist6 is not at depot2, hoist6 is not at distributor0, hoist6 is not elevating crate0, hoist6 is not elevating crate3, hoist6 is not lifting crate2, hoist6 is not located at depot0, hoist6 is not located at distributor1, hoist6 is not raising crate1, pallet0 cannot be found located at depot2, pallet0 cannot be found located at distributor2, pallet0 does not have crate0 on it, pallet0 does not have crate1 on it, pallet0 does not have crate3 on it, pallet0 is clear, pallet0 is not at depot1, pallet0 is not at distributor3, pallet1 can be found located at depot1, pallet1 cannot be found located at depot0, pallet1 is not at depot2, pallet1 is not at distributor2, pallet1 is not at distributor3, pallet1 is not clear of any crates, pallet1 is not located at distributor1, pallet2 can be found located at depot2, pallet2 cannot be found located at distributor2, pallet2 does not have crate1 on it, pallet2 does not have crate2 on it, pallet2 does not have crate3 on it, pallet2 is clear of any crates, pallet2 is not at depot1, pallet2 is not located at distributor1, pallet3 cannot be found located at depot1, pallet3 does not have crate1 on it, pallet3 is clear, pallet3 is located at distributor0, pallet3 is not located at depot0, pallet3 is not located at depot2, pallet4 cannot be found located at distributor3, pallet4 does not have crate0 on it, pallet4 does not have crate1 on it, pallet4 does not have crate2 on it, pallet4 is clear, pallet4 is not at depot1, pallet4 is not at depot2, pallet4 is not located at depot0, pallet4 is not located at distributor0, pallet5 cannot be found located at depot2, pallet5 cannot be found located at distributor0, pallet5 is located at distributor2, pallet5 is not clear, pallet5 is not located at depot1, pallet5 is not located at distributor1, pallet6 cannot be found located at depot0, pallet6 cannot be found located at depot1, pallet6 does not have crate0 on it, pallet6 does not have crate2 on it, pallet6 is located at distributor3, pallet6 is not at distributor1, pallet6 is not at distributor2, pallet6 is not clear of any crates, pallet6 is not located at distributor0, truck0 can be found located at distributor2, truck0 cannot be found located at depot1, truck0 cannot be found located at depot2, truck0 cannot be found located at distributor0, truck0 cannot be found located at distributor3, truck0 is not at distributor1, truck1 cannot be found located at distributor3, truck1 does not contain crate2, truck1 does not contain crate3, truck1 is located at depot1, truck1 is not at distributor0, truck1 is not located at distributor1, truck2 cannot be found located at depot1, truck2 cannot be found located at depot2, truck2 cannot be found located at distributor0, truck2 cannot be found located at distributor1, truck2 does not contain crate0, truck2 does not contain crate2, truck2 is at distributor3 and truck2 is not at distributor2", "plan_length": 10, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1; truck1 then proceeds from depot0 to depot1, where hoist1 unloads crate2 from truck1 and places it on pallet1; meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which then drives to distributor3, where hoist6 unloads crate3 from truck2, resulting in the current state. In this state, if hoist6 at distributor3 places crate3 on pallet6, what are all the valid properties of the state, including both affirmative and negated properties? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is positioned at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and ready for use, hoist4 is available for work and located at distributor1, hoist5 is available and situated at distributor2, hoist6 is available and located at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any obstructions and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "b20c1a51-4dd7-4e15-9076-92f01939deb3", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, hoist2 lifts crate3 from crate2 at depot2, at depot2, hoist2 loads crate3 into truck2, crate2 is lifted from crate1 at depot2 by hoist2, crate2 is loaded by hoist2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3, hoist3 loads crate0 into truck2 at distributor0, crate1 is unloaded by hoist3 from truck2 at distributor0, truck2 is driven to distributor1 from distributor0, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1, at distributor2, hoist5 unloads crate3 from truck2, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, hoist5 drops crate3 on pallet5 at distributor2 and hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, if at distributor2, hoist5 drops crate0 on crate3, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "crate0 cannot be found located at depot1, crate0 cannot be found located at depot2, crate0 cannot be found located at distributor0, crate0 is at distributor2, crate0 is clear of any crates, crate0 is not at depot0, crate0 is not at distributor1, crate0 is not in truck1, crate0 is not inside truck0, crate0 is not inside truck2, crate0 is not on crate1, crate0 is not on pallet2, crate0 is not on pallet3, crate0 is not on pallet5, crate0 is not on top of crate0, crate0 is not on top of crate2, crate0 is not on top of pallet1, crate0 is on top of crate3, crate1 cannot be found located at depot0, crate1 cannot be found located at distributor1, crate1 does not have crate3 on it, crate1 is clear, crate1 is not at depot2, crate1 is not at distributor2, crate1 is not in truck0, crate1 is not inside truck1, crate1 is not located at depot1, crate1 is not on crate0, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet5, crate1 is not on top of crate1, crate1 is not on top of pallet0, crate1 is not on top of pallet4, crate1 is on top of pallet3, crate2 cannot be found located at depot2, crate2 does not have crate1 on it, crate2 is clear, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not located at depot0, crate2 is not located at distributor2, crate2 is not on crate1, crate2 is not on crate2, crate2 is not on pallet0, crate2 is not on top of crate0, crate2 is not on top of crate3, crate2 is not on top of pallet1, crate2 is not on top of pallet2, crate2 is not on top of pallet5, crate2 is on top of pallet4, crate3 can be found located at distributor2, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor1, crate3 does not have crate1 on it, crate3 is not at depot0, crate3 is not clear, crate3 is not in truck0, crate3 is not in truck1, crate3 is not inside truck2, crate3 is not on crate2, crate3 is not on top of crate0, crate3 is not on top of crate3, crate3 is not on top of pallet3, crate3 is not on top of pallet4, crate3 is on top of pallet5, depot0 is where hoist0 is located, depot0 is where pallet1 is not located, depot0 is where pallet4 is not located, depot1 is where crate2 is not located, depot1 is where hoist3 is not located, depot1 is where pallet5 is not located, depot2 is where crate3 is not located, depot2 is where hoist1 is not located, depot2 is where hoist5 is not located, depot2 is where truck1 is not located, depot2 is where truck2 is not located, distributor0 is where crate1 is located, distributor0 is where crate2 is not located, distributor0 is where crate3 is not located, distributor0 is where hoist5 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet5 is not located, distributor0 is where truck1 is not located, distributor0 is where truck2 is not located, distributor1 is where crate2 is located, distributor1 is where pallet3 is not located, distributor2 is where hoist2 is not located, distributor2 is where hoist4 is not located, distributor2 is where hoist5 is located, distributor2 is where pallet2 is not located, distributor2 is where truck0 is not located, distributor2 is where truck1 is not located, hoist0 cannot be found located at distributor0, hoist0 is accessible, hoist0 is not at depot1, hoist0 is not at depot2, hoist0 is not at distributor1, hoist0 is not elevating crate1, hoist0 is not lifting crate0, hoist0 is not located at distributor2, hoist0 is not raising crate2, hoist0 is not raising crate3, hoist1 can be found located at depot1, hoist1 cannot be found located at distributor1, hoist1 is available for work, hoist1 is not elevating crate2, hoist1 is not located at depot0, hoist1 is not located at distributor0, hoist1 is not located at distributor2, hoist1 is not raising crate0, hoist1 is not raising crate1, hoist1 is not raising crate3, hoist2 can be found located at depot2, hoist2 cannot be found located at depot1, hoist2 cannot be found located at distributor0, hoist2 is accessible, hoist2 is not elevating crate0, hoist2 is not lifting crate1, hoist2 is not located at depot0, hoist2 is not located at distributor1, hoist2 is not raising crate2, hoist2 is not raising crate3, hoist3 cannot be found located at depot0, hoist3 cannot be found located at distributor2, hoist3 is accessible, hoist3 is at distributor0, hoist3 is not at distributor1, hoist3 is not elevating crate0, hoist3 is not elevating crate3, hoist3 is not lifting crate1, hoist3 is not located at depot2, hoist3 is not raising crate2, hoist4 can be found located at distributor1, hoist4 cannot be found located at depot1, hoist4 cannot be found located at distributor0, hoist4 is available, hoist4 is not at depot2, hoist4 is not elevating crate1, hoist4 is not lifting crate3, hoist4 is not located at depot0, hoist4 is not raising crate0, hoist4 is not raising crate2, hoist5 cannot be found located at depot0, hoist5 cannot be found located at depot1, hoist5 is available, hoist5 is not at distributor1, hoist5 is not lifting crate1, hoist5 is not lifting crate2, hoist5 is not raising crate0, hoist5 is not raising crate3, pallet0 cannot be found located at depot2, pallet0 does not have crate0 on it, pallet0 does not have crate3 on it, pallet0 is clear of any crates, pallet0 is located at depot0, pallet0 is not at depot1, pallet0 is not located at distributor1, pallet0 is not located at distributor2, pallet1 does not have crate3 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet1 is not located at depot2, pallet1 is not located at distributor0, pallet1 is not located at distributor1, pallet1 is not located at distributor2, pallet2 can be found located at depot2, pallet2 cannot be found located at depot0, pallet2 does not have crate3 on it, pallet2 is clear of any crates, pallet2 is not at depot1, pallet2 is not at distributor1, pallet2 is not located at distributor0, pallet3 cannot be found located at depot1, pallet3 does not have crate2 on it, pallet3 is at distributor0, pallet3 is not at depot0, pallet3 is not at distributor2, pallet3 is not clear, pallet3 is not located at depot2, pallet4 cannot be found located at depot1, pallet4 does not have crate0 on it, pallet4 is at distributor1, pallet4 is not at depot2, pallet4 is not at distributor2, pallet4 is not clear, pallet4 is not located at distributor0, pallet5 cannot be found located at depot0, pallet5 is at distributor2, pallet5 is not clear of any crates, pallet5 is not located at depot2, pallet5 is not located at distributor1, truck0 cannot be found located at depot0, truck0 cannot be found located at depot1, truck0 cannot be found located at depot2, truck0 is at distributor0, truck0 is not at distributor1, truck1 cannot be found located at depot0, truck1 is located at depot1, truck1 is not at distributor1, truck2 can be found located at distributor2, truck2 cannot be found located at distributor1, truck2 does not contain crate1, truck2 does not contain crate2, truck2 is not at depot0 and truck2 is not located at depot1", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot0 to depot2, then hoist2 lifts crate3 from crate2 at depot2, followed by hoist2 loading crate3 into truck2 at depot2. Next, hoist2 lifts crate2 from crate1 at depot2 and loads it into truck2. At depot2, hoist2 also lifts crate1 off pallet2 and loads it into truck2. Subsequently, truck2 is driven from depot2 to distributor0. At distributor0, hoist3 lifts crate0 off pallet3 and loads it into truck2, while unloading crate1 from truck2. Then, truck2 is driven from distributor0 to distributor1, where hoist4 unloads crate2 from truck2. Truck2 is then driven from distributor1 to distributor2, where hoist5 unloads crate3 from truck2. Finally, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, and hoist5 drops crate3 on pallet5 at distributor2, and hoist5 unloads crate0 from truck2 at distributor2, resulting in the current state. In this state, if at distributor2, hoist5 drops crate0 on crate3, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state None.", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the location of hoist3. Hoist0 is available and situated at depot0. Hoist1 is at depot1 and available, hoist2 is at depot2 and available for work, and hoist3 is also available for work. Hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and empty, and pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "b6cc3e5f-be2d-49a8-9ed3-96ae0a45216f", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck0, from depot2, truck0 is driven to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate0 is lifted from pallet4 at distributor1 by hoist4 and at distributor1, hoist4 loads crate0 into truck2 to reach the current state. In this state, if crate3 is lifted from pallet5 at distributor2 by hoist5, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "crate0 is in truck2, crate0 is not at depot1, crate0 is not clear, crate0 is not inside truck0, crate0 is not located at depot0, crate0 is not located at distributor1, crate0 is not located at distributor2, crate0 is not on pallet0, crate0 is not on pallet2, crate0 is not on top of crate0, crate0 is not on top of crate1, crate0 is not on top of crate3, crate0 is not on top of pallet1, crate0 is not on top of pallet5, crate1 cannot be found located at depot0, crate1 cannot be found located at distributor1, crate1 does not have crate1 on it, crate1 does not have crate2 on it, crate1 is clear of any crates, crate1 is not at distributor2, crate1 is not in truck0, crate1 is not in truck2, crate1 is not inside truck1, crate1 is not located at depot1, crate1 is not on crate3, crate1 is not on top of crate0, crate1 is not on top of crate2, crate1 is not on top of pallet0, crate1 is not on top of pallet2, crate1 is not on top of pallet4, crate1 is not on top of pallet5, crate1 is on pallet3, crate2 does not have crate0 on it, crate2 does not have crate3 on it, crate2 is in truck2, crate2 is not at depot0, crate2 is not at depot1, crate2 is not at distributor1, crate2 is not clear of any crates, crate2 is not inside truck0, crate2 is not on pallet0, crate2 is not on top of crate0, crate2 is not on top of crate2, crate2 is not on top of crate3, crate2 is not on top of pallet2, crate2 is not on top of pallet3, crate2 is not on top of pallet5, crate3 cannot be found located at distributor1, crate3 is not at distributor2, crate3 is not clear of any crates, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not inside truck2, crate3 is not located at depot1, crate3 is not located at distributor0, crate3 is not on pallet5, crate3 is not on top of crate0, crate3 is not on top of crate1, crate3 is not on top of crate3, crate3 is not on top of pallet1, crate3 is not on top of pallet2, crate3 is not on top of pallet3, crate3 is not on top of pallet4, depot0 is where crate3 is not located, depot0 is where pallet1 is not located, depot0 is where pallet3 is not located, depot0 is where pallet4 is not located, depot0 is where pallet5 is not located, depot0 is where truck0 is not located, depot0 is where truck2 is not located, depot1 is where hoist0 is not located, depot1 is where pallet0 is not located, depot1 is where pallet4 is not located, depot1 is where truck1 is not located, depot2 is where crate0 is not located, depot2 is where crate1 is not located, depot2 is where crate2 is not located, depot2 is where crate3 is not located, depot2 is where hoist3 is not located, depot2 is where hoist4 is not located, depot2 is where pallet1 is not located, depot2 is where truck0 is not located, distributor0 is where crate0 is not located, distributor0 is where crate1 is located, distributor0 is where crate2 is not located, distributor0 is where hoist0 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist3 is located, distributor0 is where truck1 is not located, distributor0 is where truck2 is not located, distributor1 is where hoist4 is located, distributor1 is where hoist5 is not located, distributor2 is where crate2 is not located, distributor2 is where hoist1 is not located, distributor2 is where hoist3 is not located, distributor2 is where hoist4 is not located, distributor2 is where pallet0 is not located, distributor2 is where pallet2 is not located, distributor2 is where pallet3 is not located, distributor2 is where pallet4 is not located, hoist0 can be found located at depot0, hoist0 cannot be found located at distributor2, hoist0 is accessible, hoist0 is not at depot2, hoist0 is not at distributor1, hoist0 is not elevating crate0, hoist0 is not elevating crate2, hoist0 is not elevating crate3, hoist0 is not raising crate1, hoist1 can be found located at depot1, hoist1 is accessible, hoist1 is not at depot2, hoist1 is not at distributor1, hoist1 is not elevating crate0, hoist1 is not elevating crate1, hoist1 is not lifting crate2, hoist1 is not lifting crate3, hoist1 is not located at depot0, hoist2 can be found located at depot2, hoist2 cannot be found located at depot1, hoist2 is available for work, hoist2 is not at distributor1, hoist2 is not elevating crate3, hoist2 is not lifting crate1, hoist2 is not lifting crate2, hoist2 is not located at depot0, hoist2 is not located at distributor2, hoist2 is not raising crate0, hoist3 cannot be found located at depot1, hoist3 is available for work, hoist3 is not at distributor1, hoist3 is not elevating crate2, hoist3 is not lifting crate0, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist3 is not located at depot0, hoist4 cannot be found located at depot0, hoist4 cannot be found located at distributor0, hoist4 is available for work, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not lifting crate1, hoist4 is not located at depot1, hoist4 is not raising crate3, hoist5 can be found located at distributor2, hoist5 cannot be found located at depot1, hoist5 cannot be found located at depot2, hoist5 is not accessible, hoist5 is not at distributor0, hoist5 is not elevating crate2, hoist5 is not located at depot0, hoist5 is not raising crate0, hoist5 is not raising crate1, hoist5 is raising crate3, pallet0 cannot be found located at distributor1, pallet0 does not have crate3 on it, pallet0 is clear, pallet0 is located at depot0, pallet0 is not at depot2, pallet0 is not located at distributor0, pallet1 cannot be found located at distributor1, pallet1 does not have crate1 on it, pallet1 does not have crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet1 is not located at distributor0, pallet1 is not located at distributor2, pallet2 cannot be found located at depot1, pallet2 cannot be found located at distributor0, pallet2 cannot be found located at distributor1, pallet2 is clear of any crates, pallet2 is located at depot2, pallet2 is not at depot0, pallet3 cannot be found located at depot2, pallet3 does not have crate0 on it, pallet3 is located at distributor0, pallet3 is not at depot1, pallet3 is not clear, pallet3 is not located at distributor1, pallet4 can be found located at distributor1, pallet4 cannot be found located at depot2, pallet4 does not have crate0 on it, pallet4 does not have crate2 on it, pallet4 is clear, pallet4 is not at distributor0, pallet5 is clear of any crates, pallet5 is located at distributor2, pallet5 is not at depot1, pallet5 is not at distributor0, pallet5 is not located at depot2, pallet5 is not located at distributor1, truck0 is located at distributor0, truck0 is not at depot1, truck0 is not at distributor1, truck0 is not at distributor2, truck1 cannot be found located at depot0, truck1 cannot be found located at depot2, truck1 cannot be found located at distributor1, truck1 does not contain crate0, truck1 does not contain crate2, truck1 is at distributor2, truck2 is located at distributor1, truck2 is not at depot1, truck2 is not at depot2 and truck2 is not at distributor2", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, which is then driven from depot0 to distributor1. At depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. Meanwhile, at distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, resulting in the current state. If, in this state, hoist5 lifts crate3 from pallet5 at distributor2, what are all the valid properties of the resulting state, including both affirmative and negative properties? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 is empty, crate3 is placed on pallet5, depot0 is the location of crate2, depot1 is the location of hoist1, depot2 houses pallet2 and truck0, depot2 is also the location of pallet2, distributor0 is home to hoist3 and pallet3, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible and situated at depot0, hoist1 is available for work and located at depot1, hoist2 is available and located at depot2, hoist3 is accessible and located at distributor0, hoist4 is available and situated at distributor1, hoist5 is available for work and located at distributor2, pallet0 is located at depot0, pallet1 is empty and situated at depot1, pallet3 is clear of any crates, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "ace660a2-00d4-4050-8e79-4bc86bbfa1e5", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, hoist1 unloads crate2 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, at depot2, hoist2 lifts crate3 off pallet2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3, hoist6 unloads crate3 from truck2 at distributor3, at distributor3, hoist6 drops crate3 on pallet6, hoist5 lifts crate1 from crate0 at distributor2, hoist5 loads crate1 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, at distributor2, hoist5 loads crate0 into truck0, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, at distributor0, hoist3 unloads crate0 from truck0 and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, if crate1 is dropped on pallet5 at distributor2 by hoist5, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "crate0 cannot be found located at depot1, crate0 cannot be found located at distributor1, crate0 does not have crate1 on it, crate0 is clear, crate0 is located at distributor0, crate0 is not at depot0, crate0 is not in truck0, crate0 is not in truck1, crate0 is not located at distributor3, crate0 is not on crate0, crate0 is not on crate3, crate0 is not on top of crate2, crate0 is not on top of pallet0, crate0 is not on top of pallet1, crate0 is not on top of pallet2, crate0 is not on top of pallet6, crate0 is on pallet3, crate1 cannot be found located at distributor0, crate1 cannot be found located at distributor3, crate1 does not have crate0 on it, crate1 is clear, crate1 is not at depot1, crate1 is not at distributor1, crate1 is not in truck1, crate1 is not in truck2, crate1 is not located at depot0, crate1 is not located at depot2, crate1 is not on top of crate1, crate1 is not on top of crate2, crate1 is not on top of pallet1, crate1 is not on top of pallet2, crate1 is not on top of pallet3, crate1 is on pallet5, crate2 can be found located at depot1, crate2 cannot be found located at depot2, crate2 cannot be found located at distributor2, crate2 does not have crate3 on it, crate2 is clear, crate2 is not in truck1, crate2 is not inside truck0, crate2 is not located at depot0, crate2 is not on crate3, crate2 is not on pallet3, crate2 is not on pallet4, crate2 is not on pallet6, crate2 is not on top of crate0, crate2 is not on top of crate1, crate2 is not on top of crate2, crate2 is not on top of pallet5, crate2 is on top of pallet1, crate3 cannot be found located at depot0, crate3 does not have crate1 on it, crate3 does not have crate3 on it, crate3 is clear, crate3 is located at distributor3, crate3 is not at depot1, crate3 is not at distributor2, crate3 is not in truck0, crate3 is not in truck1, crate3 is not inside truck2, crate3 is not located at distributor0, crate3 is not on crate0, crate3 is not on pallet0, crate3 is not on pallet1, crate3 is not on pallet3, crate3 is not on pallet5, crate3 is not on top of crate1, crate3 is not on top of pallet2, crate3 is not on top of pallet4, depot0 is where hoist4 is not located, depot0 is where hoist5 is not located, depot0 is where pallet4 is not located, depot1 is where hoist1 is located, depot1 is where hoist6 is not located, depot1 is where pallet1 is located, depot1 is where pallet4 is not located, depot2 is where crate0 is not located, depot2 is where crate3 is not located, depot2 is where hoist2 is located, depot2 is where hoist3 is not located, depot2 is where hoist4 is not located, depot2 is where pallet2 is located, depot2 is where pallet3 is not located, distributor0 is where crate2 is not located, distributor0 is where pallet3 is located, distributor0 is where truck0 is located, distributor1 is where crate2 is not located, distributor1 is where crate3 is not located, distributor1 is where pallet3 is not located, distributor1 is where pallet5 is not located, distributor1 is where truck1 is not located, distributor2 is where crate0 is not located, distributor2 is where crate1 is located, distributor2 is where hoist0 is not located, distributor2 is where hoist5 is located, distributor3 is where crate2 is not located, distributor3 is where hoist0 is not located, distributor3 is where hoist1 is not located, distributor3 is where hoist2 is not located, distributor3 is where pallet2 is not located, distributor3 is where pallet5 is not located, distributor3 is where truck0 is not located, distributor3 is where truck1 is not located, distributor3 is where truck2 is located, hoist0 cannot be found located at depot1, hoist0 cannot be found located at distributor1, hoist0 is at depot0, hoist0 is available for work, hoist0 is not elevating crate0, hoist0 is not elevating crate2, hoist0 is not located at depot2, hoist0 is not located at distributor0, hoist0 is not raising crate1, hoist0 is not raising crate3, hoist1 cannot be found located at depot0, hoist1 cannot be found located at distributor0, hoist1 cannot be found located at distributor1, hoist1 is available, hoist1 is not elevating crate3, hoist1 is not lifting crate0, hoist1 is not lifting crate2, hoist1 is not located at depot2, hoist1 is not located at distributor2, hoist1 is not raising crate1, hoist2 cannot be found located at depot0, hoist2 cannot be found located at distributor1, hoist2 is available for work, hoist2 is not at distributor0, hoist2 is not at distributor2, hoist2 is not elevating crate3, hoist2 is not lifting crate1, hoist2 is not lifting crate2, hoist2 is not located at depot1, hoist2 is not raising crate0, hoist3 cannot be found located at depot0, hoist3 cannot be found located at distributor3, hoist3 is at distributor0, hoist3 is available for work, hoist3 is not at depot1, hoist3 is not elevating crate0, hoist3 is not elevating crate1, hoist3 is not lifting crate2, hoist3 is not located at distributor1, hoist3 is not located at distributor2, hoist3 is not raising crate3, hoist4 can be found located at distributor1, hoist4 cannot be found located at distributor0, hoist4 is available, hoist4 is not at distributor2, hoist4 is not at distributor3, hoist4 is not elevating crate1, hoist4 is not elevating crate3, hoist4 is not lifting crate2, hoist4 is not located at depot1, hoist4 is not raising crate0, hoist5 cannot be found located at depot2, hoist5 is available for work, hoist5 is not at distributor0, hoist5 is not at distributor1, hoist5 is not elevating crate2, hoist5 is not lifting crate0, hoist5 is not located at depot1, hoist5 is not located at distributor3, hoist5 is not raising crate1, hoist5 is not raising crate3, hoist6 is accessible, hoist6 is located at distributor3, hoist6 is not at depot0, hoist6 is not elevating crate1, hoist6 is not lifting crate2, hoist6 is not located at depot2, hoist6 is not located at distributor0, hoist6 is not located at distributor1, hoist6 is not located at distributor2, hoist6 is not raising crate0, hoist6 is not raising crate3, pallet0 can be found located at depot0, pallet0 cannot be found located at depot1, pallet0 cannot be found located at distributor1, pallet0 does not have crate1 on it, pallet0 does not have crate2 on it, pallet0 is clear, pallet0 is not at depot2, pallet0 is not at distributor0, pallet0 is not at distributor3, pallet0 is not located at distributor2, pallet1 cannot be found located at depot2, pallet1 cannot be found located at distributor0, pallet1 cannot be found located at distributor3, pallet1 is not at depot0, pallet1 is not at distributor1, pallet1 is not at distributor2, pallet1 is not clear of any crates, pallet2 does not have crate2 on it, pallet2 is clear, pallet2 is not at depot0, pallet2 is not at distributor0, pallet2 is not at distributor1, pallet2 is not located at depot1, pallet2 is not located at distributor2, pallet3 cannot be found located at distributor3, pallet3 is not at depot0, pallet3 is not at depot1, pallet3 is not at distributor2, pallet3 is not clear, pallet4 cannot be found located at depot2, pallet4 does not have crate0 on it, pallet4 does not have crate1 on it, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet4 is not at distributor0, pallet4 is not at distributor2, pallet4 is not at distributor3, pallet5 can be found located at distributor2, pallet5 cannot be found located at depot0, pallet5 does not have crate0 on it, pallet5 is not at depot1, pallet5 is not at depot2, pallet5 is not clear of any crates, pallet5 is not located at distributor0, pallet6 cannot be found located at depot0, pallet6 cannot be found located at distributor0, pallet6 does not have crate1 on it, pallet6 has crate3 on it, pallet6 is located at distributor3, pallet6 is not at depot1, pallet6 is not at distributor1, pallet6 is not clear of any crates, pallet6 is not located at depot2, pallet6 is not located at distributor2, truck0 does not contain crate1, truck0 is not at depot0, truck0 is not at depot1, truck0 is not located at depot2, truck0 is not located at distributor1, truck0 is not located at distributor2, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor0, truck1 cannot be found located at distributor2, truck1 is located at depot1, truck1 is not located at depot2, truck2 cannot be found located at depot0, truck2 does not contain crate0, truck2 does not contain crate2, truck2 is not at depot1, truck2 is not at distributor1, truck2 is not located at depot2, truck2 is not located at distributor0 and truck2 is not located at distributor2", "plan_length": 19, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1; truck1 then proceeds to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, loads it into truck2, and truck2 is driven to distributor3; upon arrival, hoist6 unloads crate3 and drops it on pallet6. At distributor2, hoist5 lifts crate1 from crate0, loads it into truck0, and also lifts crate0 from pallet5, loading it into truck0 as well; however, crate1 is then unloaded from truck0 by hoist5. Subsequently, truck0 travels to distributor0, where hoist3 unloads crate0 and drops it on pallet3, resulting in the current state. In this state, if hoist5 drops crate1 on pallet5 at distributor2, what are all the valid properties of the state (including both affirmative and negated properties)? If none exist, indicate 'None'.", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is positioned at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any obstructions and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "9a86a834-8cf4-481e-bc22-edf48efa0ac6", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, if at depot0, hoist0 loads crate2 into truck2, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "crate0 cannot be found located at depot0, crate0 cannot be found located at distributor0, crate0 is not at depot1, crate0 is not at distributor2, crate0 is not inside truck0, crate0 is not inside truck2, crate0 is not on crate0, crate0 is not on crate2, crate0 is not on pallet0, crate0 is not on pallet2, crate0 is not on top of crate1, crate0 is not on top of crate3, crate0 is not on top of pallet3, crate0 is not on top of pallet5, crate1 cannot be found located at distributor0, crate1 cannot be found located at distributor2, crate1 is not at depot1, crate1 is not in truck1, crate1 is not in truck2, crate1 is not on crate0, crate1 is not on crate1, crate1 is not on pallet4, crate1 is not on pallet5, crate1 is not on top of crate3, crate1 is not on top of pallet1, crate1 is not on top of pallet3, crate2 cannot be found located at depot1, crate2 does not have crate1 on it, crate2 does not have crate3 on it, crate2 is not at depot2, crate2 is not clear, crate2 is not inside truck1, crate2 is not located at depot0, crate2 is not located at distributor0, crate2 is not located at distributor2, crate2 is not on crate0, crate2 is not on crate1, crate2 is not on crate2, crate2 is not on pallet1, crate2 is not on pallet2, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet3, crate2 is not on top of pallet4, crate3 cannot be found located at distributor1, crate3 is not at depot2, crate3 is not in truck0, crate3 is not inside truck2, crate3 is not located at depot0, crate3 is not located at depot1, crate3 is not on crate1, crate3 is not on crate3, crate3 is not on pallet1, crate3 is not on pallet3, crate3 is not on pallet4, crate3 is not on top of crate0, depot0 is where crate1 is not located, depot0 is where hoist1 is not located, depot0 is where pallet1 is not located, depot1 is where hoist5 is not located, depot1 is where truck1 is not located, depot2 is where crate0 is not located, depot2 is where hoist0 is not located, depot2 is where hoist1 is not located, depot2 is where hoist4 is not located, depot2 is where hoist5 is not located, depot2 is where pallet1 is not located, distributor0 is where crate3 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist5 is not located, distributor0 is where pallet1 is not located, distributor0 is where pallet5 is not located, distributor1 is where crate1 is not located, distributor1 is where crate2 is not located, distributor1 is where hoist3 is not located, distributor1 is where hoist5 is not located, distributor1 is where pallet1 is not located, distributor1 is where pallet2 is not located, distributor2 is where hoist0 is not located, distributor2 is where hoist1 is not located, distributor2 is where truck0 is not located, hoist0 cannot be found located at depot1, hoist0 is not at distributor0, hoist0 is not at distributor1, hoist0 is not elevating crate3, hoist0 is not lifting crate1, hoist0 is not lifting crate2, hoist0 is not raising crate0, hoist1 is not at distributor0, hoist1 is not at distributor1, hoist1 is not lifting crate2, hoist1 is not lifting crate3, hoist1 is not raising crate0, hoist1 is not raising crate1, hoist2 cannot be found located at depot0, hoist2 is not at distributor2, hoist2 is not elevating crate1, hoist2 is not elevating crate2, hoist2 is not elevating crate3, hoist2 is not located at depot1, hoist2 is not located at distributor1, hoist2 is not raising crate0, hoist3 cannot be found located at depot0, hoist3 cannot be found located at depot1, hoist3 cannot be found located at distributor2, hoist3 is not at depot2, hoist3 is not elevating crate1, hoist3 is not lifting crate0, hoist3 is not raising crate2, hoist3 is not raising crate3, hoist4 cannot be found located at distributor2, hoist4 is not at distributor0, hoist4 is not elevating crate3, hoist4 is not lifting crate1, hoist4 is not located at depot0, hoist4 is not located at depot1, hoist4 is not raising crate0, hoist4 is not raising crate2, hoist5 is not elevating crate1, hoist5 is not lifting crate0, hoist5 is not located at depot0, hoist5 is not raising crate2, hoist5 is not raising crate3, pallet0 cannot be found located at distributor0, pallet0 does not have crate1 on it, pallet0 does not have crate3 on it, pallet0 is not located at depot1, pallet0 is not located at depot2, pallet0 is not located at distributor1, pallet0 is not located at distributor2, pallet1 does not have crate0 on it, pallet1 is not located at distributor2, pallet2 cannot be found located at depot0, pallet2 does not have crate3 on it, pallet2 is not at distributor0, pallet2 is not clear, pallet2 is not located at depot1, pallet2 is not located at distributor2, pallet3 cannot be found located at depot1, pallet3 cannot be found located at distributor2, pallet3 is not at depot2, pallet3 is not located at depot0, pallet3 is not located at distributor1, pallet4 is not at depot0, pallet4 is not at depot1, pallet4 is not at depot2, pallet4 is not at distributor0, pallet4 is not at distributor2, pallet4 is not clear, pallet5 cannot be found located at distributor1, pallet5 does not have crate2 on it, pallet5 is not at depot0, pallet5 is not clear of any crates, pallet5 is not located at depot1, pallet5 is not located at depot2, truck0 cannot be found located at depot0, truck0 cannot be found located at depot1, truck0 does not contain crate1, truck0 does not contain crate2, truck0 is not located at distributor0, truck0 is not located at distributor1, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor0, truck1 does not contain crate0, truck1 does not contain crate3, truck1 is not at depot2, truck1 is not at distributor1, truck2 cannot be found located at depot2, truck2 cannot be found located at distributor2, truck2 is not at depot1, truck2 is not at distributor0 and truck2 is not located at distributor1", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at depot0, hoist0 lifts crate2 off pallet0 to achieve the current state. In this state, if at depot0, hoist0 loads crate2 into truck2, what are all the valid properties of the state that involve negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is situated on pallet5, depot0 is the location of crate2, depot1 is the location of hoist1, depot2 houses pallet2, depot2 is also the location of truck0, distributor0 is home to hoist3, distributor0 is also the location of pallet3, distributor1 is where crate0 is situated, distributor2 is where crate3 is located, hoist0 is accessible and situated at depot0, hoist1 is available for use, hoist2 is available and located at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "94144f98-9d84-4c75-8ebb-a60382d9fe75", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist3 lifts crate1 from pallet3 at depot3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, from distributor1, truck2 is driven to distributor2 and hoist5 drops crate2 on pallet5 at distributor1 to reach the current state. In this state, if at distributor2, hoist6 lifts crate0 off pallet6, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "crate0 cannot be found located at depot1, crate0 is not at depot3, crate0 is not clear, crate0 is not in truck1, crate0 is not inside truck0, crate0 is not located at depot0, crate0 is not located at distributor0, crate0 is not located at distributor2, crate0 is not on crate0, crate0 is not on pallet1, crate0 is not on pallet2, crate0 is not on pallet4, crate0 is not on pallet5, crate0 is not on pallet6, crate0 is not on top of crate1, crate0 is not on top of crate2, crate0 is not on top of pallet3, crate1 cannot be found located at depot1, crate1 cannot be found located at depot3, crate1 does not have crate1 on it, crate1 is not at distributor1, crate1 is not clear of any crates, crate1 is not in truck1, crate1 is not inside truck2, crate1 is not located at depot0, crate1 is not located at depot2, crate1 is not located at distributor0, crate1 is not located at distributor2, crate1 is not on crate0, crate1 is not on crate3, crate1 is not on pallet0, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet5, crate1 is not on top of pallet3, crate1 is not on top of pallet4, crate1 is not on top of pallet6, crate2 does not have crate1 on it, crate2 is not at depot1, crate2 is not at depot3, crate2 is not in truck0, crate2 is not in truck2, crate2 is not inside truck1, crate2 is not located at distributor0, crate2 is not located at distributor2, crate2 is not on crate0, crate2 is not on crate2, crate2 is not on pallet0, crate2 is not on pallet1, crate2 is not on pallet3, crate2 is not on pallet4, crate2 is not on top of crate1, crate2 is not on top of crate3, crate2 is not on top of pallet2, crate3 does not have crate0 on it, crate3 does not have crate3 on it, crate3 is not at depot0, crate3 is not at depot2, crate3 is not at distributor1, crate3 is not at distributor2, crate3 is not clear, crate3 is not in truck0, crate3 is not inside truck1, crate3 is not located at depot3, crate3 is not located at distributor0, crate3 is not on pallet2, crate3 is not on pallet5, crate3 is not on pallet6, crate3 is not on top of crate0, crate3 is not on top of crate1, crate3 is not on top of crate2, crate3 is not on top of pallet1, crate3 is not on top of pallet3, depot0 is where crate2 is not located, depot0 is where hoist2 is not located, depot0 is where hoist4 is not located, depot0 is where hoist5 is not located, depot0 is where pallet1 is not located, depot0 is where pallet2 is not located, depot0 is where truck2 is not located, depot1 is where crate3 is not located, depot1 is where hoist2 is not located, depot1 is where hoist4 is not located, depot1 is where pallet0 is not located, depot1 is where pallet5 is not located, depot2 is where crate0 is not located, depot2 is where crate2 is not located, depot2 is where hoist0 is not located, depot2 is where hoist1 is not located, depot2 is where hoist5 is not located, depot2 is where hoist6 is not located, depot2 is where pallet1 is not located, depot3 is where pallet1 is not located, depot3 is where pallet2 is not located, depot3 is where pallet4 is not located, depot3 is where pallet6 is not located, depot3 is where truck0 is not located, distributor0 is where hoist3 is not located, distributor0 is where hoist5 is not located, distributor0 is where hoist6 is not located, distributor0 is where pallet5 is not located, distributor0 is where truck2 is not located, distributor1 is where crate0 is not located, distributor1 is where hoist0 is not located, distributor1 is where hoist4 is not located, distributor1 is where pallet0 is not located, distributor1 is where pallet1 is not located, distributor1 is where truck0 is not located, distributor2 is where hoist0 is not located, distributor2 is where hoist2 is not located, distributor2 is where hoist3 is not located, distributor2 is where pallet3 is not located, hoist0 cannot be found located at depot1, hoist0 cannot be found located at distributor0, hoist0 is not elevating crate3, hoist0 is not lifting crate0, hoist0 is not lifting crate1, hoist0 is not lifting crate2, hoist0 is not located at depot3, hoist1 cannot be found located at distributor0, hoist1 cannot be found located at distributor2, hoist1 is not at depot3, hoist1 is not at distributor1, hoist1 is not elevating crate3, hoist1 is not lifting crate0, hoist1 is not lifting crate2, hoist1 is not located at depot0, hoist1 is not raising crate1, hoist2 cannot be found located at depot3, hoist2 cannot be found located at distributor0, hoist2 is not at distributor1, hoist2 is not lifting crate0, hoist2 is not lifting crate2, hoist2 is not lifting crate3, hoist2 is not raising crate1, hoist3 cannot be found located at depot0, hoist3 cannot be found located at depot1, hoist3 cannot be found located at distributor1, hoist3 is not at depot2, hoist3 is not available for work, hoist3 is not elevating crate2, hoist3 is not lifting crate0, hoist3 is not raising crate3, hoist4 cannot be found located at depot2, hoist4 is not at depot3, hoist4 is not lifting crate0, hoist4 is not lifting crate2, hoist4 is not located at distributor2, hoist4 is not raising crate1, hoist4 is not raising crate3, hoist5 is not elevating crate0, hoist5 is not elevating crate3, hoist5 is not lifting crate1, hoist5 is not lifting crate2, hoist5 is not located at depot1, hoist5 is not located at depot3, hoist5 is not located at distributor2, hoist6 cannot be found located at depot0, hoist6 cannot be found located at depot1, hoist6 is not at distributor1, hoist6 is not available, hoist6 is not elevating crate1, hoist6 is not lifting crate2, hoist6 is not located at depot3, hoist6 is not raising crate3, pallet0 cannot be found located at distributor2, pallet0 does not have crate0 on it, pallet0 does not have crate3 on it, pallet0 is not at depot2, pallet0 is not located at depot3, pallet0 is not located at distributor0, pallet1 cannot be found located at distributor0, pallet1 cannot be found located at distributor2, pallet2 is not at distributor0, pallet2 is not at distributor1, pallet2 is not at distributor2, pallet2 is not located at depot1, pallet3 cannot be found located at depot1, pallet3 is not at depot0, pallet3 is not at depot2, pallet3 is not at distributor1, pallet3 is not located at distributor0, pallet4 cannot be found located at depot0, pallet4 cannot be found located at distributor2, pallet4 does not have crate3 on it, pallet4 is not at depot1, pallet4 is not at distributor1, pallet4 is not located at depot2, pallet5 cannot be found located at depot0, pallet5 is not at depot2, pallet5 is not clear of any crates, pallet5 is not located at depot3, pallet5 is not located at distributor2, pallet6 cannot be found located at depot1, pallet6 does not have crate2 on it, pallet6 is not at distributor0, pallet6 is not at distributor1, pallet6 is not located at depot0, pallet6 is not located at depot2, truck0 does not contain crate1, truck0 is not at depot2, truck0 is not located at depot0, truck0 is not located at distributor0, truck0 is not located at distributor2, truck1 is not at depot3, truck1 is not at distributor2, truck1 is not located at depot0, truck1 is not located at depot1, truck1 is not located at depot2, truck1 is not located at distributor1, truck2 cannot be found located at depot1, truck2 does not contain crate0, truck2 is not at depot2, truck2 is not located at depot3 and truck2 is not located at distributor1", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 retrieves crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck2 at depot0, truck2 proceeds from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, at distributor1, hoist5 lifts crate3 off pallet5 and loads it into truck2, at distributor1, hoist5 unloads crate2 from truck2, and finally, truck2 is driven from distributor1 to distributor2, while hoist5 places crate2 on pallet5 at distributor1, resulting in the current state. In this state, if at distributor2, hoist6 lifts crate0 off pallet6, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, pallet6 has crate0 on it and is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "5c3b32bb-5ebd-49e8-9413-3f7eef3f8745", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven to distributor3 from depot2, hoist6 unloads crate3 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, hoist5 lifts crate1 from crate0 at distributor2, hoist5 loads crate1 into truck0 at distributor2, at distributor2, hoist5 lifts crate0 off pallet5, crate0 is loaded by hoist5 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, truck0 is driven from distributor2 to distributor0, crate0 is unloaded by hoist3 from truck0 at distributor0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, if at distributor2, hoist5 drops crate1 on pallet5, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "crate0 cannot be found located at depot0, crate0 cannot be found located at distributor1, crate0 does not have crate2 on it, crate0 is not at depot1, crate0 is not in truck0, crate0 is not in truck1, crate0 is not located at depot2, crate0 is not located at distributor2, crate0 is not located at distributor3, crate0 is not on crate0, crate0 is not on crate1, crate0 is not on crate2, crate0 is not on pallet1, crate0 is not on pallet4, crate0 is not on top of crate3, crate0 is not on top of pallet5, crate1 cannot be found located at depot0, crate1 cannot be found located at distributor0, crate1 is not at distributor1, crate1 is not at distributor3, crate1 is not in truck1, crate1 is not in truck2, crate1 is not located at depot1, crate1 is not located at depot2, crate1 is not on crate0, crate1 is not on crate2, crate1 is not on pallet1, crate1 is not on pallet4, crate1 is not on pallet6, crate1 is not on top of crate1, crate1 is not on top of pallet2, crate2 is not at distributor0, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not located at depot2, crate2 is not located at distributor2, crate2 is not located at distributor3, crate2 is not on crate1, crate2 is not on crate2, crate2 is not on pallet0, crate2 is not on pallet3, crate2 is not on pallet4, crate2 is not on pallet5, crate2 is not on pallet6, crate3 cannot be found located at depot1, crate3 does not have crate1 on it, crate3 does not have crate2 on it, crate3 is not at depot0, crate3 is not at depot2, crate3 is not at distributor2, crate3 is not located at distributor1, crate3 is not on crate2, crate3 is not on crate3, crate3 is not on top of crate0, crate3 is not on top of crate1, crate3 is not on top of pallet1, crate3 is not on top of pallet2, crate3 is not on top of pallet4, crate3 is not on top of pallet5, depot0 is where crate2 is not located, depot0 is where hoist4 is not located, depot0 is where pallet1 is not located, depot0 is where pallet2 is not located, depot0 is where truck2 is not located, depot1 is where hoist4 is not located, depot1 is where pallet2 is not located, depot1 is where truck2 is not located, depot2 is where hoist0 is not located, depot2 is where truck0 is not located, distributor0 is where crate3 is not located, distributor0 is where hoist0 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet1 is not located, distributor1 is where crate2 is not located, distributor1 is where hoist1 is not located, distributor1 is where hoist3 is not located, distributor1 is where pallet2 is not located, distributor1 is where pallet5 is not located, distributor2 is where hoist1 is not located, distributor2 is where hoist4 is not located, distributor2 is where pallet0 is not located, distributor2 is where truck1 is not located, distributor2 is where truck2 is not located, distributor3 is where hoist2 is not located, distributor3 is where pallet4 is not located, hoist0 is not at depot1, hoist0 is not at distributor1, hoist0 is not lifting crate0, hoist0 is not lifting crate1, hoist0 is not lifting crate2, hoist0 is not lifting crate3, hoist0 is not located at distributor2, hoist0 is not located at distributor3, hoist1 cannot be found located at distributor0, hoist1 is not at distributor3, hoist1 is not elevating crate0, hoist1 is not elevating crate1, hoist1 is not located at depot0, hoist1 is not located at depot2, hoist1 is not raising crate2, hoist1 is not raising crate3, hoist2 cannot be found located at depot0, hoist2 cannot be found located at distributor1, hoist2 cannot be found located at distributor2, hoist2 is not elevating crate1, hoist2 is not lifting crate3, hoist2 is not located at depot1, hoist2 is not located at distributor0, hoist2 is not raising crate0, hoist2 is not raising crate2, hoist3 cannot be found located at distributor2, hoist3 cannot be found located at distributor3, hoist3 is not elevating crate1, hoist3 is not elevating crate3, hoist3 is not lifting crate0, hoist3 is not lifting crate2, hoist3 is not located at depot0, hoist3 is not located at depot1, hoist3 is not located at depot2, hoist4 cannot be found located at depot2, hoist4 cannot be found located at distributor3, hoist4 is not elevating crate0, hoist4 is not elevating crate1, hoist4 is not elevating crate2, hoist4 is not lifting crate3, hoist4 is not located at distributor0, hoist5 cannot be found located at depot0, hoist5 cannot be found located at depot1, hoist5 cannot be found located at distributor1, hoist5 cannot be found located at distributor3, hoist5 is not at depot2, hoist5 is not elevating crate0, hoist5 is not lifting crate3, hoist5 is not located at distributor0, hoist5 is not raising crate1, hoist5 is not raising crate2, hoist6 cannot be found located at depot2, hoist6 cannot be found located at distributor0, hoist6 is not at depot0, hoist6 is not at depot1, hoist6 is not at distributor1, hoist6 is not at distributor2, hoist6 is not elevating crate1, hoist6 is not elevating crate2, hoist6 is not lifting crate3, hoist6 is not raising crate0, pallet0 cannot be found located at depot1, pallet0 cannot be found located at distributor1, pallet0 does not have crate0 on it, pallet0 does not have crate1 on it, pallet0 does not have crate3 on it, pallet0 is not at distributor3, pallet0 is not located at depot2, pallet1 cannot be found located at distributor1, pallet1 is not clear of any crates, pallet1 is not located at depot2, pallet1 is not located at distributor2, pallet1 is not located at distributor3, pallet2 does not have crate0 on it, pallet2 does not have crate2 on it, pallet2 is not at distributor2, pallet2 is not at distributor3, pallet2 is not located at distributor0, pallet3 cannot be found located at distributor1, pallet3 does not have crate1 on it, pallet3 does not have crate3 on it, pallet3 is not at depot1, pallet3 is not at distributor2, pallet3 is not clear of any crates, pallet3 is not located at depot0, pallet3 is not located at depot2, pallet3 is not located at distributor3, pallet4 cannot be found located at distributor2, pallet4 is not at depot0, pallet4 is not at distributor0, pallet4 is not located at depot1, pallet4 is not located at depot2, pallet5 cannot be found located at depot0, pallet5 cannot be found located at depot2, pallet5 is not clear of any crates, pallet5 is not located at depot1, pallet5 is not located at distributor0, pallet5 is not located at distributor3, pallet6 cannot be found located at depot2, pallet6 cannot be found located at distributor0, pallet6 cannot be found located at distributor2, pallet6 does not have crate0 on it, pallet6 is not at depot1, pallet6 is not clear of any crates, pallet6 is not located at depot0, pallet6 is not located at distributor1, truck0 cannot be found located at distributor1, truck0 cannot be found located at distributor2, truck0 does not contain crate1, truck0 does not contain crate3, truck0 is not at depot0, truck0 is not at depot1, truck0 is not located at distributor3, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor3, truck1 does not contain crate3, truck1 is not at depot2, truck1 is not at distributor1, truck1 is not located at distributor0, truck2 cannot be found located at depot2, truck2 cannot be found located at distributor0, truck2 cannot be found located at distributor1, truck2 does not contain crate0, truck2 does not contain crate2 and truck2 does not contain crate3", "plan_length": 19, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, hoist0 picks up crate2 from pallet0 at depot0 and loads it into truck1, then truck1 is driven back to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven to distributor3. At distributor3, hoist6 unloads crate3 and drops it on pallet6. At distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, then lifts crate0 off pallet5 and loads it into truck0 as well. Hoist5 then unloads crate1 from truck0, and truck0 is driven to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, if hoist5 drops crate1 on pallet5 at distributor2, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is positioned at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "bddf5cdd-e797-46f7-b464-105b5a1d3cd7", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, crate3 is lifted from pallet5 at distributor1 by hoist5, at distributor1, hoist5 loads crate3 into truck2, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven from distributor1 to distributor2, hoist5 drops crate2 on pallet5 at distributor1, crate0 is lifted from pallet6 at distributor2 by hoist6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, crate0 is unloaded by hoist3 from truck2 at depot3, truck2 is driven from depot3 to distributor0, at distributor0, hoist4 unloads crate3 from truck2, at depot3, hoist3 drops crate0 on pallet3 and at distributor0, hoist4 drops crate3 on pallet4 to reach the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "crate0 cannot be found located at depot1, crate0 cannot be found located at depot2, crate0 cannot be found located at distributor0, crate0 cannot be found located at distributor1, crate0 is clear, crate0 is not inside truck0, crate0 is not inside truck2, crate0 is not on crate2, crate0 is not on pallet4, crate0 is not on top of crate0, crate0 is not on top of crate1, crate0 is not on top of pallet2, crate0 is not on top of pallet6, crate0 is on top of pallet3, crate1 cannot be found located at depot0, crate1 cannot be found located at depot2, crate1 cannot be found located at distributor0, crate1 cannot be found located at distributor2, crate1 is not at depot3, crate1 is not clear, crate1 is not in truck2, crate1 is not inside truck0, crate1 is not inside truck1, crate1 is not located at depot1, crate1 is not on crate1, crate1 is not on crate3, crate1 is not on pallet0, crate1 is not on pallet3, crate1 is not on pallet5, crate1 is not on top of crate0, crate1 is not on top of pallet1, crate1 is not on top of pallet2, crate1 is not on top of pallet6, crate2 cannot be found located at depot0, crate2 cannot be found located at depot3, crate2 does not have crate1 on it, crate2 does not have crate2 on it, crate2 is clear, crate2 is not at depot1, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not inside truck2, crate2 is not located at distributor0, crate2 is not on crate0, crate2 is not on crate3, crate2 is not on pallet3, crate2 is not on top of crate1, crate2 is not on top of pallet0, crate2 is not on top of pallet1, crate2 is on top of pallet5, crate3 does not have crate0 on it, crate3 does not have crate3 on it, crate3 is clear of any crates, crate3 is located at distributor0, crate3 is not in truck2, crate3 is not inside truck0, crate3 is not located at depot0, crate3 is not located at depot1, crate3 is not located at depot2, crate3 is not located at depot3, crate3 is not located at distributor1, crate3 is not located at distributor2, crate3 is not on crate0, crate3 is not on crate2, crate3 is not on pallet1, crate3 is not on pallet2, crate3 is not on pallet6, crate3 is not on top of crate1, crate3 is not on top of pallet0, crate3 is not on top of pallet3, crate3 is on pallet4, depot0 is where crate0 is not located, depot0 is where hoist0 is located, depot0 is where hoist3 is not located, depot0 is where hoist4 is not located, depot0 is where hoist6 is not located, depot0 is where pallet1 is not located, depot0 is where pallet5 is not located, depot0 is where truck1 is not located, depot1 is where hoist0 is not located, depot1 is where hoist1 is located, depot1 is where hoist2 is not located, depot1 is where hoist3 is not located, depot1 is where hoist4 is not located, depot1 is where pallet1 is located, depot1 is where pallet4 is not located, depot1 is where truck0 is located, depot2 is where crate2 is not located, depot2 is where hoist0 is not located, depot2 is where hoist6 is not located, depot2 is where pallet0 is not located, depot2 is where pallet2 is located, depot2 is where pallet6 is not located, depot2 is where truck1 is not located, depot2 is where truck2 is not located, depot3 is where crate0 is located, depot3 is where pallet3 is located, depot3 is where pallet4 is not located, depot3 is where pallet6 is not located, depot3 is where truck2 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist2 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet4 is located, distributor0 is where truck2 is located, distributor1 is where crate1 is not located, distributor1 is where crate2 is located, distributor1 is where hoist1 is not located, distributor1 is where hoist6 is not located, distributor1 is where truck0 is not located, distributor1 is where truck2 is not located, distributor2 is where crate0 is not located, distributor2 is where crate2 is not located, distributor2 is where hoist2 is not located, distributor2 is where hoist4 is not located, distributor2 is where pallet0 is not located, hoist0 cannot be found located at distributor0, hoist0 cannot be found located at distributor1, hoist0 cannot be found located at distributor2, hoist0 is available, hoist0 is not lifting crate1, hoist0 is not lifting crate3, hoist0 is not located at depot3, hoist0 is not raising crate0, hoist0 is not raising crate2, hoist1 cannot be found located at depot3, hoist1 cannot be found located at distributor2, hoist1 is accessible, hoist1 is not elevating crate0, hoist1 is not elevating crate2, hoist1 is not lifting crate1, hoist1 is not lifting crate3, hoist1 is not located at depot0, hoist1 is not located at depot2, hoist2 is available for work, hoist2 is located at depot2, hoist2 is not elevating crate0, hoist2 is not elevating crate2, hoist2 is not lifting crate3, hoist2 is not located at depot0, hoist2 is not located at depot3, hoist2 is not located at distributor1, hoist2 is not raising crate1, hoist3 cannot be found located at depot2, hoist3 cannot be found located at distributor0, hoist3 is available, hoist3 is located at depot3, hoist3 is not at distributor1, hoist3 is not elevating crate1, hoist3 is not lifting crate0, hoist3 is not located at distributor2, hoist3 is not raising crate2, hoist3 is not raising crate3, hoist4 can be found located at distributor0, hoist4 cannot be found located at depot2, hoist4 is lifting crate1, hoist4 is not available for work, hoist4 is not elevating crate0, hoist4 is not elevating crate3, hoist4 is not located at depot3, hoist4 is not located at distributor1, hoist4 is not raising crate2, hoist5 can be found located at distributor1, hoist5 cannot be found located at depot2, hoist5 cannot be found located at distributor2, hoist5 is available for work, hoist5 is not at depot1, hoist5 is not at depot3, hoist5 is not lifting crate0, hoist5 is not lifting crate3, hoist5 is not located at depot0, hoist5 is not located at distributor0, hoist5 is not raising crate1, hoist5 is not raising crate2, hoist6 cannot be found located at depot1, hoist6 cannot be found located at depot3, hoist6 is at distributor2, hoist6 is available, hoist6 is not elevating crate0, hoist6 is not lifting crate1, hoist6 is not located at distributor0, hoist6 is not raising crate2, hoist6 is not raising crate3, pallet0 does not have crate0 on it, pallet0 is at depot0, pallet0 is clear, pallet0 is not at depot1, pallet0 is not at depot3, pallet0 is not at distributor0, pallet0 is not at distributor1, pallet1 cannot be found located at depot2, pallet1 cannot be found located at depot3, pallet1 does not have crate0 on it, pallet1 is clear, pallet1 is not at distributor1, pallet1 is not located at distributor0, pallet1 is not located at distributor2, pallet2 cannot be found located at depot1, pallet2 cannot be found located at depot3, pallet2 does not have crate2 on it, pallet2 is clear, pallet2 is not at depot0, pallet2 is not at distributor1, pallet2 is not at distributor2, pallet3 is not at depot0, pallet3 is not at depot2, pallet3 is not at distributor2, pallet3 is not clear, pallet3 is not located at depot1, pallet3 is not located at distributor0, pallet3 is not located at distributor1, pallet4 cannot be found located at depot0, pallet4 cannot be found located at depot2, pallet4 does not have crate1 on it, pallet4 does not have crate2 on it, pallet4 is not at distributor1, pallet4 is not clear of any crates, pallet4 is not located at distributor2, pallet5 can be found located at distributor1, pallet5 cannot be found located at depot2, pallet5 does not have crate0 on it, pallet5 does not have crate3 on it, pallet5 is not at depot1, pallet5 is not at depot3, pallet5 is not at distributor0, pallet5 is not clear of any crates, pallet5 is not located at distributor2, pallet6 cannot be found located at depot0, pallet6 does not have crate2 on it, pallet6 is at distributor2, pallet6 is clear of any crates, pallet6 is not at distributor1, pallet6 is not located at depot1, pallet6 is not located at distributor0, truck0 cannot be found located at distributor2, truck0 is not at depot0, truck0 is not at depot3, truck0 is not at distributor0, truck0 is not located at depot2, truck1 cannot be found located at depot3, truck1 cannot be found located at distributor1, truck1 does not contain crate0, truck1 does not contain crate3, truck1 is at distributor0, truck1 is not at depot1, truck1 is not located at distributor2, truck2 cannot be found located at distributor2, truck2 is not at depot0 and truck2 is not at depot1", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck2. Then, truck2 is driven to distributor1. At depot3, hoist3 lifts crate1 from pallet3, and at distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, while unloading crate2. Truck2 then proceeds to distributor2, where hoist5 drops crate2 on pallet5. At distributor2, hoist6 lifts crate0 from pallet6 and loads it into truck2. Truck2 is then driven to depot3, where hoist3 loads crate1 into truck2 and unloads crate0. Finally, truck2 is driven to distributor0, where hoist4 unloads crate3, and at depot3, hoist3 places crate0 on pallet3, while hoist4 places crate3 on pallet4 at distributor0. In this resulting state, if crate1 is unloaded by hoist4 from truck2 at distributor0, what are all the valid properties of the state (including both affirmative and negative properties)? If none exist, state None.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is situated at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "f95e3b47-9edb-4dad-9588-d402a4b1b61b", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, hoist2 lifts crate3 from crate2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, crate2 is lifted from crate1 at depot2 by hoist2, hoist2 loads crate2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven from depot2 to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3, hoist3 loads crate0 into truck2 at distributor0, crate1 is unloaded by hoist3 from truck2 at distributor0, truck2 is driven from distributor0 to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, at distributor2, hoist5 unloads crate3 from truck2, at distributor0, hoist3 drops crate1 on pallet3, at distributor1, hoist4 drops crate2 on pallet4, at distributor2, hoist5 drops crate3 on pallet5 and at distributor2, hoist5 unloads crate0 from truck2 to reach the current state. In this state, if hoist5 drops crate0 on crate3 at distributor2, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "crate0 cannot be found located at depot2, crate0 cannot be found located at distributor1, crate0 does not have crate2 on it, crate0 is not at depot0, crate0 is not in truck0, crate0 is not inside truck1, crate0 is not located at depot1, crate0 is not located at distributor0, crate0 is not on crate2, crate0 is not on pallet1, crate0 is not on pallet5, crate0 is not on top of crate0, crate0 is not on top of pallet0, crate0 is not on top of pallet4, crate1 cannot be found located at depot2, crate1 cannot be found located at distributor2, crate1 does not have crate0 on it, crate1 is not at depot0, crate1 is not inside truck1, crate1 is not on crate1, crate1 is not on pallet2, crate1 is not on pallet5, crate1 is not on top of crate0, crate1 is not on top of crate3, crate1 is not on top of pallet0, crate1 is not on top of pallet4, crate2 cannot be found located at depot0, crate2 does not have crate1 on it, crate2 does not have crate3 on it, crate2 is not at distributor2, crate2 is not in truck1, crate2 is not inside truck2, crate2 is not located at depot1, crate2 is not located at depot2, crate2 is not located at distributor0, crate2 is not on crate1, crate2 is not on pallet1, crate2 is not on pallet2, crate2 is not on pallet3, crate2 is not on top of crate2, crate2 is not on top of pallet5, crate3 cannot be found located at depot0, crate3 cannot be found located at distributor0, crate3 does not have crate2 on it, crate3 is not at depot2, crate3 is not at distributor1, crate3 is not clear of any crates, crate3 is not inside truck1, crate3 is not inside truck2, crate3 is not located at depot1, crate3 is not on pallet3, crate3 is not on top of crate0, crate3 is not on top of crate1, crate3 is not on top of crate3, crate3 is not on top of pallet1, depot0 is where hoist1 is not located, depot0 is where pallet3 is not located, depot0 is where pallet5 is not located, depot1 is where crate1 is not located, depot1 is where hoist5 is not located, depot1 is where pallet4 is not located, depot2 is where pallet3 is not located, depot2 is where pallet5 is not located, distributor0 is where pallet0 is not located, distributor0 is where truck2 is not located, distributor1 is where crate1 is not located, distributor1 is where pallet0 is not located, distributor1 is where pallet2 is not located, distributor1 is where pallet5 is not located, distributor1 is where truck2 is not located, distributor2 is where hoist3 is not located, distributor2 is where pallet2 is not located, hoist0 cannot be found located at distributor0, hoist0 cannot be found located at distributor1, hoist0 cannot be found located at distributor2, hoist0 is not at depot2, hoist0 is not elevating crate2, hoist0 is not elevating crate3, hoist0 is not lifting crate0, hoist0 is not located at depot1, hoist0 is not raising crate1, hoist1 cannot be found located at depot2, hoist1 cannot be found located at distributor2, hoist1 is not at distributor0, hoist1 is not elevating crate2, hoist1 is not lifting crate0, hoist1 is not lifting crate3, hoist1 is not located at distributor1, hoist1 is not raising crate1, hoist2 cannot be found located at depot0, hoist2 cannot be found located at distributor0, hoist2 is not at depot1, hoist2 is not at distributor1, hoist2 is not at distributor2, hoist2 is not elevating crate2, hoist2 is not elevating crate3, hoist2 is not raising crate0, hoist2 is not raising crate1, hoist3 cannot be found located at depot0, hoist3 cannot be found located at depot1, hoist3 cannot be found located at distributor1, hoist3 is not at depot2, hoist3 is not lifting crate0, hoist3 is not lifting crate2, hoist3 is not raising crate1, hoist3 is not raising crate3, hoist4 cannot be found located at depot1, hoist4 cannot be found located at depot2, hoist4 is not at depot0, hoist4 is not lifting crate0, hoist4 is not lifting crate1, hoist4 is not lifting crate2, hoist4 is not lifting crate3, hoist4 is not located at distributor0, hoist4 is not located at distributor2, hoist5 cannot be found located at depot0, hoist5 cannot be found located at distributor0, hoist5 cannot be found located at distributor1, hoist5 is not elevating crate1, hoist5 is not lifting crate3, hoist5 is not located at depot2, hoist5 is not raising crate0, hoist5 is not raising crate2, pallet0 cannot be found located at depot1, pallet0 does not have crate2 on it, pallet0 does not have crate3 on it, pallet0 is not at depot2, pallet0 is not located at distributor2, pallet1 cannot be found located at depot0, pallet1 cannot be found located at distributor0, pallet1 does not have crate1 on it, pallet1 is not at depot2, pallet1 is not at distributor1, pallet1 is not located at distributor2, pallet2 cannot be found located at depot0, pallet2 cannot be found located at distributor0, pallet2 does not have crate0 on it, pallet2 does not have crate3 on it, pallet2 is not located at depot1, pallet3 does not have crate0 on it, pallet3 is not at depot1, pallet3 is not at distributor2, pallet3 is not clear, pallet3 is not located at distributor1, pallet4 cannot be found located at depot0, pallet4 does not have crate3 on it, pallet4 is not at depot2, pallet4 is not at distributor2, pallet4 is not clear, pallet4 is not located at distributor0, pallet5 cannot be found located at distributor0, pallet5 is not at depot1, pallet5 is not clear, truck0 cannot be found located at distributor2, truck0 does not contain crate1, truck0 does not contain crate2, truck0 does not contain crate3, truck0 is not at depot0, truck0 is not at depot1, truck0 is not at depot2, truck0 is not located at distributor1, truck1 is not at depot0, truck1 is not at distributor2, truck1 is not located at depot2, truck1 is not located at distributor0, truck1 is not located at distributor1, truck2 cannot be found located at depot2, truck2 does not contain crate0, truck2 does not contain crate1, truck2 is not at depot1 and truck2 is not located at depot0", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from depot0, truck2 travels to depot2, where hoist2 lifts crate3 from crate2, loads crate3 into truck2, lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2. Then, truck2 travels from depot2 to distributor0, where hoist3 lifts crate0 from pallet3 and loads it into truck2, while unloading crate1 from truck2. Next, truck2 travels to distributor1, where hoist4 unloads crate2, and then to distributor2, where hoist5 unloads crate3. Finally, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, and hoist5 drops crate3 on pallet5 at distributor2, and unloads crate0 from truck2 to reach the current state. In this state, if hoist5 drops crate0 on crate3 at distributor2, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the site of hoist3. Hoist0 is available for use and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is also available for work. Hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is empty and located at depot1. Pallet2 is at depot2, while pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and empty, and pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "d5195fa7-f20a-432a-b911-ac52063065fe", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven from depot2 to distributor0, at distributor0, hoist3 unloads crate1 from truck0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4, at distributor1, hoist4 loads crate0 into truck2, at distributor2, hoist5 lifts crate3 off pallet5, crate3 is loaded by hoist5 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, crate3 is unloaded by hoist4 from truck1 at distributor1, crate3 is dropped on pallet4 at distributor1 by hoist4, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven from distributor1 to depot1, at depot1, hoist1 unloads crate0 from truck2 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, if crate2 is dropped on crate3 at distributor1 by hoist4, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "crate0 can be found located at depot1, crate0 is clear of any crates, crate0 is on top of pallet1, crate1 can be found located at distributor0, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 is at distributor1, crate2 is clear of any crates, crate3 has crate2 on it, crate3 is located at distributor1, crate3 is on top of pallet4, depot2 is where hoist2 is located, distributor2 is where hoist5 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is accessible, hoist2 is accessible, hoist3 can be found located at distributor0, hoist3 is available, hoist4 is at distributor1, hoist4 is available, hoist5 is available for work, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is at depot1, pallet2 is at depot2, pallet2 is clear, pallet3 is located at distributor0, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 is at distributor0, truck1 is at distributor1 and truck2 is at depot1", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 at depot0 lifts crate2 from pallet0 and loads it into truck2, then truck2 is driven from depot0 to distributor1. At depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. Meanwhile, at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, hoist4 unloads crate2 from truck2, and truck2 is driven to depot1. At depot1, hoist1 unloads crate0 from truck2 and places it on pallet1, resulting in the current state. In this state, if hoist4 drops crate2 on crate3 at distributor1, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, along with pallet3, crate0 is located at distributor1, crate3 is located at distributor2, hoist0 is accessible and situated at depot0, hoist1 is available for work, hoist2 is available and located at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for work and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "b685ec17-9588-4abf-9027-3307a43cfe16", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, hoist3 lifts crate1 from pallet3 at depot3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, from distributor1, truck2 is driven to distributor2 and crate2 is dropped on pallet5 at distributor1 by hoist5 to reach the current state. In this state, if at distributor2, hoist6 lifts crate0 off pallet6, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "crate2 can be found located at distributor1, crate2 is clear of any crates, crate2 is on pallet5, crate3 is inside truck2, depot0 is where hoist0 is located, distributor2 is where truck2 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is elevating crate1, hoist3 is located at depot3, hoist4 is at distributor0, hoist4 is available for work, hoist5 is accessible, hoist5 is at distributor1, hoist6 is elevating crate0, hoist6 is located at distributor2, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is at depot1, pallet1 is clear, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at depot3, pallet4 is at distributor0, pallet4 is clear, pallet5 is located at distributor1, pallet6 is clear, pallet6 is located at distributor2, truck0 can be found located at depot1 and truck1 can be found located at distributor0", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: truck2 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and then loads it into truck2. Truck2 then proceeds to distributor1 from depot0. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3. At distributor1, hoist5 unloads crate3 from pallet5 and loads it into truck2, then unloads crate2 from truck2. Finally, truck2 drives to distributor2, and hoist5 places crate2 on pallet5 at distributor1, resulting in the current state. In this state, if hoist6 lifts crate0 off pallet6 at distributor2, what are all the valid properties of the state that do not involve negations?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, pallet6 has crate0 on it and is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "1e054729-31a5-4fb6-998e-f61788cc817b", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet3 at depot3 by hoist3, at distributor1, hoist5 lifts crate3 off pallet5, at distributor1, hoist5 loads crate3 into truck2, crate2 is unloaded by hoist5 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, crate0 is loaded by hoist6 into truck2 at distributor2, truck2 is driven to depot3 from distributor2, crate1 is loaded by hoist3 into truck2 at depot3, at depot3, hoist3 unloads crate0 from truck2, truck2 is driven from depot3 to distributor0, crate3 is unloaded by hoist4 from truck2 at distributor0, hoist3 drops crate0 on pallet3 at depot3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, if at distributor0, hoist4 unloads crate1 from truck2, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "crate0 does not have crate2 on it, crate0 does not have crate3 on it, crate0 is not at distributor1, crate0 is not in truck1, crate0 is not located at depot2, crate0 is not located at distributor0, crate0 is not located at distributor2, crate0 is not on crate0, crate0 is not on crate3, crate0 is not on pallet4, crate0 is not on top of crate1, crate0 is not on top of pallet1, crate0 is not on top of pallet2, crate0 is not on top of pallet5, crate0 is not on top of pallet6, crate1 cannot be found located at distributor2, crate1 is not at depot0, crate1 is not at depot1, crate1 is not at distributor0, crate1 is not clear, crate1 is not in truck0, crate1 is not in truck1, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet3, crate1 is not on top of crate0, crate1 is not on top of crate1, crate1 is not on top of crate2, crate1 is not on top of crate3, crate1 is not on top of pallet0, crate1 is not on top of pallet4, crate1 is not on top of pallet6, crate2 cannot be found located at depot0, crate2 cannot be found located at depot1, crate2 does not have crate0 on it, crate2 is not at depot2, crate2 is not at distributor2, crate2 is not in truck0, crate2 is not located at depot3, crate2 is not located at distributor0, crate2 is not on crate1, crate2 is not on crate3, crate2 is not on pallet1, crate2 is not on pallet2, crate2 is not on pallet3, crate2 is not on top of crate2, crate2 is not on top of pallet0, crate2 is not on top of pallet4, crate3 cannot be found located at depot1, crate3 does not have crate3 on it, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not located at depot0, crate3 is not located at depot2, crate3 is not located at depot3, crate3 is not located at distributor1, crate3 is not located at distributor2, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet1, crate3 is not on pallet3, crate3 is not on pallet5, crate3 is not on top of pallet0, crate3 is not on top of pallet2, crate3 is not on top of pallet6, depot0 is where crate0 is not located, depot0 is where hoist1 is not located, depot0 is where hoist6 is not located, depot0 is where pallet2 is not located, depot0 is where truck2 is not located, depot1 is where crate0 is not located, depot1 is where hoist0 is not located, depot1 is where hoist2 is not located, depot1 is where pallet5 is not located, depot1 is where truck2 is not located, depot2 is where crate1 is not located, depot2 is where hoist5 is not located, depot2 is where pallet0 is not located, depot2 is where pallet1 is not located, depot2 is where pallet5 is not located, depot3 is where crate1 is not located, depot3 is where hoist5 is not located, depot3 is where pallet2 is not located, depot3 is where pallet6 is not located, distributor0 is where hoist3 is not located, distributor0 is where hoist5 is not located, distributor0 is where pallet6 is not located, distributor1 is where crate1 is not located, distributor1 is where hoist2 is not located, distributor1 is where pallet0 is not located, distributor1 is where pallet2 is not located, distributor1 is where pallet3 is not located, distributor1 is where truck0 is not located, distributor2 is where hoist0 is not located, distributor2 is where hoist3 is not located, distributor2 is where pallet3 is not located, distributor2 is where truck0 is not located, hoist0 is not at depot2, hoist0 is not at depot3, hoist0 is not at distributor0, hoist0 is not at distributor1, hoist0 is not elevating crate0, hoist0 is not elevating crate2, hoist0 is not elevating crate3, hoist0 is not lifting crate1, hoist1 cannot be found located at depot3, hoist1 is not at depot2, hoist1 is not at distributor1, hoist1 is not lifting crate1, hoist1 is not lifting crate2, hoist1 is not located at distributor0, hoist1 is not located at distributor2, hoist1 is not raising crate0, hoist1 is not raising crate3, hoist2 cannot be found located at depot0, hoist2 cannot be found located at depot3, hoist2 is not at distributor0, hoist2 is not at distributor2, hoist2 is not lifting crate0, hoist2 is not raising crate1, hoist2 is not raising crate2, hoist2 is not raising crate3, hoist3 cannot be found located at depot0, hoist3 cannot be found located at depot1, hoist3 is not lifting crate1, hoist3 is not located at depot2, hoist3 is not located at distributor1, hoist3 is not raising crate0, hoist3 is not raising crate2, hoist3 is not raising crate3, hoist4 cannot be found located at depot0, hoist4 is not accessible, hoist4 is not at depot2, hoist4 is not at distributor1, hoist4 is not at distributor2, hoist4 is not lifting crate0, hoist4 is not lifting crate2, hoist4 is not lifting crate3, hoist4 is not located at depot1, hoist4 is not located at depot3, hoist5 cannot be found located at depot1, hoist5 is not at depot0, hoist5 is not at distributor2, hoist5 is not elevating crate1, hoist5 is not elevating crate2, hoist5 is not lifting crate3, hoist5 is not raising crate0, hoist6 cannot be found located at depot2, hoist6 cannot be found located at distributor0, hoist6 cannot be found located at distributor1, hoist6 is not elevating crate1, hoist6 is not elevating crate2, hoist6 is not located at depot1, hoist6 is not located at depot3, hoist6 is not raising crate0, hoist6 is not raising crate3, pallet0 does not have crate0 on it, pallet0 is not at depot1, pallet0 is not at distributor2, pallet0 is not located at depot3, pallet0 is not located at distributor0, pallet1 cannot be found located at depot0, pallet1 cannot be found located at distributor1, pallet1 cannot be found located at distributor2, pallet1 is not at distributor0, pallet1 is not located at depot3, pallet2 is not located at depot1, pallet2 is not located at distributor0, pallet2 is not located at distributor2, pallet3 cannot be found located at depot1, pallet3 cannot be found located at depot2, pallet3 cannot be found located at distributor0, pallet3 is not at depot0, pallet3 is not clear, pallet4 cannot be found located at depot1, pallet4 cannot be found located at depot3, pallet4 is not at depot0, pallet4 is not at depot2, pallet4 is not at distributor1, pallet4 is not clear of any crates, pallet4 is not located at distributor2, pallet5 cannot be found located at depot0, pallet5 cannot be found located at distributor0, pallet5 does not have crate1 on it, pallet5 is not at depot3, pallet5 is not at distributor2, pallet5 is not clear, pallet6 cannot be found located at depot0, pallet6 cannot be found located at depot1, pallet6 does not have crate2 on it, pallet6 is not at distributor1, pallet6 is not located at depot2, truck0 cannot be found located at depot3, truck0 does not contain crate0, truck0 is not at depot0, truck0 is not at depot2, truck0 is not at distributor0, truck1 cannot be found located at depot0, truck1 cannot be found located at depot2, truck1 cannot be found located at distributor2, truck1 does not contain crate2, truck1 is not located at depot1, truck1 is not located at depot3, truck1 is not located at distributor1, truck2 cannot be found located at distributor2, truck2 does not contain crate0, truck2 does not contain crate1, truck2 does not contain crate2, truck2 does not contain crate3, truck2 is not at depot3, truck2 is not located at depot2 and truck2 is not located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot1 to depot0, where hoist0 unloads crate2 from pallet0 and loads it into truck2, then truck2 is driven to distributor1. At distributor1, hoist5 unloads crate3 from pallet5 and loads it into truck2, while crate2 is unloaded from truck2 by hoist5. Truck2 then proceeds to distributor2, where hoist5 places crate2 on pallet5. At distributor2, hoist6 unloads crate0 from pallet6 and loads it into truck2. Truck2 is then driven to depot3, where crate1 is loaded into truck2 by hoist3, and crate0 is unloaded from truck2 by hoist3. Finally, truck2 is driven to distributor0, where crate3 is unloaded by hoist4, and crate0 and crate3 are placed on pallet3 and pallet4, respectively, by hoist3 and hoist4. In this state, if hoist4 unloads crate1 from truck2 at distributor0, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is situated at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
