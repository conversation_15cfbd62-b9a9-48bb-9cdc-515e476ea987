{"question_id": "72f53c2e-2259-472a-9060-be6d2fdff9b4", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, if at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l0, cargo c0 is not situated at location l1, cargo c1 is at location l1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not located in vehicle v0, cargo c2 is not in vehicle v0, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l0, cargo c2 is situated at location l1, cargo c3 is at location l0, cargo c3 is not located in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not in vehicle v1, cargo c4 is not situated at location l0, cargo c4 is situated at location l1, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is present at location l0, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not located in vehicle v0, cargo c6 is not located in vehicle v1, cargo c6 is situated at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v0, cargo c7 is not located in vehicle v1, cargo c7 is situated at location l1, cargo c8 is at location l0, cargo c8 is not situated at location l1, fuel f1 does not exist in location l1, fuel f2 does not exist in location l1, fuel f3 does not exist in location l0, fuel f5 does not exist in location l0, fuel f5 does not exist in location l1, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f4, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f3 neighbors fuel level f4, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f6, location l0 has fuel f4, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f4, location l1 does not have fuel f6, location l1 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, space s1 does not neighbour space s0, there is a connection between locations l0 and l1, vehicle v0 contains cargo c0, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c8, vehicle v0 does not have space s1, vehicle v0 has space s0, vehicle v0 is at location l1, vehicle v0 is not at location l0, vehicle v1 contains space s1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c8, vehicle v1 does not have space s0, vehicle v1 is at location l1 and vehicle v1 is not present at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, assuming vehicle v0 is at location l1 and cargo c0 is loaded into vehicle v0 with spaces s1 and s0, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is equipped with fuel f5, locations l0 and l1 are connected, location l1 is equipped with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is situated at location l1."}
{"question_id": "395a2d1d-4f12-4f77-a9f5-84e77ed9d098", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "cargo c0 is at location l0, cargo c0 is not in vehicle v1, cargo c0 is not located in vehicle v0, cargo c0 is not situated at location l1, cargo c1 is not located in vehicle v0, cargo c1 is not located in vehicle v1, cargo c1 is not situated at location l1, cargo c1 is present at location l0, cargo c2 is in vehicle v0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l0, cargo c3 is not in vehicle v1, cargo c3 is not located in vehicle v0, cargo c3 is not situated at location l1, cargo c3 is present at location l0, cargo c4 is not in vehicle v1, cargo c4 is not situated at location l0, cargo c4 is present at location l1, cargo c5 is at location l0, cargo c5 is not located in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l1, cargo c6 is not in vehicle v1, cargo c6 is not situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v0, cargo c8 is at location l0, cargo c8 is not in vehicle v0, cargo c8 is not situated at location l1, fuel f3 does not exist in location l0, fuel f3 does not exist in location l1, fuel f4 exists in location l1, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f7, fuel level f0 neighbors fuel level f1, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f6, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f6, fuel level f3 neighbors fuel level f4, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f7, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f6, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f5 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 does not have fuel f0, location l0 does not have fuel f1, location l0 does not have fuel f4, location l0 has fuel f2, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f7, location l1 does not have fuel f0, location l1 does not have fuel f1, location l1 does not have fuel f2, location l1 does not have fuel f6, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s0 are not neighbors, vehicle v0 contains space s0, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c6, vehicle v0 does not have space s1, vehicle v0 is not situated at location l1, vehicle v0 is present at location l0, vehicle v1 does not contain cargo c7, vehicle v1 does not contain cargo c8, vehicle v1 does not have space s0, vehicle v1 has space s1, vehicle v1 is at location l1 and vehicle v1 is not situated at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Next, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f4 and f3, back to location l1, where cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Subsequently, vehicle v0 returns to location l0, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, freeing up spaces s0 and s1. Finally, vehicle v0 moves to location l1 from location l0, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, what are all the valid properties of the state (including both affirmative and negated properties)? If none exist, state 'None'.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is supplied with fuel f5, locations l0 and l1 are connected, location l1 is supplied with fuel f7, space s0 is adjacent to space s1, locations l0 and l1 are linked, vehicle v0 occupies space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is currently at location l1."}
{"question_id": "033df844-7846-40c7-96b9-94af10ba7d96", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c3 is at location l1, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel f3 exists in location l0, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 is connected to location l1, location l1 and location l0 are connected, location l1 has a fuel-level of f7, spaces s0 and s1 are neighbors, vehicle v0 contains cargo c2, vehicle v0 contains space s0, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c2 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0, what are all the valid properties of the resulting state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, f6 is adjacent to f7, f0 is adjacent to f1, f2 is adjacent to f3, f5 is adjacent to f6, and f7 is adjacent to f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "d6d4c108-5a41-4b5f-a140-9bab123b79f0", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, if vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not in vehicle v1, cargo c0 is not located in vehicle v0, cargo c1 is in vehicle v0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v1, cargo c2 is at location l0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not located in vehicle v1, cargo c3 is at location l0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not in vehicle v0, cargo c3 is not located in vehicle v1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v0, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c5 is not in vehicle v0, cargo c5 is not situated at location l0, cargo c6 is not in vehicle v0, cargo c6 is not located in vehicle v1, cargo c6 is not situated at location l1, cargo c6 is situated at location l0, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not located in vehicle v0, cargo c7 is present at location l0, cargo c8 is at location l1, cargo c8 is not located in vehicle v0, cargo c8 is not located in vehicle v1, cargo c8 is not situated at location l0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not located in vehicle v1, cargo c9 is present at location l1, fuel f0 does not exist in location l0, fuel f1 exists in location l0, fuel f3 does not exist in location l0, fuel f4 does not exist in location l0, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f8, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f2 neighbors fuel level f3, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f7 and f8 are neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f6, location l0 does not have fuel f2, location l0 does not have fuel f5, location l0 does not have fuel f7, location l0 does not have fuel f8, location l1 does not have a fuel-level of f7, location l1 does not have fuel f0, location l1 does not have fuel f1, location l1 does not have fuel f2, location l1 does not have fuel f3, location l1 does not have fuel f4, location l1 does not have fuel f5, location l1 does not have fuel f8, location l1 has fuel f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, spaces s1 and s0 are not neighbors, vehicle v0 contains space s0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s1, vehicle v0 is not situated at location l0, vehicle v0 is present at location l1, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0, vehicle v1 has space s1, vehicle v1 is not present at location l1 and vehicle v1 is present at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, where the fuel levels are f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0 using spaces s0 and s1. Next, at location l0, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, and vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. Upon arrival at location l1, cargo c0 is unloaded from vehicle v0 using spaces s0 and s1. Subsequently, at location l1, cargo c3 is loaded onto vehicle v0, occupying spaces s1 and s0, and vehicle v0 then travels from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1, what are all the valid properties of the state, including both affirmative and negated properties? If none exist, state 'None'.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are interconnected. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "34531c80-b9ad-4410-8a47-93a8d5604dd1", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, if cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "cargo c0 is located in vehicle v0, cargo c1 is situated at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l0, cargo c5 is situated at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f2 exists in location l1, fuel f4 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, location l0 is connected to location l1, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l1 and l0, vehicle v0 contains space s1, vehicle v0 is present at location l0, vehicle v1 has space s2 and vehicle v1 is at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, assuming cargo c0 is loaded into vehicle v0, which has available spaces s2 and s1 at location l0, what are all the valid properties of the state that do not involve negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Furthermore, cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Cargo c8 and c9 are also located at l1. Fuel f3 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. A connection exists between locations l0 and l1. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "0b9c76bf-7ec1-4813-91c1-cee1b9036b71", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0 to reach the current state. In this state, if cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "cargo c0 is not situated at location l0, cargo c0 is present at location l1, cargo c1 is not in vehicle v0, cargo c1 is not situated at location l1, cargo c1 is situated at location l0, cargo c10 is at location l0, cargo c10 is not at location l1cargo c10 is not present at location l1, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is situated at location l0, cargo c3 is not in vehicle v0, cargo c3 is not situated at location l1, cargo c3 is situated at location l0, cargo c4 is not in vehicle v0, cargo c4 is not situated at location l1, cargo c4 is present at location l0, cargo c5 is not situated at location l0, cargo c5 is present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not situated at location l1, cargo c6 is situated at location l0, cargo c7 is at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not in vehicle v0, cargo c8 is not in vehicle v0, cargo c8 is not situated at location l0, cargo c8 is present at location l1, cargo c9 is not in vehicle v0, cargo c9 is not situated at location l0, cargo c9 is situated at location l1, fuel f3 does not exist in location l0, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel f6 does not exist in location l1, fuel f7 does not exist in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f8, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f8, fuel level f3 neighbors fuel level f4, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f8, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f8, fuel level f6 neighbors fuel level f7, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 neighbors fuel level f8, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 does not have fuel f2, location l0 does not have fuel f8, location l0 has a fuel-level of f5, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f8, location l1 does not have fuel f0, location l1 does not have fuel f1, location l1 does not have fuel f5, location l1 has fuel f3, space s0 neighbors space s1, spaces s1 and s0 are not neighbors, there is a connection between locations l1 and l0, vehicle v0 contains space s1, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c5, vehicle v0 does not have space s0, vehicle v0 is not present at location l1 and vehicle v0 is present at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 then moves from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, if cargo c6 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state 'None'.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, with l1 also being connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "602e4bb0-9e02-45ac-9545-83a27d6facae", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c10 is not situated at location l1, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not located in vehicle v0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c5 is not situated at location l0, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not located in vehicle v0, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not in vehicle v0, fuel f2 does not exist in location l0, fuel f4 does not exist in location l0, fuel f6 does not exist in location l0, fuel f7 does not exist in location l1, fuel f8 does not exist in location l1, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f1 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f8, location l0 does not have fuel f3, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f6, location l1 does not have fuel f4, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c5, vehicle v0 does not contain cargo c7, vehicle v0 does not have space s1 and vehicle v0 is not present at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0, what are all the valid properties of the state that involve negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, while cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all found at l1. Fuel f6 is present in l1, and fuel f7 is present in l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, with l1 also being connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "2cea5364-3a96-446d-849d-0070811ff6c6", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, if at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c1 is not situated at location l0, cargo c10 is not in vehicle v0, cargo c10 is not located in vehicle v1, cargo c10 is not situated at location l1, cargo c2 is not at location l0cargo c2 is not present at location l0, cargo c2 is not located in vehicle v0, cargo c2 is not located in vehicle v1, cargo c3 is not located in vehicle v1, cargo c3 is not situated at location l0, cargo c3 is not situated at location l1, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c4 is not located in vehicle v1, cargo c4 is not situated at location l0, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not in vehicle v0, cargo c5 is not in vehicle v1, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not located in vehicle v1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not in vehicle v0, cargo c7 is not located in vehicle v1, cargo c8 is not located in vehicle v0, cargo c8 is not located in vehicle v1, cargo c8 is not situated at location l0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not located in vehicle v0, cargo c9 is not located in vehicle v1, fuel f0 does not exist in location l0, fuel f3 does not exist in location l1, fuel f4 does not exist in location l0, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f4 are not neighbors, location l0 does not have a fuel-level of f5, location l0 does not have fuel f1, location l0 does not have fuel f2, location l1 does not have a fuel-level of f5, location l1 does not have fuel f0, location l1 does not have fuel f2, location l1 does not have fuel f4, space s0 does not neighbour space s2, space s2 does not neighbour space s1, spaces s1 and s0 are not neighbors, spaces s2 and s0 are not neighbors, vehicle v0 does not contain cargo c1, vehicle v0 does not contain space s1, vehicle v0 does not contain space s2, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain space s1, vehicle v1 does not have space s0 and vehicle v1 is not at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, followed by the loading of cargo c2 into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, back to location l1. Upon arrival at location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Subsequently, cargo c10 is loaded into vehicle v0 at location l1, occupying spaces s1 and s0, and cargo c2 is unloaded from vehicle v0, vacating spaces s0 and s1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, back to location l0. At location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, resulting in the current state. In this state, if cargo c4 is loaded into vehicle v0 at location l0, occupying spaces s1 and s0, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Furthermore, cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Cargo c8 and c9 are also located at l1. Fuel f3 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. A connection exists between locations l0 and l1. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "cae020af-e0e4-48b9-9b59-b515e66b1e0e", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, if vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "cargo c0 is not located in vehicle v0, cargo c0 is not situated at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v0, cargo c10 is not at location l1cargo c10 is not present at location l1, cargo c10 is not in vehicle v0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c3 is not in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not located in vehicle v0, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not located in vehicle v0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not in vehicle v0, fuel f0 does not exist in location l1, fuel f3 does not exist in location l0, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel f5 does not exist in location l1, fuel f8 does not exist in location l1, fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f5 are not neighbors, location l0 does not have a fuel-level of f6, location l0 does not have fuel f0, location l0 does not have fuel f1, location l0 does not have fuel f5, location l0 does not have fuel f7, location l0 does not have fuel f8, location l1 does not have a fuel-level of f2, location l1 does not have fuel f3, location l1 does not have fuel f6, location l1 does not have fuel f7, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c7, vehicle v0 does not contain space s0 and vehicle v0 is not at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, utilizing spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1, where cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0, using spaces s0 and s1. This process continues with vehicle v0 moving from location l0, with fuel levels f6 and f5, to location l1, where cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f4 and f3, to location l0, where cargo c6 is unloaded from vehicle v0, utilizing spaces s0 and s1. The sequence of actions continues with vehicle v0 moving from location l0, with fuel levels f5 and f4, to location l1, where cargo c7 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f3 and f2, to location l0, where cargo c7 is unloaded from vehicle v0, using spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f4 and f3, to location l1, where cargo c8 is loaded onto vehicle v0, occupying spaces s1 and s0. Finally, vehicle v0 travels from location l1, with fuel levels f2 and f1, to location l0, where cargo c8 is unloaded from vehicle v0, utilizing spaces s0 and s1, resulting in the current state. In this state, if vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is found at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, and space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "50141d71-9066-4324-b1d7-282a83164314", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "cargo c0 is not in vehicle v0, cargo c0 is not in vehicle v1, cargo c0 is not situated at location l0, cargo c1 is not in vehicle v1, cargo c1 is not located in vehicle v0, cargo c1 is not situated at location l0, cargo c2 is not in vehicle v0, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is not located in vehicle v1, cargo c3 is not situated at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v0, cargo c4 is not located in vehicle v1, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c6 is not in vehicle v0, cargo c6 is not located in vehicle v1, cargo c6 is not situated at location l1, cargo c7 is not located in vehicle v0, cargo c7 is not situated at location l0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not located in vehicle v0, cargo c8 is not located in vehicle v1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not situated at location l1, fuel f3 does not exist in location l1, fuel f6 does not exist in location l0, fuel f8 does not exist in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f8, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f8, location l0 does not have fuel f1, location l0 does not have fuel f3, location l0 does not have fuel f4, location l0 does not have fuel f7, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f6, location l1 does not have a fuel-level of f7, location l1 does not have fuel f0, location l1 does not have fuel f1, location l1 does not have fuel f2, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c3, vehicle v0 does not have space s1, vehicle v0 is not present at location l1, vehicle v1 does not contain cargo c7, vehicle v1 does not contain cargo c9, vehicle v1 does not have space s0 and vehicle v1 is not present at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, at location l0, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, at location l1, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f7 and f6, to location l0. Upon arrival at location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. Next, at location l0, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, at location l1, cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1. Next, at location l0, cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l1, with fuel levels f5 and f4, to location l0, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, and c9 are all situated at location l1, with cargo c8 also present there. Meanwhile, cargo c6 and c7 are positioned at l0. Fuel f3 is available at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1, as well as between l1 and l0. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "c6b38b89-88ab-48f4-be91-b529aa57fcac", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c0 is not in vehicle v1, cargo c0 is not located in vehicle v0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v0, cargo c1 is not in vehicle v1, cargo c2 is not at location l0cargo c2 is not present at location l0, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is not located in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not located in vehicle v1, cargo c5 is not in vehicle v0, cargo c5 is not situated at location l1, cargo c6 is not in vehicle v1, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l0, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v0, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not located in vehicle v0, fuel f0 does not exist in location l0, fuel f3 does not exist in location l0, fuel f4 does not exist in location l0, fuel f5 does not exist in location l0, fuel f6 does not exist in location l1, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f6, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, location l0 does not have fuel f1, location l0 does not have fuel f6, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f5, location l1 does not have fuel f0, location l1 does not have fuel f1, location l1 does not have fuel f2, location l1 does not have fuel f7, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c4, vehicle v0 does not contain space s1, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c7, vehicle v1 does not contain cargo c8, vehicle v1 does not have space s0 and vehicle v1 is not situated at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: vehicle v0 relocates from location l0, which has fuel levels of f5 and f4, to location l1. Upon arrival at l1, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from location l1, which has fuel levels of f7 and f6, back to location l0. At location l0, cargo c0 is then unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then moves from location l0, which has fuel levels of f4 and f3, back to location l1. At location l1, cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels of f6 and f5, back to location l0. Upon arrival at l0, cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then moves from location l0, which has fuel levels of f3 and f2, back to location l1, and cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0 at location l1, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels of f5 and f4, to location l0, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is supplied with fuel f5, locations l0 and l1 are connected, location l1 is supplied with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is currently at location l1."}
{"question_id": "73747240-7b37-4db5-8503-f0391c22312f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l0, cargo c7 is situated at location l1, cargo c8 is situated at location l1, cargo c9 is located in vehicle v0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f0, location l1 and location l0 are connected, location l1 has a fuel-level of f4, space s0 neighbors space s1, vehicle v0 has space s0, vehicle v0 is present at location l0, vehicle v1 has space s1 and vehicle v1 is at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, cargo c0 is loaded onto vehicle v0 at location l0, and then vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0 at location l1, and then vehicle v0 moves to location l0 from location l1, which has fuel levels f7 and f6. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. Then, cargo c1 is loaded onto vehicle v0 at location l0, and vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1. Upon arrival at location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c5 is loaded onto vehicle v0 at location l1, and then vehicle v0 moves to location l0 from location l1, which has fuel levels f6 and f5. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1. Then, cargo c7 is loaded onto vehicle v0 at location l0, and vehicle v0 moves to location l1 from location l0, which has fuel levels f1 and f0. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1. Finally, cargo c9 is loaded onto vehicle v0 at location l1, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "4a1e1814-bf8f-473e-846a-2a66b53c4c52", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, if cargo c9 is unloaded from vehicle v0 with space s0 and space s1 at location l0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "cargo c0 is present at location l1, cargo c1 is situated at location l1, cargo c10 is at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is at location l1, cargo c5 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is present at location l1, cargo c9 is present at location l0, fuel f2 exists in location l0, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 is connected to location l1, location l1 and location l0 are connected, location l1 has a fuel-level of f0, space s1 neighbors space s2, spaces s0 and s1 are neighbors, vehicle v0 contains cargo c6, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 has space s2 and vehicle v1 is present at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, then cargo c0 is loaded onto vehicle v0, which has available spaces s2 and s1, at location l0. Subsequently, at location l0, cargo c2 is loaded onto vehicle v0, which has available spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has available spaces s0 and s1, and cargo c10 is loaded onto vehicle v0, which has available spaces s1 and s0. Then, cargo c2 is unloaded from vehicle v0, which has available spaces s0 and s1, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, to location l0. At location l0, cargo c10 is unloaded from vehicle v0, which has available spaces s1 and s2, and cargo c3 is loaded onto vehicle v0, which has available spaces s2 and s1. Additionally, at location l0, cargo c4 is loaded onto vehicle v0, which has available spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c3 is unloaded from vehicle v0, which has available spaces s0 and s1, and cargo c4 is unloaded from vehicle v0, which has available spaces s1 and s2. Then, cargo c5 is loaded onto vehicle v0, which has available spaces s2 and s1, and cargo c9 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f1 and f0, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has available spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, which has available spaces s1 and s0, resulting in the current state. In this state, if cargo c9 is unloaded from vehicle v0, which has available spaces s0 and s1, at location l0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Furthermore, cargo c8 and c9 are located at l1. Fuel f3 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are interconnected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. Locations l0 and l1 are connected. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "ac0091a3-10ad-410f-999c-54d40c4c7a91", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, if at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "cargo c0 is located in vehicle v0, cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not situated at location l1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not located in vehicle v0, cargo c1 is not located in vehicle v1, cargo c1 is present at location l1, cargo c10 is not located in vehicle v1, cargo c10 is not situated at location l0, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not in vehicle v0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not located in vehicle v1, cargo c3 is situated at location l0, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c4 is not in vehicle v0, cargo c4 is not in vehicle v1, cargo c4 is situated at location l0, cargo c5 is at location l1, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not located in vehicle v1, cargo c6 is not in vehicle v0, cargo c6 is not situated at location l1, cargo c6 is present at location l0, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not located in vehicle v0, cargo c7 is not located in vehicle v1, cargo c7 is situated at location l0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v1, cargo c8 is not located in vehicle v0, cargo c8 is situated at location l1, cargo c9 is not in vehicle v0, cargo c9 is not located in vehicle v1, cargo c9 is not situated at location l0, cargo c9 is situated at location l1, fuel f0 does not exist in location l0, fuel f0 does not exist in location l1, fuel f1 does not exist in location l0, fuel f1 does not exist in location l1, fuel f3 does not exist in location l0, fuel f4 does not exist in location l1, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f4, fuel level f1 neighbors fuel level f2, fuel level f3 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f3, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f4 are not neighbors, location l0 does not have fuel f2, location l0 has a fuel-level of f4, location l1 and location l0 are connected, location l1 does not have a fuel-level of f5, location l1 does not have fuel f3, location l1 has fuel f2, space s0 neighbors space s1, spaces s0 and s2 are not neighbors, spaces s1 and s0 are not neighbors, spaces s1 and s2 are neighbors, spaces s2 and s0 are not neighbors, spaces s2 and s1 are not neighbors, there is a connection between locations l0 and l1, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c5, vehicle v0 does not contain space s0, vehicle v0 does not contain space s2, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v0 is not present at location l1, vehicle v1 does not contain cargo c0, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c6, vehicle v1 does not contain space s0, vehicle v1 does not have space s1, vehicle v1 has space s2, vehicle v1 is at location l1 and vehicle v1 is not at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, if cargo c0 is loaded into vehicle v0, which has spaces s2 and s1, at location l0, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Furthermore, cargo c8 and c9 are located at l1. Fuel f3 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. Locations l0 and l1 are connected. Vehicle v0 contains space s2 and is situated at l1. Similarly, vehicle v1 also contains space s2 and is situated at l1."}
{"question_id": "f5ad6059-84bc-46bc-8930-b575534dd34c", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v1, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is not in vehicle v1, cargo c3 is not situated at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c5 is not situated at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not located in vehicle v0, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not located in vehicle v0, cargo c7 is not located in vehicle v1, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not located in vehicle v1, cargo c9 is not situated at location l0, fuel f2 does not exist in location l0, fuel f6 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f8, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f4, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f8, location l0 does not have fuel f3, location l0 does not have fuel f7, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f7, location l1 does not have fuel f0, location l1 does not have fuel f3, location l1 does not have fuel f4, location l1 does not have fuel f8, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c5, vehicle v0 does not contain cargo c8, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s1, vehicle v0 is not at location l0, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c9, vehicle v1 does not have space s0 and vehicle v1 is not situated at location l1", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, where the fuel levels are f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, with fuel levels f3 and f2, to location l1. Upon arrival at location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. Finally, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0, resulting in the current state. In this state, if vehicle v0 moves from location l0, with fuel levels f2 and f1, to location l1, what are all the valid properties of the state that involve negations? If there are none, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "c35a4b51-53eb-4b1d-8945-10660979f014", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, if cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l0, cargo c5 is situated at location l0, cargo c6 is at location l0, cargo c7 is present at location l1, cargo c8 is present at location l0, fuel f0 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l1 has a fuel-level of f2, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, then cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Next, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, at location l0. Vehicle v0 then proceeds to location l1, which has fuel levels f4 and f3, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0, at location l1. This is followed by vehicle v0 moving to location l0, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1, at location l0. The sequence continues with vehicle v0 moving to location l1, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Vehicle v0 then relocates to location l0, which has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1, at location l0. Next, vehicle v0 moves to location l1, which has fuel levels f2 and f1, and cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0, at location l1. Vehicle v0 then proceeds to location l0, which has fuel levels f4 and f3, and cargo c4 is unloaded from vehicle v0, releasing spaces s0 and s1, at location l0. The sequence concludes with vehicle v0 moving to location l1, which has fuel levels f1 and f0, and cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1, and finally, vehicle v0 relocates to location l0, which has fuel levels f3 and f2. In this final state, if cargo c6 is unloaded from vehicle v0, occupying spaces s0 and s1, at location l0, what are all the valid properties of the state that do not involve negations? Provide 'None' if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is equipped with fuel f5, locations l0 and l1 are connected, location l1 is equipped with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is situated at location l1."}
{"question_id": "c710512b-e7fa-4d02-a72b-204dc2cfd0ed", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, if cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c1 is at location l0, cargo c10 is at location l0, cargo c2 is at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l1, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 has space s1 and vehicle v0 is at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and then vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. Next, at location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1, and then vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1. Subsequently, at location l1, cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, if cargo c6 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found in l1, and fuel f7 is found in l0. Fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, and fuel level f7 is adjacent to fuel level f8. Additionally, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, and fuel levels f5 and f6 are adjacent. Locations l0 and l1 are interconnected, and space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently at location l1."}
