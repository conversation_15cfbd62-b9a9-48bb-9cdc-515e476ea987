{"question_id": "44ab3a1a-5cc5-45d0-8e63-86ed281014b6", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "satellite0 turns to star12 from groundstation2, from groundstation2, satellite0 turns to phenomenon14, satellite0 turns from groundstation2 to planet11, satellite1 turns from planet13 to phenomenon14, satellite0 turns from groundstation2 to groundstation6, satellite0 turns to groundstation8 from groundstation2, from groundstation2, satellite0 turns to groundstation1, from planet13, satellite1 turns to groundstation9, satellite0 turns from groundstation2 to groundstation0, satellite1 turns to planet11 from planet13, satellite0 turns to star3 from groundstation2, instrument4 that is on satellite1 is turned on, satellite1 turns from planet13 to groundstation2, on satellite0, instrument1 is switched off, satellite0 turns from groundstation2 to planet13, satellite1 turns from planet13 to star10, satellite0 turns to groundstation5 from groundstation2, from planet13, satellite1 turns to groundstation0, satellite0 turns from groundstation2 to groundstation9, satellite1 turns from planet13 to groundstation1, satellite0 turns to phenomenon15 from groundstation2, satellite0 turns to groundstation7 from groundstation2, satellite1 turns to phenomenon15 from planet13, from groundstation2, satellite0 turns to star10, from planet13, satellite1 turns to groundstation8, satellite1 turns from planet13 to groundstation6, satellite1 turns to star3 from planet13, satellite0 turns to star4 from groundstation2, satellite1 turns to star16 from planet13, satellite1 turns from planet13 to star4, satellite1 turns from planet13 to groundstation5, satellite1 turns from planet13 to groundstation7, satellite1 turns from planet13 to star12 and from groundstation2, satellite0 turns to star16", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, enumerate all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, which it supports image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4. Instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "239b466a-ce75-4193-b2d2-aa72c9890691", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, from groundstation2, satellite0 turns to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from star10, satellite0 turns to groundstation2, satellite0 turns from star10 to groundstation6, satellite0 turns to phenomenon14 from star10, satellite1 turns from planet13 to phenomenon14, satellite0 turns from star10 to groundstation1, satellite0 turns to star16 from star10, from star10, satellite0 turns to groundstation0, satellite0 turns from star10 to groundstation7, from planet13, satellite1 turns to groundstation9, satellite0 turns to star4 from star10, instrument1 which is on satellite0 takes an image of star10 in image6, satellite1 turns from planet13 to planet11, instrument4 on satellite1 is switched on, satellite1 turns to groundstation2 from planet13, satellite0 turns to groundstation9 from star10, satellite0 turns from star10 to planet13, image of star10 is taken with instrument1 on satellite0 in image5, on satellite0, instrument1 is switched off, satellite1 turns to star10 from planet13, from star10, satellite0 turns to planet11, satellite1 turns from planet13 to groundstation0, satellite1 turns to groundstation1 from planet13, satellite1 turns to phenomenon15 from planet13, from planet13, satellite1 turns to groundstation8, from star10, satellite0 turns to star12, satellite1 turns from planet13 to groundstation6, satellite1 turns from planet13 to star3, satellite1 turns from planet13 to star16, from planet13, satellite1 turns to star4, from planet13, satellite1 turns to groundstation5, from planet13, satellite1 turns to groundstation7, satellite1 turns from planet13 to star12, satellite0 turns from star10 to groundstation5, image of star10 is taken with instrument1 on satellite0 in spectrograph2, satellite0 turns to phenomenon15 from star10, satellite0 turns from star10 to groundstation8 and satellite0 turns to star3 from star10", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 is activated on satellite0, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, then satellite0 reorients from groundstation0 to planet11, captures an image of planet11 using instrument1 on satellite0 and stores it in image5 and image6, reorients from planet11 to planet13, captures an image of planet13 using instrument1 on satellite0 and stores it in image5, captures another image of planet13 using instrument1 on satellite0 and stores it in spectrograph2, and finally reorients from planet13 to star10 to reach the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, which it supports image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4. Instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "1c31642c-c98e-401f-8cf5-6739b5033df2", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "None", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, and instrument2 is on board satellite1. Instrument1 supports spectrograph1, while instrument2 supports both spectrograph0 and spectrograph2. Instrument3, which is on board satellite1, is calibrated for star6 and supports spectrograph1. The power status of the satellites indicates that satellite0 has power available and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "acbb02e0-a309-4862-a93c-d0240a1df785", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns from groundstation2 to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, from planet13, satellite0 turns to star10, satellite0's instrument1 takes an image of star10 in image6, image of star10 is taken with instrument1 on satellite0 in spectrograph2, instrument1 on satellite0 is switched off, on satellite0, instrument2 is switched on, from star10, satellite0 turns to star4, calibration of instrument2 which is on satellite0 to star4 is complete, satellite0 turns from star4 to star16, image of star16 is taken with instrument2 on satellite0 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "satellite0 turns to star3 from star16, from star16, satellite0 turns to groundstation2, from star16, satellite0 turns to groundstation5, satellite1 turns to phenomenon14 from planet13, satellite0 turns from star16 to groundstation1, from planet13, satellite1 turns to groundstation9, from star16, satellite0 turns to planet13, satellite0 turns to groundstation7 from star16, from star16, satellite0 turns to planet11, from planet13, satellite1 turns to planet11, on satellite1, instrument4 is switched on, satellite1 turns to groundstation2 from planet13, satellite0 turns to phenomenon14 from star16, satellite0 turns to groundstation8 from star16, satellite1 turns from planet13 to star10, satellite1 turns to groundstation0 from planet13, satellite1 turns to groundstation1 from planet13, satellite1 turns to phenomenon15 from planet13, instrument2 that is on satellite0 is turned on, satellite1 turns to groundstation8 from planet13, satellite1 turns to groundstation6 from planet13, satellite1 turns to star3 from planet13, satellite0 turns to star10 from star16, satellite1 turns from planet13 to star16, satellite0 turns to phenomenon15 from star16, satellite0 turns from star16 to star12, instrument0 on satellite0 is switched on, satellite0 turns from star16 to groundstation0, from planet13, satellite1 turns to star4, satellite1 turns to groundstation5 from planet13, satellite1 turns from planet13 to groundstation7, satellite1 turns from planet13 to star12, satellite0 turns to groundstation6 from star16, instrument3 that is on satellite0 is turned on, satellite0 turns from star16 to star4, on satellite0, instrument1 is switched on and from star16, satellite0 turns to groundstation9", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 also captures an image of planet11 and stores it in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 captures an image of planet13 and stores it in spectrograph2, satellite0 then reorients from planet13 to star10, an image of star10 is captured using instrument1 on satellite0 and stored in image6, another image of star10 is captured using instrument1 on satellite0 and stored in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated with respect to star4, satellite0 then reorients from star4 to star16, an image of star16 is captured using instrument2 on satellite0 and stored in image0, and finally instrument2 on satellite0 is deactivated to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, providing support for image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4, while instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "d8303a67-37fa-455b-8605-130700c215a6", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, satellite1 turns from star3 to star1, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, image of phenomenon10 is taken with instrument1 on satellite1 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, image of phenomenon5 is taken with instrument1 on satellite1 in image5, satellite1 turns from phenomenon5 to phenomenon7, satellite1 turns to phenomenon9 from star6, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, from phenomenon7, satellite1 turns to phenomenon9, satellite1's instrument1 takes an image of phenomenon9 in image5, image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns from phenomenon9 to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "satellite1 turns to phenomenon9 from star6", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite1, instrument1 is activated, then satellite1 reorients from star3 to star1, followed by the calibration of instrument1 on satellite1 to star1. Next, satellite1 turns from star1 to phenomenon10, and an image of phenomenon10 is captured using instrument1 on satellite1, stored in image5. Additionally, satellite1's instrument1 captures an image of phenomenon10 in spectrograph3. Satellite1 then reorients from phenomenon10 to phenomenon11, and an image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1. The satellite then turns from phenomenon11 to phenomenon5, and instrument1 on satellite1 captures an image of phenomenon5 in image4. Another image of phenomenon5 is taken with instrument1 on satellite1 in image5. Satellite1 then turns from phenomenon5 to phenomenon7, and then from star6 to phenomenon9. Instrument1 on satellite1 captures an image of phenomenon7 in image4. From phenomenon7, satellite1 turns to phenomenon9, and satellite1's instrument1 captures an image of phenomenon9 in image5. Furthermore, an image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1. Finally, satellite1 turns from phenomenon9 to planet8, and instrument1 on satellite1 captures an image of planet8 in image5, resulting in the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and image2 is also supported by instrument0. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "d1cfe387-b166-41c3-9b06-55c60882012d", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: satellite0 turns from star6 to phenomenon7 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from star6, satellite0 turns to phenomenon7", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: satellite0 transitions from star6 to phenomenon7 to achieve the current state. What is the first action in the sequence that cannot be executed? If none exist, write None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5. Satellite0 has instrument0 onboard and is functioning with available power. Satellite1, which also has instrument1 onboard, is operational with power and is directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "9776fb38-1605-411a-963a-467809a2869e", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from phenomenon10, satellite1 turns to groundstation2, satellite1 turns from phenomenon10 to planet14, satellite0 turns to star1 from groundstation3, satellite0 turns to planet12 from groundstation3, from groundstation3, satellite0 turns to star8, satellite1 turns to star8 from phenomenon10, satellite0 turns to planet11 from groundstation3, satellite0 turns from groundstation3 to groundstation4, satellite1 turns to groundstation0 from phenomenon10, from phenomenon10, satellite1 turns to star15, satellite0 turns to phenomenon10 from groundstation3, from phenomenon10, satellite1 turns to star1, instrument2 on satellite0 is switched on, from phenomenon10, satellite1 turns to planet11, from groundstation3, satellite0 turns to star9, from groundstation3, satellite0 turns to phenomenon17, satellite1 turns to star6 from phenomenon10, from phenomenon10, satellite1 turns to planet12, satellite1 turns to groundstation4 from phenomenon10, satellite1 turns to groundstation7 from phenomenon10, instrument1 that is on satellite0 is turned on, satellite1 turns to planet13 from phenomenon10, satellite1 turns from phenomenon10 to star9, satellite1 turns from phenomenon10 to groundstation5, on satellite0, instrument0 is switched on, satellite0 turns to planet14 from groundstation3, from groundstation3, satellite0 turns to planet13, satellite0 turns to groundstation0 from groundstation3, instrument3 on satellite1 is switched off, satellite0 turns from groundstation3 to groundstation7, satellite1 turns to phenomenon17 from phenomenon10, from groundstation3, satellite0 turns to phenomenon16, from groundstation3, satellite0 turns to star6, satellite0 turns from groundstation3 to star15, satellite1 turns from phenomenon10 to groundstation3, from groundstation3, satellite0 turns to groundstation2, satellite0 turns to groundstation5 from groundstation3 and satellite1 turns to phenomenon16 from phenomenon10", "plan_length": 1, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument3 on satellite1 is activated to achieve the current state. Once in this state, list all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and also has instrument0. Satellite0 has available power. Satellite1 carries instrument3 on board, has available power, and is currently pointing towards phenomenon10."}
{"question_id": "e5222a31-e54e-45e5-81c9-d9ad75a3b290", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "on satellite1, instrument3 is switched off, satellite1 turns to star8 from groundstation4, from groundstation4, satellite1 turns to groundstation3, satellite0 turns from star1 to star8, satellite1 turns to groundstation5 from groundstation4, from star1, satellite0 turns to star7, satellite1 turns from groundstation4 to star6, satellite1 turns from groundstation4 to star16, from star1, satellite0 turns to star10, from groundstation4, satellite1 turns to star7, satellite1 turns to phenomenon15 from groundstation4, satellite1 turns to star1 from groundstation4, from groundstation4, satellite1 turns to star10, from star1, satellite0 turns to planet14, satellite1 turns from groundstation4 to star12, satellite0 turns from star1 to groundstation5, satellite1 turns to star13 from groundstation4, from groundstation4, satellite1 turns to groundstation9, from groundstation4, satellite1 turns to star0, instrument1 that is on satellite0 is turned on, satellite1 turns to star11 from groundstation4, satellite0 turns to groundstation4 from star1, satellite0 turns from star1 to phenomenon15, from star1, satellite0 turns to groundstation9, from star1, satellite0 turns to star12, satellite1 turns from groundstation4 to groundstation2, satellite1 turns from groundstation4 to planet14, from star1, satellite0 turns to star6, satellite0 turns to groundstation3 from star1, satellite0 turns from star1 to star13, satellite0 turns from star1 to star16, satellite0 turns to star11 from star1, from star1, satellite0 turns to groundstation2, instrument0 that is on satellite0 is turned on and satellite0 turns to star0 from star1", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, list all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, and instrument2 supports both spectrograph0 and spectrograph2. Moreover, instrument3 is calibrated for star6, instrument3 is on board satellite1, and instrument3 supports spectrograph1. The power status of the satellites is as follows: satellite0 has power available, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "2e6f04c0-3f4f-4bcf-9025-ec94d1464fcc", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 is calibrated on satellite1 to star1, from star1, satellite1 turns to phenomenon10, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1 turns from phenomenon11 to phenomenon5 and satellite1's instrument1 takes an image of phenomenon5 in image4 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "satellite0 turns to groundstation4 from phenomenon5, from phenomenon5, satellite1 turns to phenomenon10, satellite1 turns to planet8 from phenomenon5, satellite1 turns from phenomenon5 to groundstation0, satellite0 turns to planet8 from phenomenon5, from phenomenon5, satellite1 turns to star1, from phenomenon5, satellite1 turns to groundstation4, satellite1 turns from phenomenon5 to phenomenon9, satellite1's instrument1 takes an image of phenomenon5 in image2, instrument1 that is on satellite1 is turned off, satellite1 turns from phenomenon5 to groundstation2, satellite0 turns to phenomenon7 from phenomenon5, from phenomenon5, satellite1 turns to phenomenon11, satellite1 turns from phenomenon5 to phenomenon7, satellite1's instrument1 takes an image of phenomenon5 in image4, from phenomenon5, satellite0 turns to star3, from phenomenon5, satellite1 turns to star6, from phenomenon5, satellite0 turns to phenomenon9, satellite0 turns to phenomenon10 from phenomenon5, instrument1 which is on satellite1 takes an image of phenomenon5 in spectrograph3, satellite0 turns to phenomenon11 from phenomenon5, satellite1's instrument1 takes an image of phenomenon5 in image0, satellite0 turns to star6 from phenomenon5, satellite1's instrument1 takes an image of phenomenon5 in image5, from phenomenon5, satellite0 turns to groundstation2, from phenomenon5, satellite1 turns to star3, satellite0 turns from phenomenon5 to groundstation0, instrument0 that is on satellite0 is turned on, instrument1 which is on satellite1 takes an image of phenomenon5 in spectrograph1 and satellite0 turns from phenomenon5 to star1", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 reorients from star1 to phenomenon10, and instrument1 on satellite1 captures an image of phenomenon10 in image5 and spectrograph3, subsequently, satellite1 reorients from phenomenon10 to phenomenon11, and instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1, after that, satellite1 reorients from phenomenon11 to phenomenon5, and finally, instrument1 on satellite1 captures an image of phenomenon5 in image4, resulting in the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Additionally, instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5, and it has instrument0 onboard with available power. Satellite1, which also has power, is carrying instrument1 and is directed towards star3. Furthermore, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "1c8e94aa-2b16-4766-a4f0-358eba58d872", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: satellite0 turns from planet11 to groundstation1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "satellite0 turns to groundstation1 from planet11", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: satellite0 turns from planet11 to groundstation1 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Instrument1 supports image5 and image6. Infrared7 is compatible with instrument4. Instrument0 has been calibrated for groundstation7 and star3, and it is installed on satellite0, which also supports image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4. Instrument3 supports image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Additionally, thermograph3 is compatible with instrument3."}
{"question_id": "55c6b196-6288-407b-829a-26802b875f34", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, instrument1 that is on satellite1 is calibrated to groundstation3, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns from star6 to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "calibration of instrument1 which is on satellite1 to groundstation3 is complete", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, instrument1 on satellite1 is calibrated with groundstation3, the calibration of instrument3 on satellite1 to star6 is finalized, satellite1 reorients from star6 to planet14, an image of planet14 is captured by instrument3 on satellite1 using spectrograph1, satellite1 then reorients from planet14 to star10, an image of star10 is captured by instrument3 on satellite1 using spectrograph1, and finally, satellite1 reorients from star10 to star12, capturing an image of star12 using spectrograph1 on instrument3 to reach the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, and instrument2 is on board satellite1. Instrument1 supports spectrograph1, while instrument2 supports both spectrograph0 and spectrograph2. Instrument3, which is on board satellite1, is calibrated for star6 and supports spectrograph1. The power status of the satellites indicates that satellite0 has power available and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks reveal that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "5b58b315-ad23-4275-ad03-84bba58f242b", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns from phenomenon10 to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1's instrument3 takes an image of planet11 in image3 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "satellite1 turns to star9 from planet11, satellite1 turns from planet11 to star15, from groundstation3, satellite0 turns to star1, image of planet11 is taken with instrument3 on satellite1 in image0, from groundstation3, satellite0 turns to planet12, from planet11, satellite1 turns to star8, satellite1 turns to phenomenon16 from planet11, from planet11, satellite1 turns to phenomenon17, satellite1 turns to groundstation5 from planet11, satellite0 turns from groundstation3 to star8, satellite1 turns from planet11 to groundstation2, satellite0 turns to planet11 from groundstation3, satellite0 turns from groundstation3 to groundstation4, from planet11, satellite1 turns to star6, satellite0 turns to phenomenon10 from groundstation3, from planet11, satellite1 turns to groundstation4, satellite0 turns from groundstation3 to star9, from planet11, satellite1 turns to star1, satellite1 turns from planet11 to planet13, instrument0 on satellite0 is switched off, satellite1's instrument3 takes an image of planet11 in image2, from groundstation3, satellite0 turns to phenomenon17, satellite1 turns to planet12 from planet11, satellite1 turns to planet14 from planet11, satellite1's instrument3 takes an image of planet11 in image3, from planet11, satellite1 turns to groundstation0, satellite1 turns to groundstation3 from planet11, satellite1 turns to phenomenon10 from planet11, satellite0 turns to planet14 from groundstation3, satellite0 turns from groundstation3 to planet13, satellite0 turns to groundstation0 from groundstation3, from planet11, satellite1 turns to groundstation7, instrument3 that is on satellite1 is turned off, satellite0 turns to groundstation7 from groundstation3, satellite0 turns to phenomenon16 from groundstation3, from groundstation3, satellite0 turns to star6, from groundstation3, satellite0 turns to star15, satellite0 turns to groundstation2 from groundstation3 and from groundstation3, satellite0 turns to groundstation5", "plan_length": 10, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured using instrument3 on satellite1 and stored in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 using instrument3 on satellite1 and storing it in image3, resulting in the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 also carries instrument3 on board, has power available, and is currently pointing to phenomenon10."}
{"question_id": "4abb6844-d9c1-49e2-9ce0-6f09bc092ae9", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from planet12, satellite1 turns to groundstation7 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from planet12, satellite1 turns to groundstation7", "plan_length": 1, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: starting from planet12, satellite1 turns to groundstation7 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and is also supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and is supported by instrument3. Infrared1 is compatible with instrument0 and is supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and also has instrument0. Satellite0 has power available. Satellite1 carries instrument3 on board, has power available, and is currently pointing towards phenomenon10."}
{"question_id": "cf8ad60d-7d82-42e8-88d0-272c2b34a391", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 on satellite0 is switched on, satellite1 turns from groundstation4 to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns to planet14 from star6, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12, instrument3 which is on satellite1 takes an image of star12 in spectrograph1, from star12, satellite1 turns to star0, satellite0 turns from star1 to groundstation2, calibration of instrument0 which is on satellite0 to groundstation2 is complete, satellite0 turns from groundstation2 to phenomenon15, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, from phenomenon15, satellite0 turns to star11, instrument0 which is on satellite0 takes an image of star11 in thermograph4, from star11, satellite0 turns to star13 and image of star13 is taken with instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "instrument3 that is on satellite1 is turned off, satellite1 turns to groundstation2 from star0, satellite1 turns from star0 to star6, satellite0 turns from star13 to star10, satellite0 turns to star0 from star13, satellite1 turns to groundstation4 from star0, satellite1 turns to star10 from star0, instrument3 which is on satellite1 takes an image of star0 in spectrograph1, instrument0 that is on satellite0 is turned off, satellite1 turns to star7 from star0, satellite1 turns from star0 to star16, from star13, satellite0 turns to star12, satellite1 turns from star0 to groundstation5, instrument0 which is on satellite0 takes an image of star13 in thermograph4, from star0, satellite1 turns to star12, from star0, satellite1 turns to star1, satellite1 turns to star13 from star0, satellite0 turns to star11 from star13, instrument0 which is on satellite0 takes an image of star13 in spectrograph0, satellite0 turns to star16 from star13, from star13, satellite0 turns to star8, satellite0 turns from star13 to groundstation9, satellite0 turns to planet14 from star13, satellite1 turns to planet14 from star0, satellite1 turns from star0 to star8, from star13, satellite0 turns to star7, satellite1 turns from star0 to groundstation3, from star13, satellite0 turns to phenomenon15, satellite0 turns from star13 to star1, satellite1 turns from star0 to groundstation9, from star13, satellite0 turns to groundstation3, from star0, satellite1 turns to phenomenon15, from star0, satellite1 turns to star11, satellite0 turns to star6 from star13, from star13, satellite0 turns to groundstation4, from star13, satellite0 turns to groundstation2, satellite0 turns from star13 to groundstation5 and satellite1's instrument3 takes an image of star0 in spectrograph2", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 completes calibration to star6, satellite1 then reorients from star6 to planet14, instrument3 on satellite1 captures an image of planet14 using spectrograph1, satellite1 reorients from planet14 to star10, instrument3 on satellite1 captures an image of star10 using spectrograph1, satellite1 reorients from star10 to star12, instrument3 on satellite1 captures an image of star12 using spectrograph1, satellite1 then reorients from star12 to star0, satellite0 reorients from star1 to groundstation2, instrument0 on satellite0 completes calibration to groundstation2, satellite0 reorients from groundstation2 to phenomenon15, an image of phenomenon15 is captured using instrument0 on satellite0 in spectrograph0, satellite0 reorients from phenomenon15 to star11, instrument0 on satellite0 captures an image of star11 using thermograph4, and finally, satellite0 reorients from star11 to star13, capturing an image of star13 using instrument0 on satellite0 in spectrograph0, resulting in the current state. In this state, list all possible executable actions. If there are none, write None.", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, and instrument2 supports both spectrograph0 and spectrograph2. Moreover, instrument3 is calibrated for star6, instrument3 is on board satellite1, and instrument3 supports spectrograph1. The power status of the satellites is as follows: satellite0 has power available, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "2047c9f3-873e-4bfb-86fb-3d96ad708123", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns from groundstation2 to groundstation0, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, instrument0 is calibrated on satellite1 to groundstation7, instrument1 which is on satellite0 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "instrument0 is calibrated on satellite1 to groundstation7", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite0, instrument1 is activated, then satellite0 reorients from groundstation2 to groundstation0, followed by the calibration of instrument1 on satellite0 relative to groundstation0. Next, satellite0 reorients from groundstation0 to planet11, and two images of planet11 are captured using instrument1 on satellite0, stored in image5 and image6 respectively. Meanwhile, instrument0 is calibrated on satellite1 relative to groundstation7. Subsequently, instrument1 on satellite0 captures an image of planet13 in image5, and another image of planet13 is taken using instrument1 on satellite0 in spectrograph2. Finally, satellite0 transitions from planet13 to star10 to reach the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, providing support for image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4, while instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "a9788c6f-bb14-46e8-8296-54d81c890f9c", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "None", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is activated to achieve the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Additionally, instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 is equipped with instrument0, and it has a power supply. Satellite1, which carries instrument1, also has power and is currently directed towards star3. Furthermore, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "661f7883-e64b-4cba-bdbf-cb16ca3d2a4b", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "satellite0 turns to groundstation4 from phenomenon5, satellite0 turns to planet8 from phenomenon5, satellite1 turns to groundstation2 from star3, satellite1 turns from star3 to phenomenon9, satellite1 turns to star1 from star3, on satellite1, instrument1 is switched off, satellite0 turns to phenomenon7 from phenomenon5, satellite0 turns from phenomenon5 to star3, satellite0 turns to phenomenon9 from phenomenon5, satellite1 turns to phenomenon5 from star3, satellite0 turns to phenomenon10 from phenomenon5, satellite1 turns from star3 to phenomenon11, satellite0 turns to phenomenon11 from phenomenon5, from star3, satellite1 turns to groundstation0, from phenomenon5, satellite0 turns to star6, from phenomenon5, satellite0 turns to groundstation2, from phenomenon5, satellite0 turns to groundstation0, satellite1 turns from star3 to groundstation4, from star3, satellite1 turns to phenomenon7, on satellite0, instrument0 is switched on, satellite1 turns to phenomenon10 from star3, from phenomenon5, satellite0 turns to star1, satellite1 turns from star3 to planet8 and satellite1 turns from star3 to star6", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is activated to achieve the current state. In this state, list all possible actions that can be executed. Write None if there are no actions available.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 is equipped with instrument0 and has a power supply. Satellite1, which also has power, is carrying instrument1 and is currently directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
