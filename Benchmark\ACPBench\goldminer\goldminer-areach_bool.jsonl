{"id": -5185900214886679112, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f3-1f, f1-2f, f1-1f, f3-3f, and f1-3f. The following locations have soft rock: f2-2f, f2-3f, f0-2f, f3-2f, and f0-3f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Is it possible to transition to a state where the action \"pick up gold at loc f2-1f\" can be applied?", "answer": "no"}
{"id": -2040999324568741556, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-2f, f3-3f, and f1-3f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, and f3-2f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the action \"detonate the bomb at location f0-3f connected to location f1-3f\" can be applied?", "answer": "no"}
{"id": 7988110880572192527, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-2f, f1-1f, f3-3f, and f1-3f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, f0-2f, f3-2f, f0-1f, and f0-3f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Is it possible to transition to a state where the action \"pick up gold at loc f1-1f\" can be applied?", "answer": "no"}
{"id": 612869136284885273, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, f1-2f, and f0-2f. The gold is at f1-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the action \"retrieve gold from location f1-2f\" can be applied?", "answer": "no"}
{"id": -4298082174934767471, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-3f and is holding gold. The following locations have hard rock: f1-1f, f1-4f, and f1-3f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, and f2-4f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the action \"connect location f0-1f to location f0-0f\" can be applied?", "answer": "no"}
{"id": 5161508049851913784, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a bomb. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-1f location.", "question": "Is it possible to transition to a state where the action \"travel from location f1-0f to location f0-0f\" can be applied?", "answer": "yes"}
{"id": -5805041894911372400, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-4f, and f1-3f. The following locations have soft rock: f0-4f, f2-1f, f2-2f, f2-3f, f1-2f, and f2-4f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the action \"move from loc f0-2f to loc f0-3f\" can be applied?", "answer": "yes"}
{"id": -3674750475377599172, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the action \"connect location f0-0f to location f1-0f\" can be applied?", "answer": "no"}
{"id": 8439843970908597774, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the action \"move from location f1-2f to location f1-1f\" can be applied?", "answer": "yes"}
{"id": -8042048943001215427, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f2-1f, f0-1f, f1-3f, f2-3f, f0-3f, f2-2f, and f1-2f. The gold is at f0-3f location. The laser is at f2-0f location.", "question": "Is it possible to transition to a state where the action \"trigger the explosion of the bomb at location f1-0f, which is connected to location f0-0f\" can be applied?", "answer": "no"}
