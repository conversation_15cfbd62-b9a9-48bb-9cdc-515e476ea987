{"id": 8157576587122823653, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball2. Additionally, ball4 is at room1, ball3 is at room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (carry robot1 ball1 left1) (carry robot1 ball2 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -6285509328285300784, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball1. Additionally, ball3 and ball2 are at room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball2 room2) (at ball3 room2) (at-robby robot1 room2) (carry robot1 ball1 right1) (carry robot1 ball4 left1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -7313142837650435376, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball12, ball6, ball13, ball3, ball15, and ball1 are at room2, ball5 and ball7 are at room3, ball10, ball8, ball4, ball2, ball14, ball11, and ball9 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at ball8 room1) (at ball9 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 507109648950255215, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball6 is at room2, ball5 and ball1 are at room3, ball7, ball4, ball2, and ball3 are at room1. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 1082292025470882426, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room7, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room4, ball3 is at room10. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball3 room10) (at-robby robot1 room7) (carry robot1 ball2 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": 8768732294326454174, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball3. Additionally, ball4 is at room5, ball2 is at room3, ball1 is at room2. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball2 room3) (at ball4 room5) (at-robby robot1 room1) (carry robot1 ball3 right1) (free robot1 left1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 5411881102700084534, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball2. Additionally, ball4 is at room5, ball1 is at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball4 room5) (at-robby robot1 room3) (carry robot1 ball2 right1) (carry robot1 ball3 left1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 5799009648598760553, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball9, ball7, ball10, ball14, and ball11 are at room3, ball1, ball13, ball3, and ball12 are at room2, ball8, ball15, ball2, ball4, ball5, and ball6 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 1938112933055230712, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball15, and right gripper is carrying the ball ball6. Additionally, ball12, ball13, ball3, and ball1 are at room2, ball5 and ball7 are at room3, ball10, ball8, ball4, ball2, ball14, ball11, and ball9 are at room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room3) (at ball7 room3) (at ball8 room1) (at ball9 room1) (at-robby robot1 room2) (carry robot1 ball15 left1) (carry robot1 ball6 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 6982467069141560388, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room4, right gripper is free, and left gripper is carrying the ball ball4. Additionally, ball1 and ball3 are at room4, ball2 is at room3. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room4) (at ball2 room3) (at ball3 room4) (at-robby robot1 room4) (carry robot1 ball4 left1) (free robot1 right1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 6944398301684749261, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball3. Additionally, ball4 is at room3, ball2 is at room1. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball2 room1) (at ball4 room3) (at-robby robot1 room2) (carry robot1 ball1 left1) (carry robot1 ball3 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 4016427767252625501, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room4, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball4 and ball1 are at room1, ball2 is at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room1) (at ball2 room2) (at ball4 room1) (at-robby robot1 room4) (carry robot1 ball3 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -8680031870502993317, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room5 and both grippers are free. Additionally, ball3 is at room5, ball2 and ball1 are at room3, ball4 is at room6. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room3) (at ball2 room3) (at ball3 room5) (at ball4 room6) (at-robby robot1 room5) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 1710439087808246981, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room6, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball2 and ball3 are at room3, ball4 is at room6. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball2 room3) (at ball3 room3) (at ball4 room6) (at-robby robot1 room6) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 1093569118892229019, "group": "reachable_action_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room4, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball1. Additionally, ball4 is at room1, ball3 is at room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": [], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball3 room3) (at ball4 room1) (at-robby robot1 room4) (carry robot1 ball1 right1) (carry robot1 ball2 left1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
