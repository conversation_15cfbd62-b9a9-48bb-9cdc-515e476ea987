{"id": 8085264433634198793, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 7 depots, 9 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet8, pallet7, pallet6, pallet4, pallet2, pallet5, pallet0, pallet1, and crate1 are clear; hoist8, hoist3, hoist7, hoist1, hoist0, hoist4, hoist6, and hoist2 are available; pallet2 is at depot2, pallet6 is at depot6, crate1 is at depot3, hoist5 is at depot5, truck0 is at depot5, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, pallet8 is at distributor1, pallet0 is at depot0, pallet3 is at depot3, pallet1 is at depot1, hoist6 is at depot6, pallet7 is at distributor0, hoist3 is at depot3, hoist0 is at depot0, hoist8 is at distributor1, hoist4 is at depot4, hoist1 is at depot1, truck1 is at depot4, and hoist7 is at distributor0; crate1 is on pallet3; hoist5 is lifting crate0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. hoist7 is lifting crate0 and crate0 is on pallet3. B. pallet1 is at depot4. C. pallet4 is at depot0. D. truck0 is at depot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["hoist7 is lifting crate0 and crate0 is on pallet3", "pallet1 is at depot4", "pallet4 is at depot0", "truck0 is at depot1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -836593609968258800, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist3, hoist1, and hoist2 are available; hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at depot0, pallet0 is at depot0, truck1 is at distributor1, hoist1 is at depot1, pallet1 is at depot1, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate0 is in truck0; hoist0 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. truck1 is at distributor0 and truck0 is at depot1. B. crate0 is at depot1 and crate0 is at distributor1. C. pallet2 is at depot1. D. pallet1 is at depot1 and hoist2 is at depot0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at distributor0 and truck0 is at depot1", "crate0 is at depot1 and crate0 is at distributor1", "pallet2 is at depot1", "pallet1 is at depot1 and hoist2 is at depot0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 7497973151672570125, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 hoists, 3 depots, 5 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, crate0, pallet4, pallet0, and crate1 are clear; hoist3, hoist1, hoist0, hoist4, and hoist2 are available; pallet2 is at depot2, hoist2 is at depot2, truck0 is at distributor1, pallet0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet1 is at depot1, crate1 is at depot2, pallet4 is at distributor1, hoist0 is at depot0, truck1 is at depot2, hoist4 is at distributor1, hoist1 is at depot1, and pallet3 is at distributor0; crate0 is on pallet1 and crate1 is on pallet2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. pallet1 is clear and truck1 is at depot1. B. pallet4 is at depot1. C. hoist2 is at distributor0 and crate0 is clear. D. crate1 is in truck1 and crate1 is on crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pallet1 is clear and truck1 is at depot1", "pallet4 is at depot1", "hoist2 is at distributor0 and crate0 is clear", "crate1 is in truck1 and crate1 is on crate0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 7818126190465323287, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 10 depots, 12 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet11, pallet7, pallet8, pallet6, pallet3, pallet10, pallet4, pallet9, pallet2, pallet5, pallet0, and pallet1 are clear; hoist3, hoist7, hoist11, hoist1, hoist0, hoist10, hoist5, hoist4, hoist6, and hoist2 are available; pallet2 is at depot2, truck1 is at depot8, pallet8 is at depot8, pallet6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, hoist8 is at depot8, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, hoist11 is at distributor1, pallet11 is at distributor1, pallet0 is at depot0, hoist9 is at depot9, pallet3 is at depot3, truck0 is at depot9, pallet1 is at depot1, hoist6 is at depot6, hoist7 is at depot7, hoist3 is at depot3, hoist10 is at distributor0, hoist0 is at depot0, pallet9 is at depot9, hoist4 is at depot4, pallet7 is at depot7, and hoist1 is at depot1; hoist8 is lifting crate0 and hoist9 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. truck1 is at distributor1. B. pallet9 is at depot8. C. hoist1 is at distributor0. D. hoist8 is lifting crate0 and crate0 is at distributor1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at distributor1", "pallet9 is at depot8", "hoist1 is at distributor0", "hoist8 is lifting crate0 and crate0 is at distributor1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -8428628700310186481, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 10 depots, 12 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet11, pallet7, pallet8, pallet6, pallet3, pallet10, pallet4, pallet2, pallet5, pallet0, pallet1, and crate1 are clear; hoist8, hoist3, hoist7, hoist1, hoist0, hoist9, hoist10, hoist5, hoist4, hoist6, and hoist2 are available; pallet2 is at depot2, pallet8 is at depot8, pallet6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, hoist8 is at depot8, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, hoist11 is at distributor1, truck1 is at depot6, crate1 is at depot9, pallet11 is at distributor1, pallet0 is at depot0, hoist9 is at depot9, pallet3 is at depot3, pallet1 is at depot1, hoist6 is at depot6, hoist7 is at depot7, hoist3 is at depot3, truck0 is at depot8, hoist10 is at distributor0, hoist0 is at depot0, pallet9 is at depot9, hoist4 is at depot4, pallet7 is at depot7, and hoist1 is at depot1; crate1 is on pallet9; hoist11 is lifting crate0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. pallet10 is at depot5. B. truck1 is at depot0. C. hoist3 is at depot6. D. crate0 is at depot2 and hoist2 is at depot2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pallet10 is at depot5", "truck1 is at depot0", "hoist3 is at depot6", "crate0 is at depot2 and hoist2 is at depot2"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 8082057447230157368, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 10 depots, 12 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet7, pallet8, pallet6, pallet3, crate0, pallet10, pallet4, pallet9, pallet2, pallet5, pallet0, and pallet1 are clear; hoist8, hoist3, hoist7, hoist11, hoist1, hoist0, hoist9, hoist10, hoist5, hoist4, and hoist6 are available; pallet2 is at depot2, truck1 is at depot0, pallet8 is at depot8, pallet6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, hoist8 is at depot8, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, hoist11 is at distributor1, truck0 is at depot6, crate0 is at distributor1, pallet11 is at distributor1, pallet0 is at depot0, hoist9 is at depot9, pallet3 is at depot3, pallet1 is at depot1, hoist6 is at depot6, hoist7 is at depot7, hoist3 is at depot3, hoist10 is at distributor0, hoist0 is at depot0, pallet9 is at depot9, hoist4 is at depot4, pallet7 is at depot7, and hoist1 is at depot1; crate0 is on pallet11; hoist2 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. truck1 is at depot5 and truck1 is at depot6. B. truck1 is at depot6 and truck0 is at depot3. C. crate0 is clear and hoist9 is at depot4. D. pallet10 is at depot8 and pallet4 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at depot5 and truck1 is at depot6", "truck1 is at depot6 and truck0 is at depot3", "crate0 is clear and hoist9 is at depot4", "pallet10 is at depot8 and pallet4 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -6791540920004025164, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist3, hoist1, hoist0, and hoist2 are available; hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at depot0, pallet0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, truck1 is at distributor0, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate0 is in truck1 and crate1 is in truck1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. pallet3 is at distributor1 and hoist2 is at depot1. B. truck1 is at depot0 and truck0 is at depot1. C. hoist3 is at depot0. D. crate0 is on pallet1 and pallet1 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pallet3 is at distributor1 and hoist2 is at depot1", "truck1 is at depot0 and truck0 is at depot1", "hoist3 is at depot0", "crate0 is on pallet1 and pallet1 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -2339578857696258511, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist1, hoist0, and hoist2 are available; truck1 is at depot0, hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at distributor1, pallet0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate0 is in truck1; hoist3 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. hoist3 is available and truck0 is at depot1. B. hoist3 is at depot1 and hoist2 is at distributor0. C. truck1 is at distributor1 and truck1 is at distributor0. D. hoist2 is at depot0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["hoist3 is available and truck0 is at depot1", "hoist3 is at depot1 and hoist2 is at distributor0", "truck1 is at distributor1 and truck1 is at distributor0", "hoist2 is at depot0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -2316809582027835589, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 4 depots, 6 pallets, 3 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet4, pallet2, pallet5, pallet0, and pallet1 are clear; hoist0, hoist5, hoist4, and hoist2 are available; pallet2 is at depot2, hoist5 is at distributor1, truck1 is at depot0, hoist2 is at depot2, truck0 is at depot3, pallet0 is at depot0, pallet3 is at depot3, pallet4 is at distributor0, hoist1 is at depot1, hoist4 is at distributor0, pallet1 is at depot1, hoist3 is at depot3, hoist0 is at depot0, and pallet5 is at distributor1; crate1 is in truck0; hoist1 is lifting crate2 and hoist3 is lifting crate0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. crate2 is clear and crate2 is at depot1. B. crate1 is in truck0 and pallet0 is at depot2. C. truck0 is at depot3 and hoist4 is at distributor1. D. crate0 is on pallet5 and crate2 is on pallet5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate2 is clear and crate2 is at depot1", "crate1 is in truck0 and pallet0 is at depot2", "truck0 is at depot3 and hoist4 is at distributor1", "crate0 is on pallet5 and crate2 is on pallet5"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -9106507804115105684, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet1, and crate1 are clear; hoist3, hoist1, hoist0, and hoist2 are available; hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at depot0, crate1 is at depot0, truck1 is at depot1, pallet0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate1 is on pallet0; crate0 is in truck1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. crate0 is on pallet0 and pallet0 is clear. B. pallet0 is at distributor0. C. pallet0 is at distributor1. D. crate0 is clear and crate0 is on pallet1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate0 is on pallet0 and pallet0 is clear", "pallet0 is at distributor0", "pallet0 is at distributor1", "crate0 is clear and crate0 is on pallet1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
