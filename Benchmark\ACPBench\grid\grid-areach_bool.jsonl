{"id": 4495088736228089217, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-2f and is holding key0-0. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-2 is at position f1-4f.", "question": "Is it possible to transition to a state where the action \"pick up the key key0-2 at the current position place f4-2f and loose the key key0-2 being held\" can be applied?", "answer": "no"}
{"id": -3652889119381205262, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-2f and is holding key0-0. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the action \"transition from the current position f1-4f to the next position f0-4f\" can be applied?", "answer": "yes"}
{"id": 8447014087913446853, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-0 is at position f3-3f. Key key0-1 is at position f1-2f.", "question": "Is it possible to transition to a state where the action \"travel from the current position f0-3f to the next position f0-4f\" can be applied?", "answer": "yes"}
{"id": 6087420608423366634, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f3-3f and is holding key1-1. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-0f.", "question": "Is it possible to transition to a state where the action \"pick up the key key1-1 at the current position place f2-2f and loose the key key1-1 being held\" can be applied?", "answer": "no"}
{"id": -1152859031210628295, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-4f and is holding key0-1. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-0 is at position f0-1f. Key key0-2 is at position f4-0f.", "question": "Is it possible to transition to a state where the action \"place the key key0-1 at the current position place f2-3f\" can be applied?", "answer": "yes"}
{"id": -3255777502980333296, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f2-4f and is holding key1-1. All the positions are open except the following: . Key key1-0 is at position f1-0f.", "question": "Is it possible to transition to a state where the action \"travel from the current position f1-3f to the next position f1-2f\" can be applied?", "answer": "yes"}
{"id": -1221257497462132960, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f4-4f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-0 is at position f3-3f. Key key0-1 is at position f1-2f. Key key0-2 is at position f3-4f.", "question": "Is it possible to transition to a state where the action \"destroy the key key0-2 at the current position f3-3f\" can be applied?", "answer": "no"}
{"id": -7217612928648648, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-1f and is holding key0-3. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-0 is at position f0-0f. Key key0-2 is at position f3-0f. Key key0-1 is at position f4-2f.", "question": "Is it possible to transition to a state where the action \"place the key key0-3 at the current position place f1-0f\" can be applied?", "answer": "yes"}
{"id": -3841135662995941881, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-1f and is holding key0-1. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f3-3f. Key key0-0 is at position f1-3f.", "question": "Is it possible to transition to a state where the action \"put down the key key0-1 at the current position f0-2f\" can be applied?", "answer": "yes"}
{"id": 8142048986121093941, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-0f and is holding key0-0. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f.", "question": "Is it possible to transition to a state where the action \"pick up key f1-2f at current position place f2-3f and loose key shape0 being held\" can be applied?", "answer": "no"}
{"id": 6488288210978008810, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-2f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-1 is at position f1-3f.", "question": "Is it possible to transition to a state where the action \"transition from the current position f3-4f to the next position f4-4f\" can be applied?", "answer": "yes"}
{"id": 3152774823157808789, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-1f and is holding key0-0. All the positions are open except the following: f3-4f has shape0 shaped lock. Key key0-1 is at position f3-0f.", "question": "Is it possible to transition to a state where the action \"transition from the current position f2-1f to the next position f2-2f\" can be applied?", "answer": "yes"}
{"id": -6797981648364313471, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f4-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f2-2f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-2 is at position f1-2f. Key key0-0 is at position f4-1f. Key key0-1 is at position f2-4f.", "question": "Is it possible to transition to a state where the action \"acquire the key f1-3f from the place f0-4f\" can be applied?", "answer": "no"}
{"id": -747865961698299762, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-1f and is holding key0-2. All the positions are open except the following: f2-2f has shape0 shaped lock. Key key0-0 is at position f1-1f. Key key0-1 is at position f2-4f.", "question": "Is it possible to transition to a state where the action \"acquire the key key0-0 from the place f1-1f\" can be applied?", "answer": "yes"}
{"id": -4236453997252481340, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-3f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f0-4f.", "question": "Is it possible to transition to a state where the action \"pick up the key key0-0 at the current position place f2-0f and loose the key key0-0 being held\" can be applied?", "answer": "no"}
