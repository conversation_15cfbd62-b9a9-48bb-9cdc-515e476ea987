{"id": 5848559229288385036, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x1-y2, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x2-y0?", "answer": "yes"}
{"id": -3707823257616714862, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0, loc-x0-y2, loc-x3-y3, and loc-x3-y1. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y2, loc-x1-y1, loc-x2-y1, and loc-x1-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y1 and the robot is in place loc-x0-y1?", "answer": "no"}
{"id": 8667516452484671595, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y0, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, and loc-x0-y0.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y3 and the robot is in place loc-x2-y3?", "answer": "no"}
{"id": -5824563948979246597, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x0-y2.The following places have been visited: loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x3-y0, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, loc-x1-y2, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x3-y3 and the robot is in place loc-x1-y3?", "answer": "no"}
{"id": 9209347401036085916, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y3, loc-x1-y1, loc-x0-y3, loc-x1-y2, loc-x0-y0, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y3?", "answer": "yes"}
{"id": -5210007184100988901, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y0, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, loc-x1-y2, loc-x0-y0, and loc-x2-y3.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y0 and the robot is in place loc-x0-y1?", "answer": "no"}
{"id": 7010375140385281994, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x2-y2, loc-x0-y2, loc-x0-y1, loc-x1-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the following holds: Place loc-x3-y2 has been visited?", "answer": "yes"}
{"id": -511082433697583788, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x1-y0, loc-x3-y0, loc-x0-y2, loc-x3-y3, loc-x0-y1, loc-x3-y2, loc-x2-y0, loc-x0-y0, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y3 and the robot is in place loc-x1-y2?", "answer": "no"}
{"id": 5705969304590475747, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y2, loc-x2-y1, loc-x0-y3, loc-x0-y0, loc-x2-y3, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y0 and the robot is in place loc-x3-y2?", "answer": "no"}
{"id": 5625704496036238137, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y2.The following places have been visited: loc-x1-y0, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y0, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x2-y3, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x2-y3 and the robot is in place loc-x0-y0?", "answer": "no"}
{"id": -2600541151178733482, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0, loc-x0-y3, and loc-x3-y3. Currently, the robot is in place loc-x0-y1.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x3-y0, loc-x0-y1, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x3-y2, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x2-y1?", "answer": "yes"}
{"id": 4739079759859289365, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x3-y0, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x3-y2, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x2-y3 and the robot is in place loc-x3-y3?", "answer": "no"}
{"id": -2984151980683011083, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x0-y3, loc-x1-y2, and loc-x2-y3.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x3-y1 and Place loc-x3-y1 has been visited?", "answer": "yes"}
{"id": 2509410456201452078, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0, loc-x0-y3, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y0, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x3-y2, loc-x1-y2, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y2?", "answer": "yes"}
{"id": -8185664364293476672, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y3, loc-x1-y1, loc-x0-y3, loc-x1-y2, loc-x0-y0, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y0 and the robot is in place loc-x2-y3?", "answer": "no"}
