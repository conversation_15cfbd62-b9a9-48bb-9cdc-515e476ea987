{"question_id": "c9636c0f-5409-4889-9289-e634f809cdee", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, from room2, robot2's rgripper2 picks up ball3, robot2 moves from room2 to room1, in room1, robot2's lgripper2 drops ball4, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, rgripper2 of robot2 picks up ball6 in room1, robot2 moves from room1 to room2 and rgripper1 of robot2 picks up ball1 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot1's rgripper2, ball1 is being carried by robot2's rgripper2, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball2 is not being carried by robot1's lgripper2, ball3 is being carried by robot2's lgripper1, ball4 is being carried by robot1's lgripper2, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper1, ball5 is being carried by robot2's rgripper2, ball6 is being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper2, ball7 is being carried by robot2's lgripper2, ball7 is being carried by robot2's rgripper2, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot2 is carrying ball1, lgripper1 of robot2 is carrying ball2, lgripper1 of robot2 is carrying ball5, lgripper1 of robot2 is carrying ball7, lgripper2 of robot1 is carrying ball3, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is carrying ball2, lgripper2 of robot2 is carrying ball4, lgripper2 of robot2 is not carrying ball5, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball7, rgripper1 of robot2 is carrying ball6, rgripper1 of robot2 is not carrying ball4, rgripper2 of robot1 is not carrying ball2, robot1 is carrying ball1 with lgripper1, robot1 is carrying ball1 with lgripper2, robot1 is carrying ball2 with rgripper1, robot1 is carrying ball4 with rgripper1, robot1 is carrying ball5 with rgripper2, robot1 is carrying ball6 with lgripper2, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper2, robot2 is carrying ball2 with rgripper2, robot2 is carrying ball5 with rgripper1, robot2 is carrying ball7 with rgripper1, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball6 with lgripper1 and robot2 is not carrying ball6 with rgripper2.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot2 will move from room2 to room3, robot2's left gripper (lgripper2) will pick up ball1 in room3, robot2's right gripper (rgripper2) will pick up ball2 in room3, robot2 will move back to room2 from room3, robot2's lgripper2 will drop ball1 in room2, robot2's rgripper2 will drop ball2 in room2, robot2 will move from room2 to room3, robot2's lgripper2 will pick up ball4 in room3, robot2's rgripper2 will pick up ball7 in room3, robot2 will move back to room2 from room3, robot2's rgripper2 will drop ball7 in room2, robot2's rgripper2 will pick up ball3 in room2, robot2 will move from room2 to room1, robot2's lgripper2 will drop ball4 in room1, robot2's lgripper2 will pick up ball5 in room1, robot2's rgripper2 will drop ball3 in room1, robot2's rgripper2 will pick up ball6 in room1, robot2 will move from room1 to room2, and robot2's right gripper (rgripper1) will pick up ball1 in room2 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being held by robot1's rgripper2, ball1 is being held by robot2's rgripper2, ball1 is not being held by robot2's lgripper2, ball1 is not being held by robot2's rgripper1, ball2 is not being held by robot1's lgripper2, ball3 is being held by robot2's lgripper1, ball4 is being held by robot1's lgripper2, ball4 is not being held by robot1's lgripper1, ball4 is not being held by robot1's rgripper2, ball4 is not being held by robot2's lgripper1, ball5 is being held by robot2's rgripper2, ball6 is being held by robot1's lgripper1, ball6 is not being held by robot1's rgripper2, ball7 is being held by robot2's lgripper2, ball7 is being held by robot2's rgripper2, robot1's lgripper1 is holding ball2, robot1's lgripper1 is not holding ball3, robot2's lgripper1 is holding ball1, robot2's lgripper1 is holding ball2, robot2's lgripper1 is holding ball5, robot2's lgripper1 is holding ball7, robot1's lgripper2 is holding ball3, robot1's lgripper2 is not holding ball7, robot2's lgripper2 is holding ball2, robot2's lgripper2 is holding ball4, robot2's lgripper2 is not holding ball5, robot2's lgripper2 is not holding ball6, robot1's rgripper1 is not holding ball3, robot1's rgripper1 is not holding ball7, robot2's rgripper1 is holding ball6, robot2's rgripper1 is not holding ball4, robot1's rgripper2 is not holding ball2, robot1 is holding ball1 with lgripper1, robot1 is holding ball1 with lgripper2, robot1 is holding ball2 with rgripper1, robot1 is holding ball4 with rgripper1, robot1 is holding ball5 with rgripper2, robot1 is holding ball6 with lgripper2, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball3 with rgripper2, robot1 is not holding ball5 with lgripper1, robot1 is not holding ball5 with lgripper2, robot1 is not holding ball5 with rgripper1, robot1 is not holding ball6 with rgripper1, robot1 is not holding ball7 with lgripper1, robot1 is not holding ball7 with rgripper2, robot2 is holding ball2 with rgripper2, robot2 is holding ball5 with rgripper1, robot2 is holding ball7 with rgripper1, robot2 is not holding ball2 with rgripper1, robot2 is not holding ball3 with lgripper2, robot2 is not holding ball3 with rgripper1, robot2 is not holding ball3 with rgripper2, robot2 is not holding ball4 with rgripper2, robot2 is not holding ball6 with lgripper1, and robot2 is not holding ball6 with rgripper2.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "209c5262-cac2-430e-9e3f-dc0500598933", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, from room4, robot1 moves to room2 and lgripper1 of robot1 drops ball2 in room1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Ball2 is located at room4, ball2 is not at room3, ball2 is not at room5, ball2 is not located at room2 and ball2 is not present at room1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: starting from room4, robot1 will move to room1, pick up ball1 from room1 using lgripper1, then proceed to room2, where robot1's rgripper1 will pick up ball2, followed by a move to room3, where lgripper1 will drop ball1, then robot1 will return to room4, drop ball2, move back to room2, and finally drop ball2 in room1 using lgripper1 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties (including their negations) true for ball2: is ball2 located in room4, is ball2 not in room3, is ball2 not in room5, is ball2 not in room2, and is ball2 not in room1?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "64b41e0c-6df5-413f-a606-b18d57232ede", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room1, robot1's lgripper1 drops ball6, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2, ball4 is picked from room2 with lgripper1 by robot1, ball5 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room5, lgripper1 of robot1 drops ball4 in room5, robot1 moves to room1 from room5, ball3 is picked from room1 with lgripper1 by robot1, in room1, robot1's rgripper1 drops ball5, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not being carried by robot1's lgripper1, ball2 is being carried by robot1's rgripper1, ball4 is being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, ball6 is being carried by robot1's rgripper1, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball4, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball6 with lgripper1 and robot1 is not carrying ball5 with lgripper1.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1's lgripper1 releases ball6 in room1, then robot1's lgripper1 picks up ball1 from room1, followed by robot1 moving from room1 to room2. In room2, robot1's rgripper1 picks up ball2, and then robot1 proceeds to room3 from room2. In room3, robot1's lgripper1 drops ball1, and robot1 moves to room4 from room3. In room4, robot1's rgripper1 releases ball2, and then robot1 moves back to room2. In room2, robot1's lgripper1 picks up ball4 and robot1's rgripper1 picks up ball5. From room2, robot1 moves to room5, where robot1's lgripper1 drops ball4. Then, robot1 moves to room1 from room5, and in room1, robot1's lgripper1 picks up ball3. In room1, robot1's rgripper1 releases ball5, and then robot1's rgripper1 picks up ball6. Finally, robot1 moves to room5 from room1, and robot1's lgripper1 drops ball3 in room5 to reach the current state. The question is whether the following properties (both with and without negations) are true before the first inexecutable action in the sequence: ball1 is not held by robot1's lgripper1, ball2 is held by robot1's rgripper1, ball4 is held by robot1's lgripper1, ball5 is not held by robot1's rgripper1, ball6 is held by robot1's rgripper1, robot1's lgripper1 is not holding ball3, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball4, robot1 is holding ball2 with lgripper1, robot1 is holding ball3 with rgripper1, robot1 is holding ball6 with lgripper1, and robot1 is not holding ball5 with lgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "009dea35-0d27-4c60-a2f8-3eb0d1d1846a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room2, robot2's rgripper2 drops ball4 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball4 before the first inexecutable action in the sequence? Ball4 is being carried by robot1's rgripper1, ball4 is being carried by robot2's lgripper2, ball4 is being carried by robot2's rgripper2, rgripper1 of robot2 is not carrying ball4, robot1 is carrying ball4 with lgripper2, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper2 and robot2 is not carrying ball4 with lgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: in room2, robot2's rgripper2 drops ball4 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball4 before the first inexecutable action in the sequence? Is ball4 being held by robot1's rgripper1, is ball4 being held by robot2's lgripper2, is ball4 being held by robot2's rgripper2, is robot2's rgripper1 not holding ball4, is robot1 holding ball4 with its lgripper2, is robot1 not holding ball4 with its lgripper1, is robot1 not holding ball4 with its rgripper2, and is robot2 not holding ball4 with its lgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "fe428027-6576-4c55-8194-15dd614fc9e9", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, in room3, robot2's rgripper2 drops ball2, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2 and from room3, robot2 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball3 before the first inexecutable action in the sequence? Ball3 is being carried by robot2's rgripper1, ball3 is not being carried by robot1's rgripper1, lgripper1 of robot2 is carrying ball3, lgripper2 of robot2 is not carrying ball3, rgripper2 of robot1 is carrying ball3, rgripper2 of robot2 is not carrying ball3, robot1 is carrying ball3 with lgripper2 and robot1 is not carrying ball3 with lgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Considering the initial condition, the following actions are scheduled to take place: robot2 will move from room2 to room3, then robot2's left gripper (lgripper2) will pick up ball1 in room3, and its right gripper (rgripper2) will pick up ball2 in room3. Next, robot2 will move back from room3 to room2, where it will drop ball1 using lgripper2 and ball2 using rgripper2. Additionally, in room3, robot2's rgripper2 will drop ball2, and then robot2's lgripper2 will pick up ball4 in room3, while its rgripper2 will pick up ball7 in room3. Finally, robot2 will move from room3 to room2 to reach the current state. Now, before the first inexecutable action in the sequence, are the following properties (including their negations) true for ball3? Is ball3 being carried by robot2's right gripper (rgripper1)? Is ball3 not being carried by robot1's right gripper (rgripper1)? Is robot2's left gripper (lgripper1) carrying ball3? Is robot2's right gripper (rgripper2) not carrying ball3? Is robot1's right gripper (rgripper2) carrying ball3? Is robot2's right gripper (rgripper2) not carrying ball3? Is robot1 carrying ball3 with its left gripper (lgripper2)? Is robot1 not carrying ball3 with its left gripper (lgripper1)?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "55eec83b-0c0f-43d4-9758-5f024d03c10f", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, ball4 is picked from room3 with lgripper1 by robot1, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room1 to room3, in room3, robot1's rgripper1 drops ball6, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves to room4 from room3, lgripper1 of robot1 drops ball1 in room4, rgripper1 of robot1 drops ball7 in room4, robot1 moves from room4 to room2, ball2 is picked from room2 with lgripper1 by robot1 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball1 before the first inexecutable action in the sequence? Lgripper1 of robot1 is carrying ball1 and rgripper1 of robot1 is carrying ball1.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then from room1, robot1's left gripper (lgripper1) will grasp ball1, and from room1, robot1's right gripper (rgripper1) will grasp ball3. Next, from room1, robot1 will proceed to room5, where it will release ball3 using rgripper1. From room5, robot1 will move to room2, where rgripper1 will pick up ball4. However, it is noted that ball4 is actually picked up from room3 using lgripper1 by robot1. Then, rgripper1 of robot1 will release ball4 in room1, and subsequently, rgripper1 of robot1 will pick up ball6 in room1. Robot1 will then move from room1 to room3, where it will release ball6 using rgripper1. In room3, robot1's rgripper1 will pick up ball7. Next, robot1 will move from room3 to room4, where lgripper1 of robot1 will release ball1, and rgripper1 of robot1 will release ball7. Finally, robot1 will move from room4 to room2, where it will pick up ball2 using lgripper1, and rgripper1 of robot1 will pick up ball5 in room2 to reach the current state. The question is whether the following properties (with and without negations) are true for ball1 before the first inexecutable action in the sequence: Is ball1 being carried by lgripper1 of robot1, and is ball1 being carried by rgripper1 of robot1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "0ac77f77-4f7c-4d84-af32-2cc4c141e324", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball4, ball5 is picked from room2 with rgripper1 by robot1, ball1 is picked from room2 with lgripper1 by robot1, in room5, robot1's lgripper1 drops ball4, from room5, robot1 moves to room1, ball3 is picked from room1 with lgripper1 by robot1, ball5 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is located at room1, ball1 is not located at room3, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball2 is at room5, ball2 is not at room1, ball2 is not at room3, ball2 is not at room4, ball2 is present at room2, ball3 is not at room1, ball3 is not at room3, ball3 is not at room5, ball3 is not located at room2, ball3 is not located at room4, ball4 is at room1, ball4 is at room3, ball4 is located at room4, ball4 is not present at room2, ball4 is not present at room5, ball5 is located at room2, ball5 is located at room5, ball5 is not at room4, ball5 is not located at room1, ball5 is not present at room3, ball6 is not at room2, ball6 is not at room3, ball6 is not located at room1, ball6 is present at room4, ball6 is present at room5, robot1 is at room5, robot1 is not at room2, robot1 is not at room3, robot1 is not at room4 and robot1 is present in room1.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then use its left gripper (lgripper1) to pick up ball1 in room1. Next, robot1 will move from room1 to room2, where its right gripper (rgripper1) will pick up ball2. Robot1 will then proceed to room3, where it will drop ball1 using lgripper1. From room3, robot1 will move to room4 and drop ball2 using rgripper1. After that, robot1 will move back to room2, pick up ball4 with lgripper1, and pick up ball5 with rgripper1. Additionally, robot1 will pick up ball1 with lgripper1 in room2. Then, robot1 will move to room5 and drop ball4 using lgripper1. From room5, robot1 will move to room1, pick up ball3 with lgripper1, and drop ball5 using rgripper1. Next, robot1 will pick up ball6 with rgripper1 in room1 and move to room5, where it will drop ball3 using lgripper1 to reach the current state. \n\nThe following properties of the state (both with and without negations) are to be verified as true before the first inexecutable action in the sequence: \n- Ball1 is in room1.\n- Ball1 is not in room3.\n- Ball1 is not in room5.\n- Ball1 is not in room2.\n- Ball1 is not in room4.\n- Ball2 is not in room5.\n- Ball2 is not in room1.\n- Ball2 is not in room3.\n- Ball2 is not in room4.\n- Ball2 is in room2.\n- Ball3 is not in room1.\n- Ball3 is not in room3.\n- Ball3 is not in room5.\n- Ball3 is not in room2.\n- Ball3 is not in room4.\n- Ball4 is not in room1.\n- Ball4 is not in room3.\n- Ball4 is not in room4.\n- Ball4 is not in room2.\n- Ball4 is not in room5.\n- Ball5 is in room2.\n- Ball5 is in room5.\n- Ball5 is not in room4.\n- Ball5 is not in room1.\n- Ball5 is not in room3.\n- Ball6 is not in room2.\n- Ball6 is not in room3.\n- Ball6 is not in room1.\n- Ball6 is not in room4.\n- Ball6 is not in room5.\n- Robot1 is not in room5.\n- Robot1 is not in room2.\n- Robot1 is not in room3.\n- Robot1 is not in room4.\n- Robot1 is in room1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present at room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "c66d184e-80f9-4550-9b0d-2acd9a8d9a46", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, in room1, robot2's lgripper1 drops ball4, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3 and robot2 moves from room3 to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot2's lgripper2, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's rgripper2, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper2, ball2 is not being carried by robot2's lgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper2, ball4 is not being carried by robot1's lgripper2, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's lgripper2, ball4 is not being carried by robot2's rgripper1, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's rgripper1, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball3, lgripper1 of robot2 is not carrying ball7, lgripper2 of robot1 is not carrying ball1, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is not carrying ball5, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not carrying ball7, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is carrying ball2, rgripper2 of robot2 is not carrying ball6, rgripper2 of robot2 is not carrying ball7, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper2, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball6 with lgripper2 and robot2 is not carrying ball7 with lgripper2.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot2 moves from room2 to room3, then from room3, robot2's left gripper (lgripper2) picks up ball1, and from room3, robot2's right gripper (rgripper2) picks up ball2. In room1, robot2's left gripper (lgripper1) drops ball4, and in room2, robot2's left gripper (lgripper2) drops ball1. Additionally, robot2's right gripper (rgripper2) drops ball2 in room2. Then, robot2 moves from room2 to room3, picks up ball4 from room3 with its left gripper (lgripper2), and its right gripper (rgripper2) picks up ball7 in room3. Finally, robot2 moves from room3 to room2 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being held by robot2's left gripper (lgripper2), ball1 is not being held by robot1's left gripper (lgripper1), ball1 is not being held by robot1's right gripper (rgripper1), ball1 is not being held by robot2's right gripper (rgripper2), ball2 is not being held by robot1's left gripper (lgripper1), ball2 is not being held by robot1's right gripper (rgripper2), ball2 is not being held by robot2's left gripper (lgripper1), ball3 is not being held by robot1's right gripper (rgripper2), ball3 is not being held by robot2's left gripper (lgripper2), ball4 is not being held by robot1's left gripper (lgripper2), ball4 is not being held by robot1's right gripper (rgripper1), ball4 is not being held by robot1's right gripper (rgripper2), ball4 is not being held by robot2's left gripper (lgripper1), ball4 is not being held by robot2's left gripper (lgripper2), ball4 is not being held by robot2's right gripper (rgripper1), ball5 is not being held by robot1's right gripper (rgripper1), ball5 is not being held by robot1's right gripper (rgripper2), ball6 is not being held by robot2's right gripper (rgripper1), robot1's left gripper (lgripper1) is not holding ball4, robot1's left gripper (lgripper1) is not holding ball5, robot1's left gripper (lgripper1) is not holding ball6, robot2's left gripper (lgripper1) is not holding ball1, robot2's left gripper (lgripper1) is not holding ball3, robot2's left gripper (lgripper1) is not holding ball7, robot1's left gripper (lgripper2) is not holding ball1, robot1's left gripper (lgripper2) is not holding ball5, robot1's left gripper (lgripper2) is not holding ball6, robot1's left gripper (lgripper2) is not holding ball7, robot2's left gripper (lgripper2) is not holding ball5, robot1's right gripper (rgripper1) is not holding ball2, robot1's right gripper (rgripper1) is not holding ball6, robot1's right gripper (rgripper1) is not holding ball7, robot2's right gripper (rgripper1) is not holding ball5, robot2's right gripper (rgripper1) is not holding ball7, robot1's right gripper (rgripper2) is not holding ball6, robot2's right gripper (rgripper2) is holding ball2, robot2's right gripper (rgripper2) is not holding ball6, robot2's right gripper (rgripper2) is not holding ball7, robot1 is not holding ball1 with its right gripper (rgripper2), robot1 is not holding ball2 with its left gripper (lgripper2), robot1 is not holding ball3 with its left gripper (lgripper1), robot1 is not holding ball3 with its left gripper (lgripper2), robot1 is not holding ball3 with its right gripper (rgripper1), robot1 is not holding ball7 with its left gripper (lgripper1), robot1 is not holding ball7 with its right gripper (rgripper2), robot2 is not holding ball1 with its right gripper (rgripper1), robot2 is not holding ball2 with its left gripper (lgripper2), robot2 is not holding ball2 with its right gripper (rgripper1), robot2 is not holding ball3 with its right gripper (rgripper1), robot2 is not holding ball3 with its right gripper (rgripper2), robot2 is not holding ball4 with its right gripper (rgripper2), robot2 is not holding ball5 with its left gripper (lgripper1), robot2 is not holding ball5 with its right gripper (rgripper2), robot2 is not holding ball6 with its left gripper (lgripper1), robot2 is not holding ball6 with its left gripper (lgripper2), and robot2 is not holding ball7 with its left gripper (lgripper2).", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "cafabc15-1601-47d1-94e1-0d90908d48de", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves from room3 to room4, ball2 is dropped in room4 with rgripper1 by robot1, from room4, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball4, from room2, robot1's rgripper1 picks up ball5, from room2, robot1 moves to room5, rgripper1 of robot1 picks up ball3 in room2, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball3, rgripper1 of robot1 drops ball5 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room5 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball4 before the first inexecutable action in the sequence? Ball4 is not being carried by robot1's rgripper1 and robot1 is carrying ball4 with lgripper1.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then robot1's left gripper (lgripper1) will pick up ball1 in room1, followed by robot1 moving from room1 to room2, where robot1's right gripper (rgripper1) will pick up ball2, then robot1 will move from room2 to room3, drop ball1 in room3 using lgripper1, and move from room3 to room4, where ball2 will be dropped using rgripper1. Next, robot1 will move from room4 to room2, pick up ball4 with lgripper1 and ball5 with rgripper1 in room2, then move to room5, pick up ball3 with rgripper1 in room2, move from room5 to room1, pick up ball3 with lgripper1 in room1, drop ball5 with rgripper1 in room1, pick up ball6 with rgripper1 in room1, move from room1 to room5, and finally drop ball3 with lgripper1 in room5 to reach the current state. Are the following properties (with and without negations) true for ball4 before the first inexecutable action in the sequence: ball4 is not being carried by robot1's rgripper1 and robot1 is carrying ball4 with lgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "5f5c7e70-122a-425a-ab0b-1a65555cd4a3", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1's lgripper1 picks up ball3, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball4 in room2, ball5 is picked from room2 with rgripper1 by robot1, robot1 moves to room5 from room2, in room5, robot1's lgripper1 drops ball4, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball3, rgripper1 of robot1 drops ball5 in room1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room5 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is not at room1, ball1 is not at room5, ball1 is not located at room2, ball1 is not located at room4, ball1 is present at room3, ball2 is not at room2, ball2 is not at room4, ball2 is not being carried by robot1's lgripper1, ball2 is not present at room1, ball2 is not present at room3, ball2 is not present at room5, ball3 is at room1, ball3 is not at room4, ball3 is not located at room2, ball3 is not located at room3, ball3 is not present at room5, ball4 is not at room3, ball4 is not at room4, ball4 is not located at room1, ball4 is not located at room5, ball4 is present at room2, ball5 is located at room2, ball5 is not at room4, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room5, ball5 is not present at room1, ball5 is not present at room3, ball6 is at room1, ball6 is not at room4, ball6 is not at room5, ball6 is not present at room2, ball6 is not present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not free, robot1 is at room3, robot1 is carrying ball2 with rgripper1, robot1 is not at room2, robot1 is not at room4, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room1 and robot1 is not present in room5.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 moves from room4 to room1, the left gripper of robot1 (lgripper1) picks up ball1 in room1, robot1 moves from room1 to room2, the right gripper of robot1 (rgripper1) picks up ball2 in room2, robot1 moves from room2 to room3, robot1 drops ball1 in room3 using lgripper1, then robot1's lgripper1 picks up ball3 in room3, robot1's rgripper1 drops ball2 in room4, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball4 in room2, robot1's rgripper1 picks up ball5 in room2, robot1 moves from room2 to room5, robot1's lgripper1 drops ball4 in room5, robot1 moves from room5 to room1, robot1's lgripper1 picks up ball3 in room1, robot1's rgripper1 drops ball5 in room1, robot1's rgripper1 picks up ball6 in room1, robot1 moves from room1 to room5, and finally, robot1's lgripper1 drops ball3 in room5 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state of the balls and robot1 is as follows: \n- Ball1 is not in room1, room2, room4, or room5, but it is in room3.\n- Ball2 is not in room2, room4, room1, room3, or room5, and it is not being carried by robot1's lgripper1.\n- Ball3 is in room1, but not in room4, room2, room3, or room5.\n- Ball4 is not in room3, room4, room1, or room5, but it is in room2.\n- Ball5 is in room2, but not in room4, room5, room1, or room3, and it is not being carried by robot1's rgripper1.\n- Ball6 is in room1, but not in room4, room5, room2, or room3.\n- Robot1's lgripper1 is free and not carrying any balls.\n- Robot1's rgripper1 is not free and is carrying ball2.\n- Robot1 is in room3, but not in room2, room4, room1, or room5, and it is not carrying ball1, ball4, or ball6 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated at room2, ball6 is situated at room1, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "da407dad-296d-4bb0-bbf4-2fa11b4eac8e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7, lgripper2 of robot1 drops ball7 in room1, rgripper2 of robot2 drops ball7 in room2, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves from room2 to room1, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball4 before the first inexecutable action in the sequence? Ball4 is being carried by robot2's lgripper2, ball4 is not being carried by robot1's rgripper1, lgripper1 of robot2 is not carrying ball4, rgripper1 of robot2 is not carrying ball4, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot2 is not carrying ball4, robot1 is not carrying ball4 with lgripper1 and robot1 is not carrying ball4 with lgripper2.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then use its left gripper (lgripper2) to pick up ball1 in room3, and its right gripper (rgripper2) to pick up ball2 in room3. Next, robot2 will move back to room2, where it will drop ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 will return to room3, pick up ball4 with lgripper2, and ball7 with rgripper2. However, robot1's left gripper (lgripper2) will drop ball7 in room1, and robot2's right gripper (rgripper2) will drop ball7 in room2. Robot2 will then pick up ball3 with rgripper2 in room2, move to room1, and drop ball4 with lgripper2. In room1, robot2 will pick up ball5 with lgripper2, drop ball3 with rgripper2, pick up ball6 with rgripper2, move to room2, and finally drop ball5 with lgripper2 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties true for ball4 (both with and without negations)? Is ball4 being carried by robot2's lgripper2? Is ball4 not being carried by robot1's right gripper (rgripper1)? Is ball4 not being carried by robot2's left gripper (lgripper1)? Is ball4 not being carried by robot2's right gripper (rgripper1)? Is ball4 not being carried by robot1's right gripper (rgripper2)? Is ball4 not being carried by robot2's right gripper (rgripper2)? Is robot1 not carrying ball4 with its left gripper (lgripper1)? Is robot1 not carrying ball4 with its left gripper (lgripper2)?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "891a99a9-b3c9-4ddb-a11b-769d2112b5ca", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room3, robot1's rgripper1 drops ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not present at room1, ball1 is not present at room3, ball1 is present at room2, ball2 is located at room3, ball2 is not at room2, ball2 is not located at room1, ball3 is located at room3, ball3 is not at room1, ball3 is not present at room2, ball4 is at room1, ball4 is located at room3, ball4 is not at room2, ball5 is located at room3, ball5 is not at room2, ball5 is present at room1, ball6 is located at room2, ball6 is not located at room3, ball6 is present at room1, ball7 is not located at room2, ball7 is not present at room3, ball7 is present at room1, robot1 is at room2, robot1 is at room3, robot1 is present in room1, robot2 is at room1, robot2 is at room2 and robot2 is located at room3.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: in room3, robot1's rgripper1 drops ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is absent from room1, ball1 is absent from room3, ball1 is present in room2, ball2 is situated in room3, ball2 is not in room2, ball2 is not in room1, ball3 is situated in room3, ball3 is not in room1, ball3 is not present in room2, ball4 is situated in room1, ball4 is situated in room3, ball4 is not in room2, ball5 is situated in room3, ball5 is not in room2, ball5 is present in room1, ball6 is situated in room2, ball6 is not in room3, ball6 is present in room1, ball7 is not in room2, ball7 is not in room3, ball7 is present in room1, robot1 is located in room2, robot1 is located in room3, robot1 is present in room1, robot2 is located in room1, robot2 is located in room2, and robot2 is located in room3.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "fdb68f81-8d70-4603-b79f-ce0b55c15ec6", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is dropped in room1 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball3 is not being carried by robot1's rgripper1, ball4 is being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's lgripper1, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is carrying ball3, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is carrying ball6, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5 and robot1 is carrying ball1 with lgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 uses rgripper1 to drop ball3 in room1 to achieve the current state. Are the following properties of the state (including both affirmative and negative statements) true prior to the first inexecutable action in the sequence? The following conditions are evaluated: ball3 is not held by robot1's rgripper1, ball4 is held by robot1's lgripper1, ball4 is not held by robot1's rgripper1, ball6 is not held by robot1's lgripper1, robot1's lgripper1 is holding ball2, robot1's lgripper1 is holding ball3, robot1's lgripper1 is not holding ball5, robot1's rgripper1 is holding ball1, robot1's rgripper1 is holding ball6, robot1's rgripper1 is not holding ball2, robot1's rgripper1 is not holding ball5, and robot1 is holding ball1 with lgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "ebd332f9-a36a-4c0b-8ba3-07595b1e3903", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, lgripper1 of robot1 picks up ball2 in room5, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not being carried by robot1's rgripper1, ball2 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's lgripper1, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is carrying ball2 with rgripper1, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1 and robot1 is not carrying ball5 with rgripper1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then its left gripper (lgripper1) will pick up ball1 in room1, followed by robot1 moving to room2, where its right gripper (rgripper1) will pick up ball2, then robot1 will proceed to room3, where lgripper1 will drop ball1, next, lgripper1 will pick up ball2 in room5, and then rgripper1 will drop ball2 in room4, after which robot1 will move to room2 and finally, lgripper1 will pick up ball4 to reach the current state. The question is whether the following properties (including their negations) are true before the first unexecutable action in the sequence: ball1 is not held by robot1's rgripper1, ball2 is not held by robot1's lgripper1, ball3 is not held by robot1's lgripper1, ball6 is not held by robot1's lgripper1, robot1's lgripper1 is not holding ball3, robot1's rgripper1 is not holding ball4, robot1's rgripper1 is not holding ball6, robot1 is holding ball2 with rgripper1, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball5 with lgripper1, and robot1 is not holding ball5 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "f06ccb54-dfbe-4799-93ef-a05674b95655", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper1 of robot1 is not free and rgripper1 of robot1 is free.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1 will move from room2 to room1 to achieve the current state. Are the following properties of the state (including both affirmative and negative assertions) valid before the first unexecutable action in the sequence: the left gripper of robot1 is occupied and the right gripper of robot1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "0c7a8808-0b29-4a57-a3f3-bbfcb9507683", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, rgripper1 of robot1 drops ball2 in room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, ball1 is dropped in room5 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball3 and from room5, robot1 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for lgripper1 before the first inexecutable action in the sequence? Lgripper1 of robot1 is free.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1's lgripper1 will grasp ball7 in room4, robot1's rgripper1 will release ball2 in room5, robot1's lgripper1 will release ball7 in room5, robot1 will move from room5 to room1, robot1's lgripper1 will pick up ball1 in room1, robot1's rgripper1 will pick up ball3 from room1, robot1 will move from room1 to room5, robot1's lgripper1 will release ball1 in room5, robot1's rgripper1 will release ball3 in room5, and finally, robot1 will move from room5 to room2 to reach the current state. Are the following properties of the state (both affirmative and negative) true for lgripper1 before the first unexecutable action in the sequence? Is lgripper1 of robot1 in a free state?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "89d37bac-f8e6-4803-b4b6-dd4a22fe7781", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves from room3 to room4, in room4, robot1's rgripper1 drops ball2, from room4, robot1 moves to room2, ball4 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball5, robot1 moves from room2 to room5, lgripper1 of robot1 drops ball4 in room5, from room5, robot1 moves to room1, ball3 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 drops ball5 in room1, rgripper1 of robot1 picks up ball1 in room3, robot1 moves to room5 from room1 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Robot1's lgripper1 is not free and robot1's rgripper1 is available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then use its left gripper (lgripper1) to pick up ball1 in room1. Next, robot1 will move to room2, where its right gripper (rgripper1) will pick up ball2. From room2, robot1 will proceed to room3, where it will drop ball1 using lgripper1. Then, robot1 will move to room4 and drop ball2 using rgripper1. After that, robot1 will return to room2, pick up ball4 with lgripper1, and then use rgripper1 to pick up ball5. Robot1 will then move to room5, drop ball4 using lgripper1, and proceed to room1. In room1, robot1 will pick up ball3 with lgripper1 and drop ball5 using rgripper1. Next, robot1 will move to room3 to pick up ball1 with rgripper1, then move to room5, and finally drop ball3 using lgripper1. Considering the state before the first inexecutable action in the sequence, are the following properties true for robot1: robot1's lgripper1 is occupied and robot1's rgripper1 is available, both with and without negations?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "3a4eccbf-fdfe-49cc-ae01-fd4be7b04999", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3 and lgripper1 of robot1 picks up ball2 in room4 to reach the current state. Are the following valid properties of the state (both with and without negations) true for room5 before the first inexecutable action in the sequence? Ball1 is located at room5, ball2 is located at room5, ball3 is not located at room5, ball4 is at room5, ball5 is not at room5, ball6 is not at room5, ball7 is not at room5 and robot1 is at room5.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned: starting from room4, robot1's left gripper (lgripper1) will pick up ball7, then robot1 will move to room5, and lgripper1 will drop ball7 in room5. Next, robot1 will move to room1 from room5, pick up ball1 with lgripper1, and also pick up ball3 with its right gripper (rgripper1) from room1. Then, robot1 will move to room5, drop ball1 with lgripper1, and drop ball3 with rgripper1 in room5. Lastly, lgripper1 of robot1 will pick up ball2 in room4 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties true for room5 (both with and without negations)? Is ball1 located at room5, is ball2 located at room5, is ball3 not located at room5, is ball4 located at room5, is ball5 not located at room5, is ball6 not located at room5, is ball7 not located at room5, and is robot1 located at room5?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "8a4c473d-3b72-4309-9794-a01c81bf2dd4", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, ball3 is dropped in room3 with rgripper1 by robot1, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Robot1's lgripper1 is not free and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then from room1, robot1's left gripper (lgripper1) will grasp ball1, after which robot1 will proceed to room2 from room1, and its right gripper (rgripper1) will pick up ball2 in room2. Next, robot1 will move to room3 from room2, where it will release ball1 using lgripper1, and then drop ball3 using rgripper1. Subsequently, in room4, robot1's rgripper1 will release ball2, and then robot1 will move back to room2, where it will pick up ball4 using lgripper1 to reach the current state. The question is whether the following properties (with and without negations) are true before the first unexecutable action in the sequence: robot1's lgripper1 is occupied and robot1's rgripper1 is unavailable.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "23c2acea-18c4-40a0-9a65-89659b4a1dc8", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, lgripper1 of robot1 picks up ball2 in room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, from room5, robot1's rgripper1 picks up ball4, rgripper1 of robot1 picks up ball5 in room3 and from room3, robot1 moves to room6 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Ball2 is being carried by robot1's lgripper1 and rgripper1 of robot1 is not carrying ball2.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: starting from room4, robot1's left gripper (lgripper1) picks up ball7, then robot1 moves to room5, and lgripper1 drops ball7 in room5. Next, robot1 moves to room1, where it picks up ball1 with lgripper1 and ball3 with its right gripper (rgripper1). Robot1 then moves to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1. After that, robot1 moves to room2, picks up ball2 with lgripper1, and picks up ball4 with rgripper1. Robot1 then moves to room1, drops ball4 with rgripper1, picks up ball6 with rgripper1, and moves to room3. From room5, robot1's rgripper1 picks up ball4, and in room3, rgripper1 picks up ball5. Finally, robot1 moves from room3 to room6 to reach the current state. The question is whether the following properties (with and without negations) are true for ball2 before the first inexecutable action in the sequence: Is ball2 being carried by robot1's lgripper1, and is robot1's rgripper1 not carrying ball2?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "9816fbfd-48a1-4159-ab32-f881fb216da9", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, ball1 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, from room3, robot2's lgripper2 picks up ball4, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, ball7 is dropped in room2 with rgripper2 by robot2, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves from room2 to room1, ball4 is dropped in room1 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, rgripper2 of robot2 picks up ball6 in room1, from room1, robot2 moves to room2 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper2 of robot2 is free, rgripper1 of robot1 is free, rgripper1 of robot2 is not free, rgripper2 of robot1 is not free, rgripper2 of robot2 is not free, robot1's lgripper1 is available, robot1's lgripper2 is not free and robot2's lgripper1 is not available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then use its left gripper (lgripper2) to pick up ball1 in room3, followed by using its right gripper (rgripper2) to pick up ball2 in room3. Next, robot2 will move back to room2, where it will drop ball1 using its left gripper. Then, robot2 will move back to room3, pick up ball4 using its left gripper, and move back to room2. In room2, robot2 will drop ball7 using its right gripper after picking it up in room3. Subsequently, robot2 will pick up ball3 in room2 using its right gripper and move to room1. In room1, robot2 will drop ball4 using its left gripper, pick up ball5 using its left gripper, drop ball3 using its right gripper, and pick up ball6 using its right gripper. Finally, robot2 will move to room2 and drop ball5 using its left gripper to reach the current state. The question is whether the following properties (with and without negations) are true before the first inexecutable action in the sequence: robot2's left gripper is free, robot1's right gripper is free, robot2's right gripper is not free, robot1's left gripper is not free, robot2's right gripper is not free, robot1's left gripper is available, robot1's right gripper is not free, and robot2's left gripper is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "e7424a13-267e-4cdb-af04-53237270e4b6", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, lgripper1 of robot1 drops ball2 in room1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room1 to room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball7 in room3, robot1 moves to room4 from room3, in room4, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball7 in room4, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball2 in room2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball3 before the first inexecutable action in the sequence? Ball3 is not being carried by robot1's lgripper1 and robot1 is carrying ball3 with rgripper1.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then pick up ball1 with its left gripper (lgripper1) and ball3 with its right gripper (rgripper1) in room1. Next, robot1 will drop ball2 with its lgripper1 in room1 and ball3 with its rgripper1 in room5. After that, robot1 will move from room5 to room2, pick up ball4 with its rgripper1 in room2, and then move to room1. In room1, robot1 will drop ball4 with its rgripper1, pick up ball6 with its rgripper1, and then move to room3. In room3, robot1 will drop ball6 with its rgripper1, pick up ball7 with its rgripper1, and then move to room4. In room4, robot1 will drop ball1 with its lgripper1 and ball7 with its rgripper1. Then, robot1 will move from room4 to room2, pick up ball2 with its lgripper1 in room2, and pick up ball5 with its rgripper1 in room2 to reach the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for ball3 (both with and without negations)? Is ball3 not being held by robot1's lgripper1, and is robot1 holding ball3 with its rgripper1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "c2a965a8-260b-4052-9023-2cb3146799ff", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, lgripper1 of robot1 drops ball7 in room5, rgripper1 of robot1 drops ball3 in room5, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2, ball4 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball5 and robot1 moves to room6 from room3 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball4 before the first inexecutable action in the sequence? Ball4 is not at room6, ball4 is not located at room1, ball4 is not located at room3, ball4 is not present at room4, ball4 is not present at room5 and ball4 is present at room2.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, where lgripper1 drops ball7, and robot1's right gripper (rgripper1) drops ball3; subsequently, lgripper1 picks up ball1 in room1, and rgripper1 picks up ball3 in room1, after which robot1 moves from room1 to room5, dropping ball1 with lgripper1 and ball3 with rgripper1; from room5, robot1 proceeds to room2, where lgripper1 picks up ball2 and rgripper1 picks up ball4, then moves to room1, dropping ball4 with rgripper1; in room1, rgripper1 picks up ball6, and robot1 moves to room3, where rgripper1 drops ball6; from room3, rgripper1 picks up ball5, and robot1 moves to room6, reaching the current state. Are the following properties (with and without negations) true for ball4 before the first inexecutable action in the sequence? Specifically, is ball4 not located at room6, not in room1, not in room3, not in room4, not in room5, and is ball4 present in room2?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "367b18f7-18f4-4392-9876-fff5ece32d32", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, ball3 is dropped in room5 with rgripper1 by robot1, rgripper1 of robot1 drops ball1 in room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball6 before the first inexecutable action in the sequence? Ball6 is not being carried by robot1's rgripper1 and robot1 is carrying ball6 with lgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then use its lgripper1 to pick up ball1 in room1, and use its rgripper1 to pick up ball3 in room1. Next, robot1 will move to room5 from room1, drop ball3 in room5 using rgripper1, and then drop ball1 in room2 using rgripper1. From room2, robot1's rgripper1 will pick up ball4, and then robot1 will move back to room1 from room2. In room1, robot1 will drop ball4 using rgripper1 and pick up ball6 using rgripper1 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties true for ball6 (both with and without negations): ball6 is not being carried by robot1's rgripper1, and robot1 is carrying ball6 with lgripper1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "64a682f7-9b90-4cf3-ab1c-5d9d9e2d7c11", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Lgripper1 of robot1 is not free and robot1's rgripper1 is available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: the left gripper of robot1 is to grasp ball5 in room2 to achieve the current state. Are the following properties of the state (with and without negations) valid for robot1 prior to the first inexecutable action in the sequence? The left gripper of robot1 is occupied and the right gripper of robot1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "d3b753df-51c9-4aaf-966e-f4fd09de0672", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room5, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Ball3 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper1, ball6 is being carried by robot1's rgripper1, rgripper1 of robot1 is carrying ball7, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball4 and robot1 is carrying ball1 with rgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: from room5, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? It is not the case that robot1's rgripper1 is carrying ball3, robot1's rgripper1 is not holding ball5, robot1's rgripper1 is holding ball6, robot1's rgripper1 has ball7, robot1's rgripper1 does not have ball2, robot1's rgripper1 does not have ball4, and robot1 is holding ball1 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "ebe470ac-2241-4b88-bf52-36aa10604562", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball6 in room1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is not located at room2, ball1 is not present at room3, ball1 is not present at room4, ball1 is not present at room5, ball1 is present at room1, ball2 is located at room2, ball2 is not at room3, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room5, ball2 is not present at room1, ball2 is not present at room4, ball3 is at room1, ball3 is not at room2, ball3 is not located at room4, ball3 is not located at room5, ball3 is not present at room3, ball4 is located at room2, ball4 is not at room3, ball4 is not at room5, ball4 is not located at room1, ball4 is not located at room4, ball5 is at room2, ball5 is not at room5, ball5 is not present at room1, ball5 is not present at room3, ball5 is not present at room4, ball6 is at room1, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room3, ball6 is not located at room4, ball7 is not at room1, ball7 is not at room4, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room5, ball7 is not present at room2, ball7 is present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is at room4, robot1 is not at room1, robot1 is not at room5, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not located at room2, robot1 is not located at room3 and robot1's rgripper1 is available.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are intended to be executed: robot1's lgripper1 is supposed to pick up ball6 in room1 to achieve the current state. Is this the state preceding the first inexecutable action in the sequence? True or False? The location of the balls and the robot is as follows: ball1 is not in room2, room3, room4, or room5, but it is in room1; ball2 is in room2, but not in room1, room3, room4, or room5, and not being carried by robot1's rgripper1; ball3 is in room1, but not in room2, room3, room4, or room5; ball4 is in room2, but not in room1, room3, room4, or room5; ball5 is in room2, but not in room1, room3, room4, or room5; ball6 is in room1, but not in room2, room3, room4, or room5, and not being carried by robot1's lgripper1; ball7 is in room3, but not in room1, room2, room4, or room5, and not being carried by robot1's lgripper1 or rgripper1. Additionally, robot1's lgripper1 is free and not carrying any balls, robot1 is in room4, but not in room1, room2, room3, or room5, and robot1's rgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "5a148363-a8b2-4e4d-af1a-384df54ceb03", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, in room2, robot1's rgripper1 drops ball4, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper1 of robot1 is not free and rgripper1 of robot1 is free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: starting from room4, robot1 will move to room1, pick up ball1 in room1 using lgripper1, then proceed to room2, drop ball4 in room2 using rgripper1, move to room3, drop ball1 in room3 using lgripper1, return to room4, drop ball2 in room4 using rgripper1, move back to room2, and finally pick up ball4 in room2 using lgripper1 to reach the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first action in the sequence that cannot be executed: robot1's lgripper1 is occupied and robot1's rgripper1 is available?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "c2ed3be9-264e-4ae2-8a3c-510551347118", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, ball3 is picked from room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, ball2 is picked from room2 with lgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and from room3, robot1 moves to room6 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is being carried by robot1's lgripper1, ball1 is not at room1, ball1 is not at room6, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room3, ball2 is at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not at room5, ball2 is not located at room4, ball2 is not present at room6, ball3 is being carried by robot1's rgripper1, ball3 is not at room3, ball3 is not at room4, ball3 is not at room6, ball3 is not located at room1, ball3 is not located at room2, ball3 is not present at room5, ball4 is at room2, ball4 is not at room1, ball4 is not at room3, ball4 is not at room4, ball4 is not at room6, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room5, ball5 is located at room3, ball5 is not at room4, ball5 is not at room5, ball5 is not located at room2, ball5 is not located at room6, ball5 is not present at room1, ball6 is not at room3, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room4, ball6 is not located at room6, ball6 is not present at room5, ball6 is present at room1, ball7 is at room5, ball7 is not at room1, ball7 is not at room6, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room2, ball7 is not located at room3, ball7 is not present at room4, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not free, robot1 is at room5, robot1 is not at room1, robot1 is not at room2, robot1 is not at room4, robot1 is not at room6, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1 and robot1 is not present in room3.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: starting from room4, robot1's left gripper (lgripper1) picks up ball7, then robot1 moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and then uses its right gripper (rgripper1) to pick up ball3 in room1. Robot1 then moves from room1 to room5, picks up ball3 with lgripper1, and drops ball3 in room5 using rgripper1. After that, robot1 moves from room5 to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, then moves from room2 to room1 and drops ball4 in room1 using rgripper1. From room1, robot1's rgripper1 picks up ball6, moves to room3, and drops ball6 in room3. In room3, rgripper1 of robot1 picks up ball5, and then robot1 moves from room3 to room6 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state is as follows: robot1's lgripper1 is carrying ball1, ball1 is not in room1, room6, room4, room5, or room2, and it is not being carried by robot1's rgripper1. Ball2 is in room2, but not in room1, room3, room5, room4, or room6. Ball3 is being carried by robot1's rgripper1, but it is not in room3, room4, room6, room1, room2, or room5. Ball4 is in room2, but not in room1, room3, room4, room6, or being carried by robot1's rgripper1, and it is not in room5. Ball5 is in room3, but not in room4, room5, room2, room6, or room1. Ball6 is in room1, but not in room3, being carried by robot1's lgripper1, room2, room4, room6, or room5. Ball7 is in room5, but not in room1, room6, being carried by robot1's lgripper1, room2, room3, or room4. Additionally, robot1's lgripper1 is not carrying ball2 or ball4, and it is not free. Robot1's rgripper1 is not carrying ball6 or ball7, and it is not free. Robot1 is in room5, but not in room1, room2, room4, room6, or room3, and it is not carrying ball2 with rgripper1, ball3 with lgripper1, ball5 with lgripper1 or rgripper1.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "7b06a734-354d-4c40-93fd-d622098204cc", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room3, in room3, robot1's rgripper1 drops ball6, ball5 is picked from room3 with rgripper1 by robot1 and lgripper1 of robot1 drops ball7 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Ball1 is being carried by robot1's rgripper1, ball1 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball5 is being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is carrying ball6, robot1 is carrying ball3 with lgripper1, robot1 is carrying ball4 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball7 with lgripper1 and robot1 is not carrying ball7 with rgripper1.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, where lgripper1 drops ball7, and robot1 proceeds to room1. In room1, robot1's lgripper1 picks up ball1, and its right gripper (rgripper1) picks up ball3. Robot1 then moves to room5, where it drops ball1 with lgripper1 and ball3 with rgripper1. Next, robot1 moves to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, and then returns to room1. In room1, robot1 drops ball4 with rgripper1, picks up ball6 with rgripper1, and moves to room3. In room3, robot1 drops ball6 with rgripper1 and picks up ball5 with rgripper1. Meanwhile, robot1's lgripper1 drops ball7 in room2 to reach the current state. The question is whether the following properties (with and without negations) are true for robot1 before the first inexecutable action in the sequence: Is ball1 being carried by robot1's rgripper1? Is ball1 not being carried by robot1's lgripper1? Is ball3 not being carried by robot1's rgripper1? Is ball5 being carried by robot1's lgripper1? Is ball5 not being carried by robot1's rgripper1? Is lgripper1 of robot1 not carrying ball2? Is lgripper1 of robot1 not carrying ball6? Is rgripper1 of robot1 carrying ball6? Is robot1 carrying ball3 with lgripper1? Is robot1 carrying ball4 with lgripper1? Is robot1 not carrying ball2 with rgripper1? Is robot1 not carrying ball4 with rgripper1? Is robot1 not carrying ball7 with lgripper1? Is robot1 not carrying ball7 with rgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "26949c28-e65a-41a0-97b9-1690d2165b2a", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, from room3, robot1's lgripper1 picks up ball4, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Ball2 is being carried by robot1's lgripper1 and ball2 is being carried by robot1's rgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: starting from room4, robot1 will move to room1, where its left gripper (lgripper1) will pick up ball1, then robot1 will proceed to room2, where its right gripper (rgripper1) will pick up ball2, next, robot1 will move from room2 to room3, then from room3, its left gripper (lgripper1) will pick up ball4, after which robot1 will return to room4 from room3, and its right gripper (rgripper1) will drop ball2 in room4, subsequently, robot1 will move back to room2 from room4, and finally, its left gripper (lgripper1) will pick up ball4 to achieve the current state. Considering the state prior to the first inexecutable action in the sequence, are the following properties (including their negations) true for ball2: is ball2 being carried by robot1's left gripper (lgripper1), and is ball2 being carried by robot1's right gripper (rgripper1)?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present at room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "5eacfaf2-b8ea-4b04-8c5e-389e899e3c1a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is dropped in room2 with lgripper2 by robot2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is located at room3, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not present at room1, ball1 is not present at room2, ball2 is at room3, ball2 is not at room1, ball2 is not being carried by robot1's rgripper1, ball2 is not being carried by robot2's rgripper2, ball2 is not located at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's rgripper2, ball3 is not present at room1, ball3 is present at room2, ball4 is located at room3, ball4 is not being carried by robot1's rgripper2, ball4 is not located at room1, ball4 is not present at room2, ball5 is not being carried by robot2's rgripper2, ball5 is not present at room2, ball5 is not present at room3, ball5 is present at room1, ball6 is located at room1, ball6 is not at room2, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper1, ball6 is not being carried by robot2's rgripper2, ball6 is not located at room3, ball7 is located at room3, ball7 is not at room1, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's rgripper2, ball7 is not present at room2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot2 is not carrying ball2, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball4, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball5, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot2 is free, rgripper2 of robot2 is not carrying ball1, robot1 is at room2, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with lgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room1, robot1's lgripper1 is free, robot1's lgripper2 is not available, robot1's rgripper1 is free, robot1's rgripper2 is not available, robot2 is not at room3, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper1, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not carrying ball7 with rgripper1, robot2 is not located at room1, robot2 is present in room2 and robot2's rgripper1 is not available.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are intended to be executed: ball1 is to be dropped in room2 by robot2 using lgripper2 to achieve the current state. Is this the state preceding the first inexecutable action in the sequence? True or False? The current state is characterized by the following conditions: ball1 is in room3, not being held by robot1's lgripper2, not being held by robot2's lgripper2, not being held by robot2's rgripper1, not present in room1, and not present in room2. Ball2 is in room3, not in room1, not being carried by robot1's rgripper1, not being carried by robot2's rgripper2, and not in room2. Ball3 is not in room3, not being carried by robot1's lgripper2, not being carried by robot1's rgripper1, not being carried by robot1's rgripper2, not being carried by robot2's rgripper2, not in room1, but present in room2. Ball4 is in room3, not being carried by robot1's rgripper2, not in room1, and not in room2. Ball5 is not being carried by robot2's rgripper2, not in room2, not in room3, but present in room1. Ball6 is in room1, not in room2, not being carried by robot1's rgripper1, not being carried by robot2's lgripper1, not being carried by robot2's rgripper2, and not in room3. Ball7 is in room3, not in room1, not being carried by robot1's lgripper2, not being carried by robot1's rgripper1, not being carried by robot2's lgripper1, not being carried by robot2's rgripper2, and not in room2. Additionally, the following conditions apply to the grippers: robot1's lgripper1 is not carrying ball4, ball6, or ball7; robot2's lgripper1 is not carrying ball1, ball2, or ball4, and is not free; robot1's lgripper2 is not carrying ball5 or ball6; robot2's lgripper2 is free; robot1's rgripper1 is not carrying ball1, ball4, or ball5; robot2's rgripper1 is not carrying ball2, ball3, or ball4; robot1's rgripper2 is not carrying ball1, ball2, ball5, ball6, or ball7; and robot2's rgripper2 is free and not carrying ball1. Furthermore, robot1 is in room2, not in room3, not carrying ball1 with lgripper1, not carrying ball2 with lgripper1 or lgripper2, not carrying ball3 with lgripper1, not carrying ball4 with lgripper2, not carrying ball5 with lgripper1, not in room1, with lgripper1 free and lgripper2 and rgripper2 not available. Robot2 is not in room3, not carrying ball2 with lgripper2, not carrying ball3 with lgripper1 or lgripper2, not carrying ball4 with lgripper2 or rgripper2, not carrying ball5 with lgripper1, lgripper2, or rgripper1, not carrying ball6 with lgripper2 or rgripper1, not carrying ball7 with lgripper2 or rgripper1, not in room1, but present in room2, and rgripper1 is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "029ac4ea-2a4d-4e69-b5bc-63df3563092d", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves to room5 from room4, robot1 moves from room4 to room3, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5 and robot1 moves from room5 to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Robot1's lgripper1 is not available and robot1's rgripper1 is free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1's lgripper1 retrieves ball7 from room4, then robot1 relocates from room4 to room5, followed by a move from room4 to room3, then from room5 to room1, where lgripper1 of robot1 picks up ball1, and from room1, robot1's rgripper1 picks up ball3. Subsequently, robot1 moves from room1 to room5, drops ball1 in room5 using lgripper1, and rgripper1 of robot1 drops ball3 in room5, ultimately moving from room5 to room2 to reach the current state. The question is whether the following properties (including their negations) are true for robot1 before the first inexecutable action in the sequence: robot1's lgripper1 is unavailable and robot1's rgripper1 is free.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "6b67253d-a803-4486-8351-492d8f33a4a8", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, ball6 is dropped in room1 with rgripper2 by robot2, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper2 of robot2 is not free, rgripper1 of robot2 is not free, rgripper2 of robot2 is not free, robot1's lgripper1 is available, robot1's lgripper2 is not available, robot1's rgripper1 is free, robot1's rgripper2 is not free and robot2's lgripper1 is not free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then robot2's left gripper (lgripper2) will pick up ball1 in room3, followed by robot2's right gripper (rgripper2) picking up ball2 in room3. Next, robot2 will drop ball6 in room1 using rgripper2, then drop ball1 in room2 using lgripper2, and drop ball2 in room2 using rgripper2. After that, robot2 will move from room2 back to room3, pick up ball4 with lgripper2 in room3, and pick up ball7 with rgripper2 in room3. Finally, robot2 will move from room3 to room2 to reach the current state. The question is whether the following properties of the state (including both affirmative and negative statements) are true before the first action in the sequence that cannot be executed: robot2's lgripper2 is occupied, robot2's rgripper1 is occupied, robot2's rgripper2 is occupied, robot1's left gripper (lgripper1) is available, robot1's left gripper 2 (lgripper2) is not available, robot1's right gripper (rgripper1) is free, robot1's right gripper 2 (rgripper2) is not free, and robot2's left gripper 1 (lgripper1) is occupied.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "a376ca96-13cf-42b0-a996-5c2bbd402a08", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, from room4, robot1's lgripper1 picks up ball1, from room4, robot1 moves to room2 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is located at room2, ball1 is located at room4, ball1 is not located at room1, ball1 is not located at room5, ball1 is present at room3, ball2 is at room1, ball2 is at room3, ball2 is not at room2, ball2 is present at room4, ball2 is present at room5, ball3 is at room5, ball3 is not located at room4, ball3 is not present at room1, ball3 is not present at room2, ball3 is not present at room3, ball4 is at room1, ball4 is at room5, ball4 is being carried by robot1's rgripper1, ball4 is not at room3, ball4 is not at room4, ball4 is not being carried by robot1's lgripper1, ball4 is present at room2, ball5 is at room4, ball5 is being carried by robot1's lgripper1, ball5 is being carried by robot1's rgripper1, ball5 is located at room2, ball5 is not located at room3, ball5 is not present at room1, ball5 is not present at room5, ball6 is at room1, ball6 is being carried by robot1's lgripper1, ball6 is not at room4, ball6 is not present at room2, ball6 is not present at room5, ball6 is present at room3, lgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, robot1 is carrying ball1 with rgripper1, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball2 with rgripper1, robot1 is carrying ball3 with lgripper1, robot1 is located at room3, robot1 is located at room5, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room4, robot1 is not present in room1, robot1 is present in room2, robot1's lgripper1 is not available and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1, then moves to room2, where rgripper1 of robot1 picks up ball2, followed by a move to room3, where lgripper1 of robot1 drops ball1, then robot1 returns to room4 from room3, picks up ball1 with lgripper1 from room4, moves to room2 from room4, and finally, uses lgripper1 to pick up ball4 in room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current locations of the balls are as follows: ball1 is in room2, also in room4, but not in room1 or room5, and is present in room3, ball2 is in room1, room3, and room4, but not in room2, and is also present in room5, ball3 is in room5, but not in room1, room2, room3, or room4, ball4 is in room1, room2, and room5, is being carried by robot1's rgripper1, but not in room3 or room4, and is not being carried by robot1's lgripper1, ball5 is in room2 and room4, is being carried by both robot1's lgripper1 and rgripper1, but not in room1, room3, or room5, ball6 is in room1 and room3, is being carried by robot1's lgripper1, but not in room2, room4, or room5, the status of robot1's grippers is as follows: lgripper1 is not carrying ball1, rgripper1 is not carrying ball3, robot1 is carrying ball1 with rgripper1, ball2 with both lgripper1 and rgripper1, and ball3 with lgripper1, robot1 is located in room3 and room5, but not in room4, and is present in room2, but not in room1, and both of robot1's grippers are currently unavailable.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present at room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "b2aa4751-9bfa-41b3-b07d-75f4d61156f2", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, ball2 is picked from room5 with lgripper1 by robot1, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, rgripper1 of robot1 drops ball6 in room3, ball5 is picked from room3 with rgripper1 by robot1 and robot1 moves from room3 to room6 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Robot1's rgripper1 is available.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1, using lgripper1, retrieves ball7 from room4, then moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 proceeds from room5 to room1, where it picks up ball1 with lgripper1 and ball3 with rgripper1. Then, robot1 goes back to room5, picks up ball2 with lgripper1, drops ball1 in room5 using lgripper1, and drops ball3 in room5 using rgripper1. After that, robot1 moves from room5 to room2, picks up ball2 with lgripper1, and ball4 with rgripper1. Subsequently, robot1 returns to room1, drops ball4 using rgripper1, picks up ball6 with rgripper1, and then moves to room3. In room3, robot1 drops ball6 using rgripper1, picks up ball5 with rgripper1, and finally moves from room3 to room6 to reach the current state. Are the following properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Specifically, is robot1's rgripper1 available?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "371b7f66-2a4a-4469-a3f9-38966372c054", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, lgripper2 of robot2 drops ball4 in room2, from room3, robot2 moves to room2, in room2, robot2's rgripper2 drops ball7, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, robot2 moves from room1 to room2 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Rgripper1 of robot1 is free and robot2's rgripper1 is not available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then pick up ball1 from room3 using lgripper2 and ball2 from room3 using rgripper2, both with robot2. Next, robot2 will move back to room2 from room3, drop ball1 in room2 using lgripper2, and then drop ball2 in room2 using rgripper2. After that, robot2 will move to room3 again, pick up ball4 from room3 using lgripper2, and then drop ball4 in room2. Following this, robot2 will move back to room2, drop ball7 in room2 using rgripper2, pick up ball3 from room2 using rgripper2, and then move to room1. In room1, robot2 will drop ball4 using lgripper2, pick up ball5 using lgripper2, drop ball3 using rgripper2, pick up ball6 using rgripper2, and then move to room2. Finally, robot2 will drop ball5 in room2 using lgripper2 to reach the current state. The question is whether the following properties (with and without negations) are true for rgripper1 before the first inexecutable action in the sequence: is rgripper1 of robot1 free, and is robot2's rgripper1 not available?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "e771dd10-1600-4a8a-940c-4b6add0b22bf", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1's lgripper1 picks up ball3, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3 and from room5, robot1 moves to room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room3, ball1 is being carried by robot1's rgripper1, ball1 is located at room4, ball1 is not at room1, ball1 is not at room5, ball1 is not located at room6, ball1 is present at room2, ball2 is at room4, ball2 is at room5, ball2 is located at room1, ball2 is not located at room2, ball2 is not located at room6, ball2 is not present at room3, ball3 is at room4, ball3 is located at room5, ball3 is not at room6, ball3 is not located at room3, ball3 is not present at room1, ball3 is not present at room2, ball4 is not at room1, ball4 is not at room2, ball4 is not at room4, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not located at room6, ball4 is present at room5, ball5 is located at room6, ball5 is not located at room1, ball5 is not located at room5, ball5 is present at room2, ball5 is present at room3, ball5 is present at room4, ball6 is not located at room2, ball6 is not located at room3, ball6 is not located at room6, ball6 is not present at room1, ball6 is not present at room4, ball6 is not present at room5, ball7 is at room3, ball7 is not at room1, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room2, ball7 is not present at room6, ball7 is present at room4, ball7 is present at room5, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is carrying ball3, lgripper1 of robot1 is not free, rgripper1 of robot1 is carrying ball3, rgripper1 of robot1 is carrying ball7, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, robot1 is at room5, robot1 is carrying ball2 with rgripper1, robot1 is carrying ball6 with lgripper1, robot1 is located at room3, robot1 is not at room1, robot1 is not at room6, robot1 is not carrying ball5 with lgripper1, robot1 is not present in room2, robot1 is not present in room4 and robot1's rgripper1 is available.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then from room4, robot1's lgripper1 picks up ball3, next, robot1 drops ball7 in room5 using lgripper1, after which robot1 moves from room5 to room1, then from room1, robot1's lgripper1 picks up ball1, and ball3 is picked up from room1 by robot1's right gripper (rgripper1), from room1, robot1 moves to room5, then robot1's lgripper1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, and finally, robot1 moves from room5 to room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state of the balls and robot is as follows: ball1 is located at room3, it is being carried by robot1's right gripper, it is also located at room4, but not at room1, room5, or room6, and it is present at room2. Ball2 is at room4 and room5, also at room1, but not at room2 or room6, and not present at room3. Ball3 is at room4 and room5, but not at room6, room3, room1, or room2. Ball4 is not at room1, room2, room4, or being carried by robot1's grippers, not at room3 or room6, but present at room5. Ball5 is located at room6, not at room1 or room5, but present at room2, room3, and room4. Ball6 is not at room2, room3, room6, room1, room4, or room5. Ball7 is at room3, not at room1, not being carried by robot1's lgripper1, not at room2 or room6, but present at room4 and room5. \n\nThe state of robot1's grippers is as follows: robot1's lgripper1 is carrying ball1, ball2, and ball3, and is not free. Robot1's rgripper1 is carrying ball3 and ball7, not carrying ball5 or ball6. Robot1 is at room5, carrying ball2 with rgripper1, carrying ball6 with lgripper1, located at room3, not at room1 or room6, not carrying ball5 with lgripper1, not present in room2 or room4, and robot1's rgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "a24decf0-6f23-47e9-a87c-6af3a8187465", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is dropped in room2 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Ball1 is being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball6 is being carried by robot1's lgripper1, lgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is carrying ball4, robot1 is carrying ball2 with rgripper1, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball7 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with rgripper1 and robot1 is not carrying ball7 with rgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: ball3 is dropped in room2 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Robot1's lgripper1 is holding ball1, robot1's rgripper1 is not holding ball1, robot1's lgripper1 is not holding ball3, robot1's rgripper1 is not holding ball3, robot1's lgripper1 is holding ball6, robot1's lgripper1 has ball2, robot1's rgripper1 has ball4, robot1 is holding ball2 with its rgripper1, robot1 is holding ball5 with its rgripper1, robot1 is holding ball7 with its lgripper1, robot1 is not holding ball4 with its lgripper1, robot1 is not holding ball5 with its lgripper1, robot1 is not holding ball6 with its rgripper1, and robot1 is not holding ball7 with its rgripper1.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "7173da77-591b-4353-950b-caa93fcab5c6", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room6, in room5, robot1's lgripper1 drops ball7, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves from room5 to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball2 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's lgripper1, lgripper1 of robot1 is carrying ball7, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball7, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball4 with lgripper1 and robot1 is not carrying ball6 with rgripper1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1, using lgripper1, retrieves ball7 from room4, then uses lgripper1 to pick up ball1 in room6, drops ball7 in room5 using lgripper1, moves from room5 to room1, picks up ball1 in room1 with lgripper1, uses rgripper1 to pick up ball3 in room1, moves from room1 to room5, drops ball1 in room5 using lgripper1, drops ball3 in room5 using rgripper1, and finally moves from room5 to room2 to reach the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first unexecutable action in the sequence? Ball1 is not held by robot1's lgripper1, ball1 is not held by robot1's rgripper1, ball2 is not held by robot1's lgripper1, ball3 is not held by robot1's lgripper1, ball3 is not held by robot1's rgripper1, ball5 is not held by robot1's lgripper1, ball5 is not held by robot1's rgripper1, ball6 is not held by robot1's lgripper1, robot1's lgripper1 is holding ball7, robot1's rgripper1 is not holding ball4, robot1's rgripper1 is not holding ball7, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball4 with lgripper1, and robot1 is not holding ball6 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "64836c68-4a24-40ab-8e6a-22c27efd2b43", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot2's lgripper1 picks up ball4 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot2 before the first inexecutable action in the sequence? Rgripper1 of robot2 is not free, robot2's lgripper1 is not available, robot2's lgripper2 is free and robot2's rgripper2 is available.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: from room1, robot2's lgripper1 is to grasp ball4 to achieve the current state. Are the following properties of the state (both with and without negations) valid for robot2 prior to the first inexecutable action in the sequence? The rgripper1 of robot2 is occupied, robot2's lgripper1 is unavailable, robot2's lgripper2 is unoccupied and robot2's rgripper2 is available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "eedd5e85-a244-4445-88ca-cfbfb6a1a563", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, lgripper1 of robot1 drops ball6 in room2, robot1 moves to room4 from room3, ball1 is dropped in room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, ball2 is picked from room2 with lgripper1 by robot1 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball4 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's lgripper1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball7, robot1 is carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with rgripper1 and robot1 is not carrying ball6 with rgripper1.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following sequence of actions is planned: robot1 will move from room4 to room1, then pick up ball1 in room1 using lgripper1, followed by picking up ball3 in room1 using rgripper1, both with robot1. Next, robot1 will move from room1 to room5, drop ball3 in room5 using rgripper1, and then proceed to room2. In room2, rgripper1 of robot1 will pick up ball4, and robot1 will move to room1, where rgripper1 will drop ball4. Subsequently, rgripper1 of robot1 will pick up ball6 in room1, and robot1 will move to room3 to drop ball6. However, it seems there is an inconsistency as the text also mentions lgripper1 of robot1 dropping ball6 in room2, which is not in sequence. Following the sequence, robot1 will move to room4 from room3, drop ball1 in room4 using lgripper1, and rgripper1 of robot1 will drop ball7 in room4. Then, robot1 will move to room2 from room4, pick up ball2 in room2 using lgripper1, and finally, rgripper1 of robot1 will pick up ball5 in room2 to reach the current state. The question is whether the following properties (with and without negations) are true before the first inexecutable action in the sequence: Ball4 is not held by robot1's lgripper1, ball7 is not held by robot1's lgripper1, lgripper1 of robot1 is not holding ball2, lgripper1 of robot1 is not holding ball5, lgripper1 of robot1 is not holding ball6, rgripper1 of robot1 is not holding ball4, rgripper1 of robot1 is not holding ball7, robot1 is holding ball1 with lgripper1, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball5 with rgripper1, and robot1 is not holding ball6 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "c90f53f9-c53a-4c2e-99a8-b04799bb0d07", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, rgripper1 of robot1 drops ball4 in room6, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves to room2 from room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Robot1's lgripper1 is not available and robot1's rgripper1 is not free.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1's left gripper (lgripper1) will pick up ball7 in room4, robot1's right gripper (rgripper1) will drop ball4 in room6, then in room5, robot1's lgripper1 will drop ball7, after which robot1 will move from room5 to room1, pick up ball1 with its lgripper1 and ball3 with its rgripper1 in room1, move back to room5, drop ball1 with its lgripper1 and ball3 with its rgripper1 in room5, and finally move to room2 from room5 to reach the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first unexecutable action in the sequence: robot1's lgripper1 is occupied and robot1's rgripper1 is not available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "bc7bebd5-1e73-4275-a6e3-f665b1f2e7d4", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room3, robot1's lgripper1 drops ball6 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball3 before the first inexecutable action in the sequence? Robot1 is carrying ball3 with rgripper1 and robot1 is not carrying ball3 with lgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: in room3, robot1's lgripper1 releases ball6 to achieve the current state. Are the following properties of the state (both affirmative and negative) true for ball3 prior to the first inexecutable action in the sequence? Robot1 is holding ball3 with rgripper1 and robot1 is not holding ball3 with lgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "7f49c680-cde1-4325-be45-6038d151a032", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball4 is dropped in room3 with lgripper2 by robot1, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7, robot2 moves from room3 to room2, ball7 is dropped in room2 with rgripper2 by robot2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves from room2 to room1, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Robot1's rgripper1 is available and robot2's rgripper1 is free.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then use its left gripper (lgripper2) to pick up ball1 in room3, and its right gripper (rgripper2) to pick up ball2 in room3. Next, robot2 will move back to room2 from room3, and robot1 will drop ball4 in room3 using its left gripper (lgripper2). Then, robot2 will drop ball2 in room2 using its right gripper (rgripper2), move back to room3, and use its left gripper (lgripper2) to pick up ball4 in room3. Additionally, robot2's right gripper (rgripper2) will pick up ball7 in room3, and then robot2 will move to room2, drop ball7 using its right gripper (rgripper2), and pick up ball3 in room2 using the same gripper. After that, robot2 will move to room1, drop ball4 using its left gripper (lgripper2), pick up ball5 using the same gripper, drop ball3 using its right gripper (rgripper2), pick up ball6 using the same gripper, move back to room2, and finally drop ball5 using its left gripper (lgripper2) to reach the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for rgripper1 (both with and without negations): is robot1's rgripper1 available, and is robot2's rgripper1 free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "597ec61a-1b24-49bd-8f5a-c7eaa45eb3f5", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room3 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for room4 before the first inexecutable action in the sequence? Ball1 is not at room4, ball2 is not present at room4, ball3 is not at room4, ball4 is not at room4, ball5 is not at room4, ball6 is not located at room4, ball7 is at room4 and robot1 is present in room4.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: robot1 uses rgripper1 to pick ball1 from room3 to reach the current state. Are the following properties (including their negations) of the state true for room4 before the first inexecutable action in the sequence? Ball1 is not in room4, ball2 is not in room4, ball3 is not in room4, ball4 is not in room4, ball5 is not in room4, ball6 is not in room4, ball7 is in room4, and robot1 is in room4.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "701e49eb-29bc-450b-854d-04ed63140b38", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, ball4 is dropped in room4 with rgripper1 by robot1, ball4 is dropped in room1 with rgripper1 by robot1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball2 is not being carried by robot1's lgripper1, ball4 is being carried by robot1's rgripper1, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's lgripper1, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball7, robot1 is carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper1 and robot1 is not carrying ball5 with rgripper1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1 and rgripper1 to pick up ball3, then moves to room5, drops ball3 in room5 using rgripper1, moves to room2, picks up ball4 in room2 with rgripper1, drops ball4 in room4 and then in room1 using rgripper1, and finally, from room1, robot1's rgripper1 picks up ball6 to reach the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first unexecutable action in the sequence? The properties in question are: robot1's lgripper1 is not holding ball2, robot1's rgripper1 is holding ball4, robot1's lgripper1 is not holding ball6, robot1's rgripper1 is not holding ball6, robot1's lgripper1 is not holding ball7, robot1's lgripper1 is not carrying ball5, robot1's rgripper1 is not carrying ball3, robot1's rgripper1 is not carrying ball7, robot1 is holding ball1 with lgripper1, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball4 with lgripper1, and robot1 is not holding ball5 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "9d7040f7-b8b0-47e1-9b0e-3ea22870bdbb", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, ball4 is dropped in room4 with rgripper1 by robot1, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves to room2 from room5 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is not at room3, ball1 is not at room6, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball1 is present at room1, ball2 is at room2, ball2 is not located at room1, ball2 is not located at room3, ball2 is not located at room4, ball2 is not located at room5, ball2 is not present at room6, ball3 is located at room1, ball3 is not at room6, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room3, ball3 is not present at room2, ball3 is not present at room4, ball3 is not present at room5, ball4 is at room2, ball4 is not at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room1, ball4 is not present at room4, ball4 is not present at room5, ball4 is not present at room6, ball5 is at room3, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room1, ball5 is not located at room4, ball5 is not located at room5, ball5 is not present at room2, ball5 is not present at room6, ball6 is not at room5, ball6 is not located at room3, ball6 is not located at room6, ball6 is not present at room2, ball6 is not present at room4, ball6 is present at room1, ball7 is not at room1, ball7 is not at room2, ball7 is not at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not present at room4, ball7 is not present at room6, ball7 is present at room5, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball7, robot1 is located at room5, robot1 is not at room3, robot1 is not at room6, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not present in room1, robot1 is not present in room2, robot1 is not present in room4 and robot1's lgripper1 is available.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1, using lgripper1, picks up ball7 from room4, then moves from room4 to room5, and in room5, robot1's lgripper1 releases ball7. Next, robot1 drops ball4 in room4 using rgripper1. From room1, robot1's lgripper1 picks up ball1, and rgripper1 of robot1 picks up ball3 in room1. Then, robot1 moves from room1 to room5, drops ball1 in room5 using lgripper1, and drops ball3 in room5 using rgripper1. Finally, robot1 moves from room5 to room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state is characterized by the following conditions: ball1 is not in room3, room6, or being carried by robot1's lgripper1, and it is not present in room5, room2, or room4, but it is present in room1. Ball2 is in room2, but not in room1, room3, room4, room5, or room6. Ball3 is in room1, but not in room6, not being carried by robot1's lgripper1, and not present in room3, room2, room4, or room5. Ball4 is in room2, but not in room3, not being carried by robot1's lgripper1, and not present in room1, room4, room5, or room6. Ball5 is in room3, but not being carried by robot1's rgripper1, and not present in room1, room4, room5, room2, or room6. Ball6 is not in room5, room3, room6, room2, or room4, but it is present in room1. Ball7 is not in room1, room2, room3, not being carried by robot1's lgripper1, and not present in room4 or room6, but it is present in room5. Additionally, robot1's lgripper1 is not carrying ball2, ball5, or ball6, and rgripper1 of robot1 is free and not carrying ball3, ball4, or ball7. Robot1 is located in room5, but not in room3 or room6, and it is not carrying ball1, ball2, or ball6 with rgripper1. Robot1 is not present in room1, room2, or room4, and robot1's lgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "1d8d5e18-6426-42be-b0d9-08dd5f998c6f", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball4 in room2, ball5 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room5, ball4 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball3, rgripper1 of robot1 drops ball5 in room1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room5 from room1 and in room2, robot1's lgripper1 drops ball1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room2, ball1 is being carried by robot1's rgripper1, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room3, ball1 is present at room1, ball2 is at room3, ball2 is located at room1, ball2 is located at room2, ball2 is located at room4, ball2 is located at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball3 is being carried by robot1's lgripper1, ball3 is located at room5, ball3 is not at room1, ball3 is not at room2, ball3 is not present at room3, ball3 is present at room4, ball4 is at room2, ball4 is located at room5, ball4 is not at room4, ball4 is not located at room1, ball4 is present at room3, ball5 is located at room2, ball5 is located at room4, ball5 is not located at room5, ball5 is not present at room1, ball5 is not present at room3, ball6 is located at room1, ball6 is not at room3, ball6 is not at room5, ball6 is not located at room2, ball6 is present at room4, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not free, rgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is carrying ball5, rgripper1 of robot1 is not carrying ball6, robot1 is carrying ball1 with lgripper1, robot1 is carrying ball5 with lgripper1, robot1 is located at room5, robot1 is not carrying ball3 with rgripper1, robot1 is not located at room3, robot1 is not located at room4, robot1 is not present in room1, robot1 is present in room2 and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned: starting from room4, robot1 will move to room1, pick up ball1 from room1 using lgripper1, then move to room2, pick up ball2 in room2 using rgripper1, proceed to room3, drop ball1 in room3 using lgripper1, and return to room4. Then, robot1 will drop ball2 in room4 using rgripper1, move to room2, pick up ball4 in room2 using lgripper1, and pick up ball5 in room2 using rgripper1. Next, robot1 will move to room5, drop ball4 in room5 using lgripper1, return to room1, pick up ball3 using lgripper1, drop ball5 in room1 using rgripper1, pick up ball6 using rgripper1, and move to room5. Finally, in room2, robot1 will drop ball1 using lgripper1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state is as follows: ball1 is in room2 and being carried by robot1's rgripper1, but not in room4, room5, or room3, and is present in room1. Ball2 is in room3, room1, room2, room4, and room5, but not being carried by robot1's lgripper1 or rgripper1. Ball3 is being carried by robot1's lgripper1, in room5, but not in room1, room2, room3, or room4. Ball4 is in room2 and room5, but not in room4 or room1, and is present in room3. Ball5 is in room2 and room4, but not in room5, room1, or room3. Ball6 is in room1, but not in room3, room5, room2, and is present in room4. The lgripper1 of robot1 is carrying ball4, but not ball6, and is not free. The rgripper1 of robot1 is carrying ball4 and ball5, but not ball6. Robot1 is carrying ball1 with lgripper1, but not ball3 with rgripper1, and is located in room5, but not in room3 or room4, and is present in room2, but its rgripper1 is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "0e54da2b-18a1-4e1e-9ed6-05cd6bac0ab7", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room3 with rgripper1 by robot2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot1's rgripper1, ball2 is being carried by robot1's lgripper1, ball2 is being carried by robot2's lgripper2, ball2 is not being carried by robot2's rgripper2, ball3 is not being carried by robot2's rgripper2, ball4 is being carried by robot1's lgripper2, ball4 is being carried by robot2's lgripper1, ball4 is being carried by robot2's rgripper2, ball6 is being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper1, ball7 is being carried by robot1's rgripper2, ball7 is not being carried by robot1's lgripper2, lgripper1 of robot1 is carrying ball3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is carrying ball1, lgripper1 of robot2 is carrying ball7, lgripper1 of robot2 is not carrying ball5, lgripper2 of robot1 is carrying ball5, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is carrying ball1, lgripper2 of robot2 is carrying ball5, lgripper2 of robot2 is not carrying ball4, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is carrying ball4, rgripper1 of robot2 is carrying ball4, rgripper1 of robot2 is not carrying ball6, rgripper2 of robot1 is carrying ball2, rgripper2 of robot1 is not carrying ball5, rgripper2 of robot2 is carrying ball5, rgripper2 of robot2 is not carrying ball6, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball6 with rgripper2, robot1 is carrying ball7 with lgripper1, robot1 is carrying ball7 with rgripper1, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with rgripper1, robot2 is carrying ball1 with rgripper2, robot2 is carrying ball2 with lgripper1, robot2 is carrying ball2 with rgripper1, robot2 is carrying ball3 with rgripper1, robot2 is carrying ball5 with rgripper1, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball7 with lgripper2, robot2 is not carrying ball7 with rgripper1 and robot2 is not carrying ball7 with rgripper2.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room3 with rgripper1 by robot2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? \n\nThe following conditions are being evaluated: \n- Ball1 is being held by robot1's rgripper1, \n- Ball2 is being held by robot1's lgripper1, \n- Ball2 is being held by robot2's lgripper2, \n- Ball2 is not being held by robot2's rgripper2, \n- Ball3 is not being held by robot2's rgripper2, \n- Ball4 is being held by robot1's lgripper2, \n- Ball4 is being held by robot2's lgripper1, \n- Ball4 is being held by robot2's rgripper2, \n- Ball6 is being held by robot1's rgripper1, \n- Ball6 is not being held by robot2's lgripper1, \n- Ball7 is being held by robot1's rgripper2, \n- Ball7 is not being held by robot1's lgripper2, \n- Robot1's lgripper1 is holding ball3, \n- Robot1's lgripper1 is not holding ball5, \n- Robot1's lgripper1 is not holding ball6, \n- Robot2's lgripper1 is holding ball1, \n- Robot2's lgripper1 is holding ball7, \n- Robot2's lgripper1 is not holding ball5, \n- Robot1's lgripper2 is holding ball5, \n- Robot1's lgripper2 is not holding ball2, \n- Robot1's lgripper2 is not holding ball3, \n- Robot1's lgripper2 is not holding ball6, \n- Robot2's lgripper2 is holding ball1, \n- Robot2's lgripper2 is holding ball5, \n- Robot2's lgripper2 is not holding ball4, \n- Robot2's lgripper2 is not holding ball6, \n- Robot1's rgripper1 is holding ball2, \n- Robot1's rgripper1 is holding ball4, \n- Robot2's rgripper1 is holding ball4, \n- Robot2's rgripper1 is not holding ball6, \n- Robot1's rgripper2 is holding ball2, \n- Robot1's rgripper2 is not holding ball5, \n- Robot2's rgripper2 is holding ball5, \n- Robot2's rgripper2 is not holding ball6, \n- Robot1 is holding ball3 with rgripper1, \n- Robot1 is holding ball6 with rgripper2, \n- Robot1 is holding ball7 with lgripper1, \n- Robot1 is holding ball7 with rgripper1, \n- Robot1 is not holding ball1 with lgripper1, \n- Robot1 is not holding ball1 with lgripper2, \n- Robot1 is not holding ball1 with rgripper2, \n- Robot1 is not holding ball3 with rgripper2, \n- Robot1 is not holding ball4 with lgripper1, \n- Robot1 is not holding ball4 with rgripper2, \n- Robot1 is not holding ball5 with rgripper1, \n- Robot2 is holding ball1 with rgripper2, \n- Robot2 is holding ball2 with lgripper1, \n- Robot2 is holding ball2 with rgripper1, \n- Robot2 is holding ball3 with rgripper1, \n- Robot2 is holding ball5 with rgripper1, \n- Robot2 is not holding ball1 with rgripper1, \n- Robot2 is not holding ball3 with lgripper1, \n- Robot2 is not holding ball3 with lgripper2, \n- Robot2 is not holding ball7 with lgripper2, \n- Robot2 is not holding ball7 with rgripper1 and \n- Robot2 is not holding ball7 with rgripper2.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "a7d61059-7c69-4073-8644-0ade17ee5ffc", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball5 is dropped in room4 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is at room2, ball1 is at room3, ball1 is not at room4, ball1 is not located at room5, ball1 is present at room1, ball2 is located at room5, ball2 is not at room4, ball2 is not located at room1, ball2 is present at room2, ball2 is present at room3, ball3 is not at room1, ball3 is not located at room5, ball3 is not present at room2, ball3 is not present at room3, ball3 is not present at room4, ball4 is at room1, ball4 is not present at room2, ball4 is not present at room3, ball4 is present at room4, ball4 is present at room5, ball5 is located at room3, ball5 is not located at room5, ball5 is not present at room1, ball5 is present at room2, ball5 is present at room4, ball6 is not located at room5, ball6 is not present at room1, ball6 is not present at room2, ball6 is not present at room3, ball6 is present at room4, robot1 is located at room1, robot1 is not at room4, robot1 is not located at room2, robot1 is present in room3 and robot1 is present in room5.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: robot1 uses rgripper1 to drop ball5 in room4 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is in room2, ball1 is in room3, ball1 is not in room4, ball1 is not in room5, ball1 is in room1, ball2 is in room5, ball2 is not in room4, ball2 is not in room1, ball2 is in room2, ball2 is in room3, ball3 is not in room1, ball3 is not in room5, ball3 is not in room2, ball3 is not in room3, ball3 is not in room4, ball4 is in room1, ball4 is not in room2, ball4 is not in room3, ball4 is in room4, ball4 is in room5, ball5 is in room3, ball5 is not in room5, ball5 is not in room1, ball5 is in room2, ball5 is in room4, ball6 is not in room5, ball6 is not in room1, ball6 is not in room2, ball6 is not in room3, ball6 is in room4, robot1 is in room1, robot1 is not in room4, robot1 is not in room2, robot1 is in room3 and robot1 is in room5.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "25f9d96c-774b-4100-926a-6c7f95597922", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, from room3, robot1's lgripper1 picks up ball4, from room4, robot1 moves to room2, ball4 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball5, robot1 moves from room2 to room5, in room5, robot1's lgripper1 drops ball4, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball3, ball5 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room5 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Rgripper1 of robot1 is not free and robot1's lgripper1 is available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then pick up ball1 from room1 using lgripper1, proceed to room2 from room1, pick up ball2 from room2 using rgripper1, move to room3 from room2, drop ball1 in room3 using lgripper1, return to room4 from room3, pick up ball4 from room3 using lgripper1, move to room2 from room4, pick up ball4 from room2 using lgripper1, pick up ball5 from room2 using rgripper1, move to room5 from room2, drop ball4 in room5 using lgripper1, move to room1 from room5, pick up ball3 from room1 using lgripper1, drop ball5 in room1 using rgripper1, pick up ball6 from room1 using rgripper1, move to room5 from room1, and finally drop ball3 in room5 using lgripper1 to achieve the current state. The question is whether the following properties (with and without negations) are true before the first inexecutable action in the sequence: robot1's rgripper1 is occupied and robot1's lgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "4be1f345-a1a4-4d6f-af15-71b677e8561b", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, from room3, robot1 moves to room4, robot1 moves from room1 to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves to room2 from room5, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball7 in room3, robot1 moves from room3 to room4, ball1 is dropped in room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball7 in room4, from room4, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Robot1's lgripper1 is not free and robot1's rgripper1 is available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1. Next, robot1 moves from room3 to room4, followed by a move from room1 to room5. In room5, robot1's rgripper1 releases ball3. Robot1 then proceeds to room2 from room5, picks up ball4 with rgripper1 in room2, and moves to room1. Upon arrival in room1, robot1's rgripper1 drops ball4. Subsequently, rgripper1 of robot1 picks up ball6 in room1, and robot1 moves to room3. In room3, ball6 is dropped using rgripper1 by robot1, and rgripper1 of robot1 picks up ball7. Robot1 then moves from room3 to room4, drops ball1 with lgripper1 in room4, and releases ball7 with rgripper1 in room4. From room4, robot1 moves to room2, and in room2, robot1's lgripper1 picks up ball2, while robot1's rgripper1 picks up ball5, ultimately reaching the current state. The question is whether the following properties (with and without negations) are true before the first inexecutable action in the sequence: robot1's lgripper1 is occupied and robot1's rgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "9b2ad42a-c12e-4653-bd40-474c09224b8b", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room2 from room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is located at room1, ball1 is located at room2, ball1 is not located at room3, ball1 is not located at room5, ball1 is not present at room6, ball1 is present at room4, ball2 is not at room1, ball2 is not located at room2, ball2 is not located at room4, ball2 is not located at room5, ball2 is not present at room3, ball2 is present at room6, ball3 is at room5, ball3 is located at room1, ball3 is not located at room4, ball3 is not located at room6, ball3 is not present at room3, ball3 is present at room2, ball4 is at room6, ball4 is not at room2, ball4 is not at room5, ball4 is present at room1, ball4 is present at room3, ball4 is present at room4, ball5 is at room2, ball5 is at room4, ball5 is at room5, ball5 is not at room6, ball5 is not located at room1, ball5 is present at room3, ball6 is at room1, ball6 is at room2, ball6 is at room6, ball6 is located at room5, ball6 is not at room3, ball6 is not located at room4, ball7 is at room2, ball7 is at room4, ball7 is located at room1, ball7 is not at room5, ball7 is not present at room6, ball7 is present at room3, robot1 is at room2, robot1 is not located at room3, robot1 is not located at room5, robot1 is not present in room4, robot1 is present in room1 and robot1 is present in room6.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room2 from room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? \n\nThe location of ball1 is room1, ball1 is in room2, ball1 is not in room3, ball1 is not in room5, ball1 is absent from room6, ball1 is present in room4, ball2 is not in room1, ball2 is not in room2, ball2 is not in room4, ball2 is not in room5, ball2 is absent from room3, ball2 is in room6, ball3 is in room5, ball3 is also in room1, ball3 is not in room4, ball3 is not in room6, ball3 is absent from room3, ball3 is in room2, ball4 is in room6, ball4 is not in room2, ball4 is not in room5, ball4 is present in room1, ball4 is present in room3, ball4 is present in room4, ball5 is in room2, ball5 is in room4, ball5 is in room5, ball5 is not in room6, ball5 is absent from room1, ball5 is present in room3, ball6 is in room1, ball6 is in room2, ball6 is in room6, ball6 is also in room5, ball6 is not in room3, ball6 is not in room4, ball7 is in room2, ball7 is in room4, ball7 is in room1, ball7 is not in room5, ball7 is absent from room6, ball7 is present in room3, robot1 is in room2, robot1 is not in room3, robot1 is not in room5, robot1 is absent from room4, robot1 is present in room1 and robot1 is present in room6.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "633ba48b-7252-4fae-8956-3ad499cee4d4", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, ball7 is dropped in room1 with lgripper1 by robot1, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and from room3, robot1 moves to room6 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Robot1's lgripper1 is not free and robot1's rgripper1 is free.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1, using lgripper1, picks up ball7 from room4, then moves from room4 to room5, drops ball7 in room1 using lgripper1, and proceeds to move from room5 to room1. In room1, robot1's lgripper1 picks up ball1, while its rgripper1 picks up ball3. Robot1 then moves from room1 to room5, drops ball1 using lgripper1, and drops ball3 using rgripper1 in room5. Next, robot1 moves from room5 to room2, picks up ball2 with lgripper1, and ball4 with rgripper1 in room2. Robot1 then moves from room2 to room1, drops ball4 using rgripper1, and picks up ball6 with rgripper1 in room1. From room1, robot1 moves to room3, drops ball6 using rgripper1, picks up ball5 with rgripper1, and finally moves from room3 to room6 to reach the current state. The question is whether the following properties (with and without negations) are true for robot1 before the first inexecutable action in the sequence: robot1's lgripper1 is occupied and robot1's rgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "c3bd84d7-ce6f-429f-a97e-bccc0ecc66d8", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball7 is picked from room2 with rgripper1 by robot1, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, ball5 is picked from room3 with rgripper1 by robot1 and robot1 moves from room3 to room6 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball2 is being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's lgripper1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with lgripper1 and robot1 is not carrying ball7 with rgripper1.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1, using lgripper1, retrieves ball7 from room4, then proceeds from room4 to room5, where it releases ball7 using lgripper1, and subsequently moves from room5 to room1. In room1, robot1's lgripper1 picks up ball1, while its rgripper1 picks up ball3. Robot1 then moves from room1 to room5, where it drops ball1 using lgripper1 and ball3 using rgripper1. Next, robot1 moves from room5 to room2, picks up ball2 with lgripper1, and then uses its rgripper1 to pick up ball4. Robot1 then moves from room2 to room1, where it drops ball4 using rgripper1. Additionally, robot1 picks up ball7 from room2 using rgripper1, moves from room1 to room3, drops ball6 using rgripper1, picks up ball5 with rgripper1, and finally moves from room3 to room6 to reach the current state. The question is whether the following properties of the state (including both affirmative and negative statements) are true before the first inexecutable action in the sequence: ball1 is not held by robot1's lgripper1, ball1 is not held by robot1's rgripper1, ball2 is held by robot1's lgripper1, ball4 is not held by robot1's lgripper1, ball5 is not held by robot1's rgripper1, ball7 is not held by robot1's lgripper1, robot1's lgripper1 is not holding ball3, robot1's lgripper1 is not holding ball5, robot1's rgripper1 is not holding ball3, robot1's rgripper1 is not holding ball4, robot1's rgripper1 is not holding ball6, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball6 with lgripper1, and robot1 is not holding ball7 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "c4b6e178-63f1-48c8-a4e3-a3467847a076", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room5, robot1's lgripper1 drops ball2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room2, ball1 is located at room3, ball1 is located at room5, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not present at room1, ball2 is being carried by robot1's lgripper1, ball2 is located at room3, ball2 is not at room4, ball2 is not located at room5, ball2 is not present at room2, ball2 is present at room1, ball3 is at room3, ball3 is at room5, ball3 is being carried by robot1's lgripper1, ball3 is located at room1, ball3 is located at room4, ball3 is not being carried by robot1's rgripper1, ball3 is present at room2, ball4 is located at room1, ball4 is located at room3, ball4 is not at room2, ball4 is not at room5, ball4 is not being carried by robot1's rgripper1, ball4 is not present at room4, ball5 is at room5, ball5 is being carried by robot1's lgripper1, ball5 is not at room3, ball5 is not located at room1, ball5 is not present at room4, ball5 is present at room2, ball6 is at room2, ball6 is at room3, ball6 is located at room1, ball6 is located at room5, ball6 is not at room4, ball7 is not at room1, ball7 is not at room5, ball7 is not located at room4, ball7 is not present at room2, ball7 is not present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball7, robot1 is carrying ball1 with lgripper1, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball7 with lgripper1, robot1 is located at room3, robot1 is located at room4, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room5, robot1 is present in room1 and robot1 is present in room2.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: in room5, robot1's lgripper1 releases ball2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current state of the balls and robot is as follows: ball1 is in room2, ball1 is also in room3, ball1 is also in room5, ball1 is not in room4, ball1 is not being held by robot1's rgripper1, ball1 is not present in room1, ball2 is being carried by robot1's lgripper1, ball2 is in room3, ball2 is not in room4, ball2 is not in room5, ball2 is not present in room2, ball2 is present in room1, ball3 is in room3, ball3 is in room5, ball3 is being carried by robot1's lgripper1, ball3 is in room1, ball3 is in room4, ball3 is not being held by robot1's rgripper1, ball3 is present in room2, ball4 is in room1, ball4 is in room3, ball4 is not in room2, ball4 is not in room5, ball4 is not being held by robot1's rgripper1, ball4 is not present in room4, ball5 is in room5, ball5 is being carried by robot1's lgripper1, ball5 is not in room3, ball5 is not in room1, ball5 is not present in room4, ball5 is present in room2, ball6 is in room2, ball6 is in room3, ball6 is in room1, ball6 is in room5, ball6 is not in room4, ball7 is not in room1, ball7 is not in room5, ball7 is not in room4, ball7 is not present in room2, ball7 is not present in room3, robot1's lgripper1 is available, robot1's lgripper1 is not carrying ball4, robot1's rgripper1 is available, robot1's rgripper1 is not carrying ball7, robot1 is carrying ball1 with lgripper1, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball7 with lgripper1, robot1 is in room3, robot1 is in room4, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not in room5, robot1 is present in room1 and robot1 is present in room2.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "43eca323-6908-4b9c-828c-3da715023397", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, from room5, robot1's lgripper1 picks up ball4, from room4, robot1 moves to room2 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Robot1 is carrying ball2 with rgripper1 and robot1 is not carrying ball2 with lgripper1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then robot1's left gripper (lgripper1) will pick up ball1 in room1. Next, robot1 will move from room1 to room2, and then robot1's right gripper (rgripper1) will pick up ball2 in room2. After that, robot1 will move from room2 to room3, and then robot1's lgripper1 will drop ball1 in room3. Following this, robot1 will move from room3 to room4, then from room4 to room5 is not mentioned but from room5, robot1's lgripper1 will pick up ball4, then robot1 will move from room4 to room2 and robot1's lgripper1 will pick up ball4 in room2 to reach the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for ball2 (both with and without negations): robot1 is holding ball2 with its rgripper1, and robot1 is not holding ball2 with its lgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "09a15f8f-e3bb-407c-afbe-2ca50d330718", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves from room4 to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, lgripper1 of robot1 picks up ball2 in room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, from room1, robot1's rgripper1 picks up ball6, ball6 is picked from room6 with rgripper1 by robot1, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room5, ball1 is located at room1, ball1 is not at room3, ball1 is present at room2, ball1 is present at room4, ball1 is present at room6, ball2 is located at room3, ball2 is not at room2, ball2 is not at room4, ball2 is not located at room5, ball2 is present at room1, ball2 is present at room6, ball3 is located at room3, ball3 is located at room4, ball3 is located at room5, ball3 is not at room1, ball3 is not at room2, ball3 is present at room6, ball4 is located at room1, ball4 is not at room3, ball4 is not at room6, ball4 is not located at room5, ball4 is not present at room2, ball4 is not present at room4, ball5 is located at room1, ball5 is located at room4, ball5 is not located at room2, ball5 is not located at room5, ball5 is not present at room3, ball5 is present at room6, ball6 is being carried by robot1's lgripper1, ball6 is located at room1, ball6 is located at room3, ball6 is located at room4, ball6 is not located at room5, ball6 is not located at room6, ball6 is present at room2, ball7 is not at room4, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room1, ball7 is not located at room6, ball7 is not present at room3, ball7 is not present at room5, ball7 is present at room2, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, robot1 is at room2, robot1 is at room5, robot1 is carrying ball1 with lgripper1, robot1 is carrying ball4 with rgripper1, robot1 is carrying ball5 with rgripper1, robot1 is located at room6, robot1 is not at room4, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room3, robot1 is not present in room1 and robot1's lgripper1 is available.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: starting from room4, robot1's left gripper (lgripper1) picks up ball7, then robot1 moves from room4 to room5, drops ball7 in room5 using lgripper1, and proceeds to room1 from room5. In room1, robot1's lgripper1 picks up ball1, and its right gripper (rgripper1) picks up ball3. Robot1 then moves to room5 from room1, drops ball1 and ball3 in room5 using lgripper1 and rgripper1 respectively, and then moves to room2. In room2, robot1's lgripper1 picks up ball2, and its rgripper1 picks up ball4. Robot1 then moves to room1 from room2, drops ball4 in room1 using rgripper1, and in room1, robot1's rgripper1 picks up ball6. However, it is mentioned that ball6 is picked from room6, and then dropped in room3 by robot1's rgripper1. Additionally, robot1's rgripper1 picks up ball5 in room3, and robot1 moves to room6 from room3 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state of the balls and robot1 is as follows: \n- Ball1 is located at room5, but not at room1, room3, room2, room4, or room6.\n- Ball2 is not at room2, room4, or room5, but is present at room1, room3, and room6.\n- Ball3 is located at room3, room4, and room5, but not at room1, room2, or room6.\n- Ball4 is located at room1, but not at room3, room5, room2, room4, or room6.\n- Ball5 is located at room1 and room4, but not at room2, room3, room5, or room6.\n- Ball6 is being carried by robot1's lgripper1, and is located at room1 and room3, but not at room5 or room6, and is present at room2 and room4.\n- Ball7 is not at room4, not being carried by robot1's rgripper1, and not located at room1, room3, room5, or room6, but is present at room2.\n- The status of robot1's grippers is as follows: \n  - Lgripper1 is carrying ball2, but not ball5.\n  - Rgripper1 is free, not carrying ball1, ball2, or ball6.\n- The location of robot1 is as follows: \n  - Robot1 is at room2 and room5, carrying ball1 with lgripper1 and ball4 with rgripper1, and is located at room6.\n  - Robot1 is not at room4, not carrying ball3 with lgripper1 or rgripper1, not carrying ball7 with lgripper1, not located at room3, and not present in room1.\n  - Robot1's lgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "8b065f69-65c1-4445-881c-36e3b40014c5", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, lgripper1 of robot1 drops ball3 in room4, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5 and robot1 moves to room2 from room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not at room4, ball1 is not at room5, ball1 is not located at room2, ball1 is not located at room6, ball1 is not present at room1, ball1 is not present at room3, ball2 is located at room2, ball2 is not at room3, ball2 is not at room6, ball2 is not located at room1, ball2 is not present at room4, ball2 is not present at room5, ball3 is at room1, ball3 is not at room2, ball3 is not at room3, ball3 is not at room4, ball3 is not present at room5, ball3 is not present at room6, ball4 is not at room4, ball4 is not at room5, ball4 is not located at room1, ball4 is not located at room3, ball4 is not located at room6, ball4 is present at room2, ball5 is located at room3, ball5 is not at room5, ball5 is not located at room2, ball5 is not located at room4, ball5 is not present at room1, ball5 is not present at room6, ball6 is not at room4, ball6 is not at room5, ball6 is not located at room3, ball6 is not located at room6, ball6 is not present at room2, ball6 is present at room1, ball7 is located at room5, ball7 is not at room1, ball7 is not at room2, ball7 is not at room4, ball7 is not located at room3, ball7 is not present at room6, robot1 is at room1, robot1 is not at room2, robot1 is not at room3, robot1 is not at room5, robot1 is not present in room4 and robot1 is not present in room6.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: starting from room4, robot1's left gripper (lgripper1) will pick up ball7, then robot1 will move from room4 to room5, and in room5, robot1's lgripper1 will drop ball7. Next, robot1 will move from room5 to room1, and from room1, robot1's lgripper1 will pick up ball1. Then, robot1's lgripper1 will drop ball3 in room4, and robot1 will move from room1 to room5. In room5, robot1's lgripper1 will drop ball1, and robot1's right gripper (rgripper1) will drop ball3 in room5. Finally, robot1 will move from room5 to room2 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not in room4, ball1 is not in room5, ball1 is not in room2, ball1 is not in room6, ball1 is not in room1, ball1 is not in room3, ball2 is in room2, ball2 is not in room3, ball2 is not in room6, ball2 is not in room1, ball2 is not in room4, ball2 is not in room5, ball3 is in room1, ball3 is not in room2, ball3 is not in room3, ball3 is not in room4, ball3 is not in room5, ball3 is not in room6, ball4 is not in room4, ball4 is not in room5, ball4 is not in room1, ball4 is not in room3, ball4 is not in room6, ball4 is in room2, ball5 is in room3, ball5 is not in room5, ball5 is not in room2, ball5 is not in room4, ball5 is not in room1, ball5 is not in room6, ball6 is not in room4, ball6 is not in room5, ball6 is not in room3, ball6 is not in room6, ball6 is not in room2, ball6 is in room1, ball7 is in room5, ball7 is not in room1, ball7 is not in room2, ball7 is not in room4, ball7 is not in room3, ball7 is not in room6, robot1 is in room1, robot1 is not in room2, robot1 is not in room3, robot1 is not in room5, robot1 is not in room4, and robot1 is not in room6.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "1717ce35-951f-46e7-ac3f-334c503d78e8", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, rgripper1 of robot1 drops ball4 in room2, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, robot1 moves to room2 from room5, from room2, robot1's lgripper1 picks up ball2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, rgripper1 of robot1 drops ball4 in room1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Robot1's lgripper1 is not available and robot1's rgripper1 is available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1's lgripper1 retrieves ball7 from room4, then robot1 proceeds to room5 from room4, robot1's rgripper1 releases ball4 in room2, robot1 moves to room1 from room5, robot1's lgripper1 collects ball1 from room1, and robot1's rgripper1 collects ball3 from room1, after which robot1 moves to room5 from room1, robot1's lgripper1 releases ball1 in room5, and robot1's rgripper1 releases ball3 in room5, then robot1 moves to room2 from room5, robot1's lgripper1 picks up ball2 from room2, and robot1's rgripper1 retrieves ball4 from room2, robot1 proceeds to room1 from room2, robot1's rgripper1 releases ball4 in room1, robot1's rgripper1 collects ball6 from room1, robot1 moves to room3 from room1, robot1's rgripper1 releases ball6 in room3, and robot1's rgripper1 retrieves ball5 from room3, finally moving to room6 from room3 to reach the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first unexecutable action in the sequence: robot1's lgripper1 is unavailable and robot1's rgripper1 is available?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "18244e6f-449a-451d-af51-49e44d9d6193", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball6 in room1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball2 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball5 is being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, lgripper1 of robot1 is carrying ball7, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is carrying ball6, rgripper1 of robot1 is not carrying ball2, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball5 with rgripper1 and robot1 is carrying ball6 with lgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: the left gripper of robot1 picks up ball6 in room1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball2 is not held by robot1's left gripper, ball4 is not held by robot1's left gripper, ball4 is not held by robot1's right gripper, ball5 is held by robot1's left gripper, ball7 is not held by robot1's right gripper, robot1's left gripper is holding ball7, robot1's left gripper is not holding ball1, robot1's left gripper is not holding ball3, robot1's right gripper is holding ball1, robot1's right gripper is holding ball6, robot1's right gripper is not holding ball2, robot1 is holding ball3 with its right gripper, robot1 is holding ball5 with its right gripper, and robot1 is holding ball6 with its left gripper.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "f549dc2f-b72b-4fe3-b2aa-4d29b89cf14d", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not at room1, ball1 is not at room3, ball1 is not located at room2, ball1 is not located at room5, ball1 is not present at room4, ball2 is located at room3, ball2 is not located at room1, ball2 is not located at room2, ball2 is present at room4, ball2 is present at room5, ball3 is not at room4, ball3 is not present at room1, ball3 is not present at room5, ball3 is present at room2, ball3 is present at room3, ball4 is at room3, ball4 is located at room5, ball4 is not at room1, ball4 is not located at room2, ball4 is present at room4, ball5 is at room1, ball5 is located at room4, ball5 is not at room2, ball5 is not at room3, ball5 is not located at room5, ball6 is at room2, ball6 is not at room4, ball6 is not present at room1, ball6 is not present at room3, ball6 is not present at room5, ball7 is at room5, ball7 is not at room1, ball7 is not at room4, ball7 is not located at room2, ball7 is not located at room3, robot1 is at room4, robot1 is located at room1, robot1 is not located at room3, robot1 is not located at room5 and robot1 is not present in room2.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1 will move from room4 to room1, then from room1, robot1's left gripper (lgripper1) will grasp ball1, followed by robot1's right gripper (rgripper1) picking up ball3 from room1, then robot1 will proceed to room5 from room1, next, from room2, robot1's rgripper1 will pick up ball2, robot1 will then move from room5 to room2, from room2, robot1's rgripper1 will pick up ball4, robot1 will move from room2 to room1, and finally, in room1, robot1's rgripper1 will release ball4 and pick up ball6 using rgripper1 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not in room1, ball1 is not in room3, ball1 is not found in room2, ball1 is not found in room5, ball1 is not present in room4, ball2 is in room3, ball2 is not in room1, ball2 is not in room2, ball2 is present in room4, ball2 is present in room5, ball3 is not in room4, ball3 is not in room1, ball3 is not in room5, ball3 is in room2, ball3 is in room3, ball4 is in room3, ball4 is in room5, ball4 is not in room1, ball4 is not in room2, ball4 is present in room4, ball5 is in room1, ball5 is in room4, ball5 is not in room2, ball5 is not in room3, ball5 is not in room5, ball6 is in room2, ball6 is not in room4, ball6 is not in room1, ball6 is not in room3, ball6 is not in room5, ball7 is in room5, ball7 is not in room1, ball7 is not in room4, ball7 is not in room2, ball7 is not in room3, robot1 is initially in room4, robot1 is also in room1, robot1 is not in room3, robot1 is not in room5, and robot1 is not in room2.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "00173511-a257-4348-a96f-9db488cb0a5c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 picks up ball5 in room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not at room2, ball1 is not at room5, ball1 is not located at room3, ball1 is not present at room4, ball1 is present at room1, ball2 is not located at room1, ball2 is not located at room3, ball2 is not present at room4, ball2 is not present at room5, ball2 is present at room2, ball3 is not at room5, ball3 is not located at room2, ball3 is not present at room3, ball3 is not present at room4, ball3 is present at room1, ball4 is located at room2, ball4 is not at room1, ball4 is not at room5, ball4 is not located at room3, ball4 is not located at room4, ball5 is at room2, ball5 is not located at room1, ball5 is not located at room5, ball5 is not present at room3, ball5 is not present at room4, ball6 is not at room2, ball6 is not at room4, ball6 is not present at room3, ball6 is not present at room5, ball6 is present at room1, robot1 is located at room4, robot1 is not at room3, robot1 is not located at room1, robot1 is not located at room5 and robot1 is not present in room2.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 picks up ball5 in room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not in room2, ball1 is not in room5, ball1 is not in room3, ball1 is not in room4, ball1 is in room1, ball2 is not in room1, ball2 is not in room3, ball2 is not in room4, ball2 is not in room5, ball2 is in room2, ball3 is not in room5, ball3 is not in room2, ball3 is not in room3, ball3 is not in room4, ball3 is in room1, ball4 is in room2, ball4 is not in room1, ball4 is not in room5, ball4 is not in room3, ball4 is not in room4, ball5 is in room2, ball5 is not in room1, ball5 is not in room5, ball5 is not in room3, ball5 is not in room4, ball6 is not in room2, ball6 is not in room4, ball6 is not in room3, ball6 is not in room5, ball6 is in room1, robot1 is in room4, robot1 is not in room3, robot1 is not in room1, robot1 is not in room5, and robot1 is not in room2.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "edf385c6-6f1e-4943-afbf-fd2d228f6da1", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot1 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper1 of robot1 is free and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1 will move from room1 to room2 to attain the current state. Are the following properties of the state (including both affirmative and negative assertions) valid prior to the first unexecutable action in the sequence? The left gripper of robot1 is available and the right gripper of robot1 is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "cbf59cfb-c687-4127-8ec9-2510a69ab6eb", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, ball7 is dropped in room1 with rgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, rgripper2 of robot2 drops ball7 in room2, from room2, robot2's rgripper2 picks up ball3, robot2 moves from room2 to room1, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, in room1, robot2's rgripper2 drops ball3, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is located at room3, ball1 is not at room2, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's rgripper2, ball1 is not located at room1, ball2 is located at room3, ball2 is not being carried by robot1's rgripper2, ball2 is not being carried by robot2's lgripper1, ball2 is not located at room2, ball2 is not present at room1, ball3 is at room2, ball3 is not at room1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper1, ball3 is not located at room3, ball4 is located at room3, ball4 is not at room1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper2, ball4 is not being carried by robot2's rgripper2, ball4 is not present at room2, ball5 is not at room2, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot2's lgripper1, ball5 is present at room1, ball6 is not at room3, ball6 is not being carried by robot1's lgripper2, ball6 is not being carried by robot2's lgripper1, ball6 is not being carried by robot2's rgripper1, ball6 is not present at room2, ball6 is present at room1, ball7 is not at room1, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's lgripper2, ball7 is not being carried by robot2's rgripper1, ball7 is not present at room2, ball7 is present at room3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball4, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot2 is free, lgripper2 of robot2 is not carrying ball5, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, rgripper1 of robot2 is not carrying ball4, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball3, rgripper2 of robot2 is not carrying ball5, rgripper2 of robot2 is not carrying ball7, robot1 is at room2, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball5 with rgripper2, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with lgripper2, robot1 is not present in room1, robot1's lgripper1 is free, robot1's lgripper2 is not available, robot1's rgripper2 is not available, robot2 is at room3, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball6 with rgripper2, robot2 is not located at room1, robot2 is not located at room2, robot2's lgripper1 is not available and robot2's rgripper2 is available.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are scheduled to be executed: robot2 moves from room2 to room3, robot2 drops ball7 in room1 using rgripper2, robot2 picks up ball2 from room3 using rgripper2, robot2 moves from room3 to room2, robot2 drops ball1 in room2 using lgripper2, robot2 drops ball2 in room2 using rgripper2, robot2 moves from room2 to room3, robot2 picks up ball4 from room3 using lgripper2, robot2 picks up ball7 from room3 using rgripper2, robot2 moves from room3 to room2, robot2 drops ball7 in room2 using rgripper2, robot2 picks up ball3 from room2 using rgripper2, robot2 moves from room2 to room1, robot2 drops ball4 in room1 using lgripper2, robot2 picks up ball5 from room1 using lgripper2, robot2 drops ball3 in room1 using rgripper2, robot2 picks up ball6 from room1 using rgripper2, and robot2 moves from room1 to room2, then drops ball5 in room2 using lgripper2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False?\n\nThe current state is as follows: \n- Ball1 is in room3, not in room2, room1, or being carried by robot1's lgripper2 or robot2's rgripper2.\n- Ball2 is in room3, not in room2, room1, or being carried by robot1's rgripper2 or robot2's lgripper1.\n- Ball3 is in room2, not in room1, room3, or being carried by robot1's lgripper1, rgripper1, or robot2's lgripper1, lgripper2, or rgripper1.\n- Ball4 is in room3, not in room1, room2, or being carried by robot1's rgripper2 or robot2's lgripper2 or rgripper2.\n- Ball5 is in room1, not in room2, room3, or being carried by robot1's lgripper1, rgripper1, or robot2's lgripper1.\n- Ball6 is in room1, not in room3, room2, or being carried by robot1's lgripper2 or robot2's lgripper1 or rgripper1.\n- Ball7 is in room3, not in room1, room2, or being carried by robot2's lgripper1, lgripper2, or rgripper1.\n- Robot1's lgripper1 is not carrying ball4, robot2's lgripper1 is not carrying ball1 or ball4, and robot2's lgripper2 is not carrying ball2, ball5, or ball6.\n- Robot1's rgripper1 is free and not carrying ball2, ball6, or ball7, robot2's rgripper1 is not free and not carrying ball4, ball5, or ball6.\n- Robot1 is in room2, not in room3, and not carrying any balls with its grippers.\n- Robot2 is in room3, not in room1 or room2, and not carrying ball1, ball2, or ball6 with its grippers.\n- Robot1's lgripper1 is free, lgripper2 is not available, and rgripper2 is not available.\n- Robot2's lgripper1 is not available, and rgripper2 is available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "91c08b8a-c593-408d-ab19-bec1221b7784", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, robot1 moves to room2 from room1, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, rgripper1 of robot1 picks up ball3 in room1 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Rgripper1 of robot1 is not free and robot1's lgripper1 is available.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 will move from room4 to room1, then its left gripper (lgripper1) will grasp ball1 in room1. Next, robot1 will proceed to room2 from room1, where its right gripper (rgripper1) will pick up ball2. After that, robot1 will move to room3 from room2, and in room3, lgripper1 will release ball1. Then, robot1 will move back to room4 from room3, and in room4, rgripper1 will release ball2. Additionally, rgripper1 will pick up ball3 in room1, and lgripper1 will pick up ball4 in room2 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties true for robot1 (both with and without negations): robot1's rgripper1 is occupied, and robot1's lgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "ed8a8a8c-dd72-435a-b137-fdf7bb9ad27f", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball3 to reach the current state. Are the following valid properties of the state (both with and without negations) true for lgripper1 before the first inexecutable action in the sequence? Robot1's lgripper1 is not free.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: starting from room4, robot1's lgripper1 will grasp ball3 to achieve the current state. Are the following properties of the state (including both affirmative and negative statements) accurate for lgripper1 prior to the first inexecutable action in the sequence? Robot1's lgripper1 is not in a free state.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "430b3c0b-c6ba-4916-8df7-c998b21756f7", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, lgripper1 of robot1 drops ball2 in room5, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves to room4 from room3, in room4, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball2 in room2 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball6 before the first inexecutable action in the sequence? Ball6 is at room1, ball6 is located at room2, ball6 is not present at room3, ball6 is not present at room4 and ball6 is present at room5.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1 and rgripper1 to pick up ball3, then moves to room5, drops ball3 in room5 using rgripper1, and then heads to room2. In room2, robot1's rgripper1 picks up ball4, while its lgripper1 drops ball2 in room5. Subsequently, in room1, robot1's rgripper1 drops ball4, picks up ball6, and then moves to room3, where it drops ball6 using rgripper1 and picks up ball7. Robot1 then returns to room4, drops ball1 and ball7, and proceeds to room2, where it picks up ball2 with lgripper1 and ball5 with rgripper1, ultimately reaching the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for ball6 (including both affirmative and negative statements)? Is ball6 located at room1, is ball6 at room2, is ball6 not at room3, is ball6 not at room4, and is ball6 at room5?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "7d1f5cc9-8492-4549-8c58-60340394beb9", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, ball3 is dropped in room5 with rgripper1 by robot1, rgripper1 of robot1 drops ball6 in room3, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball2 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is carrying ball5, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is carrying ball5, rgripper1 of robot1 is carrying ball7, robot1 is carrying ball1 with lgripper1, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball4 with rgripper1, robot1 is not carrying ball1 with rgripper1 and robot1 is not carrying ball7 with lgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1 relocates from room4 to room1, robot1's left gripper (lgripper1) grasps ball1 in room1, robot1's right gripper (rgripper1) picks up ball3 in room1, robot1 then moves from room1 to room5, robot1's rgripper1 releases ball3 in room5, robot1's rgripper1 drops ball6 in room3, robot1's rgripper1 picks up ball4 from room2, robot1 moves back to room1 from room2, robot1's rgripper1 releases ball4 in room1, and finally, robot1's rgripper1 picks up ball6 in room1 to reach the current state. Are the following properties of the state (both affirmative and negative) true before the first unexecutable action in the sequence? Ball2 is not held by robot1's lgripper1, ball6 is not held by robot1's rgripper1, robot1's lgripper1 is holding ball4, robot1's lgripper1 is holding ball5, robot1's lgripper1 is not holding ball3, robot1's lgripper1 is not holding ball6, robot1's rgripper1 is holding ball2, robot1's rgripper1 is holding ball5, robot1's rgripper1 is holding ball7, robot1 is holding ball1 with its lgripper1, robot1 is holding ball3 with its rgripper1, robot1 is holding ball4 with its rgripper1, robot1 is not holding ball1 with its rgripper1, and robot1 is not holding ball7 with its lgripper1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "9f7ae8e0-e7db-4c04-afc6-b3859250500c", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot2 picks up ball7 in room3 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is being carried by robot1's rgripper1, ball1 is being carried by robot1's rgripper2, ball1 is located at room3, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's lgripper2, ball1 is present at room1, ball1 is present at room2, ball2 is being carried by robot1's rgripper1, ball2 is not at room2, ball2 is not at room3, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper2, ball2 is not being carried by robot2's lgripper1, ball2 is not being carried by robot2's lgripper2, ball2 is not located at room1, ball3 is being carried by robot1's lgripper1, ball3 is located at room2, ball3 is present at room1, ball3 is present at room3, ball4 is at room2, ball4 is at room3, ball4 is being carried by robot1's lgripper1, ball4 is not at room1, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's lgripper2, ball5 is being carried by robot1's rgripper1, ball5 is not at room2, ball5 is not located at room1, ball5 is present at room3, ball6 is at room3, ball6 is being carried by robot2's rgripper1, ball6 is not at room1, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's rgripper2, ball6 is present at room2, ball7 is being carried by robot1's rgripper2, ball7 is located at room1, ball7 is located at room3, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room2, lgripper1 of robot1 is carrying ball6, lgripper1 of robot2 is carrying ball3, lgripper1 of robot2 is carrying ball7, lgripper2 of robot1 is carrying ball6, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot1 is not free, lgripper2 of robot2 is carrying ball5, lgripper2 of robot2 is not carrying ball1, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot1 is carrying ball3, rgripper1 of robot2 is carrying ball2, rgripper1 of robot2 is carrying ball4, rgripper1 of robot2 is free, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not carrying ball7, rgripper2 of robot1 is carrying ball5, rgripper2 of robot1 is not free, rgripper2 of robot2 is carrying ball1, rgripper2 of robot2 is carrying ball5, rgripper2 of robot2 is free, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball4, robot1 is carrying ball2 with lgripper2, robot1 is carrying ball3 with rgripper2, robot1 is carrying ball5 with lgripper1, robot1 is carrying ball6 with rgripper2, robot1 is located at room1, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room2, robot1 is present in room3, robot1's lgripper1 is available, robot1's rgripper1 is not available, robot2 is carrying ball3 with lgripper2, robot2 is carrying ball6 with lgripper1, robot2 is carrying ball7 with rgripper2, robot2 is located at room1, robot2 is not carrying ball1 with lgripper1, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not present in room3, robot2 is present in room2, robot2's lgripper1 is not free and robot2's lgripper2 is not available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are intended to be executed: robot2's rgripper1 is to pick up ball7 in room3 to achieve the current state. Is this the state preceding the first inexecutable action in the sequence? True or False? The current state is characterized by the following conditions: ball1 is held by robot1's rgripper1 and rgripper2, ball1 is located in room3, ball1 is not held by robot1's lgripper1 or lgripper2, ball1 is present in room1 and room2, ball2 is held by robot1's rgripper1, ball2 is not in room2 or room3, ball2 is not held by robot1's lgripper1 or rgripper2, or by robot2's lgripper1 or lgripper2, ball2 is not in room1, ball3 is held by robot1's lgripper1, ball3 is in room2, ball3 is present in room1 and room3, ball4 is in room2 and room3, ball4 is held by robot1's lgripper1, ball4 is not in room1, ball4 is not held by robot2's lgripper1 or lgripper2, ball5 is held by robot1's rgripper1, ball5 is not in room2, ball5 is not in room1, ball5 is present in room3, ball6 is in room3, ball6 is held by robot2's rgripper1, ball6 is not in room1, ball6 is not held by robot1's rgripper1, ball6 is not held by robot2's rgripper2, ball6 is present in room2, ball7 is held by robot1's rgripper2, ball7 is in room1 and room3, ball7 is not held by robot1's rgripper1, ball7 is not in room2, robot1's lgripper1 is holding ball6, robot2's lgripper1 is holding ball3 and ball7, robot1's lgripper2 is holding ball6, robot1's lgripper2 is not holding ball4 or ball7, robot1's lgripper2 is not free, robot2's lgripper2 is holding ball5, robot2's lgripper2 is not holding ball1 or ball6, robot1's rgripper1 is holding ball3, robot2's rgripper1 is holding ball2, robot2's rgripper1 is holding ball4, robot2's rgripper1 is free, robot2's rgripper1 is not holding ball3, ball5, or ball7, robot1's rgripper2 is holding ball5, robot1's rgripper2 is not free, robot2's rgripper2 is holding ball1 and ball5, robot2's rgripper2 is free, robot2's rgripper2 is not holding ball2 or ball4, robot1 is holding ball2 with lgripper2, robot1 is holding ball3 with rgripper2, robot1 is holding ball5 with lgripper1, robot1 is holding ball6 with rgripper2, robot1 is in room1, robot1 is not holding ball3 with lgripper2, robot1 is not holding ball4 with rgripper1 or rgripper2, robot1 is not holding ball5 with lgripper2, robot1 is not holding ball7 with lgripper1, robot1 is not in room2, robot1 is present in room3, robot1's lgripper1 is available, robot1's rgripper1 is not available, robot2 is holding ball3 with lgripper2, robot2 is holding ball6 with lgripper1, robot2 is holding ball7 with rgripper2, robot2 is in room1, robot2 is not holding ball1 with lgripper1 or rgripper1, robot2 is not holding ball3 with rgripper2, robot2 is not holding ball5 with lgripper1, robot2 is not holding ball7 with lgripper2, robot2 is not present in room3, robot2 is present in room2, robot2's lgripper1 is not free, and robot2's lgripper2 is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "4161b5f2-896b-48e9-ab11-e147d0db5344", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room1 with lgripper1 by robot2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper1 of robot1 is free, rgripper1 of robot2 is free, robot1's lgripper2 is not available, robot1's rgripper1 is not free, robot1's rgripper2 is not free, robot2's lgripper1 is available, robot2's lgripper2 is not free and robot2's rgripper2 is available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are intended to be executed: robot2 uses lgripper1 to pick ball7 from room1 to achieve the current state. Are the following properties of the state (including both affirmative and negative statements) valid before the first unexecutable action in the sequence? The left gripper of robot1 is free, the right gripper of robot2 is free, the left gripper 2 of robot1 is unavailable, the right gripper 1 of robot1 is not free, the right gripper 2 of robot1 is not free, the left gripper 1 of robot2 is available, the left gripper 2 of robot2 is not free, and the right gripper 2 of robot2 is available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "bbe48486-5a2c-4da9-b2fe-bdaab2d3f328", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot1's rgripper2 picks up ball2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Rgripper1 of robot2 is not free and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: from room2, robot1's rgripper2 will grasp ball2 to achieve the current state. Are the following properties of the state (both with and without negations) true for rgripper1 prior to the first inexecutable action in the sequence? Rgripper1 of robot2 is occupied and robot1's rgripper1 is unavailable.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "f7fa7c1c-d7ea-4220-9652-ede576c33656", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, lgripper1 of robot1 picks up ball3 in room4, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Robot1's lgripper1 is not available and robot1's rgripper1 is free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1 will move from room4 to room1, then use lgripper1 to pick up ball1 in room1, followed by using lgripper1 to pick up ball3 in room4, then move from room1 to room5, drop ball3 in room5 using rgripper1, proceed to room2 from room5, pick up ball4 in room2 using rgripper1, move back to room1 from room2, drop ball4 in room1 using rgripper1, and finally pick up ball6 in room1 using rgripper1 to reach the current state. Are the following properties (including their negations) of the state true for robot1 before the first unexecutable action in the sequence: robot1's lgripper1 is unavailable and robot1's rgripper1 is available?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "b6d88787-fee2-4951-afe6-c29077830620", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room4, robot1's lgripper1 drops ball1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room2, ball1 is not at room5, ball1 is not located at room4, ball1 is not present at room1, ball1 is not present at room3, ball2 is located at room1, ball2 is not at room3, ball2 is not located at room5, ball2 is not present at room2, ball2 is present at room4, ball3 is at room1, ball3 is located at room3, ball3 is not located at room4, ball3 is not located at room5, ball3 is not present at room2, ball4 is at room5, ball4 is located at room2, ball4 is located at room4, ball4 is not at room1, ball4 is present at room3, ball5 is at room5, ball5 is being carried by robot1's lgripper1, ball5 is located at room3, ball5 is located at room4, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room1, ball5 is not located at room2, ball6 is being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room5, ball6 is not present at room1, ball6 is not present at room3, ball6 is not present at room4, lgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is carrying ball2, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball4 with rgripper1, robot1 is carrying ball6 with rgripper1, robot1 is not at room4, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not located at room1, robot1 is not present in room2, robot1 is not present in room3, robot1 is present in room5, robot1's lgripper1 is free and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: in room4, robot1's lgripper1 releases ball1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current state is characterized by the following: ball1 is in room2, not in room5, not in room4, not in room1, and not in room3. Ball2 is in room1, not in room3, not in room5, not in room2, but in room4. Ball3 is in room1 and room3, but not in room4, room5, or room2. Ball4 is in room5, room2, room4, and room3, but not in room1. Ball5 is in room5, being carried by robot1's lgripper1, in room3 and room4, but not in room1, room2, and not carried by robot1's rgripper1. Ball6 is being carried by robot1's lgripper1, not in room2, room5, room1, room3, or room4. The current state of robot1 is as follows: lgripper1 is carrying ball4, rgripper1 is carrying ball2, robot1 is carrying ball2 with lgripper1, ball4 with rgripper1, and ball6 with rgripper1. However, robot1 is not in room4, not carrying ball1 with either gripper, not carrying ball3 with either gripper, not in room1, room2, or room3, but in room5. Additionally, robot1's lgripper1 is available, while robot1's rgripper1 is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "1dea5bc7-b56a-4c86-8bb7-d332f56f668f", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room3, robot1's lgripper1 drops ball3, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2, lgripper1 of robot1 picks up ball4 in room2, ball5 is picked from room2 with rgripper1 by robot1, robot1 moves to room5 from room2, ball4 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball3, rgripper1 of robot1 drops ball5 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Rgripper1 of robot1 is free and robot1's lgripper1 is not available.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: in room3, robot1's left gripper (lgripper1) releases ball3, then robot1's lgripper1 picks up ball1 in room1, robot1 proceeds from room1 to room2, robot1's right gripper (rgripper1) picks up ball2 in room2, robot1 moves from room2 to room3, robot1 drops ball1 in room3 using lgripper1, robot1 then moves from room3 to room4, robot1's rgripper1 drops ball2 in room4, from room4, robot1 returns to room2, robot1's lgripper1 picks up ball4 in room2, and robot1's rgripper1 picks up ball5 in room2, robot1 then moves from room2 to room5, robot1 drops ball4 in room5 using lgripper1, from room5, robot1 moves back to room1, in room1, robot1's lgripper1 picks up ball3, robot1's rgripper1 drops ball5 in room1, robot1's rgripper1 then picks up ball6 in room1, from room1, robot1 moves to room5, and finally, in room5, robot1's lgripper1 drops ball3 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? The right gripper (rgripper1) of robot1 is available and robot1's left gripper (lgripper1) is occupied.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "ce6e56a1-293b-48e2-bbc2-4497d24be4cb", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room3, robot1's rgripper1 picks up ball2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not at room1, ball1 is not located at room2, ball1 is present at room3, ball2 is at room3, ball2 is not at room1, ball2 is not at room2, ball3 is located at room2, ball3 is not located at room1, ball3 is not present at room3, ball4 is located at room3, ball4 is not located at room1, ball4 is not present at room2, ball5 is at room1, ball5 is not located at room3, ball5 is not present at room2, ball6 is not located at room2, ball6 is not located at room3, ball6 is present at room1, ball7 is located at room3, ball7 is not located at room1, ball7 is not located at room2, robot1 is not at room3, robot1 is not present in room1, robot1 is present in room2, robot2 is at room2, robot2 is not at room1 and robot2 is not at room3.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: from room3, robot1's rgripper1 picks up ball2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not in room1, ball1 is not in room2, ball1 is in room3, ball2 is in room3, ball2 is not in room1, ball2 is not in room2, ball3 is in room2, ball3 is not in room1, ball3 is not in room3, ball4 is in room3, ball4 is not in room1, ball4 is not in room2, ball5 is in room1, ball5 is not in room3, ball5 is not in room2, ball6 is not in room2, ball6 is not in room3, ball6 is in room1, ball7 is in room3, ball7 is not in room1, ball7 is not in room2, robot1 is not in room3, robot1 is not in room1, robot1 is in room2, robot2 is in room2, robot2 is not in room1, and robot2 is not in room3.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "faf76e0b-c1f1-45ff-b9d4-1c18f4e94d4c", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, from room4, robot1's rgripper1 picks up ball3, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is located at room5, ball1 is not at room4, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not present at room1, ball2 is located at room3, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not located at room5, ball2 is present at room2, ball2 is present at room4, ball3 is located at room3, ball3 is located at room4, ball3 is not at room5, ball3 is not present at room1, ball3 is not present at room2, ball4 is located at room3, ball4 is located at room4, ball4 is not present at room1, ball4 is not present at room2, ball4 is not present at room5, ball5 is at room4, ball5 is located at room1, ball5 is located at room3, ball5 is present at room2, ball5 is present at room5, ball6 is at room3, ball6 is not at room1, ball6 is not located at room2, ball6 is not located at room4, ball6 is not located at room5, ball7 is at room4, ball7 is being carried by robot1's rgripper1, ball7 is located at room5, ball7 is not at room3, ball7 is not present at room1, ball7 is not present at room2, lgripper1 of robot1 is carrying ball3, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball1, robot1 is carrying ball6 with rgripper1, robot1 is carrying ball7 with lgripper1, robot1 is located at room1, robot1 is located at room2, robot1 is located at room4, robot1 is not at room5, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not present in room3 and robot1's rgripper1 is not available.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned: starting from room4, robot1 is set to move to room1, pick up ball1 from room1 using lgripper1, then use rgripper1 to pick up ball3 from room1, move to room5, drop ball3 in room5 using rgripper1, proceed to room2 from room5, pick up ball3 from room4 using rgripper1, move to room1 from room2, drop ball4 in room1 using rgripper1, and finally pick up ball6 from room1 using rgripper1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state is as follows: ball1 is in room5, not in room4, not held by robot1's lgripper1, not in room2, not in room3, and not in room1. Ball2 is in room3, not held by robot1's lgripper1 or rgripper1, not in room1, not in room5, but present in room2 and room4. Ball3 is in room3 and room4, not in room5, not in room1, and not in room2. Ball4 is in room3 and room4, not in room1, room2, or room5. Ball5 is in room4, room1, room3, room2, and room5. Ball6 is in room3, not in room1, room2, room4, or room5. Ball7 is in room4, held by robot1's rgripper1, in room5, not in room3, room1, or room2. \n\nAdditionally, robot1's lgripper1 is holding ball3 and ball4, but is also free and not holding ball6. Robot1's rgripper1 is not holding ball1, but is holding ball6 and ball7. Robot1 is in room1, room2, and room4, but not in room5, and is not holding ball3 or ball4 with rgripper1, nor ball5 with either gripper. Robot1 is also not in room3 and its rgripper1 is not available.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "2dfd5504-105e-4b76-8e42-47ed119f84e0", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, ball7 is dropped in room2 with rgripper2 by robot2, from room2, robot2's rgripper2 picks up ball3, from room2, robot2 moves to room1, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, in room1, robot2's rgripper2 drops ball3, from room1, robot1's lgripper2 picks up ball3, robot2 moves to room2 from room1 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper2 before the first inexecutable action in the sequence? Ball1 is not being carried by robot2's rgripper2, ball5 is being carried by robot1's rgripper2, ball5 is not being carried by robot2's rgripper2, ball7 is being carried by robot1's rgripper2, ball7 is not being carried by robot2's rgripper2, rgripper2 of robot1 is carrying ball2, rgripper2 of robot1 is carrying ball3, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot2 is carrying ball2, rgripper2 of robot2 is carrying ball3, rgripper2 of robot2 is carrying ball4, rgripper2 of robot2 is not carrying ball6, robot1 is not carrying ball1 with rgripper2 and robot1 is not carrying ball6 with rgripper2.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot2 relocates from room2 to room3, robot2 uses lgripper2 to pick up ball1 in room3, robot2's rgripper2 then picks up ball2 in room3, robot2 moves back to room2 from room3, robot2 drops ball1 in room2 using lgripper2, and then robot2's rgripper2 drops ball2 in room2, robot2 then moves to room3 from room2, robot2's lgripper2 picks up ball4 in room3, and robot2's rgripper2 picks up ball7 in room3, robot2 then moves to room2 from room3, robot2 drops ball7 in room2 using rgripper2, robot2's rgripper2 then picks up ball3 in room2, robot2 moves to room1 from room2, robot2 drops ball4 in room1 using lgripper2, robot2 picks up ball5 in room1 using lgripper2, robot2's rgripper2 drops ball3 in room1, robot1's lgripper2 then picks up ball3 in room1, robot2 moves to room2 from room1, and finally robot2 drops ball5 in room2 using lgripper2 to reach the current state. Are the following properties of the state (both with and without negations) true for rgripper2 before the first inexecutable action in the sequence? Ball1 is not held by robot2's rgripper2, ball5 is held by robot1's rgripper2, ball5 is not held by robot2's rgripper2, ball7 is held by robot1's rgripper2, ball7 is not held by robot2's rgripper2, robot1's rgripper2 is holding ball2, robot1's rgripper2 is holding ball3, robot1's rgripper2 is not holding ball4, robot2's rgripper2 is holding ball2, robot2's rgripper2 is holding ball3, robot2's rgripper2 is holding ball4, robot2's rgripper2 is not holding ball6, robot1 is not holding ball1 with rgripper2, and robot1 is not holding ball6 with rgripper2.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "59b2f22a-6563-456a-9404-ca9cdc46cbf9", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot1's lgripper1 picks up ball2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Ball2 is located at room2, ball2 is not at room4, ball2 is not at room5, ball2 is not present at room1 and ball2 is not present at room3.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: from room1, robot1's lgripper1 picks up ball2 to reach the current state. Are the following properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Ball2 is located at room2, ball2 is not located at room4, ball2 is not located at room5, ball2 is not located at room1, and ball2 is not located at room3.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present at room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "0da671f7-63ff-486f-a283-1248558ea2d2", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, lgripper1 of robot2 drops ball3 in room1, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7, robot2 moves from room3 to room2, in room2, robot2's rgripper2 drops ball7, from room2, robot2's rgripper2 picks up ball3, from room2, robot2 moves to room1, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot2 picks up ball5 in room1, in room1, robot2's rgripper2 drops ball3, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves from room1 to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper2 of robot1 is not free, rgripper1 of robot1 is free, rgripper2 of robot2 is free, robot1's lgripper1 is not available, robot1's rgripper2 is not free, robot2's lgripper1 is free, robot2's lgripper2 is free and robot2's rgripper1 is not free.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: starting from room2, robot2 will move to room3, where it will use its left gripper (lgripper2) to pick up ball1 and its right gripper (rgripper2) to pick up ball2; then, robot2 will return to room2 from room3. In room2, robot2 will use its left gripper (lgripper1) to drop ball3 in room1, and its right gripper (rgripper2) will drop ball2. Next, robot2 will move from room2 to room3, where it will use its left gripper (lgripper2) to pick up ball4 and its right gripper (rgripper2) to pick up ball7. After that, robot2 will move back to room2, where it will use its right gripper (rgripper2) to drop ball7. Then, in room2, robot2's right gripper (rgripper2) will pick up ball3, and robot2 will move to room1. In room1, robot2 will use its left gripper (lgripper2) to drop ball4, pick up ball5, and its right gripper (rgripper2) will drop ball3 and pick up ball6. Finally, robot2 will move from room1 to room2 and use its left gripper (lgripper2) to drop ball5, reaching the current state. The question is whether the following properties of the state (with and without negations) are true before the first inexecutable action in the sequence: robot1's left gripper (lgripper2) is not available, robot1's right gripper (rgripper1) is available, robot2's right gripper (rgripper2) is available, robot1's left gripper (lgripper1) is not available, robot1's right gripper (rgripper2) is not available, robot2's left gripper (lgripper1) is available, robot2's left gripper (lgripper2) is available, and robot2's right gripper (rgripper1) is not available.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "202d5eac-1ba8-4c23-8d0f-b67b34f86ba6", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball4 in room3, ball6 is picked from room3 with rgripper2 by robot1 and from room3, robot2 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball4 before the first inexecutable action in the sequence? Ball4 is not being carried by robot1's lgripper2, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot2's lgripper1, rgripper1 of robot2 is not carrying ball4, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot2 is not carrying ball4, robot1 is not carrying ball4 with lgripper1 and robot2 is carrying ball4 with lgripper2.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then from room3, robot2's left gripper (lgripper2) will pick up ball1, and robot2's right gripper (rgripper2) will pick up ball2 in room3. Next, robot2 will move back to room2 from room3, and in room2, robot2's lgripper2 will drop ball1, and robot2's rgripper2 will drop ball2. Then, from room2, robot2 will move to room3, where robot2's lgripper2 will pick up ball4, and robot1's rgripper2 will pick up ball6 in room3. Finally, robot2 will move from room3 to room2 to reach the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for ball4 (both with and without negations)? Ball4 is not held by robot1's lgripper2, ball4 is not held by robot1's rgripper1, ball4 is not held by robot2's lgripper1, robot2's rgripper1 is not carrying ball4, robot1's rgripper2 is not carrying ball4, robot2's rgripper2 is not carrying ball4, robot1 is not holding ball4 with lgripper1, and robot2 is holding ball4 with lgripper2.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "f91b21f9-7d3f-4f31-a90a-916fd70407b1", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, ball3 is picked from room2 with rgripper2 by robot2, ball3 is picked from room1 with rgripper1 by robot1, ball4 is dropped in room1 with lgripper2 by robot2, lgripper2 of robot2 picks up ball5 in room1, rgripper2 of robot2 drops ball3 in room1, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not being carried by robot2's lgripper1, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not being carried by robot2's rgripper2, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot1's rgripper1, ball2 is not being carried by robot2's lgripper1, ball2 is not being carried by robot2's rgripper1, ball3 is being carried by robot2's rgripper2, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's rgripper2, ball4 is being carried by robot2's lgripper2, ball4 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's lgripper2, ball5 is not being carried by robot2's lgripper1, ball5 is not being carried by robot2's rgripper1, ball6 is not being carried by robot1's lgripper2, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's rgripper2, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's rgripper2, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's rgripper2, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is not carrying ball3, lgripper1 of robot2 is not carrying ball4, lgripper2 of robot2 is not carrying ball3, lgripper2 of robot2 is not carrying ball6, lgripper2 of robot2 is not carrying ball7, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball6, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot1 is not carrying ball5, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not carrying ball6, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper1 and robot2 is not carrying ball7 with rgripper1.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are scheduled to be executed: robot2 relocates from room2 to room3, then robot2's left gripper (lgripper2) picks up ball1 in room3, followed by robot2's right gripper (rgripper2) picking up ball2 in room3. Next, robot2 moves back to room2 from room3, and in room2, robot2's lgripper2 drops ball1 and robot2's rgripper2 drops ball2. Then, robot2 returns to room3 from room2, where robot2's lgripper2 picks up ball4 and robot2's rgripper2 picks up ball7. After that, robot2 moves back to room2 from room3, and in room2, robot2's rgripper2 drops ball7. Subsequently, robot2's rgripper2 picks up ball3 in room2, while robot1's right gripper (rgripper1) picks up ball3 in room1. Then, robot2's lgripper2 drops ball4 in room1, and robot2's lgripper2 picks up ball5 in room1. Next, robot2's rgripper2 drops ball3 in room1, and robot2's rgripper2 picks up ball6 in room1. Finally, robot2 moves from room1 to room2, and in room2, robot2's lgripper2 drops ball5 to reach the current state. \n\nAre the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not held by robot2's left gripper (lgripper1), ball1 is not held by robot2's lgripper2, ball1 is not held by robot2's right gripper (rgripper1), ball1 is not held by robot2's rgripper2, ball2 is not held by robot1's lgripper2, ball2 is not held by robot1's rgripper1, ball2 is not held by robot2's lgripper1, ball2 is not held by robot2's rgripper1, ball3 is held by robot2's rgripper2, ball3 is not held by robot1's lgripper1, ball3 is not held by robot1's rgripper1, ball3 is not held by robot1's rgripper2, ball4 is held by robot2's lgripper2, ball4 is not held by robot1's rgripper1, ball5 is not held by robot1's lgripper1, ball5 is not held by robot1's lgripper2, ball5 is not held by robot2's lgripper1, ball5 is not held by robot2's rgripper1, ball6 is not held by robot1's lgripper2, ball6 is not held by robot1's rgripper1, ball6 is not held by robot1's rgripper2, ball7 is not held by robot1's lgripper2, ball7 is not held by robot1's rgripper1, ball7 is not held by robot1's rgripper2, ball7 is not held by robot2's lgripper1, ball7 is not held by robot2's rgripper2, robot1's lgripper1 is not holding ball1, robot1's lgripper1 is not holding ball2, robot1's lgripper1 is not holding ball4, robot1's lgripper1 is not holding ball6, robot2's lgripper1 is not holding ball3, robot2's lgripper1 is not holding ball4, robot2's lgripper2 is not holding ball3, robot2's lgripper2 is not holding ball6, robot2's lgripper2 is not holding ball7, robot2's rgripper1 is not holding ball3, robot2's rgripper1 is not holding ball6, robot1's rgripper2 is not holding ball2, robot1's rgripper2 is not holding ball4, robot1's rgripper2 is not holding ball5, robot2's rgripper2 is not holding ball2, robot2's rgripper2 is not holding ball4, robot2's rgripper2 is not holding ball6, robot1 is not holding ball1 with lgripper2, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball1 with rgripper2, robot1 is not holding ball3 with lgripper2, robot1 is not holding ball4 with lgripper2, robot1 is not holding ball5 with rgripper1, robot1 is not holding ball7 with lgripper1, robot2 is not holding ball2 with lgripper2, robot2 is not holding ball4 with rgripper1, robot2 is not holding ball5 with lgripper2, robot2 is not holding ball5 with rgripper2, robot2 is not holding ball6 with lgripper1, and robot2 is not holding ball7 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "576764da-2033-4a68-8900-b4473cca56ad", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, ball3 is picked from room5 with lgripper1 by robot1, robot1 moves to room2 from room4, ball4 is picked from room2 with lgripper1 by robot1, ball5 is picked from room2 with rgripper1 by robot1, robot1 moves to room5 from room2, lgripper1 of robot1 drops ball4 in room5, robot1 moves from room5 to room1, ball3 is picked from room1 with lgripper1 by robot1, ball5 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room5 and ball3 is dropped in room5 with lgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not being carried by robot1's lgripper1, ball2 is being carried by robot1's rgripper1, ball2 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball4 and robot1 is not carrying ball5 with rgripper1.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: starting from room4, robot1 proceeds to room1, where robot1's left gripper (lgripper1) picks up ball1, then robot1 moves to room2, where robot1's right gripper (rgripper1) picks up ball2, next robot1 moves to room3, drops ball1 in room3 using lgripper1, and then returns to room4, picks up ball3 from room5 using lgripper1, moves to room2, picks up ball4 and ball5 from room2 using lgripper1 and rgripper1 respectively, then moves to room5, drops ball4 in room5 using lgripper1, returns to room1, picks up ball3 using lgripper1, drops ball5 in room1 using rgripper1, picks up ball6 in room1 using rgripper1, moves to room5, and finally drops ball3 in room5 using lgripper1 to reach the current state. The question is whether the following properties (both affirmative and negative) are true before the first unexecutable action in the sequence: ball1 is not held by robot1's lgripper1, ball2 is held by robot1's rgripper1, ball2 is not held by robot1's lgripper1, ball3 is not held by either of robot1's grippers, ball4 is not held by robot1's lgripper1, ball5 is not held by robot1's lgripper1, ball6 is not held by robot1's rgripper1, robot1's lgripper1 is not holding ball6, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball4, and robot1 is not holding ball5 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "e8f7744e-8637-4c79-ac7f-c97d475e3fed", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, ball2 is picked from room2 with lgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, lgripper1 of robot1 drops ball1 in room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball3 before the first inexecutable action in the sequence? Ball3 is at room1, ball3 is at room5, ball3 is at room6, ball3 is located at room4, ball3 is not at room2 and ball3 is not present at room3.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1's lgripper1 picks up ball7 in room4, then robot1 moves to room5 from room4, followed by lgripper1 of robot1 dropping ball7 in room5. Next, robot1 moves to room1 from room5, and then lgripper1 of robot1 picks up ball1 in room1, while rgripper1 of robot1 picks up ball3 in room1. Robot1 then moves to room5 from room1, and subsequently, lgripper1 of robot1 drops ball1 in room5, and rgripper1 of robot1 drops ball3 in room5. After that, robot1 moves to room2 from room5, picks up ball2 with lgripper1 and ball4 with rgripper1 in room2, and then moves to room1 from room2. In room1, robot1 drops ball4 with rgripper1 and picks up ball6 with rgripper1. Then, lgripper1 of robot1 drops ball2 in room3, and rgripper1 of robot1 drops ball6 in room3. Furthermore, rgripper1 of robot1 picks up ball5 in room3, and finally, robot1 moves to room6 from room3 to reach the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for ball3 (both with and without negations)? Is ball3 located at room1, is ball3 located at room5, is ball3 located at room6, is ball3 located at room4, is ball3 not located at room2, and is ball3 not located at room3?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "1db84951-4e1d-4cd9-b928-607854456206", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room3, in room2, robot1's lgripper1 drops ball5, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball4 in room2, from room2, robot1's rgripper1 picks up ball5, robot1 moves to room5 from room2, in room5, robot1's lgripper1 drops ball4, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball3, in room1, robot1's rgripper1 drops ball5, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room5 and ball3 is dropped in room5 with lgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Ball1 is being carried by robot1's rgripper1, ball2 is not being carried by robot1's rgripper1, ball3 is being carried by robot1's rgripper1, ball4 is not being carried by robot1's rgripper1, robot1 is not carrying ball5 with rgripper1 and robot1 is not carrying ball6 with rgripper1.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial state, the following sequence of actions is planned: robot1 moves from room4 to room1, then picks up ball1 in room1 using lgripper1, proceeds to room2, picks up ball2 in room2 using rgripper1, moves to room3 from room2, drops ball5 in room2 using lgripper1, moves back to room4, drops ball2 in room4 using rgripper1, returns to room2, picks up ball4 in room2 using lgripper1, picks up ball5 in room2 using rgripper1, moves to room5, drops ball4 in room5 using lgripper1, moves to room1, picks up ball3 in room1 using lgripper1, drops ball5 in room1 using rgripper1, picks up ball6 in room1 using rgripper1, moves to room5, and finally drops ball3 in room5 using lgripper1 to reach the current state. The question is whether the following properties (with and without negations) are true for rgripper1 before the first inexecutable action in the sequence: Is ball1 being carried by robot1's rgripper1, is ball2 not being carried by robot1's rgripper1, is ball3 being carried by robot1's rgripper1, is ball4 not being carried by robot1's rgripper1, is robot1 not carrying ball5 with rgripper1, and is robot1 not carrying ball6 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "2617bdf0-4d95-4fb6-b65d-a81bf01314a5", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 drops ball6 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for room3 before the first inexecutable action in the sequence? Ball1 is present at room3, ball2 is at room3, ball3 is present at room3, ball4 is not located at room3, ball5 is not at room3, ball6 is not located at room3, ball7 is at room3 and robot1 is located at room3.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 drops ball6 in room2 to reach the current state. Are the following properties of the state (both with and without negations) true for room3 before the first inexecutable action in the sequence? Is it true that ball1 is in room3, ball2 is in room3, ball3 is in room3, ball4 is not in room3, ball5 is not in room3, ball6 is not in room3, ball7 is in room3, and robot1 is in room3?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "e6deaa01-f31a-4082-ab61-0f7a9a35226f", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, robot1 moves to room5 from room4, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not at room1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not located at room5, ball1 is not present at room4, ball2 is located at room2, ball2 is not at room4, ball2 is not at room5, ball2 is not located at room1, ball2 is not present at room3, ball3 is not at room3, ball3 is not at room5, ball3 is not present at room1, ball3 is not present at room2, ball3 is not present at room4, ball4 is not at room1, ball4 is not at room3, ball4 is not located at room5, ball4 is not present at room4, ball4 is present at room2, ball5 is not at room3, ball5 is not at room4, ball5 is not located at room1, ball5 is not located at room5, ball5 is present at room2, ball6 is located at room1, ball6 is not located at room4, ball6 is not located at room5, ball6 is not present at room2, ball6 is not present at room3, ball7 is not at room1, ball7 is not at room5, ball7 is not located at room4, ball7 is not present at room2, ball7 is present at room3, robot1 is located at room5, robot1 is not at room3, robot1 is not at room4, robot1 is not located at room2 and robot1 is not present in room1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1 will move from room4 to room1, then from room1, robot1's lgripper1 will grasp ball1, and robot1's rgripper1 will pick up ball3 from room1. Next, robot1 will move from room1 to room5, then from room4 to room5, and then from room5 to room2. In room2, robot1's rgripper1 will pick up ball4, and then robot1 will move from room2 to room1. In room1, robot1's rgripper1 will release ball4 and pick up ball6 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is not in room1, ball1 is not in room2, ball1 is not in room3, ball1 is not in room5, ball1 is not in room4, ball2 is in room2, ball2 is not in room4, ball2 is not in room5, ball2 is not in room1, ball2 is not in room3, ball3 is not in room3, ball3 is not in room5, ball3 is not in room1, ball3 is not in room2, ball3 is not in room4, ball4 is not in room1, ball4 is not in room3, ball4 is not in room5, ball4 is not in room4, ball4 is in room2, ball5 is not in room3, ball5 is not in room4, ball5 is not in room1, ball5 is not in room5, ball5 is in room2, ball6 is in room1, ball6 is not in room4, ball6 is not in room5, ball6 is not in room2, ball6 is not in room3, ball7 is not in room1, ball7 is not in room5, ball7 is not in room4, ball7 is not in room2, ball7 is in room3, robot1 is in room5, robot1 is not in room3, robot1 is not in room4, robot1 is not in room2, and robot1 is not in room1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "0dafc668-a699-4b01-9de3-fe0bf803a297", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 drops ball6 in room6 to reach the current state. Are the following valid properties of the state (both with and without negations) true for room6 before the first inexecutable action in the sequence? Ball1 is present at room6, ball2 is not located at room6, ball3 is at room6, ball4 is present at room6, ball5 is at room6, ball6 is not located at room6, ball7 is not located at room6 and robot1 is not at room6.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 drops ball6 in room6 to reach the current state. Are the following properties of the state (both with and without negations) true for room6 before the first inexecutable action in the sequence? Ball1 is in room6, ball2 is not in room6, ball3 is present in room6, ball4 is in room6, ball5 is present in room6, ball6 is not in room6, ball7 is not in room6, and robot1 is not in room6.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "79333a76-6afb-4b18-a9f9-aeafb37a6955", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 drops ball7 in room5, robot1 moves to room5 from room4, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, rgripper1 of robot1 drops ball3 in room5 and robot1 moves to room2 from room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot1's lgripper1, ball3 is being carried by robot1's lgripper1, ball5 is being carried by robot1's rgripper1, lgripper1 of robot1 is carrying ball7, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is carrying ball7, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, robot1 is carrying ball4 with lgripper1, robot1 is not carrying ball3 with rgripper1 and robot1 is not carrying ball6 with rgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial state, a series of actions are planned to be executed: robot1's rgripper1 releases ball7 in room5, robot1 moves from room4 to room5, robot1's lgripper1 releases ball7 in room5, then robot1 proceeds from room5 to room1, robot1's lgripper1 picks up ball1 in room1, and robot1's rgripper1 picks up ball3 in room1, after which robot1 moves from room1 to room5, robot1's lgripper1 releases ball1 in room5, and robot1's rgripper1 releases ball3 in room5, finally, robot1 moves from room5 to room2 to reach the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first unexecutable action in the sequence? Is ball1 being held by robot1's lgripper1, is ball3 being held by robot1's lgripper1, is ball5 being held by robot1's rgripper1, is robot1's lgripper1 holding ball7, is robot1's lgripper1 not holding ball2, is robot1's lgripper1 not holding ball5, is robot1's lgripper1 not holding ball6, is robot1's rgripper1 holding ball4, is robot1's rgripper1 holding ball7, is robot1's rgripper1 not holding ball1, is robot1's rgripper1 not holding ball2, is robot1 holding ball4 with lgripper1, is robot1 not holding ball3 with rgripper1, and is robot1 not holding ball6 with rgripper1.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "9cc0bb32-ab51-4d07-b349-75cac1f03c13", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball6 in room4, in room5, robot1's lgripper1 drops ball7, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves from room5 to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Rgripper1 of robot1 is free and robot1's lgripper1 is not available.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1, using lgripper1, will pick up ball7 from room4, then robot1's rgripper1 will drop ball6 in room4, next robot1's lgripper1 will drop ball7 in room5, after which robot1 will move from room5 to room1, pick up ball1 from room1 with lgripper1, and then use rgripper1 to pick up ball3 from room1, move from room1 to room5, drop ball1 in room5 using lgripper1, drop ball3 in room5 using rgripper1, and finally move from room5 to room2 to reach the current state. Are the following properties of the state (both with and without negations) true before the first inexecutable action in the sequence: robot1's rgripper1 is available and robot1's lgripper1 is occupied.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "54313b10-678e-42eb-9994-a936be4df185", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, from room5, robot1 moves to room4, robot1 moves from room1 to room5, ball1 is dropped in room5 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball3 and robot1 moves from room5 to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball1 before the first inexecutable action in the sequence? Rgripper1 of robot1 is not carrying ball1 and robot1 is carrying ball1 with lgripper1.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1's lgripper1 will grasp ball7 in room4, then robot1 will proceed from room4 to room5, where it will release ball7 using lgripper1, after which robot1 will move from room5 to room1, pick up ball1 in room1 with lgripper1, move from room5 to room4, then from room1 to room5, drop ball1 in room5 using lgripper1, and finally, in room5, robot1's rgripper1 will release ball3 and robot1 will move from room5 to room2 to reach the current state. Are the following properties of the state (both with and without negations) true for ball1 before the first inexecutable action in the sequence? Robot1's rgripper1 is not holding ball1 and robot1 is holding ball1 with lgripper1.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "885b8d20-67ea-45a6-9ffc-c34da5c6d239", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, from room3, robot1's rgripper1 picks up ball2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is not located at room1, ball1 is not located at room3, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball2 is at room2, ball2 is not at room3, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not located at room5, ball2 is not present at room4, ball3 is located at room1, ball3 is not at room2, ball3 is not at room3, ball3 is not located at room4, ball3 is not present at room5, ball4 is at room2, ball4 is not at room3, ball4 is not at room5, ball4 is not present at room1, ball4 is not present at room4, ball5 is located at room2, ball5 is not at room1, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room3, ball5 is not present at room4, ball5 is not present at room5, ball6 is not at room4, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room2, ball6 is not present at room3, ball6 is not present at room5, ball6 is present at room1, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, robot1 is at room1, robot1 is not at room5, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room2, robot1 is not located at room3, robot1 is not located at room4 and robot1's lgripper1 is not free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1 is set to move from room4 to room1, then robot1 will pick up ball1 from room1 using its lgripper1, next, robot1's rgripper1 will pick up ball2 from room3, then robot1's rgripper1 will pick up ball2 in room2, after that, robot1 will move from room2 to room3, then robot1's lgripper1 will drop ball1 in room3, followed by robot1 moving from room3 to room4, then robot1's rgripper1 will drop ball2 in room4, next, robot1 will move from room4 to room2, and finally, robot1 will pick up ball4 from room2 using its lgripper1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current state is characterized by the following conditions: ball1 is not in room1, ball1 is not in room3, ball1 is not in room5, ball1 is not in room2, ball1 is not in room4, ball2 is in room2, ball2 is not in room3, ball2 is not being held by robot1's rgripper1, ball2 is not in room1, ball2 is not in room5, ball2 is not in room4, ball3 is in room1, ball3 is not in room2, ball3 is not in room3, ball3 is not in room4, ball3 is not in room5, ball4 is in room2, ball4 is not in room3, ball4 is not in room5, ball4 is not in room1, ball4 is not in room4, ball5 is in room2, ball5 is not in room1, ball5 is not being held by robot1's rgripper1, ball5 is not in room3, ball5 is not in room4, ball5 is not in room5, ball6 is not in room4, ball6 is not being held by robot1's lgripper1, ball6 is not being held by robot1's rgripper1, ball6 is not in room2, ball6 is not in room3, ball6 is not in room5, ball6 is in room1, robot1's lgripper1 is holding ball1, robot1's lgripper1 is not holding ball2, robot1's lgripper1 is not holding ball4, robot1's rgripper1 is empty, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball3, robot1 is in room1, robot1 is not in room5, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball5 with lgripper1, robot1 is not in room2, robot1 is not in room3, robot1 is not in room4, and robot1's lgripper1 is not empty.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present at room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "6b071241-a96c-4c79-87f5-6e152b5d6bfd", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_2_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, in room2, robot2's rgripper2 drops ball4, rgripper2 of robot2 picks up ball2 in room3, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true for lgripper1 before the first inexecutable action in the sequence? Lgripper1 of robot1 is free and robot2's lgripper1 is not free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, then in room2, robot2's right gripper (rgripper2) will release ball4, and in room3, robot2's rgripper2 will pick up ball2. Next, robot2 will move from room3 back to room2, where it will drop ball1 using its left gripper (lgripper2) and then drop ball2 using its rgripper2. After that, robot2 will move from room2 to room3 again, pick up ball4 using its lgripper2, and pick up ball7 using its rgripper2 in room3. Finally, robot2 will move from room3 to room2 to reach the current state. Now, considering the state before the first inexecutable action in the sequence, are the following properties true for lgripper1 (both affirmatively and negatively)? Specifically, is lgripper1 of robot1 free, and is robot2's lgripper1 not free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "b23d43d8-bfee-46f8-a878-f9027dcc0aa6", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball3 is dropped in room3 with rgripper2 by robot1, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and robot2 moves to room2 from room3 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot2's rgripper1, ball1 is not being carried by robot2's rgripper2, ball2 is being carried by robot1's rgripper1, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot1's rgripper2, ball3 is being carried by robot1's lgripper1, ball3 is being carried by robot2's rgripper2, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper2, ball4 is not being carried by robot2's rgripper1, ball5 is being carried by robot2's lgripper1, ball5 is being carried by robot2's rgripper2, ball6 is not being carried by robot2's rgripper2, ball7 is not being carried by robot2's lgripper1, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is carrying ball6, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is carrying ball2, lgripper1 of robot2 is carrying ball6, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is carrying ball2, rgripper1 of robot1 is carrying ball1, rgripper2 of robot1 is carrying ball6, rgripper2 of robot2 is carrying ball2, rgripper2 of robot2 is not carrying ball7, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball3 with lgripper2, robot1 is carrying ball5 with lgripper2, robot1 is carrying ball5 with rgripper2, robot1 is carrying ball6 with rgripper1, robot1 is carrying ball7 with rgripper1, robot1 is carrying ball7 with rgripper2, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot2 is carrying ball1 with lgripper1, robot2 is carrying ball1 with lgripper2, robot2 is carrying ball2 with rgripper1, robot2 is carrying ball4 with lgripper2, robot2 is carrying ball4 with rgripper2, robot2 is carrying ball5 with lgripper2, robot2 is carrying ball6 with rgripper1, robot2 is carrying ball7 with lgripper2, robot2 is carrying ball7 with rgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball4 with lgripper1, robot2 is not carrying ball5 with rgripper1 and robot2 is not carrying ball6 with lgripper2.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned: starting from room2, robot2 will move to room3, pick up ball1 from room3 using lgripper2, pick up ball2 from room3 using rgripper2, move back to room2, then robot1 will drop ball3 in room3 using rgripper2, robot2 will drop ball2 in room2 using rgripper2, move back to room3, pick up ball4 using lgripper2 and ball7 using rgripper2, and finally move back to room2 to reach the current state. The question is whether the following properties of the state (including both affirmative and negative statements) are true before the first inexecutable action in the sequence: Is ball1 being held by robot2's rgripper1, and not by robot2's rgripper2? Is ball2 being held by robot1's rgripper1, and not by robot1's lgripper2 or rgripper2? Is ball3 being held by robot1's lgripper1, and by robot2's rgripper2, but not by robot1's rgripper1, robot2's lgripper1, or robot2's lgripper2? Are ball4, ball5, and ball6 not being held by various grippers of robot1 and robot2? Is ball5 being held by robot2's lgripper1 and rgripper2, and is ball6 not being held by robot2's rgripper2? Is ball7 not being held by robot2's lgripper1? Is lgripper1 of robot1 holding ball1, ball6, but not ball7? Is lgripper1 of robot2 holding ball2 and ball6? Are lgripper2 of robot1 and robot2 not holding ball6 and ball7, respectively? Is rgripper1 of robot1 holding ball1, and is rgripper2 of robot1 holding ball6? Is rgripper2 of robot2 holding ball2 but not ball7? Is robot1 holding ball2 with lgripper1, ball3 with lgripper2, ball5 with rgripper2, ball6 with rgripper1, and ball7 with rgripper1 and rgripper2, but not holding ball1 with lgripper2 or rgripper2, ball3 with rgripper2, ball4 with rgripper1 or rgripper2, or ball5 with lgripper1 or rgripper1? Is robot2 holding ball1 with lgripper1 and lgripper2, ball2 with rgripper1, ball4 with lgripper2, ball5 with lgripper2, ball6 with rgripper1, and ball7 with lgripper2 and rgripper1, but not holding ball3 with rgripper1, ball4 with lgripper1, ball5 with rgripper1, or ball6 with lgripper2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "84003f88-b157-4b85-a310-e200ef1d03ee", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot2 picks up ball2 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper1 of robot2 is not free, rgripper2 of robot2 is free, robot1's lgripper1 is free, robot1's lgripper2 is not available, robot1's rgripper1 is available, robot1's rgripper2 is not free, robot2's lgripper2 is free and robot2's rgripper1 is not free.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are intended to be executed: the left gripper of robot 2 (lgripper1) picks up ball 2 in room 2 to achieve the current state. Are the following properties of the state (including both affirmative and negative statements) valid before the first action in the sequence that cannot be executed? The left gripper of robot 2 is occupied, the right gripper of robot 2 is available, the left gripper of robot 1 is available, the left gripper of robot 1 is not accessible, the right gripper of robot 1 is accessible, the right gripper of robot 1 is occupied, the left gripper of robot 2 is available, and the right gripper of robot 2 is occupied.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "308c8bad-72e1-4eff-a156-77625e854496", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball1 in room1, ball1 is dropped in room4 with rgripper1 by robot1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball3 and robot1 moves to room2 from room5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball6 before the first inexecutable action in the sequence? Ball6 is being carried by robot1's lgripper1 and ball6 is not being carried by robot1's rgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1's lgripper1 will pick up ball7 in room4, then robot1 will move from room4 to room5, where it will drop ball7 using lgripper1, after which robot1 will move from room5 to room1, pick up ball1 with lgripper1 in room1, drop ball1 in room4 using rgripper1, move from room1 to room5, drop ball1 in room5 using lgripper1, and finally, in room5, robot1 will drop ball3 with rgripper1 and move to room2 from room5 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties (including their negations) true for ball6: is ball6 being carried by robot1's lgripper1, and is ball6 not being carried by robot1's rgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "e56a27fe-2cd2-400d-ae29-e16e02c4b939", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room4, robot1's lgripper1 drops ball1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room1, ball1 is not at room2, ball1 is not at room4, ball1 is not present at room3, ball1 is not present at room5, ball2 is at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not located at room4, ball2 is not present at room5, ball3 is at room1, ball3 is not at room2, ball3 is not at room4, ball3 is not at room5, ball3 is not located at room3, ball4 is located at room2, ball4 is not located at room1, ball4 is not located at room3, ball4 is not located at room4, ball4 is not located at room5, ball5 is located at room2, ball5 is not located at room3, ball5 is not present at room1, ball5 is not present at room4, ball5 is not present at room5, ball6 is at room1, ball6 is not located at room2, ball6 is not located at room5, ball6 is not present at room3, ball6 is not present at room4, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is located at room4, robot1 is not at room5, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not located at room2, robot1 is not present in room1, robot1 is not present in room3 and robot1's lgripper1 is available.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: in room4, robot1's lgripper1 releases ball1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current state of the balls is as follows: ball1 is in room1, not in room2, room3, room4, or room5. Ball2 is in room2, but not in room1, room3, room4, or room5. Ball3 is in room1, but not in room2, room3, room4, or room5. Ball4 is in room2, but not in room1, room3, room4, or room5. Ball5 is in room2, but not in room1, room3, room4, or room5. Ball6 is in room1, but not in room2, room3, room4, or room5. The current state of robot1 is as follows: its lgripper1 is not holding ball2 or ball3, its rgripper1 is free and not holding ball3, ball4, or ball6. Robot1 is in room4, but not in room1, room2, room3, or room5. It is not carrying ball1 with either lgripper1 or rgripper1, and it is not carrying ball2, ball4, ball5, or ball6 with either gripper. Additionally, robot1's lgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "e564f6e7-3de4-4e62-8b95-02c1fe62ebb6", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves to room2 from room1, from room2, robot1's rgripper1 picks up ball2, from room2, robot1 moves to room3, in room3, robot1's lgripper1 drops ball1, robot1 moves from room3 to room4, ball2 is dropped in room5 with lgripper1 by robot1, robot1 moves to room2 from room4 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot1's rgripper1, ball1 is not being carried by robot1's lgripper1, ball3 is being carried by robot1's rgripper1, ball3 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper1, ball5 is being carried by robot1's rgripper1, ball5 is not being carried by robot1's lgripper1, robot1 is carrying ball4 with rgripper1, robot1 is carrying ball6 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with rgripper1 and robot1 is not carrying ball6 with lgripper1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 relocates from room4 to room1, then its left gripper (lgripper1) picks up ball1 in room1. Next, robot1 proceeds to room2 from room1, and its right gripper (rgripper1) picks up ball2 in room2. From room2, robot1 moves to room3, where it drops ball1 using its left gripper. Then, robot1 moves from room3 to room4, and subsequently drops ball2 in room5 using its left gripper. Finally, robot1 returns to room2 from room4 and uses its left gripper to pick up ball4, reaching the current state. The question is whether the following properties (including their negations) are true before the first inexecutable action in the sequence: Is ball1 being held by robot1's right gripper, is ball1 not being held by robot1's left gripper, is ball3 being held by robot1's right gripper, is ball3 not being held by robot1's left gripper, is ball4 not being held by robot1's left gripper, is ball5 being held by robot1's right gripper, is ball5 not being held by robot1's left gripper, is robot1 holding ball4 with its right gripper, is robot1 holding ball6 with its right gripper, is robot1 not holding ball2 with its left gripper, is robot1 not holding ball2 with its right gripper, and is robot1 not holding ball6 with its left gripper.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "68557378-51a7-4c38-aae2-a9598f54edae", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_4_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, from room3, robot2 moves to room2, lgripper2 of robot2 drops ball1 in room2, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, from room2, robot2 moves to room1, lgripper2 of robot2 drops ball4 in room1, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and rgripper2 of robot2 picks up ball6 in room3 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is being carried by robot1's rgripper2, ball1 is being carried by robot2's lgripper1, ball1 is not at room1, ball1 is not at room2, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's lgripper2, ball1 is present at room3, ball2 is at room1, ball2 is being carried by robot2's lgripper2, ball2 is being carried by robot2's rgripper1, ball2 is located at room3, ball2 is not present at room2, ball3 is being carried by robot1's lgripper1, ball3 is not at room2, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper1, ball3 is not being carried by robot2's rgripper2, ball3 is not present at room1, ball3 is not present at room3, ball4 is not located at room3, ball4 is not present at room2, ball4 is present at room1, ball5 is being carried by robot1's lgripper1, ball5 is not at room2, ball5 is not at room3, ball5 is not being carried by robot1's rgripper2, ball5 is present at room1, ball6 is at room1, ball6 is at room2, ball6 is being carried by robot1's lgripper2, ball6 is being carried by robot2's lgripper1, ball6 is being carried by robot2's rgripper2, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room3, ball7 is being carried by robot1's lgripper2, ball7 is located at room3, ball7 is not at room2, ball7 is not being carried by robot1's rgripper2, ball7 is present at room1, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is carrying ball6, lgripper1 of robot2 is carrying ball5, lgripper1 of robot2 is not carrying ball4, lgripper2 of robot1 is carrying ball4, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball5, rgripper1 of robot1 is carrying ball3, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball6, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot2 is carrying ball4, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball7, robot1 is carrying ball1 with lgripper2, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball3 with lgripper2, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball6 with rgripper2, robot1 is not at room2, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not present in room3, robot1 is present in room1, robot1's lgripper1 is free, robot1's lgripper2 is available, robot1's rgripper1 is not free, robot1's rgripper2 is not free, robot2 is carrying ball2 with lgripper1, robot2 is carrying ball5 with rgripper1, robot2 is carrying ball6 with lgripper2, robot2 is carrying ball7 with lgripper2, robot2 is carrying ball7 with rgripper1, robot2 is not at room3, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball7 with lgripper1, robot2 is present in room1, robot2 is present in room2, robot2's lgripper1 is available, robot2's lgripper2 is not available and robot2's rgripper2 is free.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are scheduled to be executed: robot2 moves from room2 to room3, robot2 uses its lgripper2 to pick up ball1 from room3, robot2's rgripper2 picks up ball2 from room3, robot2 moves back to room2 from room3, robot2's lgripper2 drops ball1 in room2, robot2's rgripper2 drops ball2 in room2, robot2 moves from room2 to room3, robot2's lgripper2 picks up ball4 from room3, robot2 uses its rgripper2 to pick up ball7 from room3, robot2 moves from room3 to room2, robot2's rgripper2 drops ball7 in room2, robot2's rgripper2 picks up ball3 in room2, robot2 moves from room2 to room1, robot2's lgripper2 drops ball4 in room1, robot2 uses its lgripper2 to pick up ball5 from room1, robot2's rgripper2 drops ball3 in room1, robot2 uses its rgripper2 to pick up ball6 from room1, robot2 moves from room1 to room2, and robot2's rgripper2 picks up ball6 in room3 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state is as follows: ball1 is being carried by robot1's rgripper2, ball1 is being carried by robot2's lgripper1, ball1 is not in room1, ball1 is not in room2, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's lgripper2, ball1 is present in room3, ball2 is in room1, ball2 is being carried by robot2's lgripper2, ball2 is being carried by robot2's rgripper1, ball2 is in room3, ball2 is not in room2, ball3 is being carried by robot1's lgripper1, ball3 is not in room2, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper1, ball3 is not being carried by robot2's rgripper2, ball3 is not in room1, ball3 is not in room3, ball4 is not in room3, ball4 is not in room2, ball4 is in room1, ball5 is being carried by robot1's lgripper1, ball5 is not in room2, ball5 is not in room3, ball5 is not being carried by robot1's rgripper2, ball5 is in room1, ball6 is in room1, ball6 is in room2, ball6 is being carried by robot1's lgripper2, ball6 is being carried by robot2's lgripper1, ball6 is being carried by robot2's rgripper2, ball6 is not being carried by robot1's rgripper1, ball6 is not in room3, ball7 is being carried by robot1's lgripper2, ball7 is in room3, ball7 is not in room2, ball7 is not being carried by robot1's rgripper2, ball7 is in room1, robot1's lgripper1 is carrying ball4, robot1's lgripper1 is carrying ball6, robot2's lgripper1 is carrying ball5, robot2's lgripper1 is not carrying ball4, robot1's lgripper2 is carrying ball4, robot1's lgripper2 is not carrying ball2, robot1's lgripper2 is not carrying ball5, robot1's rgripper1 is carrying ball3, robot1's rgripper1 is not carrying ball2, robot2's rgripper1 is not carrying ball1, robot2's rgripper1 is not carrying ball6, robot2's rgripper1 is not free, robot1's rgripper2 is not carrying ball4, robot2's rgripper2 is carrying ball4, robot2's rgripper2 is not carrying ball1, robot2's rgripper2 is not carrying ball2, robot2's rgripper2 is not carrying ball7, robot1 is carrying ball1 with lgripper2, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball3 with lgripper2, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball6 with rgripper2, robot1 is not in room2, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not in room3, robot1 is in room1, robot1's lgripper1 is free, robot1's lgripper2 is available, robot1's rgripper1 is not free, robot1's rgripper2 is not free, robot2 is carrying ball2 with lgripper1, robot2 is carrying ball5 with rgripper1, robot2 is carrying ball6 with lgripper2, robot2 is carrying ball7 with lgripper2, robot2 is carrying ball7 with rgripper1, robot2 is not in room3, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball7 with lgripper1, robot2 is in room1, robot2 is in room2, robot2's lgripper1 is available, robot2's lgripper2 is not available, and robot2's rgripper2 is free.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "424f73a2-6631-4b6d-91f5-9923e064fd0b", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room3 with lgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball3 is not being carried by robot1's lgripper1, ball4 is being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's rgripper1, ball7 is being carried by robot1's rgripper1, lgripper1 of robot1 is carrying ball5, lgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball1, robot1 is carrying ball2 with rgripper1, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball6 with lgripper1, robot1 is carrying ball7 with lgripper1 and robot1 is not carrying ball2 with lgripper1.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 will use lgripper1 to pick up ball1 from room3 to achieve the current state. Are the following properties of the state (including both affirmative and negative statements) true before the first unexecutable action in the sequence? The following conditions are being evaluated: robot1's lgripper1 is not holding ball3, robot1's lgripper1 is holding ball4, robot1's rgripper1 is not holding ball4, robot1's rgripper1 is not holding ball5, robot1's rgripper1 is not holding ball6, robot1's rgripper1 is holding ball7, robot1's lgripper1 has ball5, robot1's lgripper1 does not have ball1, robot1's rgripper1 does not have ball1, robot1 is holding ball2 with its rgripper1, robot1 is holding ball3 with its rgripper1, robot1 is holding ball6 with its lgripper1, robot1 is holding ball7 with its lgripper1, and robot1 is not holding ball2 with its lgripper1.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "257a6e92-cd77-4a50-a061-f9aac8078a3d", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room3, from room4, robot1's rgripper1 picks up ball5, from room3, robot1's rgripper1 picks up ball7, robot1 moves to room4 from room3, lgripper1 of robot1 drops ball1 in room4, ball7 is dropped in room4 with rgripper1 by robot1, robot1 moves from room4 to room2, ball2 is picked from room2 with lgripper1 by robot1 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for ball2 before the first inexecutable action in the sequence? Ball2 is located at room2, ball2 is not at room3, ball2 is not at room4, ball2 is not present at room1 and ball2 is not present at room5.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1 will move from room4 to room1, then robot1's left gripper (lgripper1) will grasp ball1 in room1, and its right gripper (rgripper1) will pick up ball3 in room1. Next, robot1 will move from room1 to room5, where rgripper1 will release ball3, and then robot1 will proceed to room2. In room2, rgripper1 will pick up ball4, and robot1 will return to room1, where it will drop ball4 using rgripper1. Subsequently, rgripper1 will grasp ball6 in room1, and robot1 will move to room3. From room4, rgripper1 will pick up ball5, and in room3, rgripper1 will pick up ball7. Then, robot1 will move back to room4 from room3, where lgripper1 will release ball1, and rgripper1 will drop ball7. After that, robot1 will move to room2, pick up ball2 with lgripper1, and also pick up ball5 with rgripper1 to reach the current state. Considering the state before the first inexecutable action in the sequence, are the following properties (including their negations) true for ball2? Is ball2 located in room2? Is ball2 not located in room3? Is ball2 not located in room4? Is ball2 not present in room1? Is ball2 not present in room5?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "0fd463bb-c960-4f3a-a13a-f963209a8b20", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, ball5 is dropped in room5 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball7 in room3, robot1 moves to room4 from room3, in room4, robot1's lgripper1 drops ball1, in room4, robot1's rgripper1 drops ball7, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball2 in room2 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is not located at room1, ball1 is not located at room2, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room3, ball2 is not located at room3, ball2 is not located at room5, ball2 is not present at room1, ball2 is not present at room4, ball2 is present at room2, ball3 is at room5, ball3 is not at room2, ball3 is not located at room3, ball3 is not present at room1, ball3 is not present at room4, ball4 is not at room2, ball4 is not at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room1, ball4 is not located at room4, ball4 is not located at room5, ball5 is located at room2, ball5 is not at room3, ball5 is not located at room4, ball5 is not present at room1, ball5 is not present at room5, ball6 is at room1, ball6 is not at room3, ball6 is not at room4, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room2, ball6 is not located at room5, ball7 is located at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room1, ball7 is not located at room2, ball7 is not present at room4, ball7 is not present at room5, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball7, robot1 is at room1, robot1 is not at room2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room4, robot1 is not present in room3, robot1 is not present in room5, robot1's lgripper1 is not free and robot1's rgripper1 is not free.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: starting from room4, robot1 moves to room1, picks up ball1 from room1 using lgripper1, then uses rgripper1 to pick up ball3 from room1, moves to room5, drops ball3 in room5 using rgripper1, proceeds to room2, picks up ball4 from room2 using rgripper1, moves back to room1, drops ball5 in room5 using rgripper1, picks up ball6 from room1 using rgripper1, moves to room3, drops ball6 in room3, picks up ball7 from room3 using rgripper1, moves to room4, drops ball1 in room4 using lgripper1, drops ball7 in room4 using rgripper1, moves to room2, picks up ball2 from room2 using lgripper1, and picks up ball5 from room2 using rgripper1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current state is characterized by the following conditions: ball1 is not in room1, room2, room4, or room5, and is not present in room3, ball2 is not in room1, room3, room4, or room5, but is present in room2, ball3 is in room5, but not in room2, room3, room1, or room4, ball4 is not in room2, room3, room1, room4, or room5, and is not being carried by robot1's lgripper1, ball5 is in room2, but not in room3, room4, room1, or room5, ball6 is in room1, but not in room3, room4, room2, or room5, and is not being carried by robot1's rgripper1, ball7 is in room3, but not in room1, room2, room4, or room5, and is not being carried by robot1's lgripper1, robot1's lgripper1 is carrying ball1, but not ball2 or ball6, robot1's rgripper1 is carrying ball4, but not ball1, ball2, ball3, ball5, or ball7, robot1 is in room1, but not in room2, room4, room3, or room5, and is not carrying ball3 or ball5 with lgripper1, and both robot1's lgripper1 and rgripper1 are occupied.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is free."}
{"question_id": "5ffde860-89e6-42f0-8a65-f95eba3bc195", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot1's rgripper1 picks up ball5 to reach the current state. Are the following valid properties of the state (both with and without negations) true for robot1 before the first inexecutable action in the sequence? Lgripper1 of robot1 is not free and robot1's rgripper1 is available.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Considering the initial condition, the following actions are intended to be executed: starting from room1, robot1's rgripper1 is supposed to pick up ball5 to achieve the current state. Are the following properties of the state (including both affirmative and negative statements) true for robot1 prior to the first inexecutable action in the sequence? The lgripper1 of robot1 is not available for use and robot1's rgripper1 is in an available state.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "0a0718b9-2e78-4659-a1da-0147dd52cb47", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball2 is picked from room3 with lgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Robot1's lgripper1 is not available and robot1's rgripper1 is free.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1, using lgripper1, will pick up ball2 from room3, then use lgripper1 to pick up ball1 in room1, followed by using rgripper1 to pick up ball3 in room1, then move from room1 to room5, drop ball3 in room5 using rgripper1, move from room5 to room2, pick up ball4 from room2 with rgripper1, move from room2 to room1, drop ball4 in room1 using rgripper1, and finally pick up ball6 in room1 using rgripper1 to reach the current state. Are the following properties of the state (both affirmative and negative) true before the first unexecutable action in the sequence: robot1's lgripper1 is occupied and robot1's rgripper1 is available?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "a44f5d7b-b2a1-4cfa-a61c-ab918ce47320", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball7 in room3, from room3, robot1 moves to room4, ball4 is dropped in room2 with rgripper1 by robot1, rgripper1 of robot1 drops ball7 in room4, robot1 moves from room4 to room2, from room2, robot1's lgripper1 picks up ball2 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball5 is being carried by robot1's rgripper1, lgripper1 of robot1 is carrying ball6, lgripper1 of robot1 is carrying ball7, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is not carrying ball3, robot1 is carrying ball3 with lgripper1, robot1 is carrying ball5 with lgripper1, robot1 is carrying ball7 with rgripper1 and robot1 is not carrying ball6 with rgripper1.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following sequence of actions is planned: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1 and rgripper1 to pick up ball3. Then, robot1 moves to room5 and drops ball3 using rgripper1. From room5, robot1 moves to room2, picks up ball4 with rgripper1, and then returns to room1, where it drops ball4 using rgripper1. Robot1 then picks up ball6 with rgripper1 in room1 and moves to room3, where it drops ball6 using rgripper1. In room3, robot1 picks up ball7 with rgripper1, moves to room4, and drops ball7 using rgripper1. However, it is noted that ball4 is dropped in room2, not room4. Finally, robot1 moves back to room2, where it picks up ball2 with lgripper1 and ball5 with rgripper1 to reach the current state. The question is whether the following properties (both affirmative and negative) are true before the first inexecutable action in the sequence: Is ball1 being held by robot1's lgripper1? Is ball4 not being held by robot1's rgripper1? Is ball5 being held by robot1's rgripper1? Is lgripper1 of robot1 holding ball6? Is lgripper1 of robot1 holding ball7? Is lgripper1 of robot1 not holding ball2? Is lgripper1 of robot1 not holding ball4? Is rgripper1 of robot1 holding ball1? Is rgripper1 of robot1 holding ball2? Is rgripper1 of robot1 not holding ball3? Is robot1 holding ball3 with lgripper1? Is robot1 holding ball5 with lgripper1? Is robot1 holding ball7 with rgripper1? Is robot1 not holding ball6 with rgripper1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "f9ccafa0-54ef-4d68-9849-db7d3138d386", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, from room1, robot1's lgripper2 picks up ball2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7 and robot2 moves to room2 from room3 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Lgripper2 of robot2 is free, rgripper1 of robot1 is not free, rgripper2 of robot1 is free, robot1's lgripper1 is not free, robot1's lgripper2 is not free, robot2's lgripper1 is not available, robot2's rgripper1 is not available and robot2's rgripper2 is not available.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 moves from room2 to room3, robot1's lgripper2 picks up ball2 from room1, robot2 uses its rgripper2 to pick up ball2 from room3, then moves back to room2, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2, then moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and finally moves back to room2 to reach the current state. Are the following properties of the state (both affirmative and negative) true before the first action that cannot be executed in the sequence? Is robot2's lgripper2 free, is robot1's rgripper1 not free, is robot1's rgripper2 free, is robot1's lgripper1 not free, is robot1's lgripper2 not free, is robot2's lgripper1 unavailable, is robot2's rgripper1 unavailable, and is robot2's rgripper2 unavailable?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "b74f50d8-30bb-4ed6-81e3-922c1ed469fa", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball6 is picked from room1 with lgripper1 by robot1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is at room1, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room5, ball1 is not present at room3, ball1 is not present at room4, ball1 is not present at room6, ball2 is located at room2, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not located at room3, ball2 is not located at room6, ball2 is not present at room4, ball2 is not present at room5, ball3 is located at room1, ball3 is not at room2, ball3 is not at room4, ball3 is not located at room3, ball3 is not located at room5, ball3 is not located at room6, ball4 is at room2, ball4 is not at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room5, ball4 is not located at room6, ball4 is not present at room4, ball5 is located at room3, ball5 is not at room2, ball5 is not at room4, ball5 is not located at room5, ball5 is not located at room6, ball5 is not present at room1, ball6 is at room1, ball6 is not at room3, ball6 is not at room6, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room4, ball6 is not located at room5, ball6 is not present at room2, ball7 is located at room4, ball7 is not at room3, ball7 is not at room5, ball7 is not located at room2, ball7 is not located at room6, ball7 is not present at room1, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball7, robot1 is not at room5, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room1, robot1 is not located at room2, robot1 is not located at room3, robot1 is not located at room6, robot1 is present in room4 and robot1's lgripper1 is available.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: robot1 is to pick ball6 from room1 using lgripper1 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? The current state is as follows: ball1 is in room1 but not being carried by robot1's rgripper1, and it is not in room2, room3, room4, room5, or room6. Ball2 is in room2, not being carried by robot1's rgripper1, and not in room1, room3, room4, room5, or room6. Ball3 is in room1, not in room2, room3, room4, room5, or room6. Ball4 is in room2, not in room1, room3, room4, room5, or room6, and not being carried by robot1's rgripper1. Ball5 is in room3, not in room1, room2, room4, room5, or room6. Ball6 is in room1, not in room2, room3, room4, room5, or room6, and not being carried by robot1's rgripper1. Ball7 is in room4, not in room1, room2, room3, room5, or room6. Additionally, robot1's lgripper1 is not carrying ball1, ball3, or ball5, and robot1's rgripper1 is free and not carrying ball7. Robot1 is not in room1, room2, room3, room5, or room6, and is not carrying any balls with either gripper. However, robot1 is present in room4 and its lgripper1 is available.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "614fadd3-5332-4ac0-9469-bd12ab616228", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_2_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball7, robot1 moves from room3 to room4, ball1 is dropped in room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball7 in room4, rgripper1 of robot1 picks up ball2 in room3, ball2 is picked from room2 with lgripper1 by robot1 and ball5 is picked from room2 with rgripper1 by robot1 to reach the current state. Are the following valid properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Robot1's rgripper1 is not available.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1 and rgripper1 to pick up ball3. Then, robot1 moves to room5 and drops ball3 using rgripper1. Next, robot1 moves to room2, picks up ball4 with rgripper1, and then returns to room1, where it drops ball4 using rgripper1. In room1, robot1's rgripper1 picks up ball6, and then robot1 moves to room3, where it drops ball6 using rgripper1. In room3, robot1's rgripper1 picks up ball7, and then robot1 moves to room4, where it drops ball1 using lgripper1 and ball7 using rgripper1. Additionally, robot1's rgripper1 picks up ball2 in room3, and in room2, robot1 uses lgripper1 to pick up ball2 and rgripper1 to pick up ball5, ultimately reaching the current state. Are the following properties of the state (both with and without negations) true for rgripper1 before the first inexecutable action in the sequence? Specifically, is it true that robot1's rgripper1 is not available?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "3bc2b7a3-55be-4443-be41-076e0fabba15", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, ball5 is picked from room2 with lgripper1 by robot2, ball7 is picked from room3 with rgripper2 by robot2 and from room3, robot2 moves to room2 to reach the current state. Are the following valid properties of the state (both with and without negations) true before the first inexecutable action in the sequence? Ball1 is at room1, ball1 is not at room3, ball1 is not located at room2, ball2 is not at room2, ball2 is not at room3, ball2 is present at room1, ball3 is located at room1, ball3 is not located at room2, ball3 is not present at room3, ball4 is at room1, ball4 is not located at room3, ball4 is present at room2, ball5 is at room1, ball5 is not present at room3, ball5 is present at room2, ball6 is not at room2, ball6 is not at room3, ball6 is not located at room1, ball7 is not located at room2, ball7 is present at room1, ball7 is present at room3, robot1 is located at room2, robot1 is not at room1, robot1 is not at room3, robot2 is located at room2, robot2 is located at room3 and robot2 is not located at room1.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 will move from room2 to room3, pick up ball1 from room3 using lgripper2, then use rgripper2 to pick up ball2 from room3, move back to room2, drop ball1 using lgripper2 and ball2 using rgripper2 in room2, return to room3, pick up ball5 from room2 using lgripper1 and ball7 from room3 using rgripper2, and finally move back to room2 to reach the current state. The question is whether the following properties (both affirmative and negative) are true before the first unexecutable action in the sequence: Is ball1 located at room1, not at room3, and not at room2? Is ball2 not at room2 and room3, but at room1? Is ball3 at room1, but not at room2 and room3? Is ball4 at room1, not at room3, but at room2? Is ball5 at room1, not at room3, but at room2? Is ball6 not at room2, room3, or room1? Is ball7 not at room2, but at room1 and room3? Is robot1 at room2, but not at room1 and room3? Is robot2 at room2 and room3, but not at room1?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "7d153552-d049-43c8-822e-58a66594bc2b", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_4_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, ball4 is picked from room2 with lgripper1 by robot2, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2 and from room3, robot2 moves to room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? Ball1 is not at room1, ball1 is not at room2, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's lgripper1, ball1 is present at room3, ball2 is not at room1, ball2 is not at room2, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot1's rgripper1, ball2 is not being carried by robot2's lgripper2, ball2 is present at room3, ball3 is located at room2, ball3 is not at room1, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not located at room3, ball4 is at room3, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's rgripper1, ball4 is not located at room1, ball4 is not present at room2, ball5 is located at room1, ball5 is not at room3, ball5 is not being carried by robot1's lgripper2, ball5 is not being carried by robot2's rgripper2, ball5 is not present at room2, ball6 is not at room2, ball6 is not being carried by robot1's lgripper2, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper2, ball6 is not being carried by robot2's rgripper1, ball6 is not being carried by robot2's rgripper2, ball6 is not located at room3, ball6 is present at room1, ball7 is not being carried by robot2's rgripper1, ball7 is not located at room1, ball7 is not located at room2, ball7 is present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot2 is not carrying ball2, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is free, lgripper2 of robot2 is not carrying ball3, lgripper2 of robot2 is not carrying ball4, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot2 is free, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not carrying ball7, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with rgripper2, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not present in room1, robot1 is not present in room3, robot1 is present in room2, robot1's lgripper2 is not free, robot1's rgripper2 is not free, robot2 is located at room3, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball7 with lgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not present in room1, robot2 is not present in room2 and robot2's lgripper1 is not free.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial state, the following actions are scheduled to be executed: starting from room2, robot2 will move to room3, then pick up ball4 from room2 using its lgripper1, next, from room3, robot2's rgripper2 will pick up ball2, after that, robot2 will move back to room2 from room3, in room2, robot2's lgripper2 will drop ball1, and then ball2 will be dropped in room2 using rgripper2 by robot2, subsequently, robot2 will move from room2 to room3, from room3, robot2's lgripper2 will pick up ball4, and ball7 will be picked from room3 using rgripper2 by robot2, finally, from room3, robot2 will move to room2 to reach the current state. Is this the state before the first inexecutable action in the sequence? True or False? \n\nThe current state of the balls and robots is as follows: \n- Ball1 is not in room1 or room2, and it is not being carried by robot1's lgripper2 or robot2's lgripper1, but it is present in room3.\n- Ball2 is not in room1 or room2, and it is not being carried by robot1's lgripper2, robot1's rgripper1, or robot2's lgripper2, but it is present in room3.\n- Ball3 is in room2, not in room1, and not being carried by robot1's rgripper2 or robot2's lgripper1.\n- Ball4 is in room3, not being carried by robot1's rgripper2, robot2's lgripper1, or robot2's rgripper1, and not in room1 or room2.\n- Ball5 is in room1, not in room3, and not being carried by robot1's lgripper2 or robot2's rgripper2, and not in room2.\n- Ball6 is not in room2, not being carried by robot1's lgripper2, robot1's rgripper1, robot2's lgripper2, robot2's rgripper1, or robot2's rgripper2, not in room3, but present in room1.\n- Ball7 is not being carried by robot2's rgripper1, not in room1 or room2, but present in room3.\n\nThe current state of the grippers is as follows:\n- Robot1's lgripper1 is free and not carrying ball1, ball4, or ball5.\n- Robot1's lgripper2 is not carrying ball3, ball4, or ball7.\n- Robot1's rgripper1 is free and not carrying ball3, ball4, or ball5.\n- Robot2's lgripper1 is not carrying ball2.\n- Robot2's lgripper2 is free and not carrying ball3 or ball4.\n- Robot2's rgripper1 is not carrying ball3 or ball5 and is not free.\n- Robot2's rgripper2 is free and not carrying ball1, ball4, or ball7.\n\nThe current state of the robots is as follows:\n- Robot1 is not carrying ball1 with rgripper1 or rgripper2, not carrying ball2 with lgripper1 or rgripper2, not carrying ball3 with lgripper1, not carrying ball5 with rgripper2, not carrying ball6 with lgripper1, and not carrying ball7 with lgripper1 or rgripper1, not present in room1 or room3, but present in room2, and its lgripper2 and rgripper2 are not free.\n- Robot2 is located at room3, not carrying ball1 with lgripper2 or rgripper1, not carrying ball2 with rgripper1 or rgripper2, not carrying ball3 with rgripper2, not carrying ball5 with lgripper1 or lgripper2, not carrying ball6 with lgripper1, and not carrying ball7 with lgripper1 or lgripper2, not present in room1 or room2, and its lgripper1 is not free.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
