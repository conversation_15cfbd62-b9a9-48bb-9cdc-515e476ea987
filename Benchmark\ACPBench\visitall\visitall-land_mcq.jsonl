{"id": 1910086906025976655, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x3-y0, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x2-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x1-y1. B. Place loc-x3-y1 has been visited. C. the robot is in place loc-x1-y3. D. the robot is in place loc-x1-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1", "Place loc-x3-y1 has been visited", "the robot is in place loc-x1-y3", "the robot is in place loc-x1-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -4445256229344889313, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x3-y4, loc-x3-y3, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x1-y4, loc-x2-y0, loc-x2-y4, loc-x3-y2, and loc-x1-y1. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y4 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Place loc-x0-y0 has been visited. B. the robot is in place loc-x2-y0. C. the robot is in place loc-x1-y4. D. the robot is in place loc-x1-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x0-y0 has been visited", "the robot is in place loc-x2-y0", "the robot is in place loc-x1-y4", "the robot is in place loc-x1-y3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -827256317947915185, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x0-y1, loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x3-y3, loc-x0-y2, loc-x2-y0, loc-x0-y0, loc-x1-y1, loc-x1-y2, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Place loc-x1-y3 has been visited. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x2-y1. D. the robot is in place loc-x3-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x1-y3 has been visited", "the robot is in place loc-x1-y2", "the robot is in place loc-x2-y1", "the robot is in place loc-x3-y0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 8594127284290650318, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x0-y1, loc-x2-y1, loc-x2-y2, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y0. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x2-y2. D. the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y0", "the robot is in place loc-x1-y2", "the robot is in place loc-x2-y2", "the robot is in place loc-x0-y1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -8672099747881699697, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x1-y2, loc-x1-y0, loc-x2-y3, loc-x0-y3, loc-x0-y2, loc-x3-y3, loc-x0-y0, loc-x3-y2, and loc-x1-y1. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x1-y1. B. the robot is in place loc-x0-y2. C. the robot is in place loc-x2-y2. D. Place loc-x3-y0 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1", "the robot is in place loc-x0-y2", "the robot is in place loc-x2-y2", "Place loc-x3-y0 has been visited"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 7934525284400456754, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x2-y3, loc-x0-y3, loc-x2-y0, loc-x3-y2, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x1-y1. B. the robot is in place loc-x0-y3. C. the robot is in place loc-x3-y0. D. the robot is in place loc-x2-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1", "the robot is in place loc-x0-y3", "the robot is in place loc-x3-y0", "the robot is in place loc-x2-y0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 3038884635460812906, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x0-y1, loc-x1-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y0. B. the robot is in place loc-x0-y1. C. Place loc-x3-y3 has been visited. D. the robot is in place loc-x1-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y0", "the robot is in place loc-x0-y1", "Place loc-x3-y3 has been visited", "the robot is in place loc-x1-y0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 3263163352828609249, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y3, loc-x2-y2, loc-x0-y3, loc-x0-y2, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y4 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x1-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x0-y4 has been visited, and Place loc-x1-y1 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y3. B. the robot is in place loc-x1-y3. C. Place loc-x2-y0 has been visited. D. the robot is in place loc-x0-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y3", "the robot is in place loc-x1-y3", "Place loc-x2-y0 has been visited", "the robot is in place loc-x0-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 6577204245055594759, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x3-y0, loc-x2-y3, loc-x0-y3, loc-x2-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x2-y0. B. the robot is in place loc-x2-y2. C. the robot is in place loc-x2-y1. D. the robot is in place loc-x0-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0", "the robot is in place loc-x2-y2", "the robot is in place loc-x2-y1", "the robot is in place loc-x0-y3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 6526866722911959407, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x2-y1. B. the robot is in place loc-x1-y1. C. the robot is in place loc-x0-y3. D. Place loc-x2-y3 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y1", "the robot is in place loc-x1-y1", "the robot is in place loc-x0-y3", "Place loc-x2-y3 has been visited"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8388408194794152300, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x0-y1, loc-x2-y1, loc-x3-y0, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x2-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y1. B. the robot is in place loc-x0-y3. C. the robot is in place loc-x2-y1. D. Place loc-x1-y3 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y1", "the robot is in place loc-x0-y3", "the robot is in place loc-x2-y1", "Place loc-x1-y3 has been visited"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8991887225648119652, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x0-y1.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x2-y3, loc-x3-y3, loc-x0-y2, loc-x1-y2, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Place loc-x1-y0 has been visited. B. the robot is in place loc-x1-y3. C. the robot is in place loc-x3-y3. D. the robot is in place loc-x0-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x1-y0 has been visited", "the robot is in place loc-x1-y3", "the robot is in place loc-x3-y3", "the robot is in place loc-x0-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 8073549531935831821, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x2-y1, loc-x2-y2, loc-x2-y3, loc-x3-y3, loc-x3-y1, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x3-y3. B. the robot is in place loc-x3-y1. C. Place loc-x1-y1 has been visited. D. the robot is in place loc-x2-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x3-y3", "the robot is in place loc-x3-y1", "Place loc-x1-y1 has been visited", "the robot is in place loc-x2-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 6119121004050946697, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x2-y3, loc-x1-y0, loc-x3-y3, loc-x0-y3, loc-x0-y2, loc-x2-y0, loc-x3-y1, loc-x3-y2, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x2-y1. B. the robot is in place loc-x1-y1. C. the robot is in place loc-x3-y1. D. Place loc-x3-y0 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y1", "the robot is in place loc-x1-y1", "the robot is in place loc-x3-y1", "Place loc-x3-y0 has been visited"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 68786876708054702, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x2-y3, loc-x3-y3, loc-x2-y0, loc-x3-y1, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x2-y1. B. Place loc-x0-y2 has been visited. C. the robot is in place loc-x3-y1. D. the robot is in place loc-x3-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y1", "Place loc-x0-y2 has been visited", "the robot is in place loc-x3-y1", "the robot is in place loc-x3-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
