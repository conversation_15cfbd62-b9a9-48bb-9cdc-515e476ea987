{"question_id": "246c8249-1a01-4ae2-b419-659994dafa97", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0 to reach the current state. In this state, if package3 is loaded in truck1 at location s0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p1_0, driver1 is not at location p3_0, driver1 is not at location s1, driver1 is not currently at location p1_2, driver1 is not currently at location p2_0, driver1 is not currently at location s2, driver1 is not driving truck1 currently, driver1 is not driving truck2 currently, driver1 is not present at location p1_3, driver1 is not present at location p2_1, driver1 is not present at location s0, driver2 is not at location p0_1, driver2 is not at location p2_1, driver2 is not at location p3_0, driver2 is not at location s0, driver2 is not at location s2, driver2 is not currently at location p1_2, driver2 is not currently at location p1_3, driver2 is not currently at location p2_0, driver2 is not present at location p1_0, driver2 is not present at location s1, driver3 is not at location p1_2, driver3 is not at location p2_0, driver3 is not at location p2_1, driver3 is not at location s1, driver3 is not at location s2, driver3 is not at location s3, driver3 is not currently at location p1_0, driver3 is not currently at location p1_3, driver3 is not currently at location s0, driver3 is not driving truck2 currently, driver3 is not present at location p0_1, driver3 is not present at location p3_0, locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and p2_1 does not have a link between them, locations p0_1 and p2_1 does not have a path between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s2 does not have a path between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p0_1 does not have a path between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and p2_1 does not have a link between them, locations p1_0 and p2_1 does not have a path between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s3 does not have a link between them, locations p1_2 and p0_1 does not have a path between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p1_3 does not have a link between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p3_0 does not have a link between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s2 does not have a link between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p1_2 does not have a path between them, locations p1_3 and p2_0 does not have a link between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p2_0 and p1_2 does not have a link between them, locations p2_0 and p2_1 does not have a link between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and p3_0 does not have a path between them, locations p2_0 and s0 does not have a link between them, locations p2_0 and s1 does not have a path between them, locations p2_0 and s2 does not have a link between them, locations p2_0 and s3 does not have a link between them, locations p2_0 and s3 does not have a path between them, locations p2_1 and p1_3 does not have a path between them, locations p2_1 and s1 does not have a link between them, locations p3_0 and p1_0 does not have a link between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and p2_0 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations s0 and p1_0 does not have a link between them, locations s0 and s3 does not have a path between them, locations s1 and p1_3 does not have a link between them, locations s1 and p2_0 does not have a link between them, locations s1 and p2_1 does not have a path between them, locations s1 and p3_0 does not have a path between them, locations s1 and s0 does not have a path between them, locations s1 and s3 does not have a path between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_2 does not have a link between them, locations s2 and p2_1 does not have a path between them, locations s2 and p3_0 does not have a link between them, locations s2 and s1 does not have a path between them, locations s3 and p0_1 does not have a path between them, locations s3 and p1_0 does not have a link between them, locations s3 and p2_0 does not have a link between them, locations s3 and p2_0 does not have a path between them, locations s3 and p2_1 does not have a link between them, locations s3 and p2_1 does not have a path between them, package1 is not at location p1_2, package1 is not at location p2_0, package1 is not currently at location p1_0, package1 is not currently at location s2, package1 is not placed in truck1, package1 is not placed in truck2, package1 is not present at location p0_1, package1 is not present at location p1_3, package1 is not present at location p2_1, package1 is not present at location p3_0, package1 is not present at location s1, package1 is not present at location s3, package2 is not at location p1_0, package2 is not at location s1, package2 is not currently at location p0_1, package2 is not currently at location p1_2, package2 is not currently at location p2_0, package2 is not currently at location p3_0, package2 is not placed in truck1, package2 is not placed in truck2, package2 is not present at location p1_3, package2 is not present at location p2_1, package2 is not present at location s0, package2 is not present at location s3, package3 is not at location p1_0, package3 is not at location s0, package3 is not currently at location p0_1, package3 is not currently at location p2_1, package3 is not currently at location p3_0, package3 is not currently at location s1, package3 is not currently at location s2, package3 is not located in truck2, package3 is not present at location p1_2, package3 is not present at location p1_3, package3 is not present at location p2_0, package3 is not present at location s3, there doesn't exist a link between the locations p0_1 and p2_0, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p0_1 and s1, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p1_0 and p2_0, there doesn't exist a link between the locations p1_0 and s0, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and s1, there doesn't exist a link between the locations p1_2 and s3, there doesn't exist a link between the locations p1_3 and p1_0, there doesn't exist a link between the locations p1_3 and p2_1, there doesn't exist a link between the locations p1_3 and s2, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and p1_3, there doesn't exist a link between the locations p2_0 and s1, there doesn't exist a link between the locations p2_1 and p2_0, there doesn't exist a link between the locations p2_1 and s0, there doesn't exist a link between the locations p2_1 and s2, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p1_2, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations p3_0 and s0, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations p3_0 and s2, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s1 and s0, there doesn't exist a link between the locations s2 and p0_1, there doesn't exist a link between the locations s2 and p1_3, there doesn't exist a link between the locations s2 and p2_1, there doesn't exist a link between the locations s3 and p0_1, there doesn't exist a link between the locations s3 and p1_2, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p1_3, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_2 and p2_1, there doesn't exist a path between the locations p1_2 and p3_0, there doesn't exist a path between the locations p1_2 and s3, there doesn't exist a path between the locations p1_3 and p1_0, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_0, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p2_1 and p1_0, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p1_0, there doesn't exist a path between the locations s0 and p1_2, there doesn't exist a path between the locations s0 and p1_3, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s1, there is no link between location p0_1 and location p1_0, there is no link between location p0_1 and location p1_2, there is no link between location p0_1 and location p1_3, there is no link between location p0_1 and location s3, there is no link between location p1_0 and location p1_3, there is no link between location p1_0 and location p3_0, there is no link between location p1_0 and location s1, there is no link between location p1_0 and location s2, there is no link between location p1_2 and location p2_0, there is no link between location p1_2 and location p2_1, there is no link between location p1_2 and location s0, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location p1_2, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s0, there is no link between location p2_0 and location p0_1, there is no link between location p2_0 and location p1_0, there is no link between location p2_1 and location p0_1, there is no link between location p2_1 and location p1_0, there is no link between location p2_1 and location p1_2, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location p3_0, there is no link between location p2_1 and location s3, there is no link between location p3_0 and location p1_3, there is no link between location s0 and location p0_1, there is no link between location s0 and location p1_2, there is no link between location s0 and location p1_3, there is no link between location s0 and location p2_0, there is no link between location s0 and location p2_1, there is no link between location s0 and location p3_0, there is no link between location s0 and location s1, there is no link between location s1 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s1 and location p2_1, there is no link between location s2 and location p1_0, there is no link between location s2 and location p2_0, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p2_0, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location s0, there is no path between location p1_0 and location s2, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p1_0, there is no path between location p1_2 and location p2_0, there is no path between location p1_3 and location p2_1, there is no path between location p2_0 and location p1_2, there is no path between location p2_0 and location p2_1, there is no path between location p2_1 and location p0_1, there is no path between location p2_1 and location p1_2, there is no path between location p2_1 and location p2_0, there is no path between location p2_1 and location p3_0, there is no path between location p2_1 and location s0, there is no path between location p2_1 and location s1, there is no path between location p2_1 and location s2, there is no path between location p2_1 and location s3, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location p2_0, there is no path between location p3_0 and location p2_1, there is no path between location s0 and location p2_1, there is no path between location s1 and location p1_0, there is no path between location s1 and location s2, there is no path between location s2 and location p1_3, there is no path between location s2 and location s0, there is no path between location s2 and location s3, there is no path between location s3 and location p1_0, there is no path between location s3 and location p1_2, there is no path between location s3 and location s2, truck1 contains some package, truck1 is not being driven by driver2, truck1 is not currently at location p2_1, truck1 is not present at location p0_1, truck1 is not present at location p1_0, truck1 is not present at location p1_2, truck1 is not present at location p1_3, truck1 is not present at location p2_0, truck1 is not present at location p3_0, truck1 is not present at location s1, truck1 is not present at location s2, truck1 is not present at location s3, truck2 is not at location p0_1, truck2 is not at location p1_3, truck2 is not being driven by driver2, truck2 is not currently at location p2_1, truck2 is not currently at location s1, truck2 is not present at location p1_0, truck2 is not present at location p1_2, truck2 is not present at location p2_0, truck2 is not present at location p3_0, truck2 is not present at location s0 and truck2 is not present at location s3", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: driver3 boards truck1 at location s0 to achieve the current state. In this state, if package3 is loaded in truck1 at location s0, what are all the valid state properties that involve negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is also at s0. A link exists between locations s3 and s2. Additionally, paths exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, s3 and p1_3, s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Furthermore, paths are present between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and present at location s2."}
{"question_id": "ab8a88d4-780f-47aa-9533-3fab4d9cc132", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, truck1 is loaded with package3 at location s0, truck1 is loaded with package1 at location s0, truck1 is driven from location s0 to s3 by driver3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, at location s1, package3 is unloaded in truck1, package2 is loaded in truck2 at location s2, driver1 walks to location p3_0 from location s3, driver1 walks from location p3_0 to s0, driver2 walks from location s3 to p1_3, driver2 walks to location s1 from location p1_3, driver2 walks to location p1_2 from location s1, driver2 walks from location p1_2 to s2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to s2, driver3 boards truck2 at location s2 and driver3 drives truck2 from location s2 to location s3 to reach the current state. In this state, if driver3 disembarks from truck2 at location s3, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is present at location s0, driver2 is currently at location s2, driver3 is currently at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_3 and s1 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s1 have a link between them, locations s3 and p1_3 have a path between them, package1 is at location s3, package2 is located in truck2, package3 is currently at location s1, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations p3_0 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a path between location p0_1 and location s0, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location s2 and location p1_2, truck1 contains nothing, truck1 is at location s1, truck2 is at location s3 and truck2 is empty", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, driver3 gets into truck1, truck1 is loaded with package3 at location s0, and then loaded with package1 at the same location. Subsequently, truck1 is driven by driver3 from location s0 to s3, where package1 is unloaded. Then, truck1 is driven from location s3 to s1 by driver3, and driver3 gets out of truck1 at location s1. At location s1, package3 is unloaded from truck1. Meanwhile, package2 is loaded into truck2 at location s2. Driver1 walks from location s3 to p3_0 and then to s0, while driver2 walks from location s3 to p1_3, then to s1, followed by p1_2, and finally to s2. Driver3 walks from location s1 to p1_2 and then to s2, where driver3 gets into truck2 and drives it from location s2 to s3, resulting in the current state. In this state, if driver3 gets out of truck2 at location s3, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is at s0. A link exists between locations s3 and s2. Paths also exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, and s3 and p1_3. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Paths also exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and located at s2."}
{"question_id": "52b4c1d5-9b79-4387-94f0-f4564c987f5d", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, if at location s0, package3 is loaded in truck1, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is currently at location s3, driver2 is currently at location s3, driver3 is driving truck1 currently, locations p1_3 and s1 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p0_1 have a path between them, locations s1 and p0_1 have a path between them, locations s1 and s2 have a link between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s1 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is located in truck1, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there is a link between location s2 and location s3, there is a link between location s3 and location s2, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location s0, driver3 gets into truck1 to reach the current state. In this state, if at location s0, package3 is loaded into truck1, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is at s0. A link exists between locations s3 and s2. Paths also exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, and s3 and p1_3. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Paths also exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and located at s2."}
{"question_id": "8c7c2259-2a22-4ea9-a73e-8d6e072470b3", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1 to reach the current state. In this state, if driver1 walks from location s2 to location p0_2, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is present at location p0_2, driver2 is at location s2, locations p0_3 and s0 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_3 have a path between them, locations s1 and s2 have a link between them, locations s1 and s3 have a link between them, locations s2 and p0_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is placed in truck1, package4 is present at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_1 and s1, there exists a path between the locations p2_1 and s2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s0 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_2 and location s0, there is a path between location p0_2 and location s2, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location s0, package3 is loaded into truck1 to achieve the current state. In this state, if driver1 moves from location s2 to location p0_2, what are all the valid properties of the state that do not include negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link is present between s0 and s2, s0 and s3, and s3 and s0. Paths exist between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links are present between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Paths also exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at s0."}
{"question_id": "790fa899-c6e6-40f0-a094-a224fc358a4d", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, truck1 is loaded with package3 at location s0, at location s0, package1 is loaded in truck1, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, driver3 drives truck1 to location s1 from location s3, at location s1, driver3 disembarks from truck1, truck1 is unloaded with package3 at location s1, package2 is loaded in truck2 at location s2, driver1 walks to location p3_0 from location s3, driver1 walks to location s0 from location p3_0, driver2 walks from location s3 to location p1_3, driver2 walks from location p1_3 to s1, driver2 walks from location s1 to location p1_2, driver2 walks from location p1_2 to location s2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to s2, driver3 boards truck2 at location s2 and truck2 is driven from location s2 to s3 by driver3 to reach the current state. In this state, if at location s3, driver3 disembarks from truck2, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "driver1 is at location s0, driver1 is not at location p2_0, driver1 is not at location p2_1, driver1 is not at location s1, driver1 is not currently at location p0_1, driver1 is not currently at location p1_2, driver1 is not currently at location p3_0, driver1 is not currently at location s2, driver1 is not driving truck1 currently, driver1 is not driving truck2, driver1 is not present at location p1_0, driver1 is not present at location p1_3, driver1 is not present at location s3, driver2 is not at location p1_2, driver2 is not at location p1_3, driver2 is not at location p2_0, driver2 is not at location p2_1, driver2 is not at location s1, driver2 is not currently at location p1_0, driver2 is not currently at location s0, driver2 is not driving truck2, driver2 is not present at location p0_1, driver2 is not present at location p3_0, driver2 is not present at location s3, driver2 is present at location s2, driver3 is currently at location s3, driver3 is not at location p0_1, driver3 is not at location p1_2, driver3 is not at location p1_3, driver3 is not at location s0, driver3 is not at location s2, driver3 is not currently at location p2_1, driver3 is not currently at location p3_0, driver3 is not currently at location s1, driver3 is not driving truck1, driver3 is not present at location p1_0, driver3 is not present at location p2_0, locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and p1_3 does not have a path between them, locations p0_1 and p2_0 does not have a path between them, locations p0_1 and p2_1 does not have a link between them, locations p0_1 and p2_1 does not have a path between them, locations p0_1 and p3_0 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s0 does not have a link between them, locations p0_1 and s2 does not have a link between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p2_0 does not have a link between them, locations p1_0 and s0 does not have a link between them, locations p1_0 and s1 does not have a link between them, locations p1_0 and s3 does not have a link between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p3_0 does not have a path between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 does not have a link between them, locations p1_2 and s3 does not have a link between them, locations p1_2 and s3 does not have a path between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p2_0 does not have a link between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s1 have a path between them, locations p1_3 and s2 does not have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and p1_3 does not have a path between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and s0 does not have a link between them, locations p2_0 and s0 have a path between them, locations p2_0 and s1 does not have a path between them, locations p2_1 and p0_1 does not have a path between them, locations p2_1 and p1_2 does not have a path between them, locations p2_1 and p2_0 does not have a path between them, locations p2_1 and s0 does not have a link between them, locations p2_1 and s0 does not have a path between them, locations p2_1 and s1 does not have a path between them, locations p2_1 and s2 does not have a link between them, locations p2_1 and s3 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and s0 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations s0 and p1_3 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s0 and p3_0 have a path between them, locations s0 and s2 does not have a path between them, locations s1 and p0_1 does not have a link between them, locations s1 and p1_0 does not have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_0 does not have a path between them, locations s1 and s0 does not have a link between them, locations s1 and s2 does not have a path between them, locations s1 and s3 does not have a path between them, locations s2 and p0_1 does not have a path between them, locations s2 and p1_2 does not have a link between them, locations s2 and p1_2 have a path between them, locations s2 and p2_1 does not have a path between them, locations s2 and p3_0 does not have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p1_0 does not have a link between them, locations s3 and p1_0 does not have a path between them, locations s3 and p1_2 does not have a path between them, locations s3 and p2_0 does not have a link between them, locations s3 and p2_1 does not have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 does not have a path between them, locations s3 and s2 does not have a path between them, locations s3 and s2 have a link between them, package1 is at location s3, package1 is not at location p3_0, package1 is not currently at location p0_1, package1 is not currently at location p1_0, package1 is not currently at location p2_0, package1 is not currently at location s2, package1 is not in truck2, package1 is not located in truck1, package1 is not present at location p1_2, package1 is not present at location p1_3, package1 is not present at location p2_1, package1 is not present at location s0, package1 is not present at location s1, package2 is in truck2, package2 is not at location p3_0, package2 is not currently at location p1_2, package2 is not currently at location p2_0, package2 is not currently at location p2_1, package2 is not currently at location s2, package2 is not located in truck1, package2 is not present at location p0_1, package2 is not present at location p1_0, package2 is not present at location p1_3, package2 is not present at location s0, package2 is not present at location s1, package2 is not present at location s3, package3 is at location s1, package3 is not at location p2_0, package3 is not at location p2_1, package3 is not currently at location p0_1, package3 is not currently at location p1_0, package3 is not currently at location p1_2, package3 is not currently at location p1_3, package3 is not currently at location s0, package3 is not in truck2, package3 is not placed in truck1, package3 is not present at location p3_0, package3 is not present at location s2, package3 is not present at location s3, there doesn't exist a link between the locations p0_1 and p1_0, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and s1, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p1_0 and p1_3, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and p1_0, there doesn't exist a link between the locations p1_2 and p1_3, there doesn't exist a link between the locations p1_2 and p2_1, there doesn't exist a link between the locations p1_2 and s1, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and p2_1, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and p1_2, there doesn't exist a link between the locations p2_0 and p1_3, there doesn't exist a link between the locations p2_0 and s1, there doesn't exist a link between the locations p2_0 and s2, there doesn't exist a link between the locations p2_1 and p1_3, there doesn't exist a link between the locations p2_1 and p2_0, there doesn't exist a link between the locations p2_1 and p3_0, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p2_0, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s0 and s1, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s2 and p0_1, there doesn't exist a link between the locations s2 and p2_0, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p1_3, there doesn't exist a path between the locations p1_0 and p2_0, there doesn't exist a path between the locations p1_0 and p2_1, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_0 and s2, there doesn't exist a path between the locations p1_2 and p1_0, there doesn't exist a path between the locations p1_2 and p2_1, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and p2_1, there doesn't exist a path between the locations p1_3 and p3_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p1_0, there doesn't exist a path between the locations p2_0 and p1_2, there doesn't exist a path between the locations p2_0 and p2_1, there doesn't exist a path between the locations p2_0 and p3_0, there doesn't exist a path between the locations p2_0 and s3, there doesn't exist a path between the locations p2_1 and p1_3, there doesn't exist a path between the locations p3_0 and p1_0, there doesn't exist a path between the locations p3_0 and p1_2, there doesn't exist a path between the locations p3_0 and p2_0, there doesn't exist a path between the locations p3_0 and p2_1, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p1_2, there doesn't exist a path between the locations s0 and p2_1, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p2_1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s2 and location p2_0, there is no link between location p0_1 and location p1_2, there is no link between location p0_1 and location p2_0, there is no link between location p1_0 and location p2_1, there is no link between location p1_2 and location p2_0, there is no link between location p1_2 and location p3_0, there is no link between location p1_2 and location s0, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s2, there is no link between location p2_0 and location p0_1, there is no link between location p2_0 and location p1_0, there is no link between location p2_0 and location p2_1, there is no link between location p2_0 and location s3, there is no link between location p2_1 and location p0_1, there is no link between location p2_1 and location p1_0, there is no link between location p2_1 and location p1_2, there is no link between location p3_0 and location p1_0, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p1_0, there is no link between location s0 and location p1_2, there is no link between location s0 and location p3_0, there is no link between location s1 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s1 and location p2_0, there is no link between location s1 and location p2_1, there is no link between location s1 and location p3_0, there is no link between location s2 and location p1_0, there is no link between location s2 and location p1_3, there is no link between location s2 and location p2_1, there is no link between location s3 and location p1_2, there is no link between location s3 and location p1_3, there is no link between location s3 and location p3_0, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location s1, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p0_1, there is no path between location p1_2 and location p2_0, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location p1_2, there is no path between location p2_0 and location p0_1, there is no path between location p2_1 and location p1_0, there is no path between location p2_1 and location p3_0, there is no path between location p2_1 and location s2, there is no path between location p2_1 and location s3, there is no path between location s0 and location p1_0, there is no path between location s1 and location p2_1, there is no path between location s1 and location p3_0, there is no path between location s2 and location s0, there is no path between location s2 and location s1, there is no path between location s3 and location p2_0, there is no path between location s3 and location s1, truck1 is at location s1, truck1 is empty, truck1 is not at location p2_0, truck1 is not at location s0, truck1 is not being driven by driver2, truck1 is not currently at location p0_1, truck1 is not currently at location p1_0, truck1 is not currently at location p1_2, truck1 is not present at location p1_3, truck1 is not present at location p2_1, truck1 is not present at location p3_0, truck1 is not present at location s2, truck1 is not present at location s3, truck2 contains nothing, truck2 is currently at location s3, truck2 is not at location p1_0, truck2 is not at location p2_0, truck2 is not at location p2_1, truck2 is not at location s1, truck2 is not at location s2, truck2 is not being driven by driver3, truck2 is not currently at location p1_2, truck2 is not currently at location p1_3, truck2 is not currently at location p3_0, truck2 is not present at location p0_1 and truck2 is not present at location s0", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, package3 is loaded onto truck1 at location s0, package1 is also loaded onto truck1 at location s0, driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, driver3 then drives truck1 from location s3 to location s1, at location s1, driver3 gets off truck1, package3 is unloaded from truck1 at location s1, package2 is loaded onto truck2 at location s2, driver1 walks from location s3 to location p3_0 and then to location s0, driver2 walks from location s3 to location p1_3, then to location s1, then to location p1_2, and finally to location s2, driver3 walks from location s1 to location p1_2 and then to location s2, where driver3 boards truck2 and drives it from location s2 to location s3, resulting in the current state. In this state, if driver3 gets off truck2 at location s3, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state None.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is also at s0. A link exists between locations s3 and s2. Paths also exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, and s3 and p1_3. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Paths also exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and located at s2."}
{"question_id": "34ca69d2-5470-4d55-a679-b6cd1d562a21", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3 to reach the current state. In this state, if driver1 walks from location p4_3 to location s4, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "driver1 is currently at location s4, driver1 is not at location p0_5, driver1 is not at location p4_0, driver1 is not at location p4_3, driver1 is not at location p5_2, driver1 is not at location s1, driver1 is not at location s2, driver1 is not currently at location s0, driver1 is not currently at location s5, driver1 is not driving truck1 currently, driver1 is not driving truck2, driver1 is not present at location p4_1, driver1 is not present at location s3, driver2 is currently at location s4, driver2 is not at location s0, driver2 is not currently at location p0_5, driver2 is not currently at location p5_2, driver2 is not currently at location s1, driver2 is not currently at location s2, driver2 is not driving truck2, driver2 is not present at location p4_0, driver2 is not present at location p4_1, driver2 is not present at location p4_3, driver2 is not present at location s3, driver2 is not present at location s5, driver3 is not at location p4_0, driver3 is not at location p5_2, driver3 is not at location s0, driver3 is not at location s1, driver3 is not at location s2, driver3 is not currently at location p4_1, driver3 is not currently at location p4_3, driver3 is not currently at location s4, driver3 is not driving truck1, driver3 is not present at location p0_5, driver3 is not present at location s5, driver3 is present at location s3, locations p0_5 and p4_0 does not have a path between them, locations p0_5 and p4_1 does not have a path between them, locations p0_5 and p4_3 does not have a path between them, locations p0_5 and p5_2 does not have a link between them, locations p0_5 and p5_2 does not have a path between them, locations p0_5 and s0 have a path between them, locations p0_5 and s1 does not have a path between them, locations p0_5 and s4 does not have a path between them, locations p0_5 and s5 does not have a link between them, locations p4_0 and p0_5 does not have a path between them, locations p4_0 and p4_1 does not have a path between them, locations p4_0 and p4_3 does not have a path between them, locations p4_0 and p5_2 does not have a link between them, locations p4_0 and p5_2 does not have a path between them, locations p4_0 and s2 does not have a link between them, locations p4_0 and s2 does not have a path between them, locations p4_0 and s3 does not have a link between them, locations p4_0 and s4 does not have a link between them, locations p4_0 and s4 have a path between them, locations p4_1 and p0_5 does not have a path between them, locations p4_1 and p4_0 does not have a path between them, locations p4_1 and s1 does not have a link between them, locations p4_3 and p0_5 does not have a path between them, locations p4_3 and p4_1 does not have a path between them, locations p4_3 and p5_2 does not have a path between them, locations p4_3 and s1 does not have a link between them, locations p4_3 and s5 does not have a link between them, locations p5_2 and p0_5 does not have a path between them, locations p5_2 and p4_1 does not have a link between them, locations p5_2 and p4_3 does not have a link between them, locations p5_2 and p4_3 does not have a path between them, locations p5_2 and s1 does not have a path between them, locations p5_2 and s2 does not have a link between them, locations p5_2 and s4 does not have a link between them, locations p5_2 and s4 does not have a path between them, locations s0 and p0_5 does not have a link between them, locations s0 and p0_5 have a path between them, locations s0 and p4_3 does not have a link between them, locations s0 and s2 have a link between them, locations s0 and s3 does not have a link between them, locations s0 and s3 does not have a path between them, locations s0 and s5 have a link between them, locations s1 and p0_5 does not have a path between them, locations s1 and p4_3 does not have a link between them, locations s1 and p5_2 does not have a path between them, locations s1 and s2 does not have a path between them, locations s1 and s3 does not have a path between them, locations s2 and p4_0 does not have a link between them, locations s2 and p4_0 does not have a path between them, locations s2 and p4_3 does not have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 does not have a path between them, locations s2 and s5 have a link between them, locations s3 and p4_0 does not have a link between them, locations s3 and p4_1 does not have a link between them, locations s3 and s0 does not have a link between them, locations s3 and s1 does not have a link between them, locations s3 and s4 have a link between them, locations s3 and s5 does not have a path between them, locations s4 and p0_5 does not have a link between them, locations s4 and p0_5 does not have a path between them, locations s4 and p4_3 have a path between them, locations s4 and s0 does not have a path between them, locations s4 and s1 does not have a path between them, locations s4 and s2 does not have a link between them, locations s4 and s5 does not have a path between them, locations s5 and p0_5 does not have a link between them, locations s5 and p5_2 have a path between them, locations s5 and s0 does not have a path between them, locations s5 and s1 does not have a link between them, locations s5 and s2 have a link between them, locations s5 and s3 does not have a path between them, locations s5 and s4 does not have a path between them, package1 is at location s2, package1 is not at location p0_5, package1 is not at location s0, package1 is not at location s1, package1 is not at location s5, package1 is not currently at location p4_1, package1 is not currently at location p4_3, package1 is not currently at location p5_2, package1 is not currently at location s3, package1 is not in truck1, package1 is not located in truck2, package1 is not present at location p4_0, package1 is not present at location s4, package2 is at location s2, package2 is not at location p0_5, package2 is not at location p4_1, package2 is not at location p5_2, package2 is not currently at location p4_3, package2 is not currently at location s4, package2 is not in truck1, package2 is not placed in truck2, package2 is not present at location p4_0, package2 is not present at location s0, package2 is not present at location s1, package2 is not present at location s3, package2 is not present at location s5, package3 is currently at location s3, package3 is not at location p4_1, package3 is not at location p4_3, package3 is not at location s2, package3 is not at location s5, package3 is not currently at location p0_5, package3 is not currently at location s1, package3 is not currently at location s4, package3 is not placed in truck1, package3 is not placed in truck2, package3 is not present at location p4_0, package3 is not present at location p5_2, package3 is not present at location s0, package4 is not at location p4_0, package4 is not at location s4, package4 is not currently at location p0_5, package4 is not currently at location s2, package4 is not currently at location s3, package4 is not in truck2, package4 is not located in truck1, package4 is not present at location p4_1, package4 is not present at location p4_3, package4 is not present at location p5_2, package4 is not present at location s1, package4 is not present at location s5, package4 is present at location s0, there doesn't exist a link between the locations p0_5 and s1, there doesn't exist a link between the locations p0_5 and s2, there doesn't exist a link between the locations p0_5 and s4, there doesn't exist a link between the locations p4_0 and p0_5, there doesn't exist a link between the locations p4_0 and p4_1, there doesn't exist a link between the locations p4_0 and s0, there doesn't exist a link between the locations p4_0 and s1, there doesn't exist a link between the locations p4_0 and s5, there doesn't exist a link between the locations p4_1 and p4_0, there doesn't exist a link between the locations p4_1 and p5_2, there doesn't exist a link between the locations p4_1 and s0, there doesn't exist a link between the locations p4_1 and s2, there doesn't exist a link between the locations p4_3 and p0_5, there doesn't exist a link between the locations p4_3 and p4_0, there doesn't exist a link between the locations p4_3 and s0, there doesn't exist a link between the locations p4_3 and s2, there doesn't exist a link between the locations p4_3 and s3, there doesn't exist a link between the locations p4_3 and s4, there doesn't exist a link between the locations p5_2 and s0, there doesn't exist a link between the locations p5_2 and s1, there doesn't exist a link between the locations p5_2 and s5, there doesn't exist a link between the locations s0 and p4_1, there doesn't exist a link between the locations s0 and p5_2, there doesn't exist a link between the locations s1 and p0_5, there doesn't exist a link between the locations s1 and p4_0, there doesn't exist a link between the locations s1 and p4_1, there doesn't exist a link between the locations s1 and p5_2, there doesn't exist a link between the locations s1 and s3, there doesn't exist a link between the locations s1 and s5, there doesn't exist a link between the locations s2 and p0_5, there doesn't exist a link between the locations s2 and p5_2, there doesn't exist a link between the locations s2 and s4, there doesn't exist a link between the locations s3 and p4_3, there doesn't exist a link between the locations s4 and p4_0, there doesn't exist a link between the locations s5 and p4_0, there doesn't exist a link between the locations s5 and p4_3, there doesn't exist a link between the locations s5 and p5_2, there doesn't exist a path between the locations p0_5 and s2, there doesn't exist a path between the locations p4_0 and s1, there doesn't exist a path between the locations p4_0 and s5, there doesn't exist a path between the locations p4_3 and p4_0, there doesn't exist a path between the locations p4_3 and s2, there doesn't exist a path between the locations p4_3 and s5, there doesn't exist a path between the locations p5_2 and s0, there doesn't exist a path between the locations p5_2 and s3, there doesn't exist a path between the locations s0 and p4_1, there doesn't exist a path between the locations s0 and p4_3, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s1 and p4_0, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s1 and s4, there doesn't exist a path between the locations s2 and p0_5, there doesn't exist a path between the locations s2 and p4_1, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s2 and s4, there doesn't exist a path between the locations s2 and s5, there doesn't exist a path between the locations s3 and p4_1, there doesn't exist a path between the locations s3 and p5_2, there doesn't exist a path between the locations s3 and s1, there doesn't exist a path between the locations s5 and p4_0, there doesn't exist a path between the locations s5 and p4_1, there doesn't exist a path between the locations s5 and p4_3, there doesn't exist a path between the locations s5 and s1, there doesn't exist a path between the locations s5 and s2, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s4, there exists a link between the locations s3 and s2, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s3, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations p5_2 and s2, there exists a path between the locations p5_2 and s5, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there is a link between location s0 and location s1, there is a link between location s0 and location s4, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s5, there is a link between location s4 and location s0, there is a link between location s5 and location s0, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location s0 and location p4_0, there is a path between location s5 and location p0_5, there is no link between location p0_5 and location p4_0, there is no link between location p0_5 and location p4_1, there is no link between location p0_5 and location p4_3, there is no link between location p0_5 and location s0, there is no link between location p0_5 and location s3, there is no link between location p4_0 and location p4_3, there is no link between location p4_1 and location p0_5, there is no link between location p4_1 and location p4_3, there is no link between location p4_1 and location s3, there is no link between location p4_1 and location s4, there is no link between location p4_1 and location s5, there is no link between location p4_3 and location p4_1, there is no link between location p4_3 and location p5_2, there is no link between location p5_2 and location p0_5, there is no link between location p5_2 and location p4_0, there is no link between location p5_2 and location s3, there is no link between location s0 and location p4_0, there is no link between location s2 and location p4_1, there is no link between location s3 and location p0_5, there is no link between location s3 and location p5_2, there is no link between location s4 and location p4_1, there is no link between location s4 and location p4_3, there is no link between location s4 and location p5_2, there is no link between location s5 and location p4_1, there is no path between location p0_5 and location s3, there is no path between location p4_0 and location s3, there is no path between location p4_1 and location p4_3, there is no path between location p4_1 and location p5_2, there is no path between location p4_1 and location s0, there is no path between location p4_1 and location s2, there is no path between location p4_1 and location s3, there is no path between location p4_1 and location s5, there is no path between location p4_3 and location s0, there is no path between location p4_3 and location s1, there is no path between location p5_2 and location p4_0, there is no path between location p5_2 and location p4_1, there is no path between location s0 and location p5_2, there is no path between location s0 and location s1, there is no path between location s0 and location s4, there is no path between location s0 and location s5, there is no path between location s1 and location p4_3, there is no path between location s1 and location s5, there is no path between location s2 and location p4_3, there is no path between location s3 and location p0_5, there is no path between location s3 and location p4_0, there is no path between location s3 and location s0, there is no path between location s3 and location s2, there is no path between location s3 and location s4, there is no path between location s4 and location p5_2, there is no path between location s4 and location s2, there is no path between location s4 and location s3, truck1 is currently at location s1, truck1 is empty, truck1 is not at location p5_2, truck1 is not at location s4, truck1 is not being driven by driver2, truck1 is not currently at location s2, truck1 is not currently at location s3, truck1 is not currently at location s5, truck1 is not present at location p0_5, truck1 is not present at location p4_0, truck1 is not present at location p4_1, truck1 is not present at location p4_3, truck1 is not present at location s0, truck2 is at location s5, truck2 is empty, truck2 is not at location p0_5, truck2 is not at location s0, truck2 is not at location s2, truck2 is not being driven by driver3, truck2 is not currently at location p4_0, truck2 is not currently at location p5_2, truck2 is not currently at location s1, truck2 is not currently at location s4, truck2 is not present at location p4_1, truck2 is not present at location p4_3 and truck2 is not present at location s3", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver1 moves from location s3 to p4_3 to attain the current state. In this state, if driver1 moves from location p4_3 to location s4, what are all the valid properties of the resulting state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between locations p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are currently at location s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "50c9f81c-9100-4ccc-bd16-b4d2e96157fe", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0 to reach the current state. In this state, if driver2 walks from location p3_0 to s0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is at location s1, driver2 is currently at location s0, driver3 is at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s1 and p0_1 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s3, package2 is currently at location s2, package3 is at location s2, package4 is present at location s1, there exists a link between the locations s1 and s0, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a path between location p2_0 and location s0, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is present at location s0, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is at location s0", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: driver2 moves from location s3 to p3_0 to attain the current state. In this state, if driver2 moves from location p3_0 to s0, what are all the valid properties of the state that do not include negations? Write None if there are no such properties.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s1, s0 and s3, and s3 and s1. Paths are present between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, s0 and p0_1, p1_2 and s2, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Additionally, links exist between s0 and s2, s1 and s2, and s2 and s1. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "710bd217-f982-4520-b181-d912ef2dd505", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, truck1 is loaded with package1 at location s0, driver3 drives truck1 from location s0 to location s3, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, package2 is loaded in truck2 at location s2 and driver1 walks to location p3_0 from location s3 to reach the current state. In this state, if driver1 walks from location p3_0 to s0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is at location s0, driver2 is present at location s3, driver3 is currently at location s1, locations p1_2 and s1 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p2_0 have a path between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is in truck2, package3 is present at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p3_0 and s3, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is currently at location s1, truck1 is empty, truck2 is at location s2 and truck2 is empty", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at location s0, driver3 gets into truck1, package3 is placed in truck1 at location s0, and package1 is also loaded into truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1. Next, driver3 drives truck1 from location s3 to location s1, gets out of truck1 at location s1, and package3 is removed from truck1 at location s1. Meanwhile, package2 is loaded into truck2 at location s2, and driver1 moves from location s3 to location p3_0 to reach the current state. In this state, if driver1 walks from location p3_0 to s0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The current location of Driver1 is s3, while Driver2 is also at location s3, and Driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, Package2 is at s2, and Package3 is at s0. A link exists between locations s3 and s2. Additionally, paths exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, and s3 and p1_3. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Paths also exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while Truck2 is also empty and located at s2."}
{"question_id": "e201458f-29f8-4528-8f9b-f6f99ee4638e", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks to location s0 from location p0_3 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, if driver2 walks from location p0_2 to location s0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "driver1 is not at location p0_3, driver1 is not at location p2_1, driver1 is not at location s1, driver1 is not currently at location p0_1, driver1 is not currently at location p0_2, driver1 is not currently at location p1_3, driver1 is not driving truck1 currently, driver1 is not driving truck2 currently, driver1 is not present at location p3_0, driver1 is not present at location s2, driver1 is not present at location s3, driver2 is not at location p0_2, driver2 is not at location p0_3, driver2 is not at location p1_3, driver2 is not at location p2_1, driver2 is not at location s1, driver2 is not at location s2, driver2 is not currently at location p0_1, driver2 is not currently at location s3, driver2 is not driving truck1 currently, driver2 is not driving truck2 currently, driver2 is not present at location p3_0, locations p0_1 and p0_2 does not have a link between them, locations p0_1 and p0_3 does not have a link between them, locations p0_1 and p1_3 does not have a path between them, locations p0_1 and p2_1 does not have a path between them, locations p0_1 and s0 does not have a link between them, locations p0_1 and s1 does not have a link between them, locations p0_2 and p2_1 does not have a path between them, locations p0_2 and p3_0 does not have a link between them, locations p0_2 and s2 does not have a link between them, locations p0_2 and s3 does not have a link between them, locations p0_2 and s3 does not have a path between them, locations p0_3 and p0_1 does not have a path between them, locations p0_3 and p1_3 does not have a link between them, locations p0_3 and p1_3 does not have a path between them, locations p0_3 and s1 does not have a link between them, locations p0_3 and s1 does not have a path between them, locations p0_3 and s2 does not have a link between them, locations p1_3 and p0_2 does not have a link between them, locations p1_3 and p0_3 does not have a link between them, locations p1_3 and s0 does not have a link between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s2 does not have a path between them, locations p1_3 and s3 does not have a link between them, locations p2_1 and p0_2 does not have a path between them, locations p2_1 and p1_3 does not have a link between them, locations p2_1 and p1_3 does not have a path between them, locations p2_1 and s0 does not have a link between them, locations p2_1 and s0 does not have a path between them, locations p2_1 and s2 does not have a link between them, locations p3_0 and p0_2 does not have a path between them, locations p3_0 and p0_3 does not have a link between them, locations p3_0 and p2_1 does not have a path between them, locations p3_0 and s1 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_1 does not have a path between them, locations s0 and p3_0 does not have a link between them, locations s0 and p3_0 does not have a path between them, locations s0 and s1 does not have a path between them, locations s1 and p0_1 does not have a link between them, locations s1 and p0_3 does not have a link between them, locations s1 and p0_3 does not have a path between them, locations s1 and p1_3 does not have a link between them, locations s1 and p2_1 does not have a link between them, locations s1 and p3_0 does not have a link between them, locations s1 and s3 does not have a path between them, locations s2 and p3_0 does not have a link between them, locations s2 and p3_0 does not have a path between them, locations s2 and s1 does not have a path between them, locations s3 and p0_3 does not have a link between them, locations s3 and p2_1 does not have a link between them, locations s3 and s1 does not have a path between them, package1 is not at location p0_1, package1 is not currently at location p0_2, package1 is not currently at location p2_1, package1 is not currently at location s0, package1 is not currently at location s2, package1 is not currently at location s3, package1 is not in truck2, package1 is not located in truck1, package1 is not present at location p0_3, package1 is not present at location p1_3, package1 is not present at location p3_0, package2 is not at location p3_0, package2 is not at location s1, package2 is not currently at location p0_1, package2 is not currently at location p0_3, package2 is not currently at location p1_3, package2 is not currently at location s3, package2 is not located in truck2, package2 is not placed in truck1, package2 is not present at location p0_2, package2 is not present at location p2_1, package2 is not present at location s0, package3 is not at location p0_2, package3 is not at location s0, package3 is not at location s1, package3 is not currently at location p2_1, package3 is not in truck1, package3 is not in truck2, package3 is not present at location p0_1, package3 is not present at location p0_3, package3 is not present at location p1_3, package3 is not present at location p3_0, package3 is not present at location s2, package4 is not at location s1, package4 is not currently at location p0_1, package4 is not currently at location p0_3, package4 is not currently at location p2_1, package4 is not currently at location p3_0, package4 is not in truck1, package4 is not placed in truck2, package4 is not present at location p0_2, package4 is not present at location p1_3, package4 is not present at location s0, package4 is not present at location s3, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p2_1, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p0_2 and p0_1, there doesn't exist a link between the locations p0_2 and p0_3, there doesn't exist a link between the locations p0_2 and p2_1, there doesn't exist a link between the locations p0_3 and p0_2, there doesn't exist a link between the locations p0_3 and p3_0, there doesn't exist a link between the locations p0_3 and s3, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and p3_0, there doesn't exist a link between the locations p1_3 and s1, there doesn't exist a link between the locations p2_1 and p0_1, there doesn't exist a link between the locations p2_1 and s3, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p0_2, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations s0 and p0_3, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s1 and p0_2, there doesn't exist a link between the locations s2 and p0_1, there doesn't exist a link between the locations s2 and p0_3, there doesn't exist a link between the locations s2 and p1_3, there doesn't exist a link between the locations s2 and p2_1, there doesn't exist a link between the locations s3 and p0_1, there doesn't exist a link between the locations s3 and p0_2, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and p0_2, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_2 and p0_3, there doesn't exist a path between the locations p0_2 and p1_3, there doesn't exist a path between the locations p0_3 and p2_1, there doesn't exist a path between the locations p0_3 and p3_0, there doesn't exist a path between the locations p0_3 and s2, there doesn't exist a path between the locations p1_3 and p0_3, there doesn't exist a path between the locations p2_1 and p0_3, there doesn't exist a path between the locations p2_1 and s3, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations p3_0 and s0, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p0_2, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s2 and s0, there is no link between location p0_1 and location p3_0, there is no link between location p0_1 and location s3, there is no link between location p0_2 and location p1_3, there is no link between location p0_2 and location s0, there is no link between location p0_2 and location s1, there is no link between location p0_3 and location p0_1, there is no link between location p0_3 and location p2_1, there is no link between location p0_3 and location s0, there is no link between location p1_3 and location p2_1, there is no link between location p2_1 and location p0_2, there is no link between location p2_1 and location p0_3, there is no link between location p2_1 and location p3_0, there is no link between location p2_1 and location s1, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p0_1, there is no link between location s0 and location p0_2, there is no link between location s0 and location p1_3, there is no link between location s2 and location p0_2, there is no link between location s3 and location p1_3, there is no path between location p0_1 and location p0_3, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s3, there is no path between location p0_2 and location p0_1, there is no path between location p0_2 and location p3_0, there is no path between location p0_2 and location s1, there is no path between location p0_3 and location p0_2, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location p0_2, there is no path between location p1_3 and location p2_1, there is no path between location p1_3 and location p3_0, there is no path between location p1_3 and location s0, there is no path between location p2_1 and location p0_1, there is no path between location p2_1 and location p3_0, there is no path between location p3_0 and location p0_3, there is no path between location p3_0 and location s1, there is no path between location p3_0 and location s2, there is no path between location p3_0 and location s3, there is no path between location s0 and location s2, there is no path between location s1 and location s2, there is no path between location s2 and location p0_1, there is no path between location s2 and location p0_3, there is no path between location s2 and location s3, there is no path between location s3 and location p0_1, there is no path between location s3 and location p0_2, there is no path between location s3 and location p2_1, there is no path between location s3 and location p3_0, there is no path between location s3 and location s0, there is no path between location s3 and location s2, truck1 is not at location p3_0, truck1 is not at location s0, truck1 is not at location s1, truck1 is not currently at location p1_3, truck1 is not currently at location p2_1, truck1 is not present at location p0_1, truck1 is not present at location p0_2, truck1 is not present at location p0_3, truck1 is not present at location s2, truck2 is not at location p1_3, truck2 is not at location s1, truck2 is not at location s2, truck2 is not currently at location p0_3, truck2 is not currently at location p3_0, truck2 is not present at location p0_1, truck2 is not present at location p0_2, truck2 is not present at location p2_1 and truck2 is not present at location s3", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3, and then returns to s0 from p0_3, while driver2 moves from s2 to p0_2 to reach the current state. In this state, if driver2 moves from p0_2 to s0, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Links exist between locations s0 and s2, s0 and s3, and s3 and s0. Paths are present between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Paths also exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, while truck2 is also empty and present at location s0."}
{"question_id": "90c16a53-46c2-479b-bf14-407600a0f9d4", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks to location s4 from location p4_3, driver1 walks to location p4_1 from location s4, driver1 walks to location s1 from location p4_1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, at location s0, package4 is loaded in truck1, driver1 drives truck1 to location s2 from location s0, package2 is loaded in truck1 at location s2, at location s2, package1 is loaded in truck1, driver1 drives truck1 to location s3 from location s2, truck1 is loaded with package3 at location s3, truck1 is unloaded with package1 at location s3, driver1 drives truck1 to location s4 from location s3, at location s4, package4 is unloaded in truck1, truck1 is unloaded with package3 at location s4, package2 is unloaded from truck1 at location s4, truck1 is driven from location s4 to s1 by driver1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, if driver2 walks from location s4 to location p4_0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "driver1 is not at location p4_0, driver1 is not at location p4_1, driver1 is not at location s5, driver1 is not currently at location p0_5, driver1 is not currently at location p4_3, driver1 is not currently at location p5_2, driver1 is not currently at location s2, driver1 is not currently at location s3, driver1 is not driving truck1, driver1 is not driving truck2, driver1 is not present at location s0, driver1 is not present at location s4, driver2 is not at location p4_3, driver2 is not at location s3, driver2 is not at location s4, driver2 is not at location s5, driver2 is not currently at location p0_5, driver2 is not currently at location p5_2, driver2 is not currently at location s1, driver2 is not currently at location s2, driver2 is not driving truck1, driver2 is not driving truck2, driver2 is not present at location p4_1, driver2 is not present at location s0, driver3 is not at location p4_0, driver3 is not at location p4_1, driver3 is not at location p4_3, driver3 is not at location p5_2, driver3 is not at location s2, driver3 is not currently at location s4, driver3 is not currently at location s5, driver3 is not driving truck2 currently, driver3 is not present at location p0_5, driver3 is not present at location s0, driver3 is not present at location s1, locations p0_5 and p4_0 does not have a link between them, locations p0_5 and p4_0 does not have a path between them, locations p0_5 and p4_1 does not have a path between them, locations p0_5 and s0 does not have a link between them, locations p0_5 and s2 does not have a link between them, locations p0_5 and s4 does not have a path between them, locations p4_0 and p0_5 does not have a link between them, locations p4_0 and p4_1 does not have a link between them, locations p4_0 and s1 does not have a path between them, locations p4_0 and s4 does not have a link between them, locations p4_0 and s5 does not have a link between them, locations p4_1 and p0_5 does not have a path between them, locations p4_1 and p5_2 does not have a path between them, locations p4_1 and s0 does not have a link between them, locations p4_1 and s2 does not have a link between them, locations p4_3 and p0_5 does not have a link between them, locations p4_3 and p0_5 does not have a path between them, locations p4_3 and s0 does not have a path between them, locations p4_3 and s1 does not have a path between them, locations p4_3 and s2 does not have a link between them, locations p4_3 and s5 does not have a link between them, locations p5_2 and p4_1 does not have a link between them, locations p5_2 and p4_3 does not have a link between them, locations p5_2 and p4_3 does not have a path between them, locations p5_2 and s5 does not have a link between them, locations s0 and p4_3 does not have a link between them, locations s0 and p5_2 does not have a link between them, locations s0 and s5 does not have a path between them, locations s1 and p0_5 does not have a link between them, locations s1 and p0_5 does not have a path between them, locations s1 and p4_0 does not have a link between them, locations s1 and p4_3 does not have a link between them, locations s1 and p4_3 does not have a path between them, locations s1 and p5_2 does not have a path between them, locations s1 and s0 does not have a path between them, locations s1 and s2 does not have a path between them, locations s1 and s5 does not have a path between them, locations s2 and p5_2 does not have a link between them, locations s3 and p4_0 does not have a path between them, locations s3 and p4_1 does not have a link between them, locations s3 and p4_1 does not have a path between them, locations s3 and s1 does not have a path between them, locations s3 and s2 does not have a path between them, locations s4 and p4_0 does not have a link between them, locations s4 and p4_1 does not have a link between them, locations s4 and p5_2 does not have a link between them, locations s4 and p5_2 does not have a path between them, locations s5 and p4_1 does not have a link between them, locations s5 and p4_1 does not have a path between them, locations s5 and s1 does not have a path between them, locations s5 and s4 does not have a path between them, package1 is not at location p4_1, package1 is not at location p4_3, package1 is not at location s1, package1 is not currently at location p0_5, package1 is not currently at location p4_0, package1 is not currently at location p5_2, package1 is not currently at location s2, package1 is not currently at location s5, package1 is not located in truck2, package1 is not placed in truck1, package1 is not present at location s0, package1 is not present at location s4, package2 is not at location p0_5, package2 is not at location p4_0, package2 is not at location p5_2, package2 is not at location s3, package2 is not at location s5, package2 is not currently at location p4_3, package2 is not currently at location s0, package2 is not currently at location s1, package2 is not placed in truck1, package2 is not placed in truck2, package2 is not present at location p4_1, package2 is not present at location s2, package3 is not at location s2, package3 is not at location s3, package3 is not at location s5, package3 is not currently at location p4_3, package3 is not currently at location s1, package3 is not in truck2, package3 is not located in truck1, package3 is not present at location p0_5, package3 is not present at location p4_0, package3 is not present at location p4_1, package3 is not present at location p5_2, package3 is not present at location s0, package4 is not at location p0_5, package4 is not at location p5_2, package4 is not at location s0, package4 is not at location s1, package4 is not at location s3, package4 is not currently at location p4_0, package4 is not currently at location p4_1, package4 is not currently at location s2, package4 is not in truck1, package4 is not placed in truck2, package4 is not present at location p4_3, package4 is not present at location s5, there doesn't exist a link between the locations p0_5 and p4_1, there doesn't exist a link between the locations p0_5 and p4_3, there doesn't exist a link between the locations p0_5 and s4, there doesn't exist a link between the locations p0_5 and s5, there doesn't exist a link between the locations p4_0 and s0, there doesn't exist a link between the locations p4_1 and p4_3, there doesn't exist a link between the locations p4_1 and p5_2, there doesn't exist a link between the locations p4_1 and s1, there doesn't exist a link between the locations p4_1 and s3, there doesn't exist a link between the locations p4_1 and s4, there doesn't exist a link between the locations p4_3 and p4_0, there doesn't exist a link between the locations p4_3 and p5_2, there doesn't exist a link between the locations p4_3 and s0, there doesn't exist a link between the locations p4_3 and s1, there doesn't exist a link between the locations p4_3 and s3, there doesn't exist a link between the locations p4_3 and s4, there doesn't exist a link between the locations p5_2 and s3, there doesn't exist a link between the locations p5_2 and s4, there doesn't exist a link between the locations s1 and p4_1, there doesn't exist a link between the locations s1 and p5_2, there doesn't exist a link between the locations s2 and p0_5, there doesn't exist a link between the locations s2 and p4_0, there doesn't exist a link between the locations s2 and p4_1, there doesn't exist a link between the locations s2 and p4_3, there doesn't exist a link between the locations s2 and s4, there doesn't exist a link between the locations s3 and p4_0, there doesn't exist a link between the locations s3 and p4_3, there doesn't exist a link between the locations s3 and p5_2, there doesn't exist a link between the locations s3 and s1, there doesn't exist a link between the locations s5 and p0_5, there doesn't exist a link between the locations s5 and p5_2, there doesn't exist a link between the locations s5 and s1, there doesn't exist a path between the locations p0_5 and p4_3, there doesn't exist a path between the locations p0_5 and p5_2, there doesn't exist a path between the locations p0_5 and s2, there doesn't exist a path between the locations p0_5 and s3, there doesn't exist a path between the locations p4_0 and p4_1, there doesn't exist a path between the locations p4_0 and s2, there doesn't exist a path between the locations p4_1 and p4_0, there doesn't exist a path between the locations p4_1 and s3, there doesn't exist a path between the locations p4_1 and s5, there doesn't exist a path between the locations p4_3 and p4_0, there doesn't exist a path between the locations p4_3 and p4_1, there doesn't exist a path between the locations p4_3 and s2, there doesn't exist a path between the locations p5_2 and p4_0, there doesn't exist a path between the locations p5_2 and s0, there doesn't exist a path between the locations p5_2 and s3, there doesn't exist a path between the locations p5_2 and s4, there doesn't exist a path between the locations s0 and p5_2, there doesn't exist a path between the locations s1 and p4_0, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s1 and s4, there doesn't exist a path between the locations s2 and p4_3, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s3 and p0_5, there doesn't exist a path between the locations s3 and p5_2, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s4 and s0, there doesn't exist a path between the locations s4 and s1, there doesn't exist a path between the locations s4 and s2, there doesn't exist a path between the locations s4 and s3, there doesn't exist a path between the locations s4 and s5, there doesn't exist a path between the locations s5 and p4_0, there doesn't exist a path between the locations s5 and s2, there doesn't exist a path between the locations s5 and s3, there is no link between location p0_5 and location p5_2, there is no link between location p0_5 and location s1, there is no link between location p0_5 and location s3, there is no link between location p4_0 and location p4_3, there is no link between location p4_0 and location p5_2, there is no link between location p4_0 and location s1, there is no link between location p4_0 and location s2, there is no link between location p4_0 and location s3, there is no link between location p4_1 and location p0_5, there is no link between location p4_1 and location p4_0, there is no link between location p4_1 and location s5, there is no link between location p4_3 and location p4_1, there is no link between location p5_2 and location p0_5, there is no link between location p5_2 and location p4_0, there is no link between location p5_2 and location s0, there is no link between location p5_2 and location s1, there is no link between location p5_2 and location s2, there is no link between location s0 and location p0_5, there is no link between location s0 and location p4_0, there is no link between location s0 and location p4_1, there is no link between location s0 and location s3, there is no link between location s1 and location s3, there is no link between location s1 and location s5, there is no link between location s3 and location p0_5, there is no link between location s3 and location s0, there is no link between location s4 and location p0_5, there is no link between location s4 and location p4_3, there is no link between location s4 and location s2, there is no link between location s5 and location p4_0, there is no link between location s5 and location p4_3, there is no path between location p0_5 and location s1, there is no path between location p4_0 and location p0_5, there is no path between location p4_0 and location p4_3, there is no path between location p4_0 and location p5_2, there is no path between location p4_0 and location s3, there is no path between location p4_0 and location s5, there is no path between location p4_1 and location p4_3, there is no path between location p4_1 and location s0, there is no path between location p4_1 and location s2, there is no path between location p4_3 and location p5_2, there is no path between location p4_3 and location s5, there is no path between location p5_2 and location p0_5, there is no path between location p5_2 and location p4_1, there is no path between location p5_2 and location s1, there is no path between location s0 and location p4_1, there is no path between location s0 and location p4_3, there is no path between location s0 and location s1, there is no path between location s0 and location s2, there is no path between location s0 and location s3, there is no path between location s0 and location s4, there is no path between location s2 and location p0_5, there is no path between location s2 and location p4_0, there is no path between location s2 and location p4_1, there is no path between location s2 and location s1, there is no path between location s2 and location s3, there is no path between location s2 and location s4, there is no path between location s2 and location s5, there is no path between location s3 and location s4, there is no path between location s3 and location s5, there is no path between location s4 and location p0_5, there is no path between location s5 and location p4_3, there is no path between location s5 and location s0, truck1 is not at location p4_3, truck1 is not at location s0, truck1 is not at location s2, truck1 is not at location s3, truck1 is not being driven by driver3, truck1 is not currently at location p0_5, truck1 is not currently at location p4_1, truck1 is not currently at location s4, truck1 is not currently at location s5, truck1 is not present at location p4_0, truck1 is not present at location p5_2, truck2 is not at location p0_5, truck2 is not at location p4_0, truck2 is not at location p4_3, truck2 is not at location s0, truck2 is not currently at location p4_1, truck2 is not currently at location p5_2, truck2 is not currently at location s1, truck2 is not present at location s2, truck2 is not present at location s3 and truck2 is not present at location s4", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to location p4_3, then from p4_3 to location s4, followed by a move from s4 to location p4_1, and then from p4_1 to location s1. At location s1, driver1 boards truck1, and then drives it from s1 to s0. Upon arrival at s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where package2 and package1 are loaded. Next, driver1 drives truck1 from s2 to s3, where package3 is loaded, and package1 is unloaded. Driver1 then drives truck1 from s3 to s4, where packages 4 and 3 are unloaded, and package2 is also unloaded. Finally, driver1 drives truck1 from s4 back to s1 and disembarks, reaching the current state. In this state, if driver2 walks from location s4 to location p4_0, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are currently at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "397c495c-b090-4a25-bbf2-b0f94518885b", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0, driver2 walks from location p3_0 to s0, truck1 is boarded by driver2 at location s0, truck1 is driven from location s0 to s2 by driver2, at location s2, package3 is loaded in truck1, at location s2, package2 is loaded in truck1, driver2 drives truck1 from location s2 to location s0, truck1 is unloaded with package2 at location s0, truck1 is driven from location s0 to s3 by driver2 and package1 is loaded in truck1 at location s3 to reach the current state. In this state, if driver2 drives truck1 from location s3 to location s1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p1_0, driver1 is not currently at location p1_2, driver1 is not currently at location s3, driver1 is not driving truck3 currently, driver1 is not present at location p1_3, driver1 is not present at location p2_0, driver1 is not present at location p3_0, driver1 is not present at location s0, driver1 is not present at location s2, driver2 is not at location p1_3, driver2 is not at location p2_0, driver2 is not at location s2, driver2 is not currently at location p0_1, driver2 is not currently at location p1_0, driver2 is not currently at location p3_0, driver2 is not currently at location s3, driver2 is not driving truck2 currently, driver2 is not present at location p1_2, driver2 is not present at location s0, driver2 is not present at location s1, driver3 is not at location p1_0, driver3 is not at location p1_3, driver3 is not at location p3_0, driver3 is not at location s0, driver3 is not at location s1, driver3 is not at location s2, driver3 is not currently at location p1_2, driver3 is not currently at location p2_0, driver3 is not driving truck3 currently, driver3 is not present at location p0_1, locations p0_1 and p1_2 does not have a link between them, locations p0_1 and p1_3 does not have a link between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and s2 does not have a path between them, locations p0_1 and s3 does not have a link between them, locations p1_0 and p0_1 does not have a path between them, locations p1_0 and p1_2 does not have a path between them, locations p1_0 and p1_3 does not have a path between them, locations p1_0 and p3_0 does not have a path between them, locations p1_0 and s1 does not have a link between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s2 does not have a link between them, locations p1_0 and s2 does not have a path between them, locations p1_0 and s3 does not have a link between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p1_3 does not have a link between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s2 does not have a link between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p1_0 does not have a path between them, locations p1_3 and p2_0 does not have a path between them, locations p1_3 and p3_0 does not have a link between them, locations p2_0 and p1_0 does not have a path between them, locations p2_0 and p1_3 does not have a link between them, locations p2_0 and s1 does not have a link between them, locations p2_0 and s1 does not have a path between them, locations p2_0 and s3 does not have a link between them, locations p2_0 and s3 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and s0 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s1 and p3_0 does not have a link between them, locations s2 and p0_1 does not have a link between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_2 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s1 does not have a path between them, locations s3 and p0_1 does not have a link between them, locations s3 and p1_0 does not have a link between them, locations s3 and p1_0 does not have a path between them, locations s3 and p1_2 does not have a path between them, locations s3 and p1_3 does not have a link between them, locations s3 and p2_0 does not have a link between them, locations s3 and s0 does not have a path between them, package1 is not at location p1_0, package1 is not at location p1_3, package1 is not at location s1, package1 is not at location s3, package1 is not currently at location p1_2, package1 is not currently at location p2_0, package1 is not currently at location s2, package1 is not located in truck2, package1 is not located in truck3, package1 is not present at location p0_1, package1 is not present at location p3_0, package1 is not present at location s0, package2 is not at location p2_0, package2 is not at location p3_0, package2 is not at location s1, package2 is not at location s2, package2 is not at location s3, package2 is not currently at location p1_0, package2 is not in truck2, package2 is not located in truck3, package2 is not placed in truck1, package2 is not present at location p0_1, package2 is not present at location p1_2, package2 is not present at location p1_3, package3 is not at location p1_0, package3 is not at location s1, package3 is not currently at location p0_1, package3 is not currently at location p1_2, package3 is not currently at location p1_3, package3 is not currently at location p2_0, package3 is not currently at location p3_0, package3 is not currently at location s2, package3 is not in truck2, package3 is not located in truck3, package3 is not present at location s0, package3 is not present at location s3, package4 is not at location p1_3, package4 is not at location p2_0, package4 is not at location p3_0, package4 is not at location s0, package4 is not at location s3, package4 is not currently at location p1_0, package4 is not currently at location p1_2, package4 is not located in truck2, package4 is not located in truck3, package4 is not placed in truck1, package4 is not present at location p0_1, package4 is not present at location s2, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p0_1 and s1, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and p1_3, there doesn't exist a link between the locations p1_0 and p2_0, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and s1, there doesn't exist a link between the locations p1_3 and p1_0, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and s2, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and p0_1, there doesn't exist a link between the locations p2_0 and p1_2, there doesn't exist a link between the locations p2_0 and s0, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p1_2, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s2 and p1_3, there doesn't exist a link between the locations s2 and s3, there doesn't exist a path between the locations p0_1 and p1_0, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p3_0, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p2_0, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_0 and s3, there doesn't exist a path between the locations p1_2 and p1_0, there doesn't exist a path between the locations p1_2 and p1_3, there doesn't exist a path between the locations p1_2 and p2_0, there doesn't exist a path between the locations p1_2 and p3_0, there doesn't exist a path between the locations p1_2 and s3, there doesn't exist a path between the locations p1_3 and p1_2, there doesn't exist a path between the locations p1_3 and p3_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p2_0 and p1_2, there doesn't exist a path between the locations p2_0 and p3_0, there doesn't exist a path between the locations p3_0 and p2_0, there doesn't exist a path between the locations s0 and p1_0, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p2_0, there doesn't exist a path between the locations s3 and s2, there is no link between location p0_1 and location p1_0, there is no link between location p0_1 and location p3_0, there is no link between location p1_0 and location p1_2, there is no link between location p1_0 and location s0, there is no link between location p1_2 and location p2_0, there is no link between location p1_2 and location p3_0, there is no link between location p1_2 and location s0, there is no link between location p1_2 and location s3, there is no link between location p1_3 and location p2_0, there is no link between location p1_3 and location s0, there is no link between location p1_3 and location s1, there is no link between location p2_0 and location p1_0, there is no link between location p2_0 and location p3_0, there is no link between location p2_0 and location s2, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p1_0, there is no link between location s0 and location p1_2, there is no link between location s0 and location p1_3, there is no link between location s0 and location p3_0, there is no link between location s1 and location p1_0, there is no link between location s1 and location p2_0, there is no link between location s2 and location p1_0, there is no link between location s2 and location p2_0, there is no link between location s2 and location p3_0, there is no link between location s3 and location p1_2, there is no link between location s3 and location p3_0, there is no link between location s3 and location s2, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location p2_0, there is no path between location p1_2 and location p0_1, there is no path between location p1_3 and location p0_1, there is no path between location p2_0 and location p0_1, there is no path between location p2_0 and location p1_3, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location s1, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_2, there is no path between location s0 and location s3, there is no path between location s1 and location p1_0, there is no path between location s1 and location s0, there is no path between location s1 and location s2, there is no path between location s1 and location s3, there is no path between location s2 and location p0_1, there is no path between location s2 and location p3_0, there is no path between location s3 and location s1, truck1 contains some package, truck1 is not at location p0_1, truck1 is not at location p2_0, truck1 is not at location s3, truck1 is not being driven by driver1, truck1 is not being driven by driver3, truck1 is not currently at location p1_0, truck1 is not currently at location p1_2, truck1 is not currently at location p3_0, truck1 is not present at location p1_3, truck1 is not present at location s0, truck1 is not present at location s2, truck2 is not at location p0_1, truck2 is not at location p1_3, truck2 is not being driven by driver1, truck2 is not being driven by driver3, truck2 is not currently at location p1_0, truck2 is not present at location p1_2, truck2 is not present at location p2_0, truck2 is not present at location p3_0, truck2 is not present at location s0, truck2 is not present at location s1, truck2 is not present at location s2, truck3 is not at location p1_0, truck3 is not at location p1_2, truck3 is not being driven by driver2, truck3 is not currently at location p1_3, truck3 is not currently at location p2_0, truck3 is not currently at location p3_0, truck3 is not currently at location s1, truck3 is not currently at location s3, truck3 is not present at location p0_1 and truck3 is not present at location s2", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 and package2 into truck1 at s2, drives truck1 back to s0, unloads package2 at s0, drives truck1 to s3, and loads package1 into truck1 at s3, resulting in the current state. In this state, if driver2 drives truck1 from s3 to s1, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. There are links between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, p0_1 and s0, p1_2 and s2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links are present between s0 and s2, s1 and s2, and s2 and s1. Furthermore, paths exist between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "5c1ace7b-7178-4059-8013-fd68fdde9fa5", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to location p4_1, driver1 walks from location p4_1 to s1, driver1 boards truck1 at location s1, driver1 drives truck1 to location s0 from location s1, truck1 is loaded with package4 at location s0, truck1 is driven from location s0 to s2 by driver1, at location s2, package2 is loaded in truck1, at location s2, package1 is loaded in truck1, truck1 is driven from location s2 to s3 by driver1, truck1 is loaded with package3 at location s3, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s4 by driver1, truck1 is unloaded with package4 at location s4, package3 is unloaded from truck1 at location s4, truck1 is unloaded with package2 at location s4, driver1 drives truck1 from location s4 to location s1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, if driver2 walks from location s4 to p4_0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is currently at location s1, driver2 is currently at location p4_0, driver3 is present at location s3, locations p0_5 and s5 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s3 and p4_3 have a path between them, locations s4 and p4_3 have a path between them, locations s4 and s0 have a link between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, locations s5 and s3 have a link between them, package1 is present at location s3, package2 is at location s4, package3 is present at location s4, package4 is present at location s4, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s1 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s4, there is a link between location s1 and location s4, there is a link between location s2 and location s5, there is a link between location s3 and location s2, there is a link between location s3 and location s4, there is a link between location s3 and location s5, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a path between location p4_0 and location s0, there is a path between location p4_0 and location s4, there is a path between location p4_1 and location s1, there is a path between location p5_2 and location s2, there is a path between location s0 and location p0_5, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_0, there is a path between location s4 and location p4_1, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then to s4 from p4_3, followed by s4 to p4_1, then p4_1 to s1, where driver1 boards truck1. Driver1 then drives truck1 from s1 to s0, where truck1 is loaded with package4. Next, driver1 drives truck1 from s0 to s2, where package2 and package1 are loaded into truck1. Driver1 then drives truck1 from s2 to s3, where package3 is loaded and package1 is unloaded. Subsequently, driver1 drives truck1 from s3 to s4, where package4 and package3 are unloaded, and package2 is also unloaded. Finally, driver1 drives truck1 from s4 to s1 and disembarks at s1, reaching the current state. In this state, if driver2 walks from location s4 to p4_0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are both at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "5323ec01-3b59-44bf-8a2d-9ad63dceb941", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3 to reach the current state. In this state, if driver2 walks to location s0 from location p3_0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "driver1 is currently at location s1, driver1 is not at location p1_0, driver1 is not at location p1_2, driver1 is not at location p1_3, driver1 is not at location s2, driver1 is not currently at location s0, driver1 is not currently at location s3, driver1 is not driving truck2, driver1 is not present at location p0_1, driver1 is not present at location p2_0, driver1 is not present at location p3_0, driver2 is not at location p1_0, driver2 is not at location p1_2, driver2 is not at location p2_0, driver2 is not currently at location p1_3, driver2 is not currently at location p3_0, driver2 is not currently at location s2, driver2 is not driving truck3 currently, driver2 is not present at location p0_1, driver2 is not present at location s1, driver2 is not present at location s3, driver2 is present at location s0, driver3 is at location s3, driver3 is not at location p1_0, driver3 is not at location p2_0, driver3 is not at location s1, driver3 is not currently at location p0_1, driver3 is not currently at location s0, driver3 is not currently at location s2, driver3 is not driving truck1 currently, driver3 is not present at location p1_2, driver3 is not present at location p1_3, driver3 is not present at location p3_0, locations p0_1 and p1_0 does not have a link between them, locations p0_1 and p1_2 does not have a link between them, locations p0_1 and p1_3 does not have a link between them, locations p0_1 and p1_3 does not have a path between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and s1 have a path between them, locations p0_1 and s2 does not have a link between them, locations p0_1 and s2 does not have a path between them, locations p0_1 and s3 does not have a path between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p1_3 does not have a path between them, locations p1_0 and s0 does not have a link between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s3 does not have a link between them, locations p1_2 and p0_1 does not have a link between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p1_0 does not have a path between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p3_0 does not have a path between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s3 does not have a link between them, locations p1_2 and s3 does not have a path between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p2_0 does not have a link between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 does not have a link between them, locations p1_3 and s3 have a path between them, locations p2_0 and p1_0 does not have a path between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and s2 does not have a link between them, locations p2_0 and s2 have a path between them, locations p2_0 and s3 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p1_0 does not have a link between them, locations p3_0 and p1_0 does not have a path between them, locations p3_0 and p1_2 does not have a link between them, locations p3_0 and p2_0 does not have a link between them, locations p3_0 and s0 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations p3_0 and s3 does not have a link between them, locations s0 and p0_1 have a path between them, locations s0 and p1_0 does not have a link between them, locations s0 and p1_0 does not have a path between them, locations s0 and p1_2 does not have a link between them, locations s0 and p1_2 does not have a path between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 does not have a link between them, locations s0 and p3_0 have a path between them, locations s0 and s3 does not have a path between them, locations s1 and p1_3 does not have a link between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 does not have a path between them, locations s2 and p0_1 does not have a link between them, locations s2 and p1_2 does not have a link between them, locations s2 and p1_3 does not have a link between them, locations s2 and p2_0 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s1 does not have a path between them, locations s2 and s3 does not have a link between them, locations s3 and p1_0 does not have a link between them, locations s3 and p2_0 does not have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s0 does not have a path between them, locations s3 and s2 does not have a link between them, locations s3 and s2 does not have a path between them, package1 is currently at location s3, package1 is not at location p0_1, package1 is not at location p1_0, package1 is not at location p1_3, package1 is not at location s2, package1 is not currently at location p1_2, package1 is not currently at location p3_0, package1 is not currently at location s0, package1 is not currently at location s1, package1 is not in truck2, package1 is not placed in truck1, package1 is not placed in truck3, package1 is not present at location p2_0, package2 is not at location p1_2, package2 is not at location p1_3, package2 is not at location p2_0, package2 is not currently at location p1_0, package2 is not currently at location s1, package2 is not currently at location s3, package2 is not placed in truck1, package2 is not placed in truck2, package2 is not placed in truck3, package2 is not present at location p0_1, package2 is not present at location p3_0, package2 is not present at location s0, package2 is present at location s2, package3 is not at location p1_0, package3 is not at location p1_3, package3 is not at location s3, package3 is not currently at location p1_2, package3 is not currently at location s0, package3 is not in truck1, package3 is not located in truck3, package3 is not placed in truck2, package3 is not present at location p0_1, package3 is not present at location p2_0, package3 is not present at location p3_0, package3 is not present at location s1, package3 is present at location s2, package4 is not at location s0, package4 is not at location s2, package4 is not currently at location p0_1, package4 is not currently at location p1_0, package4 is not currently at location p1_2, package4 is not currently at location p3_0, package4 is not currently at location s3, package4 is not placed in truck1, package4 is not placed in truck2, package4 is not placed in truck3, package4 is not present at location p1_3, package4 is not present at location p2_0, package4 is present at location s1, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p1_0 and p1_2, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_2 and p1_3, there doesn't exist a link between the locations p1_2 and p2_0, there doesn't exist a link between the locations p1_2 and s2, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p2_0 and p0_1, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p1_3, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p2_0, there doesn't exist a link between the locations s3 and p1_2, there doesn't exist a link between the locations s3 and p2_0, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and p1_2, there doesn't exist a path between the locations p0_1 and p3_0, there doesn't exist a path between the locations p1_0 and p1_2, there doesn't exist a path between the locations p1_0 and p2_0, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s2, there doesn't exist a path between the locations p1_2 and p2_0, there doesn't exist a path between the locations p1_3 and p1_2, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_2, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p2_0 and p3_0, there doesn't exist a path between the locations p2_0 and s1, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s3 and s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p3_0 and s3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s2 and location p1_2, there is a path between location s2 and location p2_0, there is no link between location p0_1 and location s0, there is no link between location p0_1 and location s1, there is no link between location p1_0 and location p1_3, there is no link between location p1_0 and location p2_0, there is no link between location p1_0 and location s1, there is no link between location p1_0 and location s2, there is no link between location p1_2 and location p3_0, there is no link between location p1_2 and location s0, there is no link between location p1_2 and location s1, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p1_2, there is no link between location p1_3 and location s2, there is no link between location p2_0 and location p1_0, there is no link between location p2_0 and location p1_2, there is no link between location p2_0 and location p1_3, there is no link between location p2_0 and location s0, there is no link between location p2_0 and location s1, there is no link between location p3_0 and location p0_1, there is no link between location p3_0 and location s1, there is no link between location p3_0 and location s2, there is no link between location s1 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s1 and location p3_0, there is no link between location s2 and location p1_0, there is no link between location s2 and location p3_0, there is no link between location s3 and location p0_1, there is no link between location s3 and location p1_3, there is no path between location p0_1 and location p1_0, there is no path between location p0_1 and location p2_0, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location s0, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p0_1, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location s2, there is no path between location p2_0 and location s3, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location p2_0, there is no path between location p3_0 and location s2, there is no path between location s0 and location s1, there is no path between location s0 and location s2, there is no path between location s1 and location p1_0, there is no path between location s1 and location s0, there is no path between location s1 and location s2, there is no path between location s2 and location p0_1, there is no path between location s2 and location p1_3, there is no path between location s2 and location s3, there is no path between location s3 and location p0_1, there is no path between location s3 and location p1_0, there is no path between location s3 and location p1_2, truck1 contains nothing, truck1 is at location s0, truck1 is not at location p2_0, truck1 is not at location s2, truck1 is not being driven by driver1, truck1 is not being driven by driver2, truck1 is not currently at location p1_2, truck1 is not currently at location p1_3, truck1 is not currently at location p3_0, truck1 is not currently at location s1, truck1 is not present at location p0_1, truck1 is not present at location p1_0, truck1 is not present at location s3, truck2 contains nothing, truck2 is not at location p1_2, truck2 is not being driven by driver2, truck2 is not being driven by driver3, truck2 is not currently at location p3_0, truck2 is not currently at location s1, truck2 is not currently at location s2, truck2 is not present at location p0_1, truck2 is not present at location p1_0, truck2 is not present at location p1_3, truck2 is not present at location p2_0, truck2 is not present at location s0, truck2 is present at location s3, truck3 contains nothing, truck3 is currently at location s0, truck3 is not at location p1_2, truck3 is not at location p2_0, truck3 is not at location s3, truck3 is not being driven by driver1, truck3 is not being driven by driver3, truck3 is not currently at location p0_1, truck3 is not currently at location p1_3, truck3 is not currently at location s2, truck3 is not present at location p1_0, truck3 is not present at location p3_0 and truck3 is not present at location s1", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver2 moves from location s3 to location p3_0 to achieve the current state. From this state, if driver2 moves from location p3_0 to location s0, what are all the valid properties of the resulting state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s1, s0 and s3, and s3 and s1. Paths are present between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, p0_1 and s0, p1_2 and s2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links exist between s0 and s2, s1 and s2, and s2 and s1. Furthermore, paths are present between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "f260e2c3-6265-4186-bda9-9ada95c19eae", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks to location s0 from location p3_0, driver2 boards truck1 at location s0, driver2 drives truck1 to location s2 from location s0, package3 is loaded in truck1 at location s2, at location s2, package2 is loaded in truck1, driver2 drives truck1 to location s0 from location s2, truck1 is unloaded with package2 at location s0, truck1 is driven from location s0 to s3 by driver2, truck1 is loaded with package1 at location s3, driver2 drives truck1 from location s3 to location s1, at location s1, driver2 disembarks from truck1, driver2 walks from location s1 to location p0_1, driver2 walks from location p0_1 to s0, driver2 boards truck3 at location s0, driver2 drives truck3 from location s0 to location s2, at location s1, package3 is unloaded in truck1, package1 is unloaded from truck1 at location s1 and driver3 walks from location s3 to location p3_0 to reach the current state. In this state, if driver3 walks from location p3_0 to s0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p1_0, driver1 is not at location p1_3, driver1 is not at location s3, driver1 is not currently at location p1_2, driver1 is not driving truck3, driver1 is not present at location p2_0, driver1 is not present at location p3_0, driver1 is not present at location s0, driver1 is not present at location s2, driver2 is not at location s1, driver2 is not at location s3, driver2 is not currently at location p0_1, driver2 is not currently at location p2_0, driver2 is not currently at location p3_0, driver2 is not driving truck1, driver2 is not driving truck2, driver2 is not present at location p1_0, driver2 is not present at location p1_2, driver2 is not present at location p1_3, driver2 is not present at location s0, driver2 is not present at location s2, driver3 is not at location p0_1, driver3 is not at location p1_3, driver3 is not currently at location p3_0, driver3 is not driving truck1, driver3 is not driving truck2 currently, driver3 is not driving truck3, driver3 is not present at location p1_0, driver3 is not present at location p1_2, driver3 is not present at location p2_0, driver3 is not present at location s1, driver3 is not present at location s2, driver3 is not present at location s3, locations p0_1 and p1_0 does not have a link between them, locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and p3_0 does not have a link between them, locations p0_1 and s2 does not have a link between them, locations p0_1 and s2 does not have a path between them, locations p1_0 and p0_1 does not have a path between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and p3_0 does not have a link between them, locations p1_0 and s1 does not have a path between them, locations p1_2 and p0_1 does not have a link between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p2_0 does not have a link between them, locations p1_2 and s1 does not have a link between them, locations p1_2 and s2 does not have a link between them, locations p1_2 and s3 does not have a link between them, locations p1_3 and p1_2 does not have a path between them, locations p1_3 and p2_0 does not have a link between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and s2 does not have a path between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and s0 does not have a link between them, locations p3_0 and p1_2 does not have a link between them, locations p3_0 and p1_2 does not have a path between them, locations p3_0 and p1_3 does not have a link between them, locations p3_0 and s0 does not have a link between them, locations p3_0 and s1 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations p3_0 and s2 does not have a path between them, locations p3_0 and s3 does not have a link between them, locations s0 and p1_0 does not have a link between them, locations s0 and p1_2 does not have a link between them, locations s0 and p1_2 does not have a path between them, locations s0 and p1_3 does not have a path between them, locations s0 and s3 does not have a path between them, locations s1 and p0_1 does not have a link between them, locations s1 and p2_0 does not have a path between them, locations s1 and s3 does not have a path between them, locations s2 and p0_1 does not have a link between them, locations s2 and p1_0 does not have a link between them, locations s2 and p1_3 does not have a link between them, locations s2 and p1_3 does not have a path between them, locations s2 and p3_0 does not have a link between them, locations s2 and p3_0 does not have a path between them, locations s2 and s3 does not have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p1_2 does not have a link between them, locations s3 and s0 does not have a path between them, locations s3 and s1 does not have a path between them, locations s3 and s2 does not have a link between them, package1 is not at location p0_1, package1 is not currently at location p2_0, package1 is not currently at location p3_0, package1 is not currently at location s2, package1 is not currently at location s3, package1 is not in truck1, package1 is not in truck3, package1 is not placed in truck2, package1 is not present at location p1_0, package1 is not present at location p1_2, package1 is not present at location p1_3, package1 is not present at location s0, package2 is not at location p1_3, package2 is not at location p2_0, package2 is not at location p3_0, package2 is not at location s1, package2 is not at location s2, package2 is not currently at location p0_1, package2 is not currently at location s3, package2 is not in truck1, package2 is not located in truck2, package2 is not located in truck3, package2 is not present at location p1_0, package2 is not present at location p1_2, package3 is not currently at location p0_1, package3 is not currently at location p1_3, package3 is not currently at location p2_0, package3 is not currently at location p3_0, package3 is not currently at location s0, package3 is not currently at location s2, package3 is not in truck3, package3 is not located in truck2, package3 is not placed in truck1, package3 is not present at location p1_0, package3 is not present at location p1_2, package3 is not present at location s3, package4 is not at location p0_1, package4 is not at location p1_2, package4 is not at location p1_3, package4 is not at location p2_0, package4 is not at location s0, package4 is not currently at location s3, package4 is not in truck1, package4 is not located in truck3, package4 is not placed in truck2, package4 is not present at location p1_0, package4 is not present at location p3_0, package4 is not present at location s2, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and s0, there doesn't exist a link between the locations p1_0 and s1, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_0 and s3, there doesn't exist a link between the locations p1_2 and p1_3, there doesn't exist a link between the locations p1_2 and p3_0, there doesn't exist a link between the locations p1_2 and s0, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p1_3 and s1, there doesn't exist a link between the locations p2_0 and p0_1, there doesn't exist a link between the locations p2_0 and p1_2, there doesn't exist a link between the locations p2_0 and p1_3, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations p3_0 and p2_0, there doesn't exist a link between the locations p3_0 and s2, there doesn't exist a link between the locations s1 and p1_0, there doesn't exist a link between the locations s1 and p1_2, there doesn't exist a link between the locations s2 and p1_2, there doesn't exist a link between the locations s2 and p2_0, there doesn't exist a link between the locations s3 and p1_0, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_0 and s2, there doesn't exist a path between the locations p1_0 and s3, there doesn't exist a path between the locations p1_2 and s3, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_0, there doesn't exist a path between the locations p2_0 and s1, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p2_0, there doesn't exist a path between the locations s3 and s2, there is no link between location p0_1 and location p1_2, there is no link between location p0_1 and location p1_3, there is no link between location p0_1 and location s0, there is no link between location p0_1 and location s1, there is no link between location p0_1 and location s3, there is no link between location p1_0 and location p1_3, there is no link between location p1_0 and location p2_0, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location s2, there is no link between location p1_3 and location s3, there is no link between location p2_0 and location p1_0, there is no link between location p2_0 and location s1, there is no link between location p2_0 and location s2, there is no link between location p2_0 and location s3, there is no link between location p3_0 and location p0_1, there is no link between location s0 and location p0_1, there is no link between location s0 and location p1_3, there is no link between location s0 and location p2_0, there is no link between location s0 and location p3_0, there is no link between location s1 and location p1_3, there is no link between location s1 and location p2_0, there is no link between location s1 and location p3_0, there is no link between location s3 and location p2_0, there is no link between location s3 and location p3_0, there is no path between location p0_1 and location p2_0, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s3, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p1_3, there is no path between location p1_2 and location p0_1, there is no path between location p1_2 and location p1_0, there is no path between location p1_2 and location p1_3, there is no path between location p1_2 and location p2_0, there is no path between location p1_2 and location p3_0, there is no path between location p1_2 and location s0, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location p3_0, there is no path between location p2_0 and location p1_2, there is no path between location p2_0 and location p1_3, there is no path between location p2_0 and location p3_0, there is no path between location p2_0 and location s3, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location p2_0, there is no path between location s0 and location p1_0, there is no path between location s0 and location s1, there is no path between location s0 and location s2, there is no path between location s1 and location p1_0, there is no path between location s1 and location p3_0, there is no path between location s3 and location p1_0, there is no path between location s3 and location p1_2, truck1 is not at location p3_0, truck1 is not at location s0, truck1 is not being driven by driver1, truck1 is not currently at location p1_0, truck1 is not currently at location p1_3, truck1 is not currently at location p2_0, truck1 is not present at location p0_1, truck1 is not present at location p1_2, truck1 is not present at location s2, truck1 is not present at location s3, truck2 is not at location p1_0, truck2 is not at location p1_2, truck2 is not at location p1_3, truck2 is not at location s2, truck2 is not being driven by driver1, truck2 is not currently at location s0, truck2 is not currently at location s1, truck2 is not present at location p0_1, truck2 is not present at location p2_0, truck2 is not present at location p3_0, truck3 is not at location p1_3, truck3 is not at location s3, truck3 is not currently at location p0_1, truck3 is not currently at location p1_0, truck3 is not currently at location p1_2, truck3 is not currently at location s0, truck3 is not currently at location s1, truck3 is not empty, truck3 is not present at location p2_0 and truck3 is not present at location p3_0", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 and package2 into truck1 at s2, drives truck1 back to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, loads package1 into truck1 at s3, drives truck1 from s3 to s1, disembarks from truck1 at s1, walks from s1 to p0_1 and then to s0, boards truck3 at s0, drives truck3 from s0 to s2, unloads package3 from truck1 at s1, and unloads package1 from truck1 at s1, while driver3 walks from s3 to p3_0 to reach the current state. In this state, if driver3 walks from p3_0 to s0, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. There are links between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, s0 and p0_1, s2 and p1_2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links are present between s0 and s2, s1 and s2, and s2 and s1. Furthermore, paths exist between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "0d2cc9a2-03c4-4e5f-9e79-4779056bbbad", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to s1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, truck1 is loaded with package4 at location s0, truck1 is driven from location s0 to s2 by driver1, at location s2, package2 is loaded in truck1 and package1 is loaded in truck1 at location s2 to reach the current state. In this state, if truck1 is driven from location s2 to s3 by driver1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "driver1 is not at location s0, driver1 is not at location s2, driver1 is not at location s3, driver1 is not currently at location p4_1, driver1 is not currently at location p4_3, driver1 is not currently at location p5_2, driver1 is not currently at location s1, driver1 is not currently at location s4, driver1 is not currently at location s5, driver1 is not driving truck2, driver1 is not present at location p0_5, driver1 is not present at location p4_0, driver2 is at location s4, driver2 is not at location p0_5, driver2 is not at location p5_2, driver2 is not at location s0, driver2 is not at location s1, driver2 is not at location s3, driver2 is not at location s5, driver2 is not currently at location p4_0, driver2 is not currently at location p4_1, driver2 is not driving truck1 currently, driver2 is not driving truck2, driver2 is not present at location p4_3, driver2 is not present at location s2, driver3 is currently at location s3, driver3 is not at location p4_0, driver3 is not at location p4_1, driver3 is not at location s0, driver3 is not at location s5, driver3 is not currently at location p0_5, driver3 is not currently at location p5_2, driver3 is not currently at location s2, driver3 is not driving truck2 currently, driver3 is not present at location p4_3, driver3 is not present at location s1, driver3 is not present at location s4, locations p0_5 and p4_1 does not have a link between them, locations p0_5 and p4_3 does not have a link between them, locations p0_5 and p4_3 does not have a path between them, locations p0_5 and p5_2 does not have a path between them, locations p0_5 and s2 does not have a link between them, locations p0_5 and s4 does not have a link between them, locations p4_0 and p0_5 does not have a path between them, locations p4_0 and p4_1 does not have a link between them, locations p4_0 and p4_3 does not have a link between them, locations p4_0 and s0 have a path between them, locations p4_0 and s1 does not have a path between them, locations p4_0 and s4 have a path between them, locations p4_0 and s5 does not have a path between them, locations p4_1 and p4_3 does not have a path between them, locations p4_1 and p5_2 does not have a path between them, locations p4_1 and s0 does not have a path between them, locations p4_1 and s2 does not have a path between them, locations p4_1 and s3 does not have a link between them, locations p4_1 and s3 does not have a path between them, locations p4_1 and s5 does not have a link between them, locations p4_3 and p4_0 does not have a path between them, locations p4_3 and p5_2 does not have a path between them, locations p4_3 and s0 does not have a link between them, locations p4_3 and s1 does not have a path between them, locations p4_3 and s5 does not have a link between them, locations p4_3 and s5 does not have a path between them, locations p5_2 and p4_3 does not have a link between them, locations p5_2 and s0 does not have a path between them, locations p5_2 and s2 does not have a link between them, locations p5_2 and s2 have a path between them, locations p5_2 and s3 does not have a link between them, locations s0 and p0_5 does not have a link between them, locations s0 and p4_0 does not have a link between them, locations s0 and p4_1 does not have a link between them, locations s0 and s1 does not have a path between them, locations s0 and s2 does not have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 does not have a link between them, locations s0 and s4 have a link between them, locations s0 and s5 does not have a path between them, locations s1 and p0_5 does not have a link between them, locations s1 and p4_0 does not have a link between them, locations s1 and p4_3 does not have a link between them, locations s1 and p5_2 does not have a path between them, locations s1 and s0 does not have a path between them, locations s1 and s2 does not have a path between them, locations s1 and s2 have a link between them, locations s1 and s4 does not have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s3 and p0_5 does not have a link between them, locations s3 and p0_5 does not have a path between them, locations s3 and p4_0 does not have a link between them, locations s3 and p4_1 does not have a path between them, locations s3 and p4_3 have a path between them, locations s3 and s0 does not have a link between them, locations s3 and s2 have a link between them, locations s4 and p4_1 have a path between them, locations s4 and p4_3 does not have a link between them, locations s4 and s0 have a link between them, locations s4 and s1 does not have a path between them, locations s4 and s2 does not have a path between them, locations s4 and s3 does not have a path between them, locations s4 and s5 does not have a path between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and p4_0 does not have a link between them, locations s5 and p4_1 does not have a link between them, locations s5 and s0 does not have a path between them, locations s5 and s2 have a link between them, locations s5 and s4 have a link between them, package1 is located in truck1, package1 is not at location p4_0, package1 is not at location p4_3, package1 is not at location p5_2, package1 is not at location s1, package1 is not at location s5, package1 is not currently at location p0_5, package1 is not currently at location s2, package1 is not in truck2, package1 is not present at location p4_1, package1 is not present at location s0, package1 is not present at location s3, package1 is not present at location s4, package2 is in truck1, package2 is not at location p4_1, package2 is not at location p4_3, package2 is not at location s0, package2 is not at location s1, package2 is not at location s5, package2 is not currently at location p0_5, package2 is not currently at location p5_2, package2 is not currently at location s2, package2 is not currently at location s3, package2 is not placed in truck2, package2 is not present at location p4_0, package2 is not present at location s4, package3 is currently at location s3, package3 is not at location p4_0, package3 is not currently at location p4_3, package3 is not currently at location s0, package3 is not currently at location s1, package3 is not currently at location s2, package3 is not in truck1, package3 is not located in truck2, package3 is not present at location p0_5, package3 is not present at location p4_1, package3 is not present at location p5_2, package3 is not present at location s4, package3 is not present at location s5, package4 is in truck1, package4 is not at location s4, package4 is not currently at location p0_5, package4 is not currently at location p4_1, package4 is not currently at location p4_3, package4 is not currently at location p5_2, package4 is not currently at location s0, package4 is not currently at location s2, package4 is not currently at location s3, package4 is not currently at location s5, package4 is not placed in truck2, package4 is not present at location p4_0, package4 is not present at location s1, there doesn't exist a link between the locations p0_5 and p4_0, there doesn't exist a link between the locations p0_5 and s0, there doesn't exist a link between the locations p0_5 and s1, there doesn't exist a link between the locations p0_5 and s5, there doesn't exist a link between the locations p4_0 and p0_5, there doesn't exist a link between the locations p4_0 and s0, there doesn't exist a link between the locations p4_0 and s1, there doesn't exist a link between the locations p4_0 and s3, there doesn't exist a link between the locations p4_0 and s4, there doesn't exist a link between the locations p4_0 and s5, there doesn't exist a link between the locations p4_1 and p0_5, there doesn't exist a link between the locations p4_1 and p4_3, there doesn't exist a link between the locations p4_1 and p5_2, there doesn't exist a link between the locations p4_1 and s4, there doesn't exist a link between the locations p4_3 and p4_1, there doesn't exist a link between the locations p4_3 and s1, there doesn't exist a link between the locations p4_3 and s3, there doesn't exist a link between the locations p4_3 and s4, there doesn't exist a link between the locations p5_2 and p4_0, there doesn't exist a link between the locations p5_2 and p4_1, there doesn't exist a link between the locations p5_2 and s0, there doesn't exist a link between the locations p5_2 and s5, there doesn't exist a link between the locations s0 and p5_2, there doesn't exist a link between the locations s1 and p4_1, there doesn't exist a link between the locations s1 and s3, there doesn't exist a link between the locations s1 and s5, there doesn't exist a link between the locations s2 and p0_5, there doesn't exist a link between the locations s2 and s4, there doesn't exist a link between the locations s3 and s1, there doesn't exist a link between the locations s4 and p4_1, there doesn't exist a link between the locations s4 and p5_2, there doesn't exist a link between the locations s5 and p4_3, there doesn't exist a link between the locations s5 and s1, there doesn't exist a path between the locations p0_5 and p4_0, there doesn't exist a path between the locations p0_5 and p4_1, there doesn't exist a path between the locations p0_5 and s1, there doesn't exist a path between the locations p0_5 and s3, there doesn't exist a path between the locations p0_5 and s4, there doesn't exist a path between the locations p4_0 and p5_2, there doesn't exist a path between the locations p4_0 and s3, there doesn't exist a path between the locations p4_3 and s0, there doesn't exist a path between the locations p4_3 and s2, there doesn't exist a path between the locations p5_2 and p4_3, there doesn't exist a path between the locations s0 and p4_3, there doesn't exist a path between the locations s0 and p5_2, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p4_0, there doesn't exist a path between the locations s1 and p4_3, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s1 and s5, there doesn't exist a path between the locations s2 and p4_0, there doesn't exist a path between the locations s2 and p4_1, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s2 and s4, there doesn't exist a path between the locations s3 and p4_0, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s1, there doesn't exist a path between the locations s4 and p5_2, there doesn't exist a path between the locations s4 and s0, there doesn't exist a path between the locations s5 and p4_0, there doesn't exist a path between the locations s5 and p4_1, there doesn't exist a path between the locations s5 and s1, there doesn't exist a path between the locations s5 and s2, there doesn't exist a path between the locations s5 and s3, there doesn't exist a path between the locations s5 and s4, there exists a link between the locations s0 and s1, there exists a link between the locations s2 and s0, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s0, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations s0 and p4_0, there exists a path between the locations s1 and p4_1, there is a link between location s0 and location s5, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s3 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s0, there is a path between location p0_5 and location s5, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s4, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s4 and location p4_0, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, there is no link between location p0_5 and location p5_2, there is no link between location p0_5 and location s3, there is no link between location p4_0 and location p5_2, there is no link between location p4_0 and location s2, there is no link between location p4_1 and location p4_0, there is no link between location p4_1 and location s0, there is no link between location p4_1 and location s1, there is no link between location p4_1 and location s2, there is no link between location p4_3 and location p0_5, there is no link between location p4_3 and location p4_0, there is no link between location p4_3 and location p5_2, there is no link between location p4_3 and location s2, there is no link between location p5_2 and location p0_5, there is no link between location p5_2 and location s1, there is no link between location p5_2 and location s4, there is no link between location s0 and location p4_3, there is no link between location s1 and location p5_2, there is no link between location s2 and location p4_0, there is no link between location s2 and location p4_1, there is no link between location s2 and location p4_3, there is no link between location s2 and location p5_2, there is no link between location s3 and location p4_1, there is no link between location s3 and location p4_3, there is no link between location s3 and location p5_2, there is no link between location s4 and location p0_5, there is no link between location s4 and location p4_0, there is no link between location s4 and location s2, there is no link between location s5 and location p0_5, there is no link between location s5 and location p5_2, there is no path between location p0_5 and location s2, there is no path between location p4_0 and location p4_1, there is no path between location p4_0 and location p4_3, there is no path between location p4_0 and location s2, there is no path between location p4_1 and location p0_5, there is no path between location p4_1 and location p4_0, there is no path between location p4_1 and location s5, there is no path between location p4_3 and location p0_5, there is no path between location p4_3 and location p4_1, there is no path between location p5_2 and location p0_5, there is no path between location p5_2 and location p4_0, there is no path between location p5_2 and location p4_1, there is no path between location p5_2 and location s1, there is no path between location p5_2 and location s3, there is no path between location p5_2 and location s4, there is no path between location s0 and location p4_1, there is no path between location s0 and location s4, there is no path between location s1 and location p0_5, there is no path between location s2 and location p0_5, there is no path between location s2 and location p4_3, there is no path between location s2 and location s5, there is no path between location s3 and location p5_2, there is no path between location s3 and location s2, there is no path between location s3 and location s4, there is no path between location s3 and location s5, there is no path between location s4 and location p0_5, there is no path between location s5 and location p4_3, truck1 is at location s3, truck1 is being driven by driver1, truck1 is not at location p0_5, truck1 is not at location s0, truck1 is not at location s2, truck1 is not at location s5, truck1 is not being driven by driver3, truck1 is not currently at location p4_1, truck1 is not currently at location p4_3, truck1 is not empty, truck1 is not present at location p4_0, truck1 is not present at location p5_2, truck1 is not present at location s1, truck1 is not present at location s4, truck2 contains nothing, truck2 is currently at location s5, truck2 is not at location p0_5, truck2 is not at location p4_3, truck2 is not at location p5_2, truck2 is not at location s1, truck2 is not currently at location p4_0, truck2 is not currently at location p4_1, truck2 is not currently at location s0, truck2 is not currently at location s3, truck2 is not present at location s2 and truck2 is not present at location s4", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver1 moves from location s3 to p4_3, then to s4, followed by p4_1, and finally to s1, where driver1 boards truck1. Driver1 then drives truck1 from s1 to s0, where truck1 is loaded with package4. Subsequently, driver1 drives truck1 from s0 to s2, and at location s2, both package2 and package1 are loaded into truck1, resulting in the current state. If driver1 drives truck1 from s2 to s3, what are all the valid properties of the resulting state, including both affirmative and negated properties? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are currently at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "fcfb67db-d066-49bb-8004-49cac83a70ac", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, if driver2 walks from location p0_2 to location s0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is currently at location s0, driver2 is at location s0, locations p0_1 and s1 have a path between them, locations p0_2 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and s1 have a link between them, locations s1 and p2_1 have a path between them, locations s1 and s3 have a link between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s1, package2 is present at location s2, package3 is present at location s3, package4 is at location s2, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a link between location s3 and location s1, there is a path between location p1_3 and location s3, there is a path between location s2 and location p0_2, there is a path between location s3 and location p0_3, truck1 is empty, truck1 is present at location s3, truck2 contains nothing and truck2 is at location s0", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: package3 is loaded onto truck1 at location s0, driver1 walks from location s2 to p0_2, then to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, and walks to location p0_3 and then back to s0, while driver2 walks from location s2 to p0_2 to reach the current state. In this state, if driver2 walks from location p0_2 to location s0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at the same location, s2. A path exists between locations p0_2 and s2, and another path is present between locations p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 is also linked to s2. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. A link exists between locations s0 and s2, as well as between s0 and s3, and s3 and s0. Paths are present between locations p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths are present between locations p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at location s0."}
{"question_id": "96b129c6-990b-41df-af88-fe901ef3f935", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, truck1 is boarded by driver1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0, driver2 walks to location p0_2 from location s2, driver2 walks to location s0 from location p0_2, at location s0, driver2 boards truck2, truck2 is driven from location s0 to s1 by driver2, package1 is loaded in truck2 at location s1, driver2 drives truck2 from location s1 to location s2, truck2 is loaded with package2 at location s2, package1 is unloaded from truck2 at location s2, truck2 is driven from location s2 to s1 by driver2 and driver2 disembarks from truck2 at location s1 to reach the current state. In this state, if truck2 is unloaded with package2 at location s1, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is currently at location s0, driver2 is at location s1, locations p0_1 and s1 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s1 have a path between them, locations s0 and p0_2 have a path between them, locations s1 and s2 have a link between them, locations s1 and s3 have a link between them, locations s2 and s3 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s2, package2 is at location s1, package3 is present at location s3, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s2, there exists a link between the locations s3 and s1, there exists a path between the locations p2_1 and s2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p0_2, there exists a path between the locations s2 and p2_1, there is a link between location s0 and location s3, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s0, there is a path between location p0_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s3 and location p0_3, there is a path between location s3 and location p1_3, truck1 is currently at location s3, truck1 is empty, truck2 is at location s1 and truck2 is empty", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to p0_2 and then to s0, driver1 boards truck1 at s0, drives it to s3, and then disembarks at s3, where package3 is unloaded from truck1, after which driver1 walks to p0_3 and then back to s0. Meanwhile, driver2 moves from s2 to p0_2 and then to s0, boards truck2 at s0, drives it to s1, loads package1 onto truck2, drives to s2, loads package2 onto truck2, unloads package1, drives back to s1, and disembarks from truck2. If, in this state, truck2 is unloaded with package2 at location s1, what are the valid properties of the state that do not involve negations?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. There are links between s0 and s2, s0 and s3, and s3 and s0. Paths exist between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently at location s0 and is empty, while truck2 is also empty and present at location s0."}
{"question_id": "e18bbbfd-0c89-462c-a81e-d0681b50bc02", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3 to reach the current state. In this state, if driver1 walks from location p4_3 to s4, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is currently at location s4, driver2 is currently at location s4, driver3 is currently at location s3, locations p0_5 and s5 have a path between them, locations p4_1 and s1 have a path between them, locations p4_1 and s4 have a path between them, locations p4_3 and s3 have a path between them, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s0 and s5 have a link between them, locations s1 and s4 have a link between them, locations s2 and s0 have a link between them, locations s2 and s3 have a link between them, locations s3 and s5 have a link between them, locations s4 and s0 have a link between them, locations s4 and s1 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, package1 is currently at location s2, package2 is at location s2, package3 is present at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s4, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s2, there exists a link between the locations s4 and s3, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_0 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s3 and location s4, there is a link between location s4 and location s5, there is a link between location s5 and location s2, there is a link between location s5 and location s3, there is a link between location s5 and location s4, there is a path between location p4_3 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, there is a path between location s2 and location p5_2, there is a path between location s3 and location p4_3, there is a path between location s4 and location p4_1, truck1 contains nothing, truck1 is currently at location s1, truck2 is at location s5 and truck2 is empty", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver1 moves from location s3 to location p4_3 to achieve the current state. From this state, if driver1 proceeds from location p4_3 to s4, what are all the valid properties of the resulting state that do not include negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are both at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "f6bcd398-3896-44cd-ad46-742351e0b350", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to s0, driver2 boards truck1 at location s0, driver2 drives truck1 to location s2 from location s0, truck1 is loaded with package3 at location s2, truck1 is loaded with package2 at location s2, truck1 is driven from location s2 to s0 by driver2, package2 is unloaded from truck1 at location s0, truck1 is driven from location s0 to s3 by driver2, package1 is loaded in truck1 at location s3, truck1 is driven from location s3 to s1 by driver2, from truck1, driver2 disembarks at location s1, driver2 walks from location s1 to p0_1, driver2 walks from location p0_1 to s0, driver2 boards truck3 at location s0, driver2 drives truck3 to location s2 from location s0, truck1 is unloaded with package3 at location s1, package1 is unloaded from truck1 at location s1 and driver3 walks from location s3 to location p3_0 to reach the current state. In this state, if driver3 walks to location s0 from location p3_0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "driver1 is currently at location s1, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations s0 and p3_0 have a path between them, locations s1 and p1_2 have a path between them, locations s1 and s0 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, package1 is at location s1, package2 is currently at location s0, package3 is present at location s1, package4 is at location s1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p3_0 and s0, there exists a path between the locations p3_0 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a path between location p0_1 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location s0 and location p2_0, there is a path between location s3 and location p1_3, truck1 is at location s1, truck1 is empty, truck2 is at location s3, truck2 is empty, truck3 is being driven by driver2 and truck3 is present at location s2", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads truck1 with package3 and package2 at s2, drives truck1 back to s0, unloads package2 from truck1 at s0, drives truck1 to s3, loads package1 into truck1 at s3, drives truck1 to s1, disembarks from truck1 at s1, walks to p0_1 and then to s0, boards truck3 at s0, drives truck3 to s2, unloads package3 from truck1 at s1, and unloads package1 from truck1 at s1, while driver3 walks from s3 to p3_0 to reach the current state. In this state, if driver3 proceeds to walk from p3_0 to s0, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. There are links between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, s0 and p0_1, s2 and p1_2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links are present between s0 and s2, s1 and s2, and s2 and s1. Furthermore, paths exist between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "2cd5daf4-3f3d-434b-85bf-d5d255345ba0", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, if driver1 walks from location s2 to location p0_2, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p0_3, driver1 is not at location p2_1, driver1 is not at location s1, driver1 is not currently at location p3_0, driver1 is not driving truck1 currently, driver1 is not driving truck2 currently, driver1 is not present at location p1_3, driver1 is not present at location s0, driver1 is not present at location s2, driver1 is not present at location s3, driver1 is present at location p0_2, driver2 is not at location p0_3, driver2 is not at location s1, driver2 is not currently at location p0_1, driver2 is not currently at location p0_2, driver2 is not currently at location p1_3, driver2 is not currently at location p2_1, driver2 is not currently at location s0, driver2 is not present at location p3_0, driver2 is not present at location s3, driver2 is present at location s2, locations p0_1 and p0_3 does not have a link between them, locations p0_1 and p0_3 does not have a path between them, locations p0_1 and p2_1 does not have a link between them, locations p0_1 and p2_1 does not have a path between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s1 does not have a link between them, locations p0_1 and s1 have a path between them, locations p0_2 and p0_1 does not have a link between them, locations p0_2 and p0_1 does not have a path between them, locations p0_2 and p0_3 does not have a link between them, locations p0_2 and p2_1 does not have a link between them, locations p0_2 and p2_1 does not have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s1 does not have a path between them, locations p0_2 and s2 does not have a link between them, locations p0_2 and s3 does not have a path between them, locations p0_3 and p0_2 does not have a path between them, locations p0_3 and p1_3 does not have a link between them, locations p0_3 and s1 does not have a link between them, locations p0_3 and s1 does not have a path between them, locations p0_3 and s2 does not have a path between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p0_3 does not have a path between them, locations p1_3 and p2_1 does not have a link between them, locations p1_3 and p2_1 does not have a path between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p2_1 and p0_1 does not have a link between them, locations p2_1 and p0_2 does not have a link between them, locations p2_1 and p3_0 does not have a link between them, locations p2_1 and s1 does not have a link between them, locations p2_1 and s3 does not have a link between them, locations p2_1 and s3 does not have a path between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p0_2 does not have a link between them, locations p3_0 and p0_3 does not have a path between them, locations p3_0 and p1_3 does not have a link between them, locations p3_0 and p2_1 does not have a link between them, locations p3_0 and p2_1 does not have a path between them, locations p3_0 and s2 does not have a link between them, locations p3_0 and s3 does not have a link between them, locations s0 and p0_2 does not have a link between them, locations s0 and p0_3 does not have a link between them, locations s0 and p1_3 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_1 does not have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 does not have a path between them, locations s0 and s3 have a link between them, locations s1 and p0_3 does not have a path between them, locations s1 and s0 does not have a path between them, locations s2 and p0_2 does not have a link between them, locations s2 and p2_1 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 does not have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 does not have a path between them, locations s2 and s3 have a link between them, locations s3 and p0_2 does not have a link between them, locations s3 and p0_2 does not have a path between them, locations s3 and p0_3 have a path between them, locations s3 and p1_3 does not have a link between them, locations s3 and p1_3 have a path between them, locations s3 and p2_1 does not have a path between them, locations s3 and s0 does not have a path between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is not at location p0_2, package1 is not at location p1_3, package1 is not at location p3_0, package1 is not at location s0, package1 is not at location s2, package1 is not currently at location p0_1, package1 is not currently at location p2_1, package1 is not currently at location s3, package1 is not in truck1, package1 is not located in truck2, package1 is not present at location p0_3, package1 is present at location s1, package2 is at location s2, package2 is not at location p0_2, package2 is not currently at location p0_1, package2 is not currently at location p1_3, package2 is not currently at location p2_1, package2 is not currently at location s0, package2 is not currently at location s3, package2 is not placed in truck1, package2 is not placed in truck2, package2 is not present at location p0_3, package2 is not present at location p3_0, package2 is not present at location s1, package3 is located in truck1, package3 is not at location p0_1, package3 is not at location p0_2, package3 is not at location s0, package3 is not at location s2, package3 is not currently at location p0_3, package3 is not currently at location s1, package3 is not placed in truck2, package3 is not present at location p1_3, package3 is not present at location p2_1, package3 is not present at location p3_0, package3 is not present at location s3, package4 is at location s2, package4 is not at location p0_3, package4 is not at location p1_3, package4 is not at location p2_1, package4 is not at location s3, package4 is not currently at location p0_1, package4 is not currently at location p0_2, package4 is not currently at location p3_0, package4 is not currently at location s0, package4 is not currently at location s1, package4 is not placed in truck1, package4 is not placed in truck2, there doesn't exist a link between the locations p0_1 and p0_2, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p0_2 and s0, there doesn't exist a link between the locations p0_2 and s1, there doesn't exist a link between the locations p0_2 and s3, there doesn't exist a link between the locations p0_3 and p2_1, there doesn't exist a link between the locations p0_3 and s2, there doesn't exist a link between the locations p0_3 and s3, there doesn't exist a link between the locations p1_3 and p0_2, there doesn't exist a link between the locations p1_3 and p0_3, there doesn't exist a link between the locations p1_3 and p3_0, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_1 and p0_3, there doesn't exist a link between the locations p2_1 and s2, there doesn't exist a link between the locations p3_0 and p0_3, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s1 and p0_2, there doesn't exist a link between the locations s1 and p0_3, there doesn't exist a link between the locations s1 and p2_1, there doesn't exist a link between the locations s2 and p0_3, there doesn't exist a link between the locations s2 and p1_3, there doesn't exist a link between the locations s3 and p2_1, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and p0_2, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_2 and p0_3, there doesn't exist a path between the locations p0_2 and p1_3, there doesn't exist a path between the locations p2_1 and p0_1, there doesn't exist a path between the locations p2_1 and p1_3, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p0_2, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s1 and p0_2, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p0_3, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s3 and p0_1, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_1 and s2, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s1 and p2_1, there exists a path between the locations s2 and p0_2, there exists a path between the locations s2 and p2_1, there is a link between location s1 and location s3, there is a path between location p0_1 and location s0, there is a path between location p0_2 and location s2, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s1, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_2, there is no link between location p0_1 and location s0, there is no link between location p0_1 and location s2, there is no link between location p0_2 and location p1_3, there is no link between location p0_2 and location p3_0, there is no link between location p0_3 and location p0_1, there is no link between location p0_3 and location p0_2, there is no link between location p0_3 and location p3_0, there is no link between location p0_3 and location s0, there is no link between location p1_3 and location s2, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location s0, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s1, there is no link between location s1 and location p0_1, there is no link between location s1 and location p1_3, there is no link between location s1 and location p3_0, there is no link between location s2 and location p0_1, there is no link between location s2 and location p3_0, there is no link between location s3 and location p0_1, there is no link between location s3 and location p0_3, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location s3, there is no path between location p0_2 and location p3_0, there is no path between location p0_3 and location p0_1, there is no path between location p0_3 and location p1_3, there is no path between location p0_3 and location p2_1, there is no path between location p0_3 and location p3_0, there is no path between location p1_3 and location p0_2, there is no path between location p1_3 and location s2, there is no path between location p2_1 and location p0_2, there is no path between location p2_1 and location p0_3, there is no path between location p2_1 and location p3_0, there is no path between location p2_1 and location s0, there is no path between location p3_0 and location p1_3, there is no path between location p3_0 and location s0, there is no path between location p3_0 and location s1, there is no path between location p3_0 and location s3, there is no path between location s0 and location p3_0, there is no path between location s0 and location s1, there is no path between location s1 and location s2, there is no path between location s2 and location p1_3, there is no path between location s3 and location p3_0, there is no path between location s3 and location s1, there is no path between location s3 and location s2, truck1 is empty, truck1 is not at location p0_3, truck1 is not being driven by driver2, truck1 is not currently at location p1_3, truck1 is not currently at location p2_1, truck1 is not currently at location s1, truck1 is not present at location p0_1, truck1 is not present at location p0_2, truck1 is not present at location p3_0, truck1 is not present at location s2, truck1 is not present at location s3, truck1 is present at location s0, truck2 contains nothing, truck2 is not at location p0_1, truck2 is not at location p0_2, truck2 is not at location p3_0, truck2 is not at location s2, truck2 is not being driven by driver2, truck2 is not currently at location p2_1, truck2 is not present at location p0_3, truck2 is not present at location p1_3, truck2 is not present at location s1, truck2 is not present at location s3 and truck2 is present at location s0", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: package3 is loaded into truck1 at location s0 to achieve the current state. In this state, if driver1 moves from location s2 to location p0_2, what are all the valid properties of the resulting state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected, and s3 and s2 are also linked. Package1 is at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. There are links between locations s0 and s2, s0 and s3, and s3 and s0. Paths exist between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently at location s0 and is empty, while truck2 is also empty and present at location s0."}
{"question_id": "86e04fca-5972-456b-b4e6-432312402cdf", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks to location s0 from location p0_2, truck1 is boarded by driver1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 disembarks from truck1 at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to location s0 and driver2 walks from location s2 to location p0_2 to reach the current state. In this state, if driver2 walks from location p0_2 to s0, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "driver1 is currently at location s0, driver1 is not at location p0_1, driver1 is not at location p0_2, driver1 is not at location s1, driver1 is not currently at location p0_3, driver1 is not currently at location p2_1, driver1 is not currently at location p3_0, driver1 is not currently at location s3, driver1 is not driving truck1, driver1 is not driving truck2, driver1 is not present at location p1_3, driver1 is not present at location s2, driver2 is currently at location s0, driver2 is not at location p0_1, driver2 is not at location s1, driver2 is not at location s2, driver2 is not at location s3, driver2 is not currently at location p1_3, driver2 is not currently at location p3_0, driver2 is not driving truck1 currently, driver2 is not present at location p0_2, driver2 is not present at location p0_3, driver2 is not present at location p2_1, locations p0_1 and p0_2 does not have a link between them, locations p0_1 and p0_3 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s1 does not have a link between them, locations p0_1 and s3 does not have a path between them, locations p0_2 and p0_1 does not have a link between them, locations p0_2 and p0_3 does not have a link between them, locations p0_2 and p0_3 does not have a path between them, locations p0_2 and p1_3 does not have a path between them, locations p0_2 and p2_1 does not have a path between them, locations p0_2 and s1 does not have a link between them, locations p0_3 and p0_1 does not have a link between them, locations p0_3 and p0_2 does not have a link between them, locations p0_3 and p0_2 does not have a path between them, locations p0_3 and s0 have a path between them, locations p0_3 and s1 does not have a link between them, locations p0_3 and s2 does not have a link between them, locations p0_3 and s3 have a path between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p2_1 and p0_1 does not have a path between them, locations p2_1 and p0_2 does not have a path between them, locations p2_1 and p0_3 does not have a path between them, locations p2_1 and p1_3 does not have a link between them, locations p2_1 and p1_3 does not have a path between them, locations p2_1 and s2 does not have a link between them, locations p2_1 and s2 have a path between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and s0 does not have a path between them, locations s0 and p0_3 does not have a link between them, locations s0 and p0_3 have a path between them, locations s0 and p1_3 does not have a link between them, locations s0 and p2_1 does not have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p0_2 does not have a link between them, locations s1 and s2 does not have a path between them, locations s1 and s3 have a link between them, locations s2 and p0_1 does not have a path between them, locations s2 and p0_2 does not have a link between them, locations s2 and p0_2 have a path between them, locations s2 and p0_3 does not have a link between them, locations s2 and p2_1 does not have a link between them, locations s2 and p2_1 have a path between them, locations s2 and p3_0 does not have a link between them, locations s2 and s1 does not have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 does not have a path between them, locations s2 and s3 have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p0_2 does not have a link between them, locations s3 and p0_2 does not have a path between them, locations s3 and p1_3 does not have a link between them, locations s3 and p2_1 does not have a path between them, locations s3 and p3_0 does not have a link between them, locations s3 and s0 does not have a path between them, locations s3 and s2 does not have a path between them, package1 is at location s1, package1 is not at location p0_2, package1 is not at location p1_3, package1 is not at location p2_1, package1 is not at location s2, package1 is not at location s3, package1 is not currently at location p3_0, package1 is not currently at location s0, package1 is not in truck1, package1 is not located in truck2, package1 is not present at location p0_1, package1 is not present at location p0_3, package2 is currently at location s2, package2 is not at location p0_3, package2 is not currently at location p0_1, package2 is not currently at location p1_3, package2 is not currently at location p2_1, package2 is not currently at location p3_0, package2 is not currently at location s0, package2 is not currently at location s1, package2 is not in truck2, package2 is not located in truck1, package2 is not present at location p0_2, package2 is not present at location s3, package3 is currently at location s3, package3 is not at location s0, package3 is not at location s1, package3 is not currently at location p1_3, package3 is not currently at location p3_0, package3 is not in truck2, package3 is not located in truck1, package3 is not present at location p0_1, package3 is not present at location p0_2, package3 is not present at location p0_3, package3 is not present at location p2_1, package3 is not present at location s2, package4 is currently at location s2, package4 is not at location p1_3, package4 is not at location p2_1, package4 is not at location s0, package4 is not at location s1, package4 is not currently at location p0_1, package4 is not currently at location p0_2, package4 is not currently at location p0_3, package4 is not in truck1, package4 is not in truck2, package4 is not present at location p3_0, package4 is not present at location s3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p0_2 and p1_3, there doesn't exist a link between the locations p0_2 and p2_1, there doesn't exist a link between the locations p0_2 and p3_0, there doesn't exist a link between the locations p0_2 and s2, there doesn't exist a link between the locations p0_2 and s3, there doesn't exist a link between the locations p0_3 and p1_3, there doesn't exist a link between the locations p0_3 and p2_1, there doesn't exist a link between the locations p0_3 and p3_0, there doesn't exist a link between the locations p0_3 and s0, there doesn't exist a link between the locations p1_3 and p0_3, there doesn't exist a link between the locations p1_3 and p2_1, there doesn't exist a link between the locations p2_1 and p0_3, there doesn't exist a link between the locations p2_1 and p3_0, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p2_1 and s3, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p0_2, there doesn't exist a link between the locations p3_0 and p0_3, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations p3_0 and s0, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations s0 and p0_2, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p0_3, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p2_1, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s2 and p1_3, there doesn't exist a link between the locations s3 and p2_1, there doesn't exist a path between the locations p0_1 and p0_2, there doesn't exist a path between the locations p0_1 and p0_3, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_2 and p0_1, there doesn't exist a path between the locations p0_2 and s1, there doesn't exist a path between the locations p0_2 and s3, there doesn't exist a path between the locations p0_3 and p1_3, there doesn't exist a path between the locations p0_3 and p2_1, there doesn't exist a path between the locations p0_3 and p3_0, there doesn't exist a path between the locations p0_3 and s1, there doesn't exist a path between the locations p0_3 and s2, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p0_2, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_1 and s0, there doesn't exist a path between the locations p2_1 and s3, there doesn't exist a path between the locations p3_0 and p2_1, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations p3_0 and s3, there doesn't exist a path between the locations s0 and p1_3, there doesn't exist a path between the locations s0 and p3_0, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s2 and p0_3, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p3_0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s1 and p2_1, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_2 and location s0, there is a path between location p1_3 and location s1, there is a path between location s0 and location p0_1, there is a path between location s3 and location p1_3, there is no link between location p0_1 and location p1_3, there is no link between location p0_1 and location p2_1, there is no link between location p0_1 and location s0, there is no link between location p0_2 and location s0, there is no link between location p0_3 and location s3, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location p0_2, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s0, there is no link between location p1_3 and location s2, there is no link between location p1_3 and location s3, there is no link between location p2_1 and location p0_1, there is no link between location p2_1 and location p0_2, there is no link between location p2_1 and location s0, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p0_1, there is no link between location s2 and location p0_1, there is no link between location s3 and location p0_3, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p2_1, there is no path between location p0_2 and location p3_0, there is no path between location p0_3 and location p0_1, there is no path between location p1_3 and location p0_3, there is no path between location p1_3 and location p2_1, there is no path between location p1_3 and location s2, there is no path between location p2_1 and location p3_0, there is no path between location p3_0 and location p0_2, there is no path between location p3_0 and location p0_3, there is no path between location p3_0 and location s2, there is no path between location s0 and location s2, there is no path between location s1 and location p0_2, there is no path between location s1 and location p0_3, there is no path between location s1 and location p3_0, there is no path between location s2 and location p3_0, there is no path between location s2 and location s0, there is no path between location s3 and location s1, truck1 is at location s3, truck1 is empty, truck1 is not at location p0_3, truck1 is not at location p2_1, truck1 is not at location s1, truck1 is not at location s2, truck1 is not currently at location p0_1, truck1 is not currently at location p0_2, truck1 is not currently at location p1_3, truck1 is not currently at location p3_0, truck1 is not currently at location s0, truck2 contains nothing, truck2 is at location s0, truck2 is not at location p0_2, truck2 is not at location p0_3, truck2 is not at location s1, truck2 is not at location s3, truck2 is not being driven by driver2, truck2 is not currently at location p0_1, truck2 is not currently at location p1_3, truck2 is not currently at location p3_0, truck2 is not present at location p2_1 and truck2 is not present at location s2", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to location p0_2, then proceeds to location s0 from p0_2, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, walks from location s3 to location p0_3, and then returns to location s0 from p0_3, while driver2 moves from location s2 to location p0_2 to reach the current state. In this state, if driver2 walks from location p0_2 to s0, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link is present between s0 and s2, s0 and s3, and s3 and s0. Paths exist between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links are present between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, while truck2 is also empty and present at s0."}
