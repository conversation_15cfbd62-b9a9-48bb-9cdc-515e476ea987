{"question_id": "c37600e2-7e37-45fc-8f09-6345e28ccbe0", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "160", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, what is the total count of actions that can be executed and those that cannot? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "4e98a463-bae0-4047-8952-46dca292ac56", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, rgripper1 of robot1 drops ball6 in room3, ball5 is picked from room3 with rgripper1 by robot1 and robot1 moves from room3 to room6 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "8", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, where it drops ball7 using lgripper1, and proceeds to room1. In room1, robot1 picks up ball1 with lgripper1 and ball3 with rgripper1, then moves to room5, where it drops ball1 with lgripper1 and ball3 with rgripper1. Next, robot1 moves to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, and then returns to room1, where it drops ball4 with rgripper1. Robot1 then picks up ball6 with rgripper1 in room1, moves to room3, drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves to room6 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "1865de2b-4349-4bb4-82c1-2df3e18f8194", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7, from room3, robot2 moves to room2, in room2, robot2's rgripper2 drops ball7, from room2, robot2's rgripper2 picks up ball3, robot2 moves to room1 from room2, in room1, robot2's lgripper2 drops ball4, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, ball6 is picked from room1 with rgripper2 by robot2, from room1, robot2 moves to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "12", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, where it uses its left gripper (lgripper2) to grasp ball1 and its right gripper (rgripper2) to pick up ball2. Then, robot2 returns to room2, and in room2, it releases ball1 with lgripper2 and drops ball2 using rgripper2. Next, robot2 moves back to room3, where it uses lgripper2 to pick up ball4 and rgripper2 to grasp ball7. Robot2 then proceeds to room2, where it drops ball7 with rgripper2 and picks up ball3 using the same gripper. Subsequently, robot2 moves to room1, where it releases ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 returns to room2 and drops ball5 with lgripper2 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is in room2, the left gripper of robot2 is free and the right gripper of robot2 is also available."}
{"question_id": "fc996687-5bc9-40e3-966c-69b3bd6a0c65", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "8", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, robot1 uses lgripper1 to grasp ball1 in room1, then robot1's rgripper1 picks up ball3 in room1, robot1 proceeds to move from room1 to room5, where robot1's rgripper1 releases ball3, robot1 then moves from room5 to room2, in room2, robot1's rgripper1 grasps ball4, robot1 returns to room1 from room2, and in room1, robot1's rgripper1 drops ball4 and then picks up ball6 to attain the current state. In this state, what is the total count of valid state properties that do not involve negations? Provide the answer as an integer, or None if there are no such properties.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "fd4eb951-caec-40b5-86ee-28d560e5efef", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7 and from room3, robot2 moves to room2 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "336", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: robot2 moves from room2 to room3, then it uses lgripper2 to pick up ball1 in room3 and its rgripper2 to pick up ball2 in the same room. Next, robot2 returns to room2 from room3, drops ball1 using lgripper2, and drops ball2 using rgripper2 in room2. After that, robot2 moves back to room3, picks up ball4 with lgripper2, and then picks up ball7 with rgripper2 in room3 before moving back to room2 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is situated in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
{"question_id": "56317514-4cd1-44a2-a168-fe8525887c96", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "6", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: the left gripper of robot1 picks up ball7 in room4 to achieve the current state. In this state, what is the total number of actions that can be executed? Provide the answer as an integer, or write None if there are no executable actions.", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "cba3108b-3a98-49c3-9415-1865c7d89296", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, in room3, robot1's lgripper1 drops ball1, robot1 moves from room3 to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball4 in room2, rgripper1 of robot1 picks up ball5 in room2, robot1 moves from room2 to room5, lgripper1 of robot1 drops ball4 in room5, robot1 moves from room5 to room1, ball3 is picked from room1 with lgripper1 by robot1, ball5 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room5 from room1 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "133", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then its left gripper (lgripper1) grasps ball1 in room1. Next, robot1 moves to room2, where its right gripper (rgripper1) picks up ball2. From room2, robot1 proceeds to room3, where it releases ball1 using lgripper1. Robot1 then moves to room4 and drops ball2 using rgripper1. Subsequently, robot1 returns to room2 from room4, and its lgripper1 picks up ball4, while rgripper1 picks up ball5. Robot1 then moves to room5, where it drops ball4 using lgripper1. After that, robot1 goes back to room1, where it picks up ball3 with lgripper1 and drops ball5 with rgripper1. Additionally, rgripper1 of robot1 picks up ball6 in room1. Finally, robot1 moves to room5 from room1 and drops ball3 using lgripper1, resulting in the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
{"question_id": "bd3ffc4e-f3b8-4fab-9f0d-35707088963a", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, rgripper1 of robot1 drops ball6 in room3, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "191", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves to room5, where it drops ball7 using lgripper1, and then proceeds to room1. In room1, robot1's lgripper1 picks up ball1, while its right gripper (rgripper1) picks up ball3. Robot1 then moves to room5, where it drops ball1 using lgripper1 and ball3 using rgripper1. Next, robot1 moves to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, and then returns to room1. In room1, robot1 drops ball4 using rgripper1, picks up ball6 with rgripper1, and then moves to room3. In room3, robot1 drops ball6 using rgripper1 and picks up ball5 using rgripper1 before moving to room6 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "8631fe66-aff0-46d8-9ee2-0bf6ceaf892b", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, from room2, robot2's rgripper2 picks up ball3, robot2 moves from room2 to room1, ball4 is dropped in room1 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, ball3 is dropped in room1 with rgripper2 by robot2, ball6 is picked from room1 with rgripper2 by robot2, from room1, robot2 moves to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "17", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 transitions from room2 to room3, where it uses its left gripper (lgripper2) to pick up ball1 and its right gripper (rgripper2) to pick up ball2. Then, robot2 moves back to room2, and in room2, it drops ball1 with lgripper2 and ball2 with rgripper2. Next, robot2 returns to room3, where it uses lgripper2 to pick up ball4 and rgripper2 to pick up ball7. Robot2 then moves back to room2, where it drops ball7 with rgripper2. In room2, robot2's rgripper2 picks up ball3, and then robot2 moves to room1. In room1, robot2 drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves back to room2, where it drops ball5 with lgripper2, reaching the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is in room2, the left gripper of robot2 is free and the right gripper of robot2 is also available."}
{"question_id": "00a8ae8a-1662-43ca-8ca1-2477718b586f", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "150", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, what is the total count of actions that cannot be executed? Write as an integer. Write None if there are no such actions.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "526ee9c3-9dc2-4fdc-81ec-9661dc938f94", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, ball2 is picked from room2 with lgripper1 by robot1, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball4 in room2, from room2, robot1's rgripper1 picks up ball5, from room2, robot1 moves to room5, lgripper1 of robot1 drops ball4 in room5, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball3 in room1, in room1, robot1's rgripper1 drops ball5, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room5 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "5", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 relocates from room4 to room1, then its left gripper (lgripper1) picks up ball1 in room1. Next, robot1 moves to room2, where its right gripper (rgripper1) picks up ball2. Robot1 then proceeds to room3, and in room3, it moves back to room4. In room4, robot1's rgripper1 releases ball2. From room4, robot1 returns to room2, where its lgripper1 picks up ball4, and its rgripper1 picks up ball5. Robot1 then moves to room5, where it drops ball4 using its lgripper1. Next, robot1 moves to room1, where its lgripper1 picks up ball3, and its rgripper1 drops ball5. Then, robot1's rgripper1 picks up ball6 in room1. Finally, robot1 moves to room5 and drops ball3 using its lgripper1 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
{"question_id": "8d327427-ec42-406c-9d91-0bcd040e9ae8", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball7 in room3, from room3, robot1 moves to room4, lgripper1 of robot1 drops ball1 in room4, ball7 is dropped in room4 with rgripper1 by robot1, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball2 in room2 and ball5 is picked from room2 with rgripper1 by robot1 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "48", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 moves to room5, drops ball3 in room5 using rgripper1, and then proceeds to room2. In room2, rgripper1 of robot1 picks up ball4, and robot1 moves back to room1, where it drops ball4 using rgripper1. From room1, robot1's rgripper1 picks up ball6, and then robot1 moves to room3, dropping ball6 in room3 with rgripper1. In room3, rgripper1 of robot1 picks up ball7, and then robot1 moves to room4, where it drops ball1 using lgripper1 and ball7 using rgripper1. Finally, robot1 moves to room2, picks up ball2 with lgripper1, and ball5 with rgripper1, resulting in the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "dcd48028-0bba-453d-9051-0228b687a040", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, in room1, robot2's lgripper2 drops ball6 and from room3, robot2 moves to room2 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "8", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot2 relocates from room2 to room3, then robot2's left gripper (lgripper2) picks up ball1 in room3, and its right gripper (rgripper2) picks up ball2 in room3, followed by robot2 moving back to room2, where lgripper2 drops ball1 and rgripper2 drops ball2, then robot2 returns to room3, picks up ball4 with lgripper2, drops ball6 with lgripper2 in room1, and finally moves from room3 to room2 to reach the current state. How many actions occur before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
{"question_id": "f6da9ba7-3358-42d4-8d76-76c216b9a3fe", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "9", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1's lgripper1 in room4 picks up ball7 to achieve the current state. In this state, what is the total count of valid state properties that do not include negations? Provide the answer as an integer, or None if there are no such properties.", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "52c5003d-f8fb-46ad-9f9a-090436ccb1aa", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "14", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following steps are taken: robot2 transitions from room2 to room3 to achieve the current state. In this state, what is the total count of actions that can be executed? Provide the answer as an integer, or None if there are no executable actions.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is in room2, the left gripper of robot2 is free and the right gripper of robot2 is also available."}
{"question_id": "4eadd230-6365-4b59-b9d2-8f537963823e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, rgripper1 of robot1 picks up ball5 in room3 and from room3, robot1 moves to room6 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "64", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and picks up ball3 with rgripper1. Robot1 then moves from room1 to room5, drops ball1 and ball3 in room5 using lgripper1 and rgripper1 respectively, and proceeds to room2. In room2, robot1 picks up ball2 with lgripper1 and ball4 with rgripper1, then moves to room1, drops ball4 in room1 using rgripper1, picks up ball6 with rgripper1, and moves to room3. In room3, robot1 drops ball6 using rgripper1, picks up ball5 with rgripper1, and finally moves to room6 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "38eabea2-65da-4ece-8fec-cbf9144fc6bd", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3, from room3, robot2 moves to room2, ball7 is dropped in room2 with rgripper2 by robot2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, lgripper2 of robot2 drops ball4 in room1, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, ball4 is picked from room1 with rgripper1 by robot1, from room1, robot2 moves to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "16", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 starts in room2 and moves to room3, where it uses its left gripper (lgripper2) to pick up ball1 and its right gripper (rgripper2) to pick up ball2. Then, robot2 returns to room2, drops ball1 with lgripper2 and ball2 with rgripper2. Next, robot2 moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and then returns to room2, where it drops ball7 with rgripper2 and picks up ball3 with rgripper2. After that, robot2 moves to room1, drops ball4 with lgripper2, picks up ball5 with lgripper2, and drops ball3 with rgripper2. Meanwhile, robot1 picks up ball4 with its left gripper (lgripper1) in room1. Finally, robot2 moves back to room2 and drops ball5 with lgripper2 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is positioned at room3, ball5 is found in room1, ball6 is situated in room1, ball7 is also in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is situated in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
