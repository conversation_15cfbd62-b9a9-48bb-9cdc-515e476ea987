{"id": -7059830742002094645, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: dave, carol, alice, vic, heidi, xena, zoe, and michelle. There are 8 items/roles: guitar, whale, slinky, iceskates, frisbee, zebra, quadcopter, and necklace. Currently, xena is assigned guitar, dave is assigned quadcopter, alice is assigned zebra, michelle is assigned necklace, vic is assigned frisbee, heidi is assigned iceskates, carol is assigned slinky, and zoe is assigned whale. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap michelle michelle slinky necklace)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice zebra) (assigned carol slinky) (assigned dave quadcopter) (assigned heidi iceskates) (assigned michelle necklace) (assigned vic frisbee) (assigned xena guitar) (assigned zoe whale) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
{"id": 9101081336570329712, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, bob, vic, quentin, xena, and liam. There are 6 items/roles: wrench, nibbler, sander, ratchet, knead, and pliers. Currently, bob is assigned knead, quentin is assigned nibbler, vic is assigned ratchet, frank is assigned sander, xena is assigned wrench, and liam is assigned pliers. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap xena bob knead knead)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob knead) (assigned frank sander) (assigned liam pliers) (assigned quentin nibbler) (assigned vic ratchet) (assigned xena wrench) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": 716740551244997875, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: dave, bob, alice, kevin, heidi, xena, and ted. There are 7 items/roles: yam, parsnip, mushroom, quince, ulluco, leek, and valerian. Currently, bob is assigned parsnip, heidi is assigned yam, kevin is assigned quince, xena is assigned ulluco, alice is assigned valerian, ted is assigned leek, and dave is assigned mushroom. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap heidi heidi parsnip yam)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice valerian) (assigned bob parsnip) (assigned dave mushroom) (assigned heidi yam) (assigned kevin quince) (assigned ted leek) (assigned xena ulluco) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": 761303240829628866, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: dave, carol, alice, vic, heidi, xena, zoe, and michelle. There are 8 items/roles: guitar, whale, slinky, iceskates, frisbee, zebra, quadcopter, and necklace. Currently, carol is assigned guitar, alice is assigned zebra, michelle is assigned whale, xena is assigned necklace, heidi is assigned frisbee, dave is assigned iceskates, vic is assigned quadcopter, and zoe is assigned slinky. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap heidi zoe quadcopter quadcopter)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice zebra) (assigned carol guitar) (assigned dave iceskates) (assigned heidi frisbee) (assigned michelle whale) (assigned vic quadcopter) (assigned xena necklace) (assigned zoe slinky) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
{"id": 530851363687563216, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, bob, vic, quentin, xena, and liam. There are 6 items/roles: wrench, nibbler, sander, ratchet, knead, and pliers. Currently, quentin is assigned sander, vic is assigned ratchet, bob is assigned pliers, frank is assigned knead, liam is assigned nibbler, and xena is assigned wrench. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap xena vic knead knead)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob pliers) (assigned frank knead) (assigned liam nibbler) (assigned quentin sander) (assigned vic ratchet) (assigned xena wrench) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": -2333926612469099685, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, bob, vic, quentin, xena, and liam. There are 6 items/roles: wrench, nibbler, sander, ratchet, knead, and pliers. Currently, xena is assigned pliers, frank is assigned sander, liam is assigned nibbler, bob is assigned wrench, quentin is assigned ratchet, and vic is assigned knead. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap xena xena ratchet nibbler)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob wrench) (assigned frank sander) (assigned liam nibbler) (assigned quentin ratchet) (assigned vic knead) (assigned xena pliers) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": -5123401370893052017, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, bob, vic, quentin, xena, and liam. There are 6 items/roles: wrench, nibbler, sander, ratchet, knead, and pliers. Currently, xena is assigned ratchet, bob is assigned sander, frank is assigned nibbler, vic is assigned pliers, quentin is assigned knead, and liam is assigned wrench. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap quentin quentin nibbler nibbler)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob sander) (assigned frank nibbler) (assigned liam wrench) (assigned quentin knead) (assigned vic pliers) (assigned xena ratchet) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": -870008881726412338, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: dave, bob, alice, kevin, heidi, xena, and ted. There are 7 items/roles: yam, parsnip, mushroom, quince, ulluco, leek, and valerian. Currently, bob is assigned parsnip, heidi is assigned mushroom, dave is assigned valerian, kevin is assigned leek, alice is assigned ulluco, xena is assigned quince, and ted is assigned yam. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap heidi heidi mushroom leek)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice ulluco) (assigned bob parsnip) (assigned dave valerian) (assigned heidi mushroom) (assigned kevin leek) (assigned ted yam) (assigned xena quince) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": -4624547272611540014, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, vic, steve, and bob. There are 4 items/roles: book03, book02, book04, and book01. Currently, vic is assigned book02, steve is assigned book03, zoe is assigned book04, and bob is assigned book01. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap bob bob book03 book04)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob steve vic zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned bob book01) (assigned steve book03) (assigned vic book02) (assigned zoe book04) (not-eq bob steve) (not-eq bob vic) (not-eq bob zoe) (not-eq book01 book02) (not-eq book01 book03) (not-eq book01 book04) (not-eq book02 book01) (not-eq book02 book03) (not-eq book02 book04) (not-eq book03 book01) (not-eq book03 book02) (not-eq book03 book04) (not-eq book04 book01) (not-eq book04 book02) (not-eq book04 book03) (not-eq steve bob) (not-eq steve vic) (not-eq steve zoe) (not-eq vic bob) (not-eq vic steve) (not-eq vic zoe) (not-eq zoe bob) (not-eq zoe steve) (not-eq zoe vic))\n    (:goal (and (assigned bob book02) (assigned steve book03) (assigned vic book04) (assigned zoe book01)))\n)"}
{"id": -3234309004360261988, "group": "reachable_action_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: dave, bob, alice, kevin, heidi, xena, and ted. There are 7 items/roles: yam, parsnip, mushroom, quince, ulluco, leek, and valerian. Currently, bob is assigned leek, dave is assigned ulluco, heidi is assigned mushroom, kevin is assigned quince, alice is assigned valerian, xena is assigned yam, and ted is assigned parsnip. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(swap dave dave leek mushroom)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice valerian) (assigned bob leek) (assigned dave ulluco) (assigned heidi mushroom) (assigned kevin quince) (assigned ted parsnip) (assigned xena yam) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
