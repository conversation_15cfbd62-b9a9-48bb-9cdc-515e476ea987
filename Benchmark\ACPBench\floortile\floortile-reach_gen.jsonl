{"id": 8002028155436461638, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_20 is to the right of tile_19, tile_15 is to the right of tile_14, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, and tile_3 is to the right of tile_2. Further, tile_1 is down from tile_6, tile_12 is down from tile_17, tile_8 is down from tile_13, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_4 is down from tile_9, and tile_13 is down from tile_18 Currently, robot robot3 is at tile_3 and holding color white, robot robot1 is at tile_8 and holding color black, and robot robot2 is at tile_2 and holding color black; tile_5, tile_1, and tile_4 are clear; tile_20 is painted white, tile_16 is painted white, tile_15 is painted black, tile_10 is painted white, tile_17 is painted black, tile_19 is painted black, tile_12 is painted white, tile_14 is painted white, tile_7 is painted black, tile_9 is painted black, tile_11 is painted black, tile_13 is painted black, tile_6 is painted white, and tile_18 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["robot-at robot3 tile_6"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_4) (clear tile_5) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (painted tile_7 black) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_2) (robot-at robot3 tile_3) (robot-has robot1 black) (robot-has robot2 black) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 5190380624848000947, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_6 is to the right of tile_5, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_9 is down from tile_12, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_1 and holding color black and robot robot2 is at tile_4 and holding color white; tile_3, tile_2, tile_7, and tile_10 are clear; tile_12 is painted white, tile_11 is painted black, tile_5 is painted black, tile_6 is painted white, tile_8 is painted white, and tile_9 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_10) (clear tile_2) (clear tile_3) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_11 black) (painted tile_12 white) (painted tile_5 black) (painted tile_6 white) (painted tile_8 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_4) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 6605790683535915425, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_20 is to the right of tile_19, tile_15 is to the right of tile_14, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, and tile_3 is to the right of tile_2. Further, tile_1 is down from tile_6, tile_12 is down from tile_17, tile_8 is down from tile_13, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_4 is down from tile_9, and tile_13 is down from tile_18 Currently, robot robot3 is at tile_2 and holding color white, robot robot2 is at tile_4 and holding color black, and robot robot1 is at tile_1 and holding color white; tile_3, tile_5, and tile_6 are clear; tile_20 is painted white, tile_8 is painted white, tile_16 is painted white, tile_15 is painted black, tile_10 is painted white, tile_17 is painted black, tile_19 is painted black, tile_12 is painted white, tile_14 is painted white, tile_7 is painted black, tile_9 is painted black, tile_11 is painted black, tile_13 is painted black, and tile_18 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["robot-at robot3 tile_6"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_3) (clear tile_5) (clear tile_6) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_4) (robot-at robot3 tile_2) (robot-has robot1 white) (robot-has robot2 black) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 3271866244074880065, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_2 and holding color black and robot robot2 is at tile_3 and holding color white; tile_6, tile_5, and tile_1 are clear; tile_4 is painted white, tile_8 is painted white, tile_7 is painted black, and tile_9 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_5) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_4 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_3) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -9057194255863728066, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_6 is to the right of tile_5, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_9 is down from tile_12, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_4 and holding color black and robot robot2 is at tile_2 and holding color black; tile_3, tile_8, tile_6, tile_5, tile_1, and tile_9 are clear; tile_12 is painted white, tile_11 is painted black, tile_7 is painted black, and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_7 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_2) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -8382026818826130576, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_20 is to the right of tile_19, tile_15 is to the right of tile_14, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, and tile_3 is to the right of tile_2. Further, tile_1 is down from tile_6, tile_12 is down from tile_17, tile_8 is down from tile_13, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_4 is down from tile_9, and tile_13 is down from tile_18 Currently, robot robot3 is at tile_15 and holding color white, robot robot2 is at tile_3 and holding color black, and robot robot1 is at tile_2 and holding color white; tile_11, tile_18, tile_14, tile_10, tile_5, tile_6, tile_1, tile_8, tile_19, tile_12, tile_9, tile_17, tile_13, tile_4, and tile_7 are clear; tile_20 is painted white and tile_16 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["robot-at robot3 tile_16"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_14) (clear tile_17) (clear tile_18) (clear tile_19) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_16 white) (painted tile_20 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_3) (robot-at robot3 tile_15) (robot-has robot1 white) (robot-has robot2 black) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 1172673585982560200, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_6 is to the right of tile_5, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_9 is down from tile_12, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_2 and holding color white and robot robot2 is at tile_5 and holding color white; tile_3, tile_6, tile_8, tile_1, tile_4, tile_7, and tile_9 are clear; tile_12 is painted white, tile_11 is painted black, and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_6) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_5) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -728972417539231201, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_2 and holding color black and robot robot2 is at tile_3 and holding color white; is clear; tile_4 is painted white, tile_5 is painted black, tile_6 is painted white, tile_8 is painted white, tile_7 is painted black, and tile_9 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_3) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 8668529925224873233, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_6 is to the right of tile_5, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_9 is down from tile_12, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_7 and holding color white and robot robot2 is at tile_4 and holding color black; tile_11, tile_3, tile_12, tile_5, tile_6, tile_1, tile_8, tile_2, tile_9, and tile_10 are clear. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_7) (robot-at robot2 tile_4) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -5887186708917212755, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_20 is to the right of tile_19, tile_15 is to the right of tile_14, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, and tile_3 is to the right of tile_2. Further, tile_1 is down from tile_6, tile_12 is down from tile_17, tile_8 is down from tile_13, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_4 is down from tile_9, and tile_13 is down from tile_18 Currently, robot robot3 is at tile_4 and holding color black, robot robot2 is at tile_8 and holding color white, and robot robot1 is at tile_2 and holding color white; tile_11, tile_3, tile_18, tile_5, tile_6, tile_1, tile_12, tile_9, tile_17, tile_13, and tile_7 are clear; tile_20 is painted white, tile_16 is painted white, tile_15 is painted black, tile_10 is painted white, tile_19 is painted black, and tile_14 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["robot-at robot3 tile_16"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_17) (clear tile_18) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_19 black) (painted tile_20 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_8) (robot-at robot3 tile_4) (robot-has robot1 white) (robot-has robot2 white) (robot-has robot3 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -8682376828094741291, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 \nCurrently, robot robot1 is at tile_2 and holding color white and robot robot2 is at tile_3 and holding color white; tile_8, tile_6, tile_5, and tile_1 are clear; tile_4 is painted white, tile_7 is painted black, and tile_9 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_5) (clear tile_6) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_4 white) (painted tile_7 black) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_3) (robot-has robot1 white) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 7344892383778863426, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 16 tiles and 2 robots. \nThe tiles locations are: tile_10 is to the right of tile_9, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_14 is to the right of tile_13, and tile_3 is to the right of tile_2. Further, tile_4 is down from tile_8, tile_3 is down from tile_7, tile_7 is down from tile_11, tile_10 is down from tile_14, tile_8 is down from tile_12, tile_12 is down from tile_16, tile_9 is down from tile_13, tile_11 is down from tile_15, tile_5 is down from tile_9, tile_2 is down from tile_6, tile_1 is down from tile_5, and tile_6 is down from tile_10 \nCurrently, robot robot1 is at tile_7 and holding color white and robot robot2 is at tile_10 and holding color black; tile_11, tile_13, tile_3, tile_12, tile_5, tile_6, tile_1, tile_4, tile_16, tile_8, tile_2, and tile_9 are clear; tile_14 is painted black and tile_15 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-4-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_16) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_8) (clear tile_9) (down tile_1 tile_5) (down tile_10 tile_14) (down tile_11 tile_15) (down tile_12 tile_16) (down tile_2 tile_6) (down tile_3 tile_7) (down tile_4 tile_8) (down tile_5 tile_9) (down tile_6 tile_10) (down tile_7 tile_11) (down tile_8 tile_12) (down tile_9 tile_13) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_5 tile_6) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_9 tile_10) (painted tile_14 black) (painted tile_15 white) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_6 tile_5) (right tile_7 tile_6) (right tile_8 tile_7) (robot-at robot1 tile_7) (robot-at robot2 tile_10) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_6) (up tile_11 tile_7) (up tile_12 tile_8) (up tile_13 tile_9) (up tile_14 tile_10) (up tile_15 tile_11) (up tile_16 tile_12) (up tile_5 tile_1) (up tile_6 tile_2) (up tile_7 tile_3) (up tile_8 tile_4) (up tile_9 tile_5))\n    (:goal (and (painted tile_5 white) (painted tile_6 black) (painted tile_7 white) (painted tile_8 black) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 white) (painted tile_14 black) (painted tile_15 white) (painted tile_16 black)))\n)"}
{"id": 2064180602739641629, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 \nCurrently, robot robot1 is at tile_5 and holding color black and robot robot2 is at tile_1 and holding color white;  is clear; tile_4 is painted white, tile_6 is painted white, tile_8 is painted white, tile_7 is painted black, tile_2 is painted white, and tile_9 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_3) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_2 white) (painted tile_4 white) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_5) (robot-at robot2 tile_1) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -2134372784773760055, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_6 is to the right of tile_5, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_4 is down from tile_7, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_9 is down from tile_12, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_1 is down from tile_4 \nCurrently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_6 and holding color black; tile_3, tile_5, tile_1, tile_4, and tile_2 are clear; tile_12 is painted white, tile_11 is painted black, tile_7 is painted black, tile_10 is painted white, and tile_9 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_7 black) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_6) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 2394486281273283573, "group": "reachable_atom_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 16 tiles and 2 robots. \nThe tiles locations are: tile_10 is to the right of tile_9, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_14 is to the right of tile_13, and tile_3 is to the right of tile_2. Further, tile_4 is down from tile_8, tile_3 is down from tile_7, tile_7 is down from tile_11, tile_10 is down from tile_14, tile_8 is down from tile_12, tile_12 is down from tile_16, tile_9 is down from tile_13, tile_11 is down from tile_15, tile_5 is down from tile_9, tile_2 is down from tile_6, tile_1 is down from tile_5, and tile_6 is down from tile_10 \nCurrently, robot robot1 is at tile_4 and holding color white and robot robot2 is at tile_9 and holding color white; tile_11, tile_13, tile_3, tile_12, tile_5, tile_6, tile_1, tile_8, tile_16, tile_15, tile_2, tile_7, tile_14, and tile_10 are clear. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-4-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_14) (clear tile_15) (clear tile_16) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (down tile_1 tile_5) (down tile_10 tile_14) (down tile_11 tile_15) (down tile_12 tile_16) (down tile_2 tile_6) (down tile_3 tile_7) (down tile_4 tile_8) (down tile_5 tile_9) (down tile_6 tile_10) (down tile_7 tile_11) (down tile_8 tile_12) (down tile_9 tile_13) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_5 tile_6) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_9 tile_10) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_6 tile_5) (right tile_7 tile_6) (right tile_8 tile_7) (robot-at robot1 tile_4) (robot-at robot2 tile_9) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_6) (up tile_11 tile_7) (up tile_12 tile_8) (up tile_13 tile_9) (up tile_14 tile_10) (up tile_15 tile_11) (up tile_16 tile_12) (up tile_5 tile_1) (up tile_6 tile_2) (up tile_7 tile_3) (up tile_8 tile_4) (up tile_9 tile_5))\n    (:goal (and (painted tile_5 white) (painted tile_6 black) (painted tile_7 white) (painted tile_8 black) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 white) (painted tile_14 black) (painted tile_15 white) (painted tile_16 black)))\n)"}
