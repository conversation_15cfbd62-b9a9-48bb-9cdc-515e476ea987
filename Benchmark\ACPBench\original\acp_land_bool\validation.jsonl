{"id": 1263458375528833442, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c14, c8, c3, c2, c10, c0, c6, c13, c11, c16, c9, c15, c18, and c17 are at l0; c7, c12, c19, c1, c4, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c12 is at location l0", "answer": "no"}
{"id": 2870824961692944373, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0, with the car c1 on board. The cars are at locations as follows: c14, c8, c3, c2, c10, c0, c6, c13, c11, c16, c9, c15, and c17 are at l0; c7, c12, c19, c4, c18, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ferry has car c7 on board", "answer": "no"}
{"id": -9041134821424221552, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0, with the car c2 on board. The cars are at locations as follows: c11, c7, c12, c19, c1, c14, c4, c16, c18, and c5 are at l1; c8, c3, c10, c0, c6, c13, c9, c15, and c17 are at l0. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c17 is on board the ferry", "answer": "yes"}
{"id": -1941137116217247756, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0, with the car c6 on board. The cars are at locations as follows: c0, c8, c1, c5, and c7 are at l0; c2, c3, c9, and c4 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0, Car c6 is at location l0, Car c8 is at location l0, Car c4 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c9 is at location l1, Car c1 is at location l0, Car c5 is at location l0, and Car c7 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c8 is at location l1", "answer": "no"}
{"id": -6125716678316937654, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1, with the car c18 on board. The cars are at locations as follows: c14, c8, c3, c2, c10, c0, c6, c13, c1, c11, c16, c9, c15, and c17 are at l0; c7, c12, c19, c4, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? There are no cars on the ferry", "answer": "yes"}
{"id": -4022268800635228721, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, p1, t1, p0, and p2 are at l1-0, a0 and t0 are at l0-0, p3 is in t1. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p2 is at l1-0, and p1 is at l1-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-1", "answer": "no"}
{"id": 3315077574652305966, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, p1, t1, and p2 are at l1-0, t0 is at l0-1, a0 is at l0-0, p3 is in t0, p0 is in a0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p2 is at l1-0, and p1 is at l1-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p1 is in a0", "answer": "no"}
{"id": -2702806323296001186, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, p3 is at l0-1, t0 and p0 are at l0-2, t1 and p2 are at l1-2, a0 is at l0-0, p1 is in t1. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p1 is at l1-2, and p0 is at l0-2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-0", "answer": "no"}
{"id": 9058216856046031582, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, t1 is at l1-1, p0, p3, a0, and t0 are at l0-0, p1 and p2 are in t1. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p1 is at l1-2, and p0 is at l0-2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-0", "answer": "no"}
{"id": 619848753361065374, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, p1 is at l1-1, t1, a0, p2, and p3 are at l1-0, t0 is at l0-1, p0 is in t1. The goal is to reach a state where the following facts hold: p3 is at l1-2, p2 is at l1-0, p0 is at l1-1, and p1 is at l1-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p1 is at l0-2", "answer": "no"}
{"id": 8057917460877948722, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_3 is currently situated under the block block_1, and The block block_2 is on top of block block_4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_1 is currently being held by the robotic arm", "answer": "yes"}
{"id": 733749781673395898, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5, block_3 is on block_1, and block_2 is on block_4. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_1 is currently situated above the block block_3, and The block block_2 is currently situated above the block block_4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? No blocks are placed on top of block_1", "answer": "yes"}
{"id": -7600613827097934085, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_2 and block_4. The following block(s) are stacked on top of another block: block_3 is on block_5 and block_5 is on block_2. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_1 is currently situated above the block block_3, and The block block_2 is on top of block block_4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is empty", "answer": "yes"}
{"id": -622379711637640856, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_5 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_4 and block_4 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_3 is currently situated under the block block_1, and The block block_4 is currently situated under the block block_2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_1", "answer": "yes"}
{"id": -2242158988487479973, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) is stacked on top of another block: block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3 and The block block_1 is currently situated above the block block_2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_2 is currently situated under the block block_2", "answer": "no"}
{"id": 6201321203772062025, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-4f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f1-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f0-4f location", "answer": "yes"}
{"id": -8203515767434186474, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-1f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f0-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-0 is at f2-2f location", "answer": "no"}
{"id": -8725356111934371763, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock. Key key0-1 is at position f1-3f. Key key0-0 is at position f2-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-3f location and Key key0-0 is at f2-0f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f2-1f location", "answer": "yes"}
{"id": -5152466019411708765, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock. Key key0-0 is at position f1-0f. Key key0-1 is at position f4-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-1 is at f1-1f location", "answer": "no"}
{"id": 5996452493255214976, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f1-0f location", "answer": "yes"}
{"id": 7178470481686639462, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_3 and holding color white; tile_5, tile_8, tile_2, tile_7, and tile_4 are clear; tile_11 is painted black, tile_12 is painted white, tile_6 is painted white, tile_10 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_4 is painted in white color", "answer": "yes"}
{"id": 3428044754392798758, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_2 and holding color white; tile_5, tile_8, and tile_3 are clear; tile_11 is painted black, tile_12 is painted white, tile_6 is painted white, tile_10 is painted white, tile_7 is painted black, tile_9 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at tile_8 location", "answer": "no"}
{"id": 3568421205743707471, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_2 and holding color white; tile_6, tile_1, and tile_9 are clear; tile_11 is painted black, tile_12 is painted white, tile_10 is painted white, tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at tile_7 location", "answer": "no"}
{"id": 1106730633809432161, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot1 is at tile_2 and holding color white and robot robot2 is at tile_6 and holding color black; tile_5, tile_8, tile_1, and tile_3 are clear; tile_11 is painted black, tile_12 is painted white, tile_10 is painted white, tile_7 is painted black, tile_9 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot2 is at tile_2 location", "answer": "no"}
{"id": -8388354108906682889, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_5 and holding color black; tile_2, tile_3, tile_4, and tile_6 are clear; tile_11 is painted black, tile_12 is painted white, tile_10 is painted white, tile_7 is painted black, tile_9 is painted black, and tile_8 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_5 is clear", "answer": "yes"}
{"id": 3113431555141572725, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball3, ball2, and ball1 are at room3, ball4 is at room6. The goal is to reach a state where the following facts hold: Ball ball3 is in room room3, Ball ball2 is in room room2, Ball ball1 is in room room1, and Ball ball4 is at room1 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball2 is in room room4", "answer": "no"}
{"id": -7771405907353553441, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball4 is at room1, ball3 and ball1 are at room3, ball2 is at room2. The goal is to reach a state where the following facts hold: Ball ball3 is in room room3, Ball ball1 is in room room3, Ball ball4 is at room3 location, and Ball ball2 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball2 is at room3 location", "answer": "no"}
{"id": 1399289264857206271, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 and ball2 are at room2, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball2 is in room room2, Ball ball1 is in room room1, and Ball ball4 is at room1 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room1", "answer": "yes"}
{"id": -299998383572851420, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1 is at room1, ball4 and ball2 are at room2, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball1 is at room3 location, Ball ball4 is at room3 location, and Ball ball2 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at room1 location", "answer": "yes"}
{"id": -7822544754909932731, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 and ball2 are at room1, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room3, Ball ball1 is at room3 location, Ball ball4 is at room3 location, and Ball ball2 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is carrying the ball ball2 in the right gripper", "answer": "no"}
{"id": -2321372453934886756, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective4. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective2 is visible from waypoint0. Objective objective3 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint2; Image objective0 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint2. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode high_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, and Image objective2 was communicated in mode high_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 has image objective4 in mode colour", "answer": "no"}
{"id": 6908071219795024432, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective4. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective2 is visible from waypoint0. Objective objective3 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2. Soil can be sampled at the following location(s): waypoint2. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode high_res. Rover rover0 has image objective1 in mode high_res. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, and Image objective2 was communicated in mode high_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 is at waypoint2", "answer": "no"}
{"id": -4635004410337575491, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint2; Soil data was communicated from waypoint waypoint2; Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover0 has image objective1 in mode high_res. Rover rover0 has image objective1 in mode low_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, and Image objective1 was communicated in mode high_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 has image objective0 in mode low_res", "answer": "no"}
{"id": 9148051255408773918, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective2. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint0; Rover rover0 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera0 calibrated. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint0;, and Soil data was communicated from waypoint waypoint0;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 has image objective1 in mode low_res", "answer": "yes"}
{"id": 1609315206121108632, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover1 and rover0 are available. Rover rover0 has rock analyzed in waypoint waypoint2. Rover rover0 has its camera camera2 calibrated. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode high_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 has its camera camera0 calibrated", "answer": "no"}
{"id": -4508310008424411529, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x2-y3, loc-x1-y3, loc-x0-y3, loc-x3-y3, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x2-y1 has been visited", "answer": "yes"}
{"id": -1838219358475474055, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x2-y3, loc-x0-y2, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x1-y0, loc-x3-y3, loc-x2-y1, loc-x3-y2, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y2", "answer": "no"}
{"id": 8507800824277109560, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x2-y3, loc-x0-y2, loc-x0-y0, loc-x2-y0, loc-x1-y0, loc-x3-y3, loc-x3-y1, loc-x3-y2, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x0-y0", "answer": "no"}
{"id": 2388437373313835832, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x2-y3, loc-x0-y2, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x3-y2, loc-x1-y3, loc-x1-y0, loc-x3-y3, loc-x2-y1, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x0-y0 has been visited", "answer": "yes"}
{"id": 5944644628503791475, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x1-y2, loc-x1-y1, loc-x1-y0, loc-x2-y1, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y0", "answer": "yes"}
