{"id": 3278609206966843440, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_14, block_19, block_10, block_12, block_20, block_7, block_1, block_13, and block_18. The following block(s) are stacked on top of another block: block_16 is on block_11, block_9 is on block_2, block_2 is on block_18, block_4 is on block_6, block_3 is on block_13, block_11 is on block_1, block_17 is on block_12, block_8 is on block_7, block_15 is on block_9, and block_5 is on block_14. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"unstack the object block_3 from the object block_13\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_3)", "(on block_3 block_13)", "(handempty)"], "pos": ["(clear block_13)", "(holding block_3)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_10) (clear block_15) (clear block_16) (clear block_17) (clear block_19) (clear block_20) (clear block_3) (clear block_4) (clear block_5) (clear block_8) (handempty) (on block_11 block_1) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_2 block_18) (on block_3 block_13) (on block_4 block_6) (on block_5 block_14) (on block_8 block_7) (on block_9 block_2) (ontable block_1) (ontable block_10) (ontable block_12) (ontable block_13) (ontable block_14) (ontable block_18) (ontable block_19) (ontable block_20) (ontable block_6) (ontable block_7))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": -5957003482248918398, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_6, block_1, block_8, and block_10. The following block(s) are stacked on top of another block: block_7 is on block_8, block_5 is on block_2, block_9 is on block_5, block_3 is on block_4, and block_4 is on block_9. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"unstack the object block_7 from the object block_8\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on block_7 block_8)", "(clear block_7)", "(handempty)"], "pos": ["(holding block_7)", "(clear block_8)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_10) (clear block_3) (clear block_6) (clear block_7) (handempty) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_7 block_8) (on block_9 block_5) (ontable block_1) (ontable block_10) (ontable block_2) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -564277757397921716, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_4. The following block(s) are stacked on top of another block: block_3 is on block_2 and block_5 is on block_3. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"pick up object block_4 from the table\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_4)", "(ontable block_4)", "(handempty)"], "pos": ["(holding block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_4) (clear block_5) (handempty) (on block_3 block_2) (on block_5 block_3) (ontable block_1) (ontable block_2) (ontable block_4))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -4110776068366860952, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_14, block_19, block_12, block_7, block_1, and block_18. The following block(s) are stacked on top of another block: block_2 is on block_18, block_13 is on block_4, block_15 is on block_16, block_20 is on block_8, block_4 is on block_11, block_11 is on block_1, block_10 is on block_19, block_17 is on block_12, block_8 is on block_7, block_3 is on block_9, block_5 is on block_14, block_16 is on block_20, and block_9 is on block_15. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Break down the outcomes of performing the action \"pick up object block_6 from the table\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_6)", "(handempty)", "(ontable block_6)"], "pos": ["(holding block_6)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_10) (clear block_13) (clear block_17) (clear block_2) (clear block_3) (clear block_5) (clear block_6) (handempty) (on block_10 block_19) (on block_11 block_1) (on block_13 block_4) (on block_15 block_16) (on block_16 block_20) (on block_17 block_12) (on block_2 block_18) (on block_20 block_8) (on block_3 block_9) (on block_4 block_11) (on block_5 block_14) (on block_8 block_7) (on block_9 block_15) (ontable block_1) (ontable block_12) (ontable block_14) (ontable block_18) (ontable block_19) (ontable block_6) (ontable block_7))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 4348322855930111595, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_6, block_8, and block_10. The following block(s) are stacked on top of another block: block_7 is on block_10, block_5 is on block_2, block_9 is on block_5, block_3 is on block_7, block_4 is on block_6, and block_1 is on block_8. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Break down the outcomes of performing the action \"remove the object block_3 from on top of the object block_7\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_3)", "(on block_3 block_7)", "(handempty)"], "pos": ["(clear block_7)", "(holding block_3)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_3) (clear block_4) (clear block_9) (handempty) (on block_1 block_8) (on block_3 block_7) (on block_4 block_6) (on block_5 block_2) (on block_7 block_10) (on block_9 block_5) (ontable block_10) (ontable block_2) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 9068191393679101102, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_4 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_3 and block_3 is on block_4. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"stack object block_1 on top of object block_5\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(holding block_1)", "(clear block_5)"], "pos": ["(clear block_1)", "(on block_1 block_5)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_5) (holding block_1) (on block_2 block_3) (on block_3 block_4) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 7869633516557978948, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_4, block_3, and block_5. The following block(s) is stacked on top of another block: block_2 is on block_3. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Break down the outcomes of performing the action \"stack object block_1 on top of object block_4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(holding block_1)", "(clear block_4)"], "pos": ["(on block_1 block_4)", "(clear block_1)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (clear block_5) (holding block_1) (on block_2 block_3) (ontable block_3) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 59276847189384525, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_5, block_19, block_10, block_20, block_1, block_16, and block_13. The following block(s) are stacked on top of another block: block_9 is on block_2, block_4 is on block_6, block_3 is on block_13, block_11 is on block_1, block_14 is on block_20, block_7 is on block_10, block_17 is on block_12, block_15 is on block_9, block_8 is on block_4, block_18 is on block_5, block_2 is on block_14, and block_12 is on block_7. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"remove block_19 from table\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_19)", "(ontable block_19)", "(handempty)"], "pos": ["(holding block_19)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_11) (clear block_15) (clear block_16) (clear block_17) (clear block_18) (clear block_19) (clear block_3) (clear block_8) (handempty) (on block_11 block_1) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_17 block_12) (on block_18 block_5) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (ontable block_1) (ontable block_10) (ontable block_13) (ontable block_16) (ontable block_19) (ontable block_20) (ontable block_5) (ontable block_6))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 4112429321566029914, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_3 is on block_5, block_2 is on block_3, and block_4 is on block_2. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"stack object block_1 on top of object block_4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(holding block_1)", "(clear block_4)"], "pos": ["(on block_1 block_4)", "(clear block_1)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_4) (holding block_1) (on block_2 block_3) (on block_3 block_5) (on block_4 block_2) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -7941302144818037676, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_5. The following block(s) are stacked on top of another block: block_3 is on block_5 and block_4 is on block_1. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"remove the object block_4 from on top of the object block_1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_4)", "(on block_4 block_1)", "(handempty)"], "pos": ["(holding block_4)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (handempty) (on block_3 block_5) (on block_4 block_1) (ontable block_1) (ontable block_2) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -2178129452586287213, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_1 is on block_2, block_4 is on block_5, and block_2 is on block_4. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Break down the outcomes of performing the action \"unstack object block_1 from object block_2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_1)", "(handempty)", "(on block_1 block_2)"], "pos": ["(holding block_1)", "(clear block_2)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (handempty) (on block_1 block_2) (on block_2 block_4) (on block_4 block_5) (ontable block_3) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 4414996299955473364, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_4. The following block(s) are on the table: block_2, block_3, and block_5. The following block(s) is stacked on top of another block: block_1 is on block_2. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Break down the outcomes of performing the action \"put down the object block_4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(holding block_4)"], "pos": ["(clear block_4)", "(ontable block_4)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (clear block_5) (holding block_4) (on block_1 block_2) (ontable block_2) (ontable block_3) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -7392039682555373397, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_3. The following block(s) is on the table: block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_4 is on block_1, and block_1 is on block_5. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Break down the outcomes of performing the action \"put down the object block_3\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(holding block_3)"], "pos": ["(ontable block_3)", "(clear block_3)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_4) (holding block_3) (on block_1 block_5) (on block_4 block_1) (on block_5 block_2) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 3221753690492509445, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_3. The following block(s) are stacked on top of another block: block_5 is on block_2, block_4 is on block_1, and block_1 is on block_5. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Break down the outcomes of performing the action \"collect the object block_3 from the table\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear block_3)", "(handempty)", "(ontable block_3)"], "pos": ["(holding block_3)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (handempty) (on block_1 block_5) (on block_4 block_1) (on block_5 block_2) (ontable block_2) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 1974185338792146565, "group": "progression_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_4 is on block_5, block_3 is on block_1, and block_2 is on block_3. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Break down the outcomes of performing the action \"unstack object block_4 from object block_5\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on block_4 block_5)", "(handempty)", "(clear block_4)"], "pos": ["(holding block_4)", "(clear block_5)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (handempty) (on block_2 block_3) (on block_3 block_1) (on block_4 block_5) (ontable block_1) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
