{"id": -1488277467791278856, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet5, star2, star4, planet6, groundstation1, groundstation3. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to planet5. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target star4 is available. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image planet5 thermograph0) (have_image planet6 infrared2) (have_image star4 thermograph0) (on_board instrument0 satellite0) (pointing satellite0 planet5) (power_on instrument0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 1218019303879552541, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet5, star2, star4, planet6, groundstation1, groundstation3. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to star2. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image planet5 thermograph0) (have_image planet6 infrared2) (on_board instrument0 satellite0) (pointing satellite0 star2) (power_on instrument0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 8407237671587969802, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet5, star2, star4, planet6, groundstation1, groundstation3. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation0. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A infrared2 mode image of target planet6 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image planet6 infrared2) (on_board instrument0 satellite0) (pointing satellite0 groundstation0) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -2821942727904140436, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, planet5, star2, groundstation3, groundstation1, star0, phenomenon6. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument2, instrument0. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite2 is pointing to phenomenon6. Satellite satellite1 is pointing to groundstation3. Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument1, instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (have_image phenomenon6 spectrograph0) (have_image planet5 thermograph2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation1) (pointing satellite1 groundstation3) (pointing satellite2 phenomenon6) (power_avail satellite1) (power_on instrument1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": -8854708992393417905, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet5, star2, star4, planet6, groundstation1, groundstation3. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation3. Power is available on the following satellite(s): satellite0. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibration_target instrument0 star4) (on_board instrument0 satellite0) (pointing satellite0 groundstation3) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 330541189730103841, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, planet5, star2, groundstation3, groundstation1, star0, phenomenon6. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument2, instrument0. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to planet5. Satellite satellite1 is pointing to phenomenon6. Satellite satellite2 is pointing to star2. Power is available on the following satellite(s): satellite0. Following instruments are powered on: instrument3, instrument4. Following instruments are calibrated: instrument4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 planet5) (pointing satellite1 phenomenon6) (pointing satellite2 star2) (power_avail satellite0) (power_on instrument3) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": -8041127879105114742, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet5, star2, star4, planet6, groundstation1, groundstation3. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation0. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A infrared2 mode image of target planet6 is available. A infrared2 mode image of target groundstation3 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image groundstation3 infrared2) (have_image planet6 infrared2) (on_board instrument0 satellite0) (pointing satellite0 groundstation0) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 719204978381260976, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, planet5, star2, groundstation3, groundstation1, star0, phenomenon6. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument2, instrument0. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite2 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation3. Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation1) (pointing satellite1 groundstation3) (pointing satellite2 groundstation1) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 4662977933387315229, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, planet5, star2, groundstation3, groundstation1, star0, phenomenon6. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument2, instrument0. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation4. Satellite satellite2 is pointing to star2. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A spectrograph0 mode image of target phenomenon6 is available. A spectrograph0 mode image of target star2 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (have_image phenomenon6 spectrograph0) (have_image star2 spectrograph0) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation4) (pointing satellite1 groundstation3) (pointing satellite2 star2) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 8438837534549489058, "group": "reachable_atom_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, planet5, star2, groundstation3, groundstation1, star0, phenomenon6. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument2, instrument0. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star2. Satellite satellite2 is pointing to phenomenon6. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. A thermograph2 mode image of target phenomenon6 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (have_image phenomenon6 spectrograph0) (have_image phenomenon6 thermograph2) (have_image planet5 thermograph2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 star2) (pointing satellite1 groundstation3) (pointing satellite2 phenomenon6) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
