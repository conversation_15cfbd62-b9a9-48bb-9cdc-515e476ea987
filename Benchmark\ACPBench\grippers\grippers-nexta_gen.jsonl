{"id": -844161664939330636, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 is at room5, ball2 is at room3, ball3 is at room1, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball1 is in room room4, Ball ball2 is at room3 location, Ball ball3 is in room room4, and Ball ball4 is at room5 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room1 room1)", "(move robot1 room1 room5)"], "no": ["(move robot1 room1 room4)", "(move robot1 room1 room3)", "(move robot1 room1 room2)"], "opt": "3", "yes": ["(pick robot1 ball3 room1 left1)", "(pick robot1 ball3 room1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room4) (at ball2 room3) (at ball3 room1) (at ball4 room5) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": -5743097969747139520, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball2, ball3, and ball7 are at room1, ball4 and ball5 are at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball6 is in room room2, Ball ball3 is in room room1, Ball ball7 is in room room1, Ball ball1 is at room3 location, Ball ball4 is in room room1, and Ball ball5 is in room room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(move robot1 room3 room3)", "(move robot1 room3 room2)", "(move robot1 room3 room1)"], "opt": "7", "yes": ["(pick robot1 ball4 room3 left1)", "(drop robot1 ball1 room3 right1)", "(pick robot1 ball5 room3 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball2 room1) (at ball3 room1) (at ball4 room3) (at ball5 room3) (at ball6 room2) (at ball7 room1) (at-robby robot1 room3) (carry robot1 ball1 right1) (free robot1 left1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 3907481469958796260, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5 and both grippers are free. Additionally, ball2, ball1, ball3, and ball4 are at room1. The goal is to reach a state where the following facts hold: Ball ball1 is in room room4, Ball ball2 is in room room3, Ball ball3 is at room4 location, and Ball ball4 is at room5 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room5 room2)"], "no": ["(move robot1 room5 room3)", "(move robot1 room5 room5)", "(move robot1 room5 room4)"], "opt": "13", "yes": ["(move robot1 room5 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at-robby robot1 room5) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": -5443871434562304942, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball5. Additionally, ball2, ball3, and ball4 are at room1, ball1 and ball6 are at room2, ball7 is at room3. The goal is to reach a state where the following facts hold: Ball ball2 is in room room1, Ball ball6 is at room2 location, Ball ball3 is in room room1, Ball ball7 is at room1 location, Ball ball1 is at room3 location, Ball ball4 is at room1 location, and Ball ball5 is at room2 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room2 room2)"], "no": ["(move robot1 room2 room3)", "(pick robot1 ball6 room2 right1)", "(move robot1 room2 room1)"], "opt": "7", "yes": ["(pick robot1 ball1 room2 right1)", "(drop robot1 ball5 room2 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball6 room2) (at ball7 room3) (at-robby robot1 room2) (carry robot1 ball5 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 203500164196781237, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball2 and ball4 are at room2, ball1 is at room1. The goal is to reach a state where the following facts hold: Ball ball2 is at room6 location, Ball ball4 is in room room9, Ball ball3 is in room room8, and Ball ball1 is in room room8. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drop robot1 ball3 room2 left1)", "(move robot1 room2 room6)", "(move robot1 room2 room2)", "(move robot1 room2 room5)", "(move robot1 room2 room4)", "(move robot1 room2 room7)", "(move robot1 room2 room10)"], "no": ["(move robot1 room2 room9)", "(move robot1 room2 room8)", "(move robot1 room2 room3)"], "opt": "12", "yes": ["(pick robot1 ball2 room2 right1)", "(pick robot1 ball4 room2 right1)", "(move robot1 room2 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room1) (at ball2 room2) (at ball4 room2) (at-robby robot1 room2) (carry robot1 ball3 left1) (free robot1 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": 6268354461938065311, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball5, and right gripper is carrying the ball ball3. Additionally, ball2, ball11, ball9, ball10, ball1, ball14, and ball4 are at room1, ball13, ball6, ball12, and ball15 are at room2, ball7 and ball8 are at room3. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball6 is at room1 location, Ball ball9 is at room3 location, Ball ball11 is in room room3, Ball ball1 is at room2 location, Ball ball13 is at room2 location, Ball ball3 is at room2 location, Ball ball15 is in room room1, Ball ball7 is at room3 location, Ball ball12 is at room2 location, Ball ball8 is in room room1, Ball ball14 is in room room3, Ball ball4 is at room1 location, Ball ball10 is at room3 location, and Ball ball5 is at room1 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(drop robot1 ball5 room3 left1)"], "no": ["(drop robot1 ball3 room3 right1)", "(move robot1 room3 room2)", "(move robot1 room3 room3)"], "opt": "24", "yes": ["(move robot1 room3 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball4 room1) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (carry robot1 ball3 right1) (carry robot1 ball5 left1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 4649473588844443164, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room10, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball2 and ball4 are at room2, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball2 is in room room6, Ball ball4 is in room room9, Ball ball3 is at room8 location, and Ball ball1 is at room8 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room10 room5)", "(move robot1 room10 room10)", "(move robot1 room10 room9)", "(move robot1 room10 room7)", "(move robot1 room10 room6)", "(move robot1 room10 room2)", "(move robot1 room10 room3)"], "no": ["(move robot1 room10 room8)", "(drop robot1 ball3 room10 left1)", "(move robot1 room10 room1)"], "opt": "12", "yes": ["(move robot1 room10 room4)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball2 room2) (at ball4 room2) (at-robby robot1 room10) (carry robot1 ball3 left1) (free robot1 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": 1289815508863566791, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball7. Additionally, ball2, ball3, and ball4 are at room1, ball6 is at room2, ball5 and ball1 are at room3. The goal is to reach a state where the following facts hold: Ball ball2 is in room room1, Ball ball6 is at room2 location, Ball ball3 is at room1 location, Ball ball7 is at room1 location, Ball ball1 is at room3 location, Ball ball4 is in room room1, and Ball ball5 is in room room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room3 room2)", "(pick robot1 ball1 room3 right1)"], "no": ["(move robot1 room3 room3)", "(move robot1 room3 room1)", "(drop robot1 ball7 room3 left1)"], "opt": "5", "yes": ["(pick robot1 ball5 room3 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at-robby robot1 room3) (carry robot1 ball7 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 7007308072324721887, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball2 is at room6, ball3 is at room3, ball1 is at room4, ball4 is at room10. The goal is to reach a state where the following facts hold: Ball ball2 is in room room6, Ball ball4 is in room room9, Ball ball3 is at room8 location, and Ball ball1 is in room room8. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room3 room2)", "(move robot1 room3 room5)", "(move robot1 room3 room8)", "(move robot1 room3 room6)", "(move robot1 room3 room1)", "(move robot1 room3 room10)", "(move robot1 room3 room9)"], "no": ["(move robot1 room3 room7)", "(move robot1 room3 room4)", "(move robot1 room3 room3)"], "opt": "10", "yes": ["(pick robot1 ball3 room3 right1)", "(pick robot1 ball3 room3 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball2 room6) (at ball3 room3) (at ball4 room10) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -2402714591454687605, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball2, ball1, and ball4 are at room1, ball6, ball3, and ball7 are at room2, ball5 is at room3. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball6 is at room2 location, Ball ball3 is at room1 location, Ball ball7 is in room room1, Ball ball1 is in room room3, Ball ball4 is in room room1, and Ball ball5 is in room room2. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room2 room2)", "(pick robot1 ball6 room2 left1)"], "no": ["(move robot1 room2 room3)", "(pick robot1 ball6 room2 right1)", "(move robot1 room2 room1)"], "opt": "11", "yes": ["(pick robot1 ball3 room2 right1)", "(pick robot1 ball7 room2 left1)", "(pick robot1 ball7 room2 right1)", "(pick robot1 ball3 room2 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room2) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 1593903039531996147, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4, ball2, and ball3 are at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room3 location, Ball ball3 is in room room3, Ball ball4 is at room3 location, and Ball ball2 is in room room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(drop robot1 ball1 room1 left1)", "(move robot1 room1 room1)", "(move robot1 room1 room2)"], "opt": "5", "yes": ["(move robot1 room1 room3)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball2 room3) (at ball3 room3) (at ball4 room3) (at-robby robot1 room1) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": 1236989663626872323, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room6, right gripper is free, and left gripper is carrying the ball ball4. Additionally, ball2 is at room2, ball3 and ball1 are at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room1 location, Ball ball4 is at room1 location, Ball ball3 is at room3 location, and Ball ball2 is at room2 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room6 room7)", "(move robot1 room6 room6)", "(move robot1 room6 room4)", "(move robot1 room6 room2)"], "no": ["(drop robot1 ball4 room6 left1)", "(move robot1 room6 room1)", "(move robot1 room6 room5)"], "opt": "5", "yes": ["(move robot1 room6 room3)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room3) (at ball2 room2) (at ball3 room3) (at-robby robot1 room6) (carry robot1 ball4 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 7861480837153747138, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball2 and ball4 are at room2, ball3 is at room5. The goal is to reach a state where the following facts hold: Ball ball1 is in room room1, Ball ball4 is at room1 location, Ball ball3 is in room room3, and Ball ball2 is in room room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room2 room2)", "(move robot1 room2 room5)", "(move robot1 room2 room1)", "(move robot1 room2 room4)", "(move robot1 room2 room7)", "(move robot1 room2 room3)"], "no": ["(pick robot1 ball2 room2 left1)", "(move robot1 room2 room6)", "(drop robot1 ball1 room2 right1)"], "opt": "8", "yes": ["(pick robot1 ball4 room2 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball2 room2) (at ball3 room5) (at ball4 room2) (at-robby robot1 room2) (carry robot1 ball1 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -1221201981164310916, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball4. Additionally, ball2 is at room2, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room1 location, Ball ball4 is at room1 location, Ball ball3 is at room3 location, and Ball ball2 is in room room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room2 room6)", "(move robot1 room2 room4)", "(move robot1 room2 room7)", "(drop robot1 ball1 room2 left1)", "(move robot1 room2 room3)"], "no": ["(drop robot1 ball4 room2 right1)", "(move robot1 room2 room2)", "(move robot1 room2 room5)"], "opt": "3", "yes": ["(move robot1 room2 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball2 room2) (at ball3 room3) (at-robby robot1 room2) (carry robot1 ball1 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -6689869599761918446, "group": "goal_closer_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room5, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room1, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball1 is in room room1, Ball ball4 is at room1 location, Ball ball3 is at room3 location, and Ball ball2 is in room room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(move robot1 room5 room3)", "(move robot1 room5 room4)"], "no": ["(drop robot1 ball4 room5 right1)", "(move robot1 room5 room5)", "(drop robot1 ball2 room5 left1)"], "opt": "4", "yes": ["(move robot1 room5 room1)", "(move robot1 room5 room2)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room1) (at ball3 room3) (at-robby robot1 room5) (carry robot1 ball2 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
