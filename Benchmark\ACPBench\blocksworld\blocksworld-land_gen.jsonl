{"id": 7844029944291190840, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_1 and block_3. The goal is to reach a state where the following facts hold: The block block_3 is on top of block block_1. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_1 block_1)", "(on block_2 block_3)", "(clear block_2)", "(holding block_1)", "(on block_2 block_1)", "(on block_3 block_3)", "(on block_3 block_2)", "(on block_1 block_3)", "(on block_2 block_2)", "(on block_1 block_2)", "(ontable block_2)"], "yes": ["(holding block_3)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_1) (clear block_3) (holding block_2) (ontable block_1) (ontable block_3))\n    (:goal (on block_3 block_1))\n)"}
{"id": 5732283238499427880, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_2 and block_5. The following block(s) are stacked on top of another block: block_1 is on block_2 and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_5 is on top of block block_1, The block block_5 is currently situated under the block block_2, The block block_3 is currently situated under the block block_1, and The block block_4 is on top of block block_2. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_2 block_4)", "(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(clear block_4)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_4 block_5)", "(on block_5 block_3)", "(on block_3 block_1)", "(on block_3 block_4)", "(ontable block_1)", "(on block_2 block_3)", "(on block_2 block_1)", "(on block_4 block_3)", "(on block_5 block_5)", "(holding block_3)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(ontable block_4)", "(on block_4 block_4)", "(ontable block_3)"], "yes": ["(holding block_1)", "(holding block_2)", "(clear block_5)", "(clear block_2)", "(holding block_5)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (holding block_4) (on block_1 block_2) (on block_3 block_5) (ontable block_2) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 344343520317182705, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_8, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_3 is on block_4, block_7 is on block_10, block_6 is on block_1, block_1 is on block_8, block_9 is on block_5, and block_4 is on block_9. The goal is to reach a state where the following facts hold: The block block_10 is currently situated under the block block_7, The block block_8 is currently situated under the block block_1, The block block_6 is on top of block block_3, The block block_2 is on top of block block_5, The block block_5 is on top of block block_9, The block block_9 is currently situated above the block block_1, The block block_2 is currently situated under the block block_4, and The block block_3 is currently situated above the block block_7. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear block_10)", "(on block_4 block_8)", "(on block_3 block_2)", "(on block_6 block_8)", "(on block_8 block_3)", "(on block_5 block_7)", "(on block_2 block_10)", "(on block_4 block_10)", "(on block_3 block_6)", "(on block_9 block_9)", "(on block_9 block_4)", "(on block_1 block_9)", "(on block_4 block_5)", "(on block_4 block_7)", "(ontable block_1)", "(on block_2 block_3)", "(on block_4 block_6)", "(on block_7 block_5)", "(on block_10 block_9)", "(ontable block_5)", "(on block_10 block_5)", "(on block_1 block_7)", "(on block_5 block_8)", "(ontable block_6)", "(on block_4 block_4)", "(on block_1 block_6)", "(on block_6 block_7)", "(on block_9 block_8)", "(on block_10 block_2)", "(on block_2 block_4)", "(on block_8 block_5)", "(on block_8 block_6)", "(on block_6 block_2)", "(holding block_1)", "(on block_8 block_7)", "(on block_8 block_8)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_7 block_4)", "(on block_8 block_2)", "(on block_9 block_10)", "(on block_10 block_1)", "(on block_2 block_6)", "(on block_8 block_9)", "(on block_1 block_4)", "(on block_5 block_1)", "(on block_7 block_7)", "(on block_7 block_3)", "(ontable block_3)", "(on block_10 block_3)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_9 block_3)", "(on block_1 block_5)", "(on block_7 block_8)", "(on block_6 block_6)", "(holding block_7)", "(on block_3 block_3)", "(on block_6 block_9)", "(on block_6 block_10)", "(on block_3 block_9)", "(on block_8 block_4)", "(clear block_8)", "(on block_7 block_6)", "(on block_9 block_7)", "(on block_10 block_4)", "(on block_1 block_2)", "(on block_3 block_10)", "(on block_9 block_6)", "(holding block_8)", "(on block_8 block_1)", "(on block_7 block_2)", "(on block_8 block_10)", "(on block_5 block_6)", "(on block_5 block_10)", "(holding block_10)", "(on block_10 block_6)", "(on block_1 block_3)", "(on block_1 block_10)", "(ontable block_9)", "(on block_2 block_1)", "(on block_6 block_5)", "(on block_4 block_3)", "(on block_6 block_4)", "(on block_3 block_5)", "(on block_5 block_5)", "(on block_2 block_8)", "(on block_10 block_7)", "(ontable block_7)", "(on block_10 block_8)", "(on block_1 block_1)", "(on block_4 block_1)", "(ontable block_4)", "(on block_10 block_10)", "(on block_2 block_7)", "(on block_3 block_8)", "(on block_7 block_9)", "(on block_7 block_1)", "(on block_9 block_2)", "(on block_2 block_9)"], "yes": ["(holding block_2)", "(clear block_5)", "(holding block_3)", "(holding block_4)", "(clear block_2)", "(holding block_5)", "(clear block_9)", "(holding block_6)", "(holding block_9)", "(clear block_1)", "(clear block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_3) (clear block_6) (clear block_7) (handempty) (on block_1 block_8) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_6 block_1) (on block_7 block_10) (on block_9 block_5) (ontable block_10) (ontable block_2) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -7172763937417885657, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4, block_2, and block_5. The following block(s) are stacked on top of another block: block_3 is on block_4 and block_1 is on block_5. The goal is to reach a state where the following facts hold: The block block_5 is on top of block block_1, The block block_2 is on top of block block_5, The block block_3 is currently situated under the block block_1, and The block block_4 is on top of block block_2. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_2 block_4)", "(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_4 block_5)", "(on block_5 block_3)", "(on block_3 block_1)", "(ontable block_1)", "(on block_2 block_3)", "(on block_2 block_1)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(on block_1 block_2)", "(on block_4 block_4)", "(ontable block_3)"], "yes": ["(holding block_1)", "(holding block_2)", "(clear block_5)", "(holding block_4)", "(holding block_5)", "(clear block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_2) (clear block_3) (handempty) (on block_1 block_5) (on block_3 block_4) (ontable block_2) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 1004416112542762044, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_10, block_4, block_8, and block_6. The following block(s) are stacked on top of another block: block_7 is on block_10, block_1 is on block_8, block_3 is on block_7, block_5 is on block_9, and block_9 is on block_1. The goal is to reach a state where the following facts hold: The block block_7 is on top of block block_10, The block block_1 is on top of block block_8, The block block_3 is currently situated under the block block_6, The block block_2 is currently situated above the block block_5, The block block_9 is currently situated under the block block_5, The block block_1 is currently situated under the block block_9, The block block_4 is on top of block block_2, and The block block_3 is currently situated above the block block_7. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear block_10)", "(on block_4 block_8)", "(on block_3 block_2)", "(on block_6 block_8)", "(on block_8 block_3)", "(on block_5 block_7)", "(on block_2 block_10)", "(on block_4 block_10)", "(on block_3 block_6)", "(on block_9 block_9)", "(on block_5 block_2)", "(on block_9 block_4)", "(on block_1 block_9)", "(on block_4 block_5)", "(on block_4 block_7)", "(ontable block_2)", "(ontable block_1)", "(on block_2 block_3)", "(on block_4 block_6)", "(on block_7 block_5)", "(on block_4 block_9)", "(holding block_9)", "(holding block_3)", "(on block_10 block_9)", "(ontable block_5)", "(on block_10 block_5)", "(on block_1 block_7)", "(on block_5 block_8)", "(on block_4 block_4)", "(on block_1 block_6)", "(on block_6 block_7)", "(on block_9 block_8)", "(on block_10 block_2)", "(on block_2 block_4)", "(on block_8 block_5)", "(on block_8 block_6)", "(on block_6 block_2)", "(holding block_1)", "(on block_8 block_7)", "(on block_8 block_8)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_7 block_4)", "(on block_9 block_5)", "(on block_8 block_2)", "(on block_9 block_10)", "(on block_10 block_1)", "(on block_2 block_6)", "(on block_8 block_9)", "(on block_1 block_4)", "(clear block_7)", "(on block_5 block_1)", "(on block_7 block_7)", "(on block_7 block_3)", "(ontable block_3)", "(clear block_9)", "(on block_6 block_1)", "(on block_10 block_3)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_9 block_3)", "(on block_1 block_5)", "(on block_7 block_8)", "(on block_6 block_6)", "(holding block_7)", "(on block_3 block_3)", "(on block_6 block_9)", "(on block_3 block_4)", "(on block_6 block_10)", "(on block_3 block_9)", "(on block_8 block_4)", "(clear block_8)", "(on block_7 block_6)", "(on block_9 block_7)", "(on block_10 block_4)", "(on block_1 block_2)", "(on block_3 block_10)", "(on block_9 block_6)", "(holding block_8)", "(clear block_1)", "(on block_8 block_1)", "(on block_7 block_2)", "(on block_8 block_10)", "(on block_5 block_6)", "(on block_5 block_10)", "(holding block_10)", "(on block_10 block_6)", "(on block_1 block_3)", "(on block_1 block_10)", "(ontable block_9)", "(on block_2 block_1)", "(on block_6 block_5)", "(on block_4 block_3)", "(on block_6 block_4)", "(on block_3 block_5)", "(on block_5 block_5)", "(on block_2 block_8)", "(on block_10 block_7)", "(ontable block_7)", "(on block_10 block_8)", "(on block_1 block_1)", "(on block_4 block_1)", "(on block_10 block_10)", "(on block_2 block_7)", "(on block_3 block_8)", "(on block_7 block_9)", "(holding block_5)", "(on block_7 block_1)", "(on block_9 block_2)", "(on block_2 block_9)"], "yes": ["(holding block_4)", "(clear block_2)", "(holding block_6)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_3) (clear block_4) (clear block_5) (clear block_6) (holding block_2) (on block_1 block_8) (on block_3 block_7) (on block_5 block_9) (on block_7 block_10) (on block_9 block_1) (ontable block_10) (ontable block_4) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -9145294909179947795, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_4 and block_3. The following block(s) are stacked on top of another block: block_1 is on block_4 and block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_5 is currently situated above the block block_1, The block block_2 is on top of block block_5, The block block_1 is on top of block block_3, and The block block_4 is currently situated above the block block_2. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_2 block_4)", "(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_4 block_5)", "(on block_5 block_3)", "(on block_3 block_1)", "(ontable block_2)", "(on block_3 block_4)", "(ontable block_1)", "(on block_2 block_3)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(ontable block_5)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_2)", "(on block_4 block_4)"], "yes": ["(holding block_1)", "(holding block_2)", "(clear block_5)", "(holding block_4)", "(clear block_1)", "(handempty)", "(clear block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (holding block_5) (on block_1 block_4) (on block_2 block_1) (ontable block_3) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": -2850072326548095427, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_1, block_10, block_8, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_7 is on block_10, block_9 is on block_5, block_6 is on block_8, and block_4 is on block_9. The goal is to reach a state where the following facts hold: The block block_7 is on top of block block_10, The block block_1 is currently situated above the block block_8, The block block_3 is currently situated under the block block_6, The block block_2 is on top of block block_5, The block block_5 is on top of block block_9, The block block_1 is currently situated under the block block_9, The block block_2 is currently situated under the block block_4, and The block block_3 is currently situated above the block block_7. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear block_10)", "(on block_4 block_8)", "(on block_3 block_2)", "(on block_8 block_3)", "(on block_5 block_7)", "(on block_2 block_10)", "(on block_4 block_10)", "(on block_3 block_6)", "(on block_9 block_9)", "(on block_9 block_4)", "(on block_1 block_9)", "(on block_4 block_5)", "(on block_4 block_7)", "(on block_2 block_3)", "(on block_4 block_6)", "(on block_7 block_5)", "(on block_10 block_9)", "(ontable block_5)", "(on block_10 block_5)", "(on block_1 block_7)", "(on block_5 block_8)", "(ontable block_6)", "(on block_4 block_4)", "(on block_1 block_6)", "(on block_6 block_7)", "(on block_9 block_8)", "(on block_10 block_2)", "(on block_2 block_4)", "(on block_8 block_5)", "(on block_8 block_6)", "(on block_6 block_2)", "(on block_8 block_7)", "(on block_8 block_8)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_7 block_4)", "(on block_8 block_2)", "(on block_9 block_10)", "(on block_10 block_1)", "(on block_2 block_6)", "(on block_8 block_9)", "(on block_1 block_4)", "(on block_5 block_1)", "(on block_7 block_7)", "(ontable block_3)", "(on block_7 block_3)", "(on block_6 block_1)", "(on block_10 block_3)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_9 block_3)", "(on block_1 block_5)", "(on block_7 block_8)", "(on block_6 block_6)", "(holding block_7)", "(on block_3 block_3)", "(on block_6 block_9)", "(on block_3 block_4)", "(on block_6 block_10)", "(on block_3 block_9)", "(on block_8 block_4)", "(on block_7 block_6)", "(on block_9 block_7)", "(on block_10 block_4)", "(on block_1 block_2)", "(on block_3 block_10)", "(on block_9 block_6)", "(holding block_8)", "(on block_8 block_1)", "(on block_7 block_2)", "(on block_8 block_10)", "(on block_5 block_6)", "(on block_5 block_10)", "(holding block_10)", "(on block_10 block_6)", "(on block_1 block_3)", "(on block_1 block_10)", "(ontable block_9)", "(on block_2 block_1)", "(on block_6 block_5)", "(on block_4 block_3)", "(on block_6 block_4)", "(on block_3 block_5)", "(on block_5 block_5)", "(on block_2 block_8)", "(on block_10 block_7)", "(ontable block_7)", "(on block_10 block_8)", "(on block_1 block_1)", "(on block_4 block_1)", "(ontable block_4)", "(on block_10 block_10)", "(on block_2 block_7)", "(on block_3 block_8)", "(on block_7 block_9)", "(on block_7 block_1)", "(on block_9 block_2)", "(on block_2 block_9)"], "yes": ["(holding block_1)", "(clear block_8)", "(holding block_2)", "(clear block_5)", "(holding block_4)", "(clear block_2)", "(holding block_5)", "(clear block_9)", "(holding block_6)", "(clear block_3)", "(holding block_9)", "(handempty)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_4) (clear block_6) (clear block_7) (holding block_3) (on block_4 block_9) (on block_5 block_2) (on block_6 block_8) (on block_7 block_10) (on block_9 block_5) (ontable block_1) (ontable block_10) (ontable block_2) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -917935361312603466, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_4, block_8, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_7 is on block_10, block_1 is on block_8, block_3 is on block_7, block_6 is on block_3, and block_9 is on block_5. The goal is to reach a state where the following facts hold: The block block_7 is currently situated above the block block_10, The block block_1 is on top of block block_8, The block block_3 is currently situated under the block block_6, The block block_2 is currently situated above the block block_5, The block block_9 is currently situated under the block block_5, The block block_9 is on top of block block_1, The block block_2 is currently situated under the block block_4, and The block block_3 is currently situated above the block block_7. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear block_10)", "(on block_4 block_8)", "(on block_3 block_2)", "(on block_6 block_8)", "(on block_8 block_3)", "(on block_5 block_7)", "(on block_2 block_10)", "(on block_4 block_10)", "(on block_3 block_6)", "(on block_9 block_9)", "(on block_9 block_4)", "(on block_1 block_9)", "(on block_4 block_5)", "(on block_4 block_7)", "(ontable block_1)", "(on block_2 block_3)", "(on block_4 block_6)", "(on block_7 block_5)", "(on block_4 block_9)", "(holding block_3)", "(on block_10 block_9)", "(ontable block_5)", "(on block_10 block_5)", "(on block_1 block_7)", "(on block_5 block_8)", "(ontable block_6)", "(on block_4 block_4)", "(on block_1 block_6)", "(on block_6 block_7)", "(on block_9 block_8)", "(on block_10 block_2)", "(on block_2 block_4)", "(on block_8 block_5)", "(on block_8 block_6)", "(on block_6 block_2)", "(holding block_6)", "(holding block_1)", "(on block_8 block_7)", "(on block_8 block_8)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_7 block_4)", "(on block_8 block_2)", "(on block_9 block_10)", "(on block_10 block_1)", "(on block_2 block_6)", "(on block_8 block_9)", "(on block_1 block_4)", "(clear block_7)", "(on block_5 block_1)", "(on block_7 block_7)", "(on block_7 block_3)", "(ontable block_3)", "(on block_6 block_1)", "(on block_10 block_3)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_9 block_3)", "(on block_1 block_5)", "(on block_7 block_8)", "(on block_6 block_6)", "(holding block_7)", "(on block_3 block_3)", "(on block_6 block_9)", "(on block_3 block_4)", "(on block_6 block_10)", "(on block_3 block_9)", "(on block_8 block_4)", "(clear block_8)", "(on block_7 block_6)", "(on block_9 block_7)", "(on block_10 block_4)", "(on block_1 block_2)", "(on block_3 block_10)", "(on block_9 block_6)", "(holding block_8)", "(on block_8 block_1)", "(on block_7 block_2)", "(on block_8 block_10)", "(on block_5 block_6)", "(on block_5 block_10)", "(holding block_10)", "(on block_10 block_6)", "(on block_1 block_3)", "(on block_1 block_10)", "(ontable block_9)", "(clear block_3)", "(on block_2 block_1)", "(on block_6 block_5)", "(on block_4 block_3)", "(on block_6 block_4)", "(on block_3 block_5)", "(on block_5 block_5)", "(on block_2 block_8)", "(on block_10 block_7)", "(ontable block_7)", "(on block_10 block_8)", "(on block_1 block_1)", "(on block_4 block_1)", "(on block_10 block_10)", "(on block_2 block_7)", "(on block_3 block_8)", "(on block_7 block_9)", "(on block_7 block_1)", "(on block_9 block_2)", "(on block_2 block_9)"], "yes": ["(holding block_2)", "(clear block_5)", "(holding block_4)", "(clear block_2)", "(holding block_5)", "(holding block_9)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_4) (clear block_6) (clear block_9) (handempty) (on block_1 block_8) (on block_3 block_7) (on block_5 block_2) (on block_6 block_3) (on block_7 block_10) (on block_9 block_5) (ontable block_10) (ontable block_2) (ontable block_4) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 5950080229155401765, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_4, block_8, and block_2. The following block(s) are stacked on top of another block: block_7 is on block_10, block_1 is on block_8, block_3 is on block_7, block_6 is on block_3, block_5 is on block_9, and block_9 is on block_1. The goal is to reach a state where the following facts hold: The block block_7 is on top of block block_10, The block block_1 is currently situated above the block block_8, The block block_3 is currently situated under the block block_6, The block block_2 is currently situated above the block block_5, The block block_9 is currently situated under the block block_5, The block block_1 is currently situated under the block block_9, The block block_2 is currently situated under the block block_4, and The block block_3 is on top of block block_7. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(clear block_10)", "(on block_4 block_8)", "(on block_3 block_2)", "(on block_6 block_8)", "(on block_8 block_3)", "(on block_5 block_7)", "(on block_2 block_10)", "(on block_4 block_10)", "(on block_3 block_6)", "(on block_9 block_9)", "(on block_5 block_2)", "(on block_9 block_4)", "(on block_1 block_9)", "(on block_4 block_5)", "(on block_4 block_7)", "(ontable block_1)", "(on block_2 block_3)", "(on block_4 block_6)", "(on block_7 block_5)", "(on block_4 block_9)", "(holding block_9)", "(holding block_3)", "(on block_10 block_9)", "(ontable block_5)", "(on block_10 block_5)", "(on block_1 block_7)", "(on block_5 block_8)", "(ontable block_6)", "(on block_4 block_4)", "(on block_1 block_6)", "(on block_6 block_7)", "(on block_9 block_8)", "(on block_10 block_2)", "(on block_2 block_4)", "(on block_8 block_5)", "(on block_8 block_6)", "(on block_6 block_2)", "(holding block_6)", "(holding block_1)", "(on block_8 block_7)", "(on block_8 block_8)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_7 block_4)", "(on block_9 block_5)", "(on block_8 block_2)", "(on block_9 block_10)", "(on block_10 block_1)", "(on block_2 block_6)", "(on block_8 block_9)", "(on block_1 block_4)", "(clear block_7)", "(on block_5 block_1)", "(on block_7 block_7)", "(on block_7 block_3)", "(ontable block_3)", "(clear block_9)", "(on block_6 block_1)", "(on block_10 block_3)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_9 block_3)", "(on block_1 block_5)", "(on block_7 block_8)", "(on block_6 block_6)", "(holding block_7)", "(on block_3 block_3)", "(on block_6 block_9)", "(on block_3 block_4)", "(on block_6 block_10)", "(on block_3 block_9)", "(on block_8 block_4)", "(clear block_8)", "(on block_7 block_6)", "(on block_9 block_7)", "(on block_10 block_4)", "(on block_1 block_2)", "(on block_3 block_10)", "(on block_9 block_6)", "(holding block_8)", "(clear block_1)", "(on block_8 block_1)", "(on block_7 block_2)", "(on block_8 block_10)", "(on block_5 block_6)", "(on block_5 block_10)", "(holding block_10)", "(on block_10 block_6)", "(on block_1 block_3)", "(on block_1 block_10)", "(ontable block_9)", "(clear block_3)", "(on block_2 block_1)", "(on block_6 block_5)", "(on block_4 block_3)", "(on block_6 block_4)", "(on block_3 block_5)", "(on block_5 block_5)", "(on block_2 block_8)", "(on block_10 block_7)", "(ontable block_7)", "(on block_10 block_8)", "(on block_1 block_1)", "(on block_4 block_1)", "(on block_10 block_10)", "(on block_2 block_7)", "(on block_3 block_8)", "(on block_7 block_9)", "(holding block_5)", "(on block_7 block_1)", "(on block_9 block_2)", "(on block_2 block_9)"], "yes": ["(holding block_2)", "(holding block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_2) (clear block_4) (clear block_5) (clear block_6) (handempty) (on block_1 block_8) (on block_3 block_7) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1) (ontable block_10) (ontable block_2) (ontable block_4) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -7107438321777076100, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_20, block_19, block_10, block_13, and block_6. The following block(s) are stacked on top of another block: block_7 is on block_10, block_15 is on block_9, block_3 is on block_13, block_17 is on block_12, block_14 is on block_20, block_16 is on block_11, block_12 is on block_7, block_1 is on block_17, block_18 is on block_5, block_4 is on block_6, block_11 is on block_8, block_2 is on block_14, block_8 is on block_4, block_5 is on block_1, and block_9 is on block_2. The goal is to reach a state where the following facts hold: The block block_19 is currently situated above the block block_16, The block block_10 is currently situated under the block block_7, The block block_15 is currently situated above the block block_9, The block block_7 is currently situated under the block block_12, The block block_6 is currently situated under the block block_4, The block block_17 is currently situated under the block block_1, The block block_11 is on top of block block_8, The block block_14 is currently situated under the block block_2, The block block_5 is currently situated under the block block_18, The block block_5 is on top of block block_1, The block block_3 is currently situated above the block block_13, The block block_12 is currently situated under the block block_17, The block block_14 is on top of block block_20, The block block_4 is currently situated under the block block_8, The block block_11 is currently situated under the block block_16, and The block block_9 is on top of block block_2. The available propositions are: (clear ?x) - ?x is not obstructed by any other blocks, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_13 block_13)", "(on block_16 block_7)", "(on block_4 block_8)", "(on block_13 block_12)", "(on block_6 block_3)", "(on block_11 block_12)", "(on block_9 block_11)", "(on block_3 block_2)", "(on block_17 block_11)", "(on block_12 block_15)", "(on block_16 block_12)", "(on block_17 block_7)", "(on block_20 block_14)", "(on block_5 block_7)", "(on block_20 block_11)", "(on block_4 block_10)", "(on block_11 block_11)", "(on block_4 block_14)", "(ontable block_15)", "(on block_9 block_9)", "(on block_5 block_2)", "(clear block_4)", "(on block_8 block_11)", "(on block_20 block_10)", "(on block_2 block_5)", "(on block_11 block_7)", "(on block_4 block_7)", "(ontable block_2)", "(on block_2 block_12)", "(on block_12 block_8)", "(on block_11 block_4)", "(clear block_11)", "(on block_15 block_4)", "(on block_12 block_2)", "(on block_16 block_15)", "(on block_14 block_18)", "(holding block_15)", "(on block_7 block_5)", "(on block_17 block_15)", "(holding block_9)", "(on block_11 block_15)", "(on block_4 block_20)", "(on block_16 block_13)", "(on block_16 block_20)", "(on block_10 block_9)", "(ontable block_5)", "(on block_11 block_3)", "(on block_15 block_8)", "(on block_10 block_5)", "(on block_6 block_20)", "(on block_5 block_8)", "(on block_13 block_8)", "(on block_19 block_6)", "(on block_15 block_15)", "(on block_18 block_4)", "(on block_10 block_13)", "(clear block_6)", "(on block_1 block_6)", "(on block_4 block_4)", "(on block_6 block_7)", "(on block_19 block_4)", "(on block_9 block_8)", "(on block_19 block_2)", "(on block_10 block_18)", "(on block_16 block_3)", "(on block_12 block_4)", "(on block_15 block_2)", "(on block_9 block_17)", "(on block_19 block_14)", "(on block_2 block_15)", "(on block_8 block_6)", "(on block_4 block_16)", "(on block_17 block_9)", "(on block_8 block_15)", "(on block_12 block_19)", "(on block_6 block_2)", "(holding block_17)", "(on block_5 block_17)", "(on block_18 block_10)", "(on block_8 block_7)", "(on block_15 block_10)", "(on block_16 block_14)", "(on block_3 block_1)", "(on block_5 block_15)", "(on block_5 block_3)", "(on block_2 block_16)", "(on block_5 block_11)", "(on block_12 block_12)", "(on block_15 block_6)", "(on block_16 block_19)", "(on block_7 block_4)", "(on block_8 block_18)", "(on block_15 block_7)", "(on block_9 block_5)", "(on block_12 block_16)", "(on block_8 block_20)", "(on block_8 block_2)", "(on block_2 block_18)", "(on block_14 block_8)", "(on block_9 block_10)", "(on block_8 block_13)", "(holding block_20)", "(on block_8 block_9)", "(on block_15 block_13)", "(on block_15 block_17)", "(on block_20 block_13)", "(on block_19 block_13)", "(holding block_4)", "(on block_19 block_5)", "(on block_11 block_2)", "(ontable block_12)", "(on block_8 block_14)", "(on block_16 block_1)", "(on block_1 block_20)", "(on block_10 block_3)", "(clear block_9)", "(on block_18 block_16)", "(on block_6 block_1)", "(on block_18 block_14)", "(on block_18 block_11)", "(on block_15 block_11)", "(on block_18 block_8)", "(clear block_5)", "(on block_3 block_11)", "(on block_5 block_4)", "(on block_10 block_17)", "(on block_1 block_5)", "(on block_7 block_8)", "(on block_10 block_11)", "(on block_6 block_6)", "(on block_20 block_18)", "(on block_15 block_14)", "(holding block_7)", "(on block_19 block_10)", "(on block_6 block_9)", "(on block_19 block_17)", "(on block_9 block_15)", "(on block_20 block_5)", "(on block_7 block_1)", "(on block_13 block_2)", "(on block_3 block_4)", "(on block_19 block_8)", "(on block_13 block_5)", "(on block_19 block_7)", "(on block_1 block_8)", "(on block_13 block_4)", "(on block_8 block_12)", "(on block_11 block_5)", "(clear block_12)", "(clear block_14)", "(clear block_8)", "(on block_9 block_7)", "(on block_19 block_15)", "(on block_10 block_4)", "(on block_3 block_19)", "(on block_1 block_2)", "(holding block_8)", "(on block_14 block_3)", "(holding block_11)", "(clear block_1)", "(on block_14 block_7)", "(on block_5 block_12)", "(on block_7 block_2)", "(on block_9 block_13)", "(on block_1 block_12)", "(on block_5 block_6)", "(on block_11 block_17)", "(on block_4 block_15)", "(on block_5 block_10)", "(on block_20 block_12)", "(on block_11 block_20)", "(on block_7 block_15)", "(on block_13 block_14)", "(on block_15 block_1)", "(on block_17 block_5)", "(on block_12 block_18)", "(on block_11 block_13)", "(on block_18 block_3)", "(on block_10 block_6)", "(on block_1 block_11)", "(on block_4 block_17)", "(on block_1 block_3)", "(on block_11 block_9)", "(on block_16 block_17)", "(on block_4 block_2)", "(on block_7 block_14)", "(on block_18 block_7)", "(on block_20 block_8)", "(on block_19 block_18)", "(on block_7 block_12)", "(ontable block_9)", "(on block_5 block_13)", "(on block_9 block_14)", "(on block_17 block_8)", "(on block_2 block_1)", "(on block_6 block_5)", "(on block_5 block_16)", "(on block_16 block_5)", "(on block_3 block_5)", "(on block_7 block_17)", "(on block_17 block_13)", "(on block_2 block_8)", "(on block_20 block_15)", "(on block_20 block_6)", "(ontable block_7)", "(on block_9 block_19)", "(on block_4 block_1)", "(ontable block_4)", "(on block_10 block_10)", "(on block_14 block_17)", "(on block_17 block_17)", "(on block_7 block_9)", "(on block_11 block_1)", "(on block_3 block_14)", "(on block_16 block_6)", "(holding block_5)", "(on block_20 block_17)", "(on block_12 block_17)", "(on block_18 block_6)", "(on block_7 block_19)", "(clear block_10)", "(on block_14 block_2)", "(on block_12 block_3)", "(on block_2 block_13)", "(on block_6 block_8)", "(on block_17 block_2)", "(on block_8 block_3)", "(on block_6 block_17)", "(ontable block_16)", "(on block_2 block_20)", "(on block_2 block_10)", "(on block_3 block_6)", "(on block_11 block_18)", "(on block_13 block_16)", "(on block_9 block_4)", "(on block_1 block_9)", "(on block_14 block_15)", "(on block_4 block_5)", "(on block_20 block_7)", "(on block_3 block_17)", "(on block_12 block_13)", "(on block_8 block_19)", "(on block_6 block_19)", "(on block_16 block_9)", "(on block_15 block_12)", "(clear block_17)", "(on block_11 block_19)", "(on block_17 block_16)", "(clear block_13)", "(on block_17 block_20)", "(ontable block_1)", "(on block_2 block_3)", "(on block_18 block_19)", "(on block_16 block_2)", "(on block_18 block_9)", "(on block_19 block_19)", "(on block_9 block_18)", "(on block_4 block_9)", "(on block_15 block_20)", "(on block_19 block_12)", "(on block_20 block_20)", "(holding block_3)", "(on block_3 block_7)", "(clear block_2)", "(on block_2 block_11)", "(on block_14 block_14)", "(on block_8 block_16)", "(on block_9 block_16)", "(on block_19 block_20)", "(on block_20 block_1)", "(on block_19 block_1)", "(on block_12 block_10)", "(on block_1 block_7)", "(on block_10 block_14)", "(on block_13 block_1)", "(on block_4 block_19)", "(on block_16 block_16)", "(ontable block_18)", "(on block_17 block_14)", "(on block_13 block_9)", "(on block_2 block_17)", "(on block_11 block_10)", "(on block_10 block_2)", "(on block_2 block_4)", "(on block_8 block_5)", "(ontable block_8)", "(on block_13 block_18)", "(on block_14 block_16)", "(on block_3 block_16)", "(on block_15 block_3)", "(on block_19 block_11)", "(on block_18 block_12)", "(on block_9 block_1)", "(on block_15 block_18)", "(on block_13 block_20)", "(on block_6 block_13)", "(on block_19 block_3)", "(on block_3 block_12)", "(holding block_6)", "(holding block_1)", "(on block_12 block_6)", "(on block_1 block_15)", "(on block_8 block_8)", "(clear block_20)", "(on block_10 block_19)", "(on block_19 block_9)", "(on block_5 block_20)", "(on block_13 block_15)", "(holding block_2)", "(on block_5 block_18)", "(on block_5 block_9)", "(on block_14 block_10)", "(on block_6 block_15)", "(on block_12 block_14)", "(on block_6 block_18)", "(on block_14 block_19)", "(on block_10 block_1)", "(on block_10 block_16)", "(on block_1 block_13)", "(on block_20 block_3)", "(on block_17 block_3)", "(on block_16 block_10)", "(on block_2 block_6)", "(on block_1 block_4)", "(on block_18 block_18)", "(on block_7 block_18)", "(on block_12 block_9)", "(on block_16 block_18)", "(clear block_7)", "(on block_20 block_4)", "(on block_14 block_11)", "(ontable block_11)", "(on block_7 block_7)", "(on block_20 block_16)", "(ontable block_3)", "(on block_7 block_3)", "(holding block_16)", "(holding block_13)", "(on block_4 block_13)", "(on block_20 block_9)", "(on block_3 block_20)", "(on block_2 block_2)", "(on block_1 block_19)", "(on block_9 block_3)", "(ontable block_17)", "(on block_4 block_12)", "(on block_6 block_16)", "(on block_9 block_12)", "(on block_7 block_13)", "(on block_10 block_20)", "(on block_17 block_10)", "(on block_3 block_3)", "(on block_5 block_19)", "(on block_18 block_1)", "(on block_18 block_15)", "(on block_12 block_11)", "(on block_3 block_15)", "(on block_7 block_11)", "(on block_7 block_16)", "(on block_14 block_6)", "(on block_14 block_12)", "(on block_14 block_1)", "(on block_6 block_10)", "(on block_13 block_7)", "(on block_3 block_9)", "(on block_18 block_20)", "(on block_1 block_16)", "(on block_18 block_2)", "(on block_3 block_18)", "(on block_7 block_6)", "(on block_15 block_19)", "(on block_20 block_19)", "(on block_6 block_14)", "(on block_6 block_12)", "(on block_5 block_14)", "(on block_13 block_11)", "(on block_13 block_17)", "(on block_3 block_10)", "(holding block_12)", "(on block_17 block_18)", "(on block_12 block_20)", "(on block_17 block_4)", "(on block_9 block_6)", "(holding block_14)", "(on block_8 block_1)", "(on block_2 block_19)", "(on block_8 block_10)", "(on block_1 block_14)", "(on block_20 block_2)", "(holding block_10)", "(on block_10 block_15)", "(on block_11 block_16)", "(on block_6 block_11)", "(on block_1 block_10)", "(on block_17 block_1)", "(on block_18 block_17)", "(on block_11 block_14)", "(on block_17 block_19)", "(on block_15 block_5)", "(on block_7 block_20)", "(on block_13 block_6)", "(on block_17 block_6)", "(on block_11 block_6)", "(on block_4 block_3)", "(on block_8 block_17)", "(on block_6 block_4)", "(on block_13 block_19)", "(on block_1 block_18)", "(on block_5 block_5)", "(on block_12 block_1)", "(on block_4 block_18)", "(holding block_18)", "(on block_16 block_4)", "(on block_10 block_7)", "(on block_15 block_16)", "(on block_10 block_8)", "(on block_1 block_1)", "(on block_4 block_11)", "(on block_14 block_9)", "(on block_2 block_7)", "(on block_14 block_4)", "(on block_3 block_8)", "(on block_13 block_3)", "(on block_14 block_13)", "(ontable block_14)", "(on block_14 block_5)", "(on block_10 block_12)", "(on block_12 block_5)", "(on block_13 block_10)", "(on block_9 block_20)", "(on block_16 block_8)", "(on block_18 block_13)", "(on block_2 block_9)"], "yes": ["(holding block_19)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_15) (clear block_16) (clear block_18) (clear block_19) (clear block_3) (handempty) (on block_1 block_17) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (ontable block_10) (ontable block_13) (ontable block_19) (ontable block_20) (ontable block_6))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 5792478122982408763, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_4, block_3 is on block_1, and block_4 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_3 is currently situated under the block block_1, and The block block_4 is on top of block block_5. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(clear block_5)", "(on block_2 block_2)", "(on block_5 block_4)", "(clear block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_5 block_3)", "(ontable block_2)", "(on block_4 block_2)", "(on block_3 block_4)", "(on block_2 block_3)", "(on block_2 block_1)", "(holding block_2)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(ontable block_4)", "(holding block_4)", "(on block_5 block_1)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)", "(ontable block_3)"], "yes": ["(holding block_1)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (handempty) (on block_2 block_4) (on block_3 block_1) (on block_4 block_5) (ontable block_1) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -7241724153464317212, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_2. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_1 is on block_4, block_4 is on block_5, and block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_1 is on top of block block_3, and The block block_5 is currently situated under the block block_4. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(clear block_5)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_5 block_3)", "(ontable block_2)", "(on block_4 block_2)", "(on block_3 block_4)", "(ontable block_1)", "(on block_2 block_3)", "(on block_2 block_1)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(on block_4 block_1)", "(on block_1 block_1)", "(ontable block_4)", "(clear block_2)", "(holding block_4)", "(on block_5 block_1)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)", "(ontable block_3)"], "yes": ["(holding block_1)", "(clear block_4)", "(handempty)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (holding block_2) (on block_1 block_4) (on block_3 block_1) (on block_4 block_5) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 4350095935636809747, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is holding block_2. The following block(s) is on the table: block_1. The following block(s) is stacked on top of another block: block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_2 is currently situated under the block block_1 and The block block_3 is currently situated above the block block_1. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_1 block_1)", "(on block_2 block_3)", "(on block_2 block_1)", "(on block_3 block_3)", "(on block_3 block_2)", "(on block_1 block_3)", "(on block_2 block_2)", "(ontable block_2)", "(holding block_3)", "(ontable block_3)"], "yes": ["(holding block_1)", "(clear block_2)", "(handempty)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_3) (holding block_2) (on block_3 block_1) (ontable block_1))\n    (:goal (and (on block_1 block_2) (on block_3 block_1)))\n)"}
{"id": -65212949148186136, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1, block_4, and block_3. The following block(s) are stacked on top of another block: block_2 is on block_3 and block_5 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_1 is currently situated above the block block_3, and The block block_5 is currently situated under the block block_4. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_3 block_1)", "(on block_5 block_3)", "(ontable block_2)", "(on block_4 block_2)", "(on block_3 block_4)", "(on block_2 block_1)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(ontable block_5)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)"], "yes": ["(holding block_1)", "(clear block_3)", "(holding block_2)", "(holding block_4)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (clear block_5) (handempty) (on block_2 block_3) (on block_5 block_1) (ontable block_1) (ontable block_3) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 8459605840310505775, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_1. The following block(s) is on the table: block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_4 is on block_3, and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_4, The block block_3 is currently situated under the block block_1, and The block block_4 is currently situated above the block block_5. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_4 block_2)", "(on block_3 block_4)", "(on block_2 block_3)", "(ontable block_1)", "(on block_2 block_1)", "(on block_5 block_5)", "(holding block_3)", "(ontable block_5)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(ontable block_4)", "(on block_5 block_1)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)", "(ontable block_3)", "(clear block_1)"], "yes": ["(clear block_3)", "(holding block_2)", "(holding block_4)", "(clear block_5)", "(handempty)", "(clear block_2)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_4) (holding block_1) (on block_3 block_5) (on block_4 block_3) (on block_5 block_2) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
