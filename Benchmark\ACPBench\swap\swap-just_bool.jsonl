{"id": 2102032783078088455, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book03, book04, book01, and book02. Currently, bob is assigned book01, zoe is assigned book04, vic is assigned book03, and steve is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, vic is assigned book04, steve is assigned book03, and zoe is assigned book01.", "question": "Given the plan: \"swap steve:book02 with vic:book03, swap zoe:book04 with steve:book03, swap bob:book01 with vic:book02, swap vic:book01 with steve:book04, swap steve:book01 with zoe:book03\"; can the following action be removed from this plan and still have a valid plan: swap vic:book01 with steve:book04?", "answer": "no"}
{"id": -5891027429848956335, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book03, book04, book01, and book02. Currently, bob is assigned book01, zoe is assigned book04, vic is assigned book03, and steve is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, vic is assigned book04, steve is assigned book03, and zoe is assigned book01.", "question": "Given the plan: \"trade book03 of vic for book04 of zoe, trade book03 of zoe for book04 of vic, trade book02 of steve for book01 of bob, trade book01 of steve for book04 of zoe, trade book03 of vic for book04 of steve\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: trade book03 of vic for book04 of zoe and trade book03 of zoe for book04 of vic?", "answer": "yes"}
{"id": 7531037506289764247, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 3 agents: grace, xena, and judy. There are 3 items/roles: funnel, xactoknife, and sander. Currently, judy is assigned funnel, grace is assigned xactoknife, and xena is assigned sander. The goal is to reach a state where the following facts hold: judy is assigned xactoknife, xena is assigned funnel, and grace is assigned sander.", "question": "Given the plan: \"exchange sander of xena with funnel of judy, exchange funnel of xena with xactoknife of grace, exchange xactoknife of xena with funnel of grace, exchange sander of judy with xactoknife of grace\"; can the following action be removed from this plan and still have a valid plan: exchange sander of judy with xactoknife of grace?", "answer": "no"}
{"id": -4711839525485731131, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, zoe, michelle, alice, carol, dave, vic, and heidi. There are 8 items/roles: necklace, zebra, slinky, guitar, iceskates, quadcopter, whale, and frisbee. Currently, zoe is assigned frisbee, heidi is assigned necklace, dave is assigned slinky, alice is assigned iceskates, vic is assigned quadcopter, xena is assigned whale, michelle is assigned zebra, and carol is assigned guitar. The goal is to reach a state where the following facts hold: zoe is assigned whale, michelle is assigned quadcopter, vic is assigned necklace, carol is assigned frisbee, heidi is assigned guitar, dave is assigned iceskates, xena is assigned slinky, and alice is assigned zebra.", "question": "Given the plan: \"exchange quadcopter of vic with whale of xena, exchange whale of vic with quadcopter of xena, exchange iceskates of alice with slinky of dave, exchange whale of xena with slinky of alice, exchange frisbee of zoe with whale of alice, exchange necklace of heidi with guitar of carol, exchange frisbee of alice with zebra of michelle, exchange necklace of carol with quadcopter of vic, exchange quadcopter of carol with frisbee of michelle\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: exchange quadcopter of vic with whale of xena and exchange whale of vic with quadcopter of xena?", "answer": "yes"}
{"id": 5927907804443383825, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, alice, bob, dave, ted, heidi, and kevin. There are 7 items/roles: mushroom, parsnip, quince, ulluco, leek, yam, and valerian. Currently, heidi is assigned quince, bob is assigned leek, ted is assigned ulluco, xena is assigned parsnip, alice is assigned yam, kevin is assigned mushroom, and dave is assigned valerian. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, dave is assigned ulluco, and ted is assigned leek.", "question": "Given the plan: \"exchange leek of bob with parsnip of xena, exchange valerian of dave with leek of xena, exchange leek of dave with ulluco of ted, exchange yam of alice with quince of heidi, exchange mushroom of kevin with yam of heidi, exchange valerian of xena with quince of alice\"; can the following action be removed from this plan and still have a valid plan: exchange leek of bob with parsnip of xena?", "answer": "no"}
{"id": 6721424506265291166, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, alice, bob, dave, ted, heidi, and kevin. There are 7 items/roles: mushroom, parsnip, quince, ulluco, leek, yam, and valerian. Currently, heidi is assigned quince, bob is assigned leek, ted is assigned ulluco, xena is assigned parsnip, alice is assigned yam, kevin is assigned mushroom, and dave is assigned valerian. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, dave is assigned ulluco, and ted is assigned leek.", "question": "Given the plan: \"swap bob with ted, leek for ulluco, swap ted with bob, leek for ulluco, swap dave with alice, valerian for yam, swap bob with dave, leek for yam, swap kevin with heidi, mushroom for quince, swap kevin with bob, quince for yam, swap dave with ted, leek for ulluco, swap bob with xena, quince for parsnip\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: swap bob with ted, leek for ulluco and swap ted with bob, leek for ulluco?", "answer": "yes"}
{"id": -3420810492104366399, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book03, book04, book01, and book02. Currently, bob is assigned book01, zoe is assigned book04, vic is assigned book03, and steve is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, vic is assigned book04, steve is assigned book03, and zoe is assigned book01.", "question": "Given the plan: \"exchange book04 of zoe with book02 of steve, exchange book04 of steve with book02 of zoe, exchange book02 of steve with book03 of vic, exchange book02 of vic with book01 of bob, exchange book04 of zoe with book01 of vic\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: exchange book04 of zoe with book02 of steve and exchange book04 of steve with book02 of zoe?", "answer": "yes"}
{"id": 5113069776237607197, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, liam, bob, vic, frank, and quentin. There are 6 items/roles: wrench, sander, knead, pliers, ratchet, and nibbler. Currently, vic is assigned nibbler, frank is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, liam is assigned sander, vic is assigned ratchet, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench.", "question": "Given the plan: \"trade ratchet of quentin for nibbler of vic, trade nibbler of quentin for knead of liam, trade pliers of xena for wrench of bob, trade sander of frank for pliers of bob, trade nibbler of liam for sander of bob\"; can the following action be removed from this plan and still have a valid plan: trade pliers of xena for wrench of bob?", "answer": "no"}
{"id": 8573929718351450571, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, liam, bob, vic, frank, and quentin. There are 6 items/roles: wrench, sander, knead, pliers, ratchet, and nibbler. Currently, vic is assigned nibbler, frank is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, liam is assigned sander, vic is assigned ratchet, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench.", "question": "Given the plan: \"trade nibbler of vic for ratchet of quentin, trade nibbler of quentin for ratchet of vic, trade knead of liam for nibbler of vic, trade knead of vic for ratchet of quentin, trade pliers of xena for wrench of bob, trade sander of frank for nibbler of liam, trade nibbler of frank for pliers of bob\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: trade nibbler of vic for ratchet of quentin and trade nibbler of quentin for ratchet of vic?", "answer": "yes"}
{"id": -2663677181136765059, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, liam, bob, vic, frank, and quentin. There are 6 items/roles: wrench, sander, knead, pliers, ratchet, and nibbler. Currently, vic is assigned nibbler, frank is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, liam is assigned sander, vic is assigned ratchet, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench.", "question": "Given the plan: \"exchange nibbler of vic with ratchet of quentin, exchange nibbler of quentin with knead of liam, exchange pliers of xena with wrench of bob, exchange sander of frank with pliers of bob, exchange nibbler of liam with sander of bob\"; can the following action be removed from this plan and still have a valid plan: exchange sander of frank with pliers of bob?", "answer": "no"}
