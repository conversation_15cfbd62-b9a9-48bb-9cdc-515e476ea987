{"id": 7474610897774792912, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t1 is at l1-2, p3 and t0 are at l0-1, a0 is at l0-0, p0, p2, and p1 are in t0.", "question": "Is it possible to transition to a state where the following holds: l0-1 is in a0?", "answer": "no"}
{"id": -2981570131346945014, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0; l3-1, l3-2, and l3-0 are in c3; l4-0, l4-1, and l4-2 are in c4; l2-0, l2-1, and l2-2 are in c2. Currently, p2 and t2 are at l2-2, t4 and a0 are at l4-0, t1 is at l1-2, t0 is at l0-2, p1 is at l3-1, t3 is at l3-0, p0 and p3 are in a0.", "question": "Is it possible to transition to a state where the following holds: p0 is at l2-2 and p0 is at l0-2?", "answer": "no"}
{"id": 8810249779840829756, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t0 and p3 are at l0-2, t1 is at l1-1, a0 is at l0-0, p2 is in a0, p0 and p1 are in t0.", "question": "Is it possible to transition to a state where the following holds: a0 is at l1-0?", "answer": "yes"}
{"id": -4540434072416334212, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p2 and p0 are at l0-1, a0 is at l1-0, t1 is at l1-1, t0 and p3 are at l0-0, p1 is in a0.", "question": "Is it possible to transition to a state where the following holds: l0-0 is at t0 and p2 is at l0-1?", "answer": "no"}
{"id": -7845509779827378671, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 is at l1-0, t1 is at l1-2, t0 and p3 are at l0-2, p0, p1, and p2 are in t0.", "question": "Is it possible to transition to a state where the following holds: p1 is in a0 and p1 is at l1-2?", "answer": "no"}
{"id": -3007841307099444791, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-6, l1-8, l1-2, l1-1, l1-7, l1-3, l1-0, l1-9, l1-4, and l1-5 are in c1; l3-3, l3-8, l3-6, l3-2, l3-0, l3-7, l3-1, l3-4, l3-9, and l3-5 are in c3; l2-5, l2-0, l2-4, l2-3, l2-7, l2-6, l2-8, l2-9, l2-1, and l2-2 are in c2; l4-3, l4-9, l4-4, l4-6, l4-2, l4-1, l4-7, l4-0, l4-5, and l4-8 are in c4; l0-4, l0-3, l0-8, l0-6, l0-0, l0-9, l0-5, l0-2, l0-1, and l0-7 are in c0. Currently, p2 and t3 are at l3-0, a0 is at l1-0, t4 is at l4-0, t0 is at l0-2, t1 is at l1-1, p1 is at l1-8, t2 is at l2-4, p0 and p3 are in t2.", "question": "Is it possible to transition to a state where the following holds: p3 is at l2-4 and p3 is in t3?", "answer": "no"}
{"id": -333617104970359206, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p2, p3, t0, and p0 are at l0-1, t1 is at l1-2, a0 is at l0-0, p1 is in t0.", "question": "Is it possible to transition to a state where the following holds: p0 is at l0-0 and p0 is at l0-1?", "answer": "no"}
{"id": -6277981076939153131, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0; l2-0, l2-1, and l2-2 are in c2. Currently, a0, p4, p1, and t1 are at l1-0, t0 is at l0-2, t2 is at l2-1, p0 and p3 are in t1, p2 is in t0.", "question": "Is it possible to transition to a state where the following holds: p3 is at l1-0 and t0 is at l0-0?", "answer": "yes"}
{"id": 5759802802624466637, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-6, l1-8, l1-2, l1-1, l1-7, l1-3, l1-0, l1-9, l1-4, and l1-5 are in c1; l3-3, l3-8, l3-6, l3-2, l3-0, l3-7, l3-1, l3-4, l3-9, and l3-5 are in c3; l2-5, l2-0, l2-4, l2-3, l2-7, l2-6, l2-8, l2-9, l2-1, and l2-2 are in c2; l4-3, l4-9, l4-4, l4-6, l4-2, l4-1, l4-7, l4-0, l4-5, and l4-8 are in c4; l0-4, l0-3, l0-8, l0-6, l0-0, l0-9, l0-5, l0-2, l0-1, and l0-7 are in c0. Currently, t2, p0, a0, and p1 are at l2-0, t0 is at l0-2, t4 and p2 are at l4-8, t3 is at l3-0, t1 is at l1-0, p3 is in t2.", "question": "Is it possible to transition to a state where the following holds: t2 is at l2-7 and t4 is at l4-3?", "answer": "yes"}
{"id": 2019778659293841791, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p2 and p0 are at l0-1, a0 and t1 are at l1-0, t0 is at l0-0, p1 is in t1, p3 is in a0.", "question": "Is it possible to transition to a state where the following holds: p0 is in t1 and p0 is in a0?", "answer": "no"}
{"id": -8598089088107253298, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t1, p1, and p2 are at l1-2, t0, a0, and p3 are at l0-0, p0 is in a0.", "question": "Is it possible to transition to a state where the following holds: t1 is at l1-0 and t0 is at l0-1?", "answer": "yes"}
{"id": -7437489527440600797, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t1 is at l1-2, t0 and a0 are at l0-0, p1 is at l1-1, p3 and p2 are in t0, p0 is in a0.", "question": "Is it possible to transition to a state where the following holds: t1 is at l1-1?", "answer": "yes"}
{"id": 873589922904707629, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p3 and p1 are at l1-0, t1 is at l1-1, t0 and a0 are at l0-0, p2 is in a0, p0 is in t1.", "question": "Is it possible to transition to a state where the following holds: p0 is at c1 and p3 is at l1-0?", "answer": "no"}
{"id": -1396451365000157850, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p2 and p1 are at l1-0, t1 is at l1-1, a0 is at l0-0, t0 is at l0-1, p0 and p3 are in a0.", "question": "Is it possible to transition to a state where the following holds: p3 is at l0-0?", "answer": "yes"}
{"id": 176148927386011603, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, a0, p1, and t1 are at l1-0, p0, t0, and p3 are at l0-0, p2 is in a0.", "question": "Is it possible to transition to a state where the following holds: l0-1 is at l1-1 and p1 is at l1-0?", "answer": "no"}
