{"question_id": "f8092bb8-5b7a-4ba2-ae44-7307be690729", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: cargo c2 is loaded in vehicle v0 with space s0 and space s1 at location l1. Is the action: at location l1, cargo c2 is loaded in vehicle v0 with spaces s0 and s1 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: cargo c2 is to be loaded into vehicle v0 utilizing spaces s0 and s1 at location l1. Is the action of loading cargo c2 into vehicle v0 with spaces s0 and s1 at location l1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also present at l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "2f89458d-757a-43ad-a495-f3fe8c5c1c97", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at location l1, cargo c2 is unloaded from vehicle v1 with spaces s1 and s2. Is the action: at location l1, cargo c2 is unloaded from vehicle v1 with spaces s1 and s2 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: at location l1, cargo c2 is to be unloaded from vehicle v1 that has spaces s1 and s2. Is the action: at location l1, cargo c2 is unloaded from vehicle v1 with spaces s1 and s2, executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "d975e64a-8e78-4c52-a762-2e7c6fae0e24", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f4 and f0 to location l1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5 and vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: at location l0, cargo c1 is to be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l0, cargo c4 is to be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l0, cargo c7 is to be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l0, cargo c8 is to be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l1, cargo c6 is to be loaded into vehicle v0 utilizing spaces s1 and s0, at location l1, cargo c8 is to be loaded into vehicle v0 utilizing spaces s1 and s0, cargo c1 is to be loaded into vehicle v0 utilizing space s1 and space s0 at location l1, cargo c4 is to be loaded into vehicle v0 utilizing space s1 and space s0 at location l1, cargo c6 is to be unloaded from vehicle v0 utilizing space s0 and space s1 at location l0, cargo c7 is to be loaded into vehicle v0 utilizing space s1 and space s0 at location l1, vehicle v0 is set to move from location l0, which has fuel levels f4 and f0, to location l1, vehicle v0 is set to move from location l0, which has fuel levels f4 and f3, to location l1, vehicle v0 is set to move from location l0, which has fuel levels f5 and f4, to location l1, vehicle v0 is set to move to location l0 from location l1, which has fuel levels f2 and f1, vehicle v0 is set to move to location l0 from location l1, which has fuel levels f4 and f3, vehicle v0 is set to move to location l0 from location l1, which has fuel levels f5 and f4, vehicle v0 is set to move to location l0 from location l1, which has fuel levels f6 and f5, vehicle v0 is set to move to location l1 from location l0, which has fuel levels f6 and f5, and vehicle v0 is set to move to location l1 from location l0, which has fuel levels f7 and f6. Is it possible to execute this plan, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is also located at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, f1 and f2 are neighboring fuel levels, f2 and f3 are also neighbors, and f4 and f5 are adjacent. Location l0 contains fuel f7 and is connected to location l1, which in turn is connected to l0. Space s0 is adjacent to space s1, and space s1 is part of vehicle v0, which is situated at location l1."}
{"question_id": "449777ed-04c4-4f72-b139-5b7a4e05a0ff", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 and vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location l0, cargo c2 is loaded into vehicle v0 utilizing spaces s1 and s0, at location l0, cargo c3 is loaded into vehicle v0 utilizing spaces s2 and s1, at location l1, cargo c0 is unloaded from vehicle v0 utilizing spaces s0 and s1, at location l1, cargo c10 is loaded into vehicle v0 utilizing spaces s1 and s0, at location l1, cargo c7 is unloaded from vehicle v0 utilizing spaces s0 and s2, cargo c0 is loaded into vehicle v0 utilizing space s2 and space s1 at location l0, cargo c2 is unloaded from vehicle v0 utilizing space s0 and space s1 at location l1, vehicle v0 travels from location l1, which has fuel levels f2 and f1, to location l0, vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, and vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is located at l1, cargo c6 is situated at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are connected, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are interconnected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "86ea7f68-44dd-4331-a5db-a1d45b50f22c", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0. Is the action: at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 executable at step 16, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: vehicle v0 will move from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c0 will be loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 will also be loaded into vehicle v0, occupying spaces s1 and s0. Next, vehicle v0 will move from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 will be unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 will be loaded into vehicle v0, occupying spaces s1 and s0. Additionally, cargo c2 will be unloaded from vehicle v0, freeing up spaces s0 and s1, at location l1. Vehicle v0 will then move from location l1, which has fuel levels f2 and f1, to location l0. At location l0, cargo c10 will be unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 will be loaded into vehicle v0, occupying space s2 and space s1. Furthermore, cargo c4 will be loaded into vehicle v0, occupying spaces s1 and s0, at location l0. Vehicle v0 will then move from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c3 will be unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c4 will be unloaded from vehicle v0, freeing up spaces s1 and s2. Cargo c5 will be loaded into vehicle v0, occupying space s2 and space s1, and cargo c9 will be loaded into vehicle v0, occupying space s1 and space s0, at location l1. Vehicle v0 will then move from location l1, which has fuel levels f1 and f0, to location l0. At location l0, cargo c5 will be unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c6 will be loaded into vehicle v0, occupying space s1 and space s0. Is the action of loading cargo c9 into vehicle v0, occupying spaces s1 and s0, at location l1 executable at step 16, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "f7b4199d-9c22-4a92-a75c-3bf50dff501b", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0. Is the action: vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1 executable at step 4, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: vehicle v0 is set to move from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded into vehicle v0, occupying spaces s1 and s0. Next, vehicle v0 is scheduled to move from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0. Additionally, at location l1, cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1. Then, vehicle v0 is set to move from location l1, which has fuel levels f2 and f1, to location l0. Upon arrival at location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1. Is the action of vehicle v0 moving from location l0, which has fuel levels f4 and f3, to location l1 executable at step 4, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "2cd54098-8357-44ac-8dc5-b8a8272a3a4d", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: vehicle v0 is intended to relocate from location l1, which has fuel levels f3 and f2, to location l0. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "961a9801-b829-420c-902d-dbbe96ec507e", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0. Is the action: at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 executable at step 3, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: vehicle v0 is set to move from location l0, which has fuel levels of f5 and f4, to location l1, then cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. At location l0, cargo c5 is set to be unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c0 is also set to be unloaded from vehicle v0, utilizing spaces s0 and s1. Next, vehicle v0 is set to move from location l0, which has fuel levels of f4 and f3, to location l1. Then, cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Vehicle v0 is then set to move from location l1, which has fuel levels of f6 and f5, to location l0. At location l0, cargo c1 is set to be unloaded from vehicle v0, utilizing spaces s0 and s1. Vehicle v0 is then set to move from location l0, which has fuel levels of f3 and f2, to location l1. At location l1, cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 is then set to move from location l1, which has fuel levels of f5 and f4, to location l0. Cargo c2 is then set to be unloaded from vehicle v0, utilizing spaces s0 and s1, at location l0. Vehicle v0 is then set to move from location l0, which has fuel levels of f2 and f1, to location l1. At location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 is then set to move from location l1, which has fuel levels of f4 and f3, to location l0. Cargo c4 is then set to be unloaded from vehicle v0, utilizing spaces s0 and s1, at location l0. Vehicle v0 is then set to move from location l0, which has fuel levels of f1 and f0, to location l1. At location l1, cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, and vehicle v0 is then set to move from location l1, which has fuel levels of f3 and f2, to location l0. Is the action of unloading cargo c5 from vehicle v0, utilizing spaces s0 and s1, at location l0 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "e2f31316-42c1-42a8-95e6-385f757d129c", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f7. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location l0, cargo c1 will be loaded onto vehicle v0 utilizing spaces s1 and s0, while at the same location l0, cargo c2 will be unloaded from vehicle v0 using spaces s0 and s1. Next, at location l1, cargo c2 will be loaded onto vehicle v0 with spaces s1 and s0, followed by the loading of cargo c3 onto vehicle v0 with spaces s1 and s0 at the same location l1. Additionally, cargo c0 will be unloaded from vehicle v0 using space s0 and space s1 at location l1, and cargo c3 will be unloaded from vehicle v0 using space s0 and space s1 at location l0. Furthermore, vehicle v0 will travel from location l1 to location l0 with fuel levels f7 and f6, and also from location l1 to location l0 with fuel levels f8 and f7. In the opposite direction, vehicle v0 will move from location l0 to location l1 with fuel levels f3 and f2, and also from location l0 to location l1 with fuel levels f5 and f7. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at location l0, while cargo c1 is also found at location l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1. Cargo c6 and cargo c7 are also present at location l0. Fuel f3 is available at location l0, whereas fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1. However, vehicle v1, which also contains space s1, is present at location l0."}
{"question_id": "7cea67b7-0ce0-4964-b75b-3f7005e6f987", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4. Is the action: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are planned: vehicle v0 is scheduled to move from location l0, which has fuel levels of f5 and f4, to location l1. Is the action of moving vehicle v0 from location l0 with fuel levels f5 and f4 to location l1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "b7b7401c-cd44-4431-9588-a6d5dbb7577b", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0. Is the action: at location l1, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1 executable at step 3, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, where the fuel levels are f6 and f5, to location l0. At location l1, cargo c6 is then unloaded from vehicle v0, utilizing spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, using spaces s1 and s0, and then vehicle v0 travels to location l0 from location l1, which has fuel levels f5 and f4. Cargo c4 is then unloaded from vehicle v0 at location l0, using spaces s0 and s1. Vehicle v0 then moves from location l0, which has fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded onto vehicle v0, using spaces s1 and s0, and then vehicle v0 travels to location l0 from location l1, which has fuel levels f4 and f3. Cargo c6 is then unloaded from vehicle v0 at location l0, using spaces s0 and s1. Vehicle v0 then moves from location l0, which has fuel levels f5 and f4, back to location l1. At location l1, cargo c7 is loaded onto vehicle v0, using spaces s1 and s0, and then vehicle v0 travels from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c7 is then unloaded from vehicle v0, using spaces s0 and s1. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, back to location l1. At location l1, cargo c8 is loaded onto vehicle v0, using spaces s1 and s0, and then vehicle v0 travels to location l0 from location l1, which has fuel levels f2 and f1. Finally, cargo c8 is unloaded from vehicle v0 at location l0, using spaces s0 and s1. Is the action: at location l1, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Fuel f6 is also located at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f4 and f5. Location l0 is equipped with fuel f7 and is connected to location l1, which in turn is connected to l0. Space s0 is adjacent to space s1, and space s1 is contained within vehicle v0, which is situated at location l1."}
{"question_id": "37c937cf-3523-46e4-83c9-54d605a57d1a", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7 and vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: at location l0, cargo c0 will be loaded onto vehicle v0 utilizing spaces s1 and s0, at location l0, cargo c2 will be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l0, cargo c3 will be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l1, cargo c0 will be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l1, cargo c2 will be loaded onto vehicle v0 utilizing spaces s1 and s0, cargo c1 will be loaded onto vehicle v0 utilizing space s1 and space s0 at location l0, cargo c3 will be loaded onto vehicle v0 utilizing space s1 and space s0 at location l1, vehicle v0 will travel from location l1 to location l0 with fuel levels f7 and f6, vehicle v0 will travel from location l1 to location l0 with fuel levels f8 and f7, and vehicle v0 will travel from location l0 to location l1 with fuel levels f3 and f2. Is it possible to execute this plan, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also present at l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "916a9c6f-c171-4981-bba6-8dc328021e47", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4 and vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is proposed: at location l1, cargo c4 is loaded into vehicle v0 utilizing spaces s1 and s0, and cargo c1 is also loaded into vehicle v0 using spaces s1 and s0 at location l1. Subsequently, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, and cargo c4 is also unloaded from vehicle v0 with spaces s0 and s1. Additionally, at location l1, cargo c6 is loaded into vehicle v0 using spaces s1 and s0. Vehicle v0 is then scheduled to move from location l0, which has fuel levels f6 and f5, to location l1, and also from location l0, which has fuel levels f7 and f6, to location l1. Furthermore, vehicle v0 is planned to move from location l1, which has fuel levels f4 and f3, to location l0, and also from location l1, which has fuel levels f5 and f4, to location l0, and from location l1, which has fuel levels f6 and f5, to location l0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, fuel levels f1 and f2 are neighboring, as are f2 and f3, and f4 and f5. Location l0 is equipped with fuel f7 and is connected to location l1, which is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at location l1."}
{"question_id": "0bfb753d-6a34-4a9f-b463-89a669155c6b", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0. Is the action: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: vehicle v0 is set to move from location l1, which has fuel levels f3 and f2, to location l0. Is the action of moving vehicle v0 from location l1 with fuel levels f3 and f2 to location l0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is found at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "45889109-31ce-4650-8f16-8c30a2f980ca", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: vehicle v0 moves from location l1 which has fuel-levels f5 and f6 to location l0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: vehicle v0 is intended to move from location l1, which has fuel levels of f5 and f6, to location l0. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, while cargo c10 is found at l0, cargo c2 is situated at l0, cargo c3 is positioned at l0, cargo c4 is at l1, cargo c5 is present at l1, cargo c6 is also present at l1, cargo c7 is situated at l1, cargo c8 is present at l1, and cargo c9 is present at l1. Fuel f6 is available at l1. Fuel levels are connected as follows: f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f1 and f2 are neighboring fuel levels, f2 and f3 are neighboring fuel levels, and f4 and f5 are neighboring fuel levels. Location l0 has fuel f7, and it is connected to l1, which is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at l1."}
{"question_id": "3912e4dc-d2d5-4e7b-8b90-3bfd81f69d1f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7 and vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: at location l0, vehicle v0 with spaces s1 and s0 will be loaded with cargo c0, and then at the same location, cargo c1 will also be loaded into vehicle v0 using spaces s1 and s0. Subsequently, at location l0, vehicle v0 with spaces s0 and s1 will be unloaded of cargo c2, cargo c3, and cargo c5. Following this, cargo c7 will be loaded into vehicle v0 at location l0 using spaces s1 and s0. Then, at location l1, cargo c9 will be loaded into vehicle v0 with spaces s1 and s0. At location l1, vehicle v0 will be unloaded of cargo c0 and cargo c1 using spaces s0 and s1. Next, at location l1, vehicle v0 with spaces s1 and s0 will be loaded with cargo c2, cargo c3, and cargo c5. After that, cargo c7 will be unloaded from vehicle v0 at location l1 using spaces s0 and s1. Vehicle v0 will then move from location l0, which has fuel levels f2 and f1, to location l1, and then from location l0 with fuel levels f3 and f2 to location l1. The vehicle will then move from location l1, which has fuel levels f7 and f6, to location l0, and then from location l1 with fuel levels f6 and f5 to location l0. Additionally, vehicle v0 will move from location l1 with fuel levels f8 and f7 to location l0, and finally from location l0 with fuel levels f1 and f0 to location l1. Is it possible to execute this sequence of actions, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at l0, and fuel f8 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at l1, and also within vehicle v1, which is present at l0."}
{"question_id": "dc792330-80af-448b-82db-5d898d12a17f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and vehicle v1 moves from location l0 which has fuel-levels f5 and f4 to location l1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: at location l0, cargo c0 will be loaded onto vehicle v0 utilizing spaces s1 and s0, at location l0, cargo c2 will be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l0, cargo c5 will be unloaded from vehicle v0 utilizing spaces s0 and s1, at location l0, cargo c7 will be loaded onto vehicle v0 utilizing spaces s1 and s0, at location l1, cargo c2 will be loaded onto vehicle v0 utilizing spaces s1 and s0, at location l1, cargo c3 will be loaded onto vehicle v0 utilizing spaces s1 and s0, at location l1, cargo c7 will be unloaded from vehicle v0 utilizing spaces s0 and s1, cargo c0 will be unloaded from vehicle v0 utilizing space s0 and space s1 at location l1, cargo c1 will be loaded onto vehicle v0 utilizing space s1 and space s0 at location l0, cargo c1 will be unloaded from vehicle v0 utilizing space s0 and space s1 at location l1, cargo c3 will be unloaded from vehicle v0 utilizing space s0 and space s1 at location l0, cargo c5 will be loaded onto vehicle v0 utilizing space s1 and space s0 at location l1, vehicle v0 will travel from location l0, which has fuel levels f1 and f0, to location l1, vehicle v0 will travel from location l0, which has fuel levels f2 and f1, to location l1, vehicle v0 will travel to location l0 from location l1, which has fuel levels f6 and f5, vehicle v0 will travel to location l0 from location l1, which has fuel levels f7 and f6, vehicle v0 will travel to location l0 from location l1, which has fuel levels f8 and f7, vehicle v0 will travel to location l1 from location l0, which has fuel levels f3 and f2, and vehicle v1 will travel from location l0, which has fuel levels f5 and f4, to location l1. Is it possible to execute this plan, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at l0, and fuel f8 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at l1, and also within vehicle v1, which is present at l0."}
{"question_id": "208fd31d-fcc7-4f1f-9a6a-0a5bd0629363", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c2 is unloaded from vehicle v1 with spaces s0 and s1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: at location l0, cargo c2 is to be unloaded from vehicle v1, which has spaces s0 and s1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "c933c2a9-00d0-493d-ad70-3bbdec27343b", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f6. Is the action: vehicle v0 moves from location l1 which has fuel-levels f3 and f6 to location l0 executable at step 10, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Then, vehicle v0 travels from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, at location l0, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is then unloaded from vehicle v0, which has spaces s0 and s1. Following this, cargo c3 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Then, vehicle v0 travels from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and vehicle v0 moves from location l1, which has fuel levels f3 and f6, to location l0. Is the action: vehicle v0 moves from location l1, which has fuel levels f3 and f6, to location l0 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at location l0, while cargo c1 is also found at location l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at location l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "b19578d8-257d-4bae-b95e-c76360c25b1a", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f4 and f8 to location l0. Is the action: vehicle v0 moves from location l1 which has fuel-levels f4 and f8 to location l0 executable at step 10, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: at location l1, cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0 using spaces s0 and s1, after which vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1, then cargo c4 is loaded into vehicle v0 using spaces s1 and s0 at location l1, followed by vehicle v0 moving from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0 using spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1, next cargo c6 is loaded into vehicle v0 using spaces s1 and s0 at location l1, and finally vehicle v0 moves from location l1, with fuel levels f4 and f8, to location l0. Is the action: vehicle v0 moves from location l1, with fuel levels f4 and f8, to location l0 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, f1 and f2 are neighboring fuel levels, f2 and f3 are also neighbors, and f4 and f5 are adjacent. Location l0 has fuel f7 and is connected to location l1, which in turn is connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at location l1."}
{"question_id": "c12d87b8-c014-4d82-81e1-a0b393047cf4", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1. Is the action: cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 executable at step 18, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 moves from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l1, with fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l0, with fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0. Is the action: cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 executable at step 18, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 also present at l0. Fuel f3 is available at l0, and fuel f8 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at l1, and also within vehicle v1, which is present at l0."}
{"question_id": "6b3190ce-4de2-4b10-a7a6-4dce4aebfbac", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: vehicle v0 is intended to move from location l0, which has fuel levels of f5 and f4, to location l1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is at l1, cargo c7 is found at l1, cargo c8 is at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "7d1cf84b-c160-476b-869f-e08b1750f198", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0. Is the action: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: at location l1, cargo c1 is to be loaded into vehicle v0 utilizing spaces s1 and s0. Is the action: at location l1, cargo c1 is loaded into vehicle v0 utilizing spaces s1 and s0, executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, while cargo c10 is found at l0, cargo c2 is situated at l0, cargo c3 is positioned at l0, cargo c4 is situated at l1, cargo c5 is present at l1, cargo c6 is also present at l1, cargo c7 is located at l1, cargo c8 is present at l1, and cargo c9 is present at l1. Fuel f6 is available at l1. The fuel levels are connected as follows: f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f1 and f2 are neighboring fuel levels, f2 and f3 are neighboring fuel levels, and f4 and f5 are neighboring fuel levels. Location l0 is equipped with fuel f7. Locations l0 and l1 are interconnected. Space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently situated at location l1."}
{"question_id": "27e60e55-f75b-4bcc-aa84-003b8e3405f2", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at location l0, cargo c0 will be loaded onto vehicle v0 utilizing spaces s2 and s1, then at the same location l0, cargo c10 will be unloaded from vehicle v0 using spaces s1 and s2, followed by the loading of cargo c3 onto vehicle v0 with spaces s2 and s1 at location l0, then the unloading of cargo c5 from vehicle v0 with spaces s0 and s1 at location l0, next the loading of cargo c6 onto vehicle v0 with spaces s1 and s0 at location l0, at location l1, cargo c0 will be unloaded from vehicle v0 using spaces s0 and s1, then cargo c2 will be unloaded from vehicle v0 with spaces s0 and s1 at location l1, followed by the unloading of cargo c4 from vehicle v0 with spaces s1 and s2 at location l1, then the loading of cargo c9 onto vehicle v0 with spaces s1 and s0 at location l1, cargo c10 will be loaded onto vehicle v0 with space s1 and space s0 at location l1, cargo c2 will be loaded onto vehicle v0 with space s1 and space s0 at location l0, cargo c3 will be unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 will be loaded onto vehicle v0 with space s1 and space s0 at location l0, cargo c5 will be loaded onto vehicle v0 with space s2 and space s1 at location l1, vehicle v0 will move from location l0 with fuel levels f3 and f2 to location l1, then from location l0 with fuel levels f4 and f3 to location l1, followed by a move from location l1 with fuel levels f1 and f0 to location l0, then from location l1 with fuel levels f2 and f1 to location l0, and finally vehicle v0 will move from location l1 with fuel levels f3 and f2 to location l0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "ebd8c1bd-2709-44d9-aae6-5022333adc5f", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1. Is the action: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 executable at step 10, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: vehicle v0 is set to move from location l0, which has fuel levels of f5 and f4, to location l1, then cargo c0 is loaded into vehicle v0 utilizing spaces s1 and s0 at location l1. Next, vehicle v0 is scheduled to move from location l1, which has fuel levels of f7 and f6, back to location l0, where cargo c0 is then unloaded from vehicle v0 using spaces s0 and s1. The sequence continues with vehicle v0 moving from location l0, which has fuel levels of f4 and f3, to location l1, followed by the loading of cargo c1 into vehicle v0 using spaces s1 and s0 at location l1. Then, vehicle v0 moves from location l1, which has fuel levels of f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0 using spaces s0 and s1. Finally, vehicle v0 moves from location l0, which has fuel levels of f3 and f2, to location l1, and cargo c2 is loaded into vehicle v0 using spaces s1 and s0 at location l1. Is the action of loading cargo c2 into vehicle v0 with spaces s1 and s0 at location l1 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is at l1, cargo c7 is found at l1, cargo c8 is at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "81b07ed1-acb9-4e7c-a3c8-f8222b7730e0", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: at location l0, cargo c0 is to be unloaded from vehicle v0, which has spaces s0 and s1, and at the same location l0, cargo c1 is also to be unloaded from vehicle v0 with the same spaces s0 and s1. Then, at location l1, cargo c0 is to be loaded into vehicle v0 with spaces s1 and s0, followed by loading cargo c2 into vehicle v0 with the same spaces s1 and s0 at location l1. Additionally, cargo c1 is to be loaded into vehicle v0 with spaces s1 and s0 at location l1. Conversely, cargo c2 is to be unloaded from vehicle v0 with spaces s0 and s1 at location l0. Furthermore, cargo c4 is to be loaded into vehicle v0 with spaces s1 and s0 at location l1, and then unloaded from vehicle v0 with spaces s0 and s1 at location l0. Similarly, cargo c6 is to be loaded into vehicle v0 with spaces s1 and s0 at location l1. Vehicle v0 is planned to move from location l0, which has fuel levels f2 and f1, to location l1, and also from location l0 with fuel levels f5 and f4 to location l1. In the opposite direction, vehicle v0 is to move from location l1 with fuel levels f3 and f2, f4 and f3, f5 and f4, f6 and f5, and f7 and f6 to location l0. Additionally, vehicle v0 is to move from location l0 with fuel levels f1 and f0, f3 and f2, and f4 and f3 to location l1. Is it possible to execute this sequence of actions, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "1dc74e39-bdea-4a86-94e2-52e8c2634ccd", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c10 is unloaded from vehicle v0 with spaces s1 and s0, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5 and vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location l0, cargo c4 will be unloaded from vehicle v0, which has spaces s0 and s1, then at location l1, cargo c1 will be loaded into vehicle v0, utilizing spaces s1 and s0. Subsequently, at location l1, cargo c10 will be unloaded from vehicle v0, which has spaces s1 and s0, followed by the loading of cargo c4 into vehicle v0, using spaces s1 and s0. Additionally, at location l0, cargo c1 will be unloaded from vehicle v0, occupying spaces s0 and s1, and at location l1, cargo c6 will be loaded into vehicle v0, utilizing spaces s1 and s0. Vehicle v0 will then move from location l1, which has fuel levels f5 and f4, to location l0, and then from location l1 with fuel levels f4 and f3 to location l0. Furthermore, vehicle v0 will move from location l0, which has fuel levels f6 and f5, to location l1, and also from location l0 with fuel levels f7 and f6 to location l1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, fuel levels f1 and f2 are neighboring, as are f2 and f3, and f4 and f5. Location l0 is equipped with fuel f7 and is connected to location l1, which is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at location l1."}
{"question_id": "8413774b-24e4-4f9b-ac3b-adf8c86ddbd1", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: vehicle v0 is intended to relocate from location l0, which has fuel levels f4 and f0, to location l1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 is equipped with space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "230dd71e-0d88-4cae-a5c2-f13e8d7fcbf9", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f4. Is the action: vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f4 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: vehicle v0 is set to move from location l0, which has fuel levels f2 and f4, to location l1. Is the action - vehicle v0 moving from location l0 with fuel levels f2 and f4 to location l1 - executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, f1 and f2 are neighboring fuel levels, f2 and f3 are also neighbors, and f4 and f5 are adjacent. Location l0 is equipped with fuel f7 and is connected to location l1, which in turn is connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is currently situated at location l1."}
{"question_id": "5b27644e-b44b-42db-a6c0-2c7f64457265", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c6 is unloaded from vehicle v1 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is proposed: at location l0, cargo c0 is to be unloaded from vehicle v0, which has spaces s0 and s1, and at the same location l0, cargo c1 is also to be unloaded from vehicle v0 with the same spaces s0 and s1. Then, at location l1, cargo c0 is to be loaded into vehicle v0 with spaces s1 and s0, followed by the loading of cargo c1 into vehicle v0 with the same spaces s1 and s0 at location l1, and also cargo c2 into vehicle v0 with spaces s1 and s0 at location l1. Subsequently, cargo c2 is to be unloaded from vehicle v0 with spaces s0 and s1 at location l0, and cargo c4 is to be loaded into vehicle v0 with spaces s1 and s0 at location l1, only to be unloaded from vehicle v0 with spaces s0 and s1 at location l0. Furthermore, cargo c6 is to be loaded into vehicle v0 with spaces s1 and s0 at location l1, and then unloaded from vehicle v1 with spaces s1 and s0 at location l0. Additionally, vehicle v0 is planned to move from location l0 with fuel levels f4 and f3 to location l1, and then from location l1 with fuel levels f6 and f5 to location l0. Vehicle v0 is also scheduled to move from location l1 with fuel levels f3 and f2 to location l0, and from location l1 with fuel levels f4 and f3 to location l0, and from location l1 with fuel levels f5 and f4 to location l0. Moreover, vehicle v0 is to move from location l0 with fuel levels f1 and f0 to location l1, and from location l0 with fuel levels f2 and f1 to location l1, and from location l0 with fuel levels f3 and f2 to location l1, and finally from location l0 with fuel levels f5 and f4 to location l1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "563e2804-9370-411b-bfa9-f88c65dbe195", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 and vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location l0, cargo c0 will be loaded into vehicle v0 utilizing spaces s2 and s1, and at the same location l0, cargo c3 will also be loaded into vehicle v0 using spaces s2 and s1. At location l1, cargo c10 will be loaded into vehicle v0 with spaces s1 and s0, and then cargo c2 will be unloaded from vehicle v0 with spaces s0 and s1. Additionally, cargo c0 will be unloaded from vehicle v0 with spaces s0 and s1 at location l1, and cargo c10 will be unloaded from vehicle v0 with spaces s1 and s2 at location l0. Furthermore, cargo c2 will be loaded into vehicle v0 with spaces s1 and s0 at location l0. Vehicle v0 will move from location l1, which has fuel levels f2 and f1, to location l0, and then from location l1 with fuel levels f3 and f2 to location l0, and finally from location l0 with fuel levels f4 and f3 to location l1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "7becc510-9176-4de6-84ab-ab848404a6a5", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c8 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0. Is the action: at location l0, cargo c8 is loaded in vehicle v0 with spaces s0 and s1 executable at step 13, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, where cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. At location l1, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1, where cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. At location l0, cargo c8 is loaded onto vehicle v0, which has spaces s0 and s1, but this action actually occurs after vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. After that, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1, where cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0. Is the action: at location l0, cargo c8 is loaded in vehicle v0 with spaces s0 and s1 executable at step 13, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "8e3d81cd-faee-4738-a453-c446b06d2e08", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s2 and s1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0. Is the action: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 executable at step 7, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 19: vehicle v0 is set to move from location l1 with fuel levels f3 and f2 to location l0, then cargo c0 is loaded into vehicle v0 with available spaces s2 and s1 at location l0, followed by the loading of cargo c2 into vehicle v0 with available spaces s1 and s0 at location l0, then vehicle v0 is set to move from location l0 with fuel levels f4 and f3 to location l1, at location l1, cargo c0 is set to be unloaded from vehicle v0 with spaces s0 and s1, and cargo c10 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 is set to move from location l1 with fuel levels f3 and f2 to location l0, and from location l1 with fuel levels f2 and f1 to location l0, at location l0, cargo c10 is set to be unloaded from vehicle v0 with spaces s1 and s2, and cargo c3 and c4 are loaded into vehicle v0 with spaces s2 and s1, and s1 and s0 respectively, then vehicle v0 is set to move from location l0 with fuel levels f3 and f2 to location l1, at location l1, cargo c3 and c4 are set to be unloaded from vehicle v0 with spaces s0 and s1, and s1 and s2 respectively, and cargo c5 and c9 are loaded into vehicle v0 with spaces s2 and s1, and s1 and s0 respectively, then vehicle v0 is set to move from location l1 with fuel levels f1 and f0 to location l0, and at location l0, cargo c5 is set to be unloaded from vehicle v0 with spaces s0 and s1, and cargo c6 is loaded into vehicle v0 with spaces s1 and s0. Is the action: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "b2da3afc-4104-441a-b4f1-2bbe58ee96fc", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0. Is the action: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: at location l1, cargo c2 is to be loaded into vehicle v0 utilizing spaces s1 and s0. Is the action of loading cargo c2 into vehicle v0 with spaces s1 and s0 at location l1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "f0ac13b8-8b7a-449f-9b69-fe711065c16c", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1. Is the action: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 executable at step 12, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0 using spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0 using spaces s1 and s0, followed by vehicle v0 moving from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0 using spaces s0 and s1. This is followed by vehicle v0 moving from location l0, with fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded onto vehicle v0 using spaces s1 and s0, then vehicle v0 moves from location l1, with fuel levels f4 and f3, to location l0. At location l0, cargo c6 is unloaded from vehicle v0 using spaces s0 and s1, and vehicle v0 moves from location l0, with fuel levels f5 and f4, back to location l1. Next, cargo c7 is loaded onto vehicle v0 using spaces s1 and s0 at location l1, followed by vehicle v0 moving from location l1, with fuel levels f3 and f2, to location l0, where cargo c7 is unloaded from vehicle v0 using spaces s0 and s1. Then, vehicle v0 moves from location l0, with fuel levels f4 and f3, back to location l1, where cargo c8 is loaded onto vehicle v0 using spaces s1 and s0. Finally, vehicle v0 moves from location l1, with fuel levels f2 and f1, to location l0, and cargo c8 is unloaded from vehicle v0 using spaces s0 and s1. Is the action: vehicle v0 moves from location l0, with fuel levels f5 and f4, to location l1 executable at step 12, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, while cargo c10, cargo c2, and cargo c3 are all situated at l0, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at l1. Fuel f6 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f1 and f2 are neighboring fuel levels, f2 and f3 are neighboring fuel levels, and f4 and f5 are neighboring fuel levels. Location l0 has fuel f7 and is connected to l1, which is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at l1."}
{"question_id": "46dea6ac-1536-4261-aa5d-f8704e3e2856", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: loading cargo c2 into vehicle v0, utilizing spaces s1 and s0, at location l1. Can this be executed, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 also being present at l0. Fuel f3 is available at l0, and fuel f8 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at l1, and also within vehicle v1, which is present at l0."}
{"question_id": "04e6b2aa-1a9e-4b0c-8353-1f41199d429a", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3 and vehicle v1 moves from location l1 which has fuel-levels f2 and f6 to location l0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: cargo c0 is to be loaded into vehicle v0, which has spaces s1 and s0, at location l1, then cargo c0 is to be unloaded from vehicle v0, which has spaces s0 and s1, at location l0, followed by unloading cargo c1 from vehicle v0, which has spaces s0 and s1, at location l0, then cargo c2 is to be loaded into vehicle v0, which has spaces s1 and s0, at location l1, vehicle v0 is to move from location l0, which has fuel levels f3 and f2, to location l1, then from location l0, which has fuel levels f5 and f4, to location l1, next from location l1, which has fuel levels f6 and f5, to location l0, then from location l1, which has fuel levels f7 and f6, to location l0, after that vehicle v0 is to move from location l0, which has fuel levels f4 and f3, to location l1, and simultaneously vehicle v1 is to move from location l1, which has fuel levels f2 and f6, to location l0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "2de6642c-6679-44c0-9cdd-be3d943ebf52", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5 and vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: at location l0, cargo c1 is to be unloaded from vehicle v0, which has spaces s0 and s1, at location l0, cargo c4 is to be unloaded from vehicle v0, which has spaces s0 and s1, at location l0, cargo c7 is to be unloaded from vehicle v0, which has spaces s0 and s1, at location l1, cargo c4 is to be loaded into vehicle v0, which has spaces s1 and s0, at location l1, cargo c6 is to be loaded into vehicle v0, which has spaces s1 and s0, cargo c1 is to be loaded into vehicle v0, which has space s1 and space s0 at location l1, cargo c6 is to be unloaded from vehicle v0, which has space s0 and space s1 at location l0, cargo c7 is to be loaded into vehicle v0, which has space s1 and space s0 at location l1, cargo c8 is to be loaded into vehicle v0, which has space s1 and space s0 at location l1, cargo c8 is to be unloaded from vehicle v0, which has space s0 and space s1 at location l0, vehicle v0 is to move from location l0, which has fuel levels f4 and f3, to location l1, vehicle v0 is to move from location l0, which has fuel levels f5 and f4, to location l1, vehicle v0 is to move from location l0, which has fuel levels f6 and f5, to location l1, vehicle v0 is to move from location l1, which has fuel levels f5 and f4, to location l0, vehicle v0 is to move to location l0 from location l1, which has fuel levels f2 and f1, vehicle v0 is to move to location l0 from location l1, which has fuel levels f3 and f2, vehicle v0 is to move to location l0 from location l1, which has fuel levels f4 and f3, vehicle v0 is to move to location l0 from location l1, which has fuel levels f6 and f5, and vehicle v0 is to move to location l1 from location l0, which has fuel levels f7 and f6. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10 is positioned at l0, with cargo c2 and cargo c3 also situated at l0. Meanwhile, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at l1. Additionally, fuel f6 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f4 and f5. Location l0 is equipped with fuel f7 and is connected to l1, which is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is currently situated at l1."}
{"question_id": "07956fd4-4a49-4eb9-abf1-9c93c7cc701b", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6 and vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: at location l0, cargo c0 is to be unloaded from vehicle v0, which has spaces s0 and s1, and at the same location l0, cargo c1 is also to be unloaded from vehicle v0 with the same spaces s0 and s1. Then, at location l1, cargo c0 is to be loaded into vehicle v0 with spaces s1 and s0, followed by cargo c1 being loaded into vehicle v0 with the same spaces s1 and s0, and cargo c2 also being loaded into vehicle v0 with spaces s1 and s0. Vehicle v0 is planned to move from location l0, which has fuel levels f3 and f2, to location l1, and then from location l0 with fuel levels f4 and f3 to location l1. Additionally, vehicle v0 is scheduled to move from location l1, which has fuel levels f6 and f5, to location l0, and from location l1 with fuel levels f7 and f6 to location l0, and finally from location l0 with fuel levels f5 and f4 to location l1. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "c66d7d42-11e0-4ac7-9b0d-8df4ff96089f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: vehicle v1 moves from location l0 which has fuel-levels f3 and f7 to location l1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: vehicle v1 is intended to move from location l0, which has fuel levels of f3 and f7, to location l1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at location l0, while cargo c1 is also found at location l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also present at location l0. Fuel f3 is available at location l0, whereas fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
