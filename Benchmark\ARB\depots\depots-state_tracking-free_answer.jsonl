{"question_id": "6e58ff01-a522-425e-a8aa-b65283b2220b", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at depot0, crate0 does not have crate1 on it, crate0 does not have crate3 on it, crate0 is not in truck0, crate0 is not located at depot1, crate0 is not located at distributor2, crate0 is not on crate0, crate0 is not on crate2, crate0 is not on pallet2, crate0 is not on top of pallet0, crate0 is not on top of pallet3, crate1 cannot be found located at distributor1, crate1 does not have crate0 on it, crate1 does not have crate2 on it, crate1 does not have crate3 on it, crate1 is not at distributor0, crate1 is not inside truck1, crate1 is not inside truck2, crate1 is not located at depot1, crate1 is not located at distributor2, crate1 is not on crate1, crate1 is not on crate3, crate1 is not on pallet5, crate1 is not on top of crate2, crate1 is not on top of pallet3, crate1 is not on top of pallet4, crate2 cannot be found located at distributor0, crate2 is not at depot1, crate2 is not at depot2, crate2 is not clear of any crates, crate2 is not in truck0, crate2 is not in truck1, crate2 is not located at distributor2, crate2 is not on pallet4, crate2 is not on top of crate0, crate2 is not on top of crate2, crate2 is not on top of pallet3, crate2 is not on top of pallet5, crate3 does not have crate0 on it, crate3 does not have crate2 on it, crate3 is not at depot2, crate3 is not at distributor0, crate3 is not in truck1, crate3 is not inside truck0, crate3 is not inside truck2, crate3 is not located at depot0, crate3 is not located at distributor1, crate3 is not on top of crate2, crate3 is not on top of crate3, crate3 is not on top of pallet2, crate3 is not on top of pallet3, depot0 is where crate1 is not located, depot0 is where crate2 is not located, depot0 is where hoist1 is not located, depot0 is where hoist2 is not located, depot0 is where pallet1 is not located, depot0 is where truck0 is not located, depot1 is where crate3 is not located, depot1 is where hoist0 is not located, depot1 is where hoist3 is not located, depot1 is where hoist5 is not located, depot1 is where pallet0 is not located, depot1 is where pallet5 is not located, depot2 is where crate0 is not located, depot2 is where hoist5 is not located, depot2 is where pallet1 is not located, depot2 is where pallet3 is not located, depot2 is where pallet4 is not located, depot2 is where pallet5 is not located, distributor0 is where crate0 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist5 is not located, distributor1 is where crate2 is not located, distributor1 is where hoist3 is not located, distributor1 is where pallet1 is not located, distributor1 is where truck2 is not located, distributor2 is where hoist1 is not located, distributor2 is where hoist2 is not located, distributor2 is where pallet3 is not located, distributor2 is where truck0 is not located, distributor2 is where truck2 is not located, hoist0 is not accessible, hoist0 is not at distributor1, hoist0 is not elevating crate0, hoist0 is not elevating crate3, hoist0 is not located at depot2, hoist0 is not located at distributor0, hoist0 is not located at distributor2, hoist0 is not raising crate1, hoist1 is not elevating crate0, hoist1 is not elevating crate2, hoist1 is not lifting crate1, hoist1 is not lifting crate3, hoist1 is not located at depot2, hoist1 is not located at distributor1, hoist2 is not elevating crate0, hoist2 is not lifting crate1, hoist2 is not lifting crate3, hoist2 is not located at depot1, hoist2 is not located at distributor1, hoist2 is not raising crate2, hoist3 is not at depot2, hoist3 is not elevating crate1, hoist3 is not elevating crate2, hoist3 is not located at depot0, hoist3 is not located at distributor2, hoist3 is not raising crate0, hoist3 is not raising crate3, hoist4 is not at depot1, hoist4 is not at depot2, hoist4 is not at distributor0, hoist4 is not at distributor2, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not elevating crate3, hoist4 is not lifting crate1, hoist4 is not located at depot0, hoist5 cannot be found located at depot0, hoist5 is not at distributor1, hoist5 is not elevating crate2, hoist5 is not elevating crate3, hoist5 is not lifting crate0, hoist5 is not raising crate1, pallet0 cannot be found located at depot2, pallet0 cannot be found located at distributor2, pallet0 does not have crate1 on it, pallet0 does not have crate2 on it, pallet0 does not have crate3 on it, pallet0 is not located at distributor0, pallet0 is not located at distributor1, pallet1 does not have crate0 on it, pallet1 does not have crate1 on it, pallet1 does not have crate2 on it, pallet1 does not have crate3 on it, pallet1 is not at distributor0, pallet1 is not located at distributor2, pallet2 cannot be found located at depot0, pallet2 does not have crate2 on it, pallet2 is not at distributor0, pallet2 is not at distributor1, pallet2 is not clear, pallet2 is not located at depot1, pallet2 is not located at distributor2, pallet3 cannot be found located at depot0, pallet3 cannot be found located at depot1, pallet3 is not at distributor1, pallet4 cannot be found located at distributor2, pallet4 does not have crate3 on it, pallet4 is not at depot1, pallet4 is not clear of any crates, pallet4 is not located at depot0, pallet4 is not located at distributor0, pallet5 cannot be found located at depot0, pallet5 cannot be found located at distributor1, pallet5 does not have crate0 on it, pallet5 is not at distributor0, pallet5 is not clear, truck0 does not contain crate1, truck0 is not at distributor0, truck0 is not located at depot1, truck0 is not located at distributor1, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor0, truck1 does not contain crate0, truck1 is not at depot1, truck1 is not at distributor1, truck1 is not located at depot2, truck2 does not contain crate0, truck2 does not contain crate2, truck2 is not at distributor0, truck2 is not located at depot1 and truck2 is not located at depot2", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate0 is on pallet4, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear, crate3 is located at distributor2, crate3 is on pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor1 is where crate0 is located, hoist0 is available, hoist1 is accessible, hoist1 is at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 can be found located at distributor0, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor2, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet3 can be found located at distributor0, pallet3 is clear, pallet4 is at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 is at distributor2 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: at depot0, hoist0 lifts crate2 off pallet0 to achieve the current state. In this state, identify all valid properties that include negations, or state None if none exist.", "initial_state_nl_paraphrased": "Crate0 has no crates on top, it is positioned on pallet4. Crate1 is also clear of any crates and is placed on pallet2. Similarly, crate2 has no crates on it and is situated on pallet0. Crate3 is clear and is located at distributor2, where it is placed on pallet5. Depot0 is the location of crate2 and also houses hoist0. Depot2 is where crate1 and pallet2 are located. Crate0 is situated at distributor1. Hoist0 is currently available, while hoist1 is accessible and located at depot1. Hoist2 is available and can be found at depot2. Hoist3 is available for work and is located at distributor0, and hoist4 is also available for work at distributor1. Hoist5 is accessible and situated at distributor2. Pallet0 is located at depot0, while pallet1 is clear and can be found at depot1. Pallet3 is clear and located at distributor0, and pallet4 is situated at distributor1. Pallet5 is located at distributor2. The locations of the trucks are as follows: truck0 is at depot2, truck1 is at distributor2, and truck2 is at depot0."}
{"question_id": "bb471a10-2e41-42ce-9768-4f08040e731d", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, at depot2, hoist2 lifts crate3 off crate2, at depot2, hoist2 loads crate3 into truck2, hoist2 lifts crate2 from crate1 at depot2, at depot2, hoist2 loads crate2 into truck2, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3, at distributor0, hoist3 loads crate0 into truck2, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven from distributor0 to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1, hoist5 unloads crate3 from truck2 at distributor2, at distributor0, hoist3 drops crate1 on pallet3, at distributor1, hoist4 drops crate2 on pallet4, hoist5 drops crate3 on pallet5 at distributor2 and crate0 is unloaded by hoist5 from truck2 at distributor2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at depot2, crate0 does not have crate2 on it, crate0 is not at distributor0, crate0 is not at distributor2, crate0 is not clear, crate0 is not in truck0, crate0 is not inside truck1, crate0 is not located at depot1, crate0 is not on pallet5, crate0 is not on top of crate0, crate0 is not on top of crate1, crate0 is not on top of crate3, crate0 is not on top of pallet2, crate0 is not on top of pallet4, crate1 cannot be found located at distributor1, crate1 is not at depot0, crate1 is not in truck0, crate1 is not inside truck2, crate1 is not located at depot2, crate1 is not located at distributor2, crate1 is not on crate0, crate1 is not on pallet0, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet5, crate1 is not on top of crate1, crate1 is not on top of crate3, crate1 is not on top of pallet4, crate2 does not have crate0 on it, crate2 does not have crate1 on it, crate2 is not at depot1, crate2 is not in truck0, crate2 is not in truck1, crate2 is not in truck2, crate2 is not located at depot2, crate2 is not located at distributor0, crate2 is not located at distributor2, crate2 is not on crate2, crate2 is not on pallet1, crate2 is not on pallet3, crate2 is not on top of crate1, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor0, crate3 cannot be found located at distributor1, crate3 does not have crate2 on it, crate3 is not in truck0, crate3 is not in truck1, crate3 is not inside truck2, crate3 is not located at depot0, crate3 is not located at depot2, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet0, crate3 is not on pallet1, crate3 is not on pallet2, crate3 is not on pallet3, crate3 is not on top of crate0, crate3 is not on top of crate3, depot0 is where crate0 is not located, depot0 is where crate2 is not located, depot0 is where hoist2 is not located, depot0 is where pallet5 is not located, depot0 is where truck0 is not located, depot1 is where crate1 is not located, depot1 is where hoist2 is not located, depot1 is where hoist5 is not located, depot1 is where pallet4 is not located, depot1 is where pallet5 is not located, depot1 is where truck0 is not located, depot1 is where truck2 is not located, depot2 is where hoist0 is not located, depot2 is where hoist3 is not located, depot2 is where hoist4 is not located, depot2 is where pallet0 is not located, depot2 is where pallet4 is not located, depot2 is where truck0 is not located, depot2 is where truck1 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist5 is not located, distributor0 is where pallet0 is not located, distributor1 is where crate0 is not located, distributor1 is where hoist2 is not located, distributor1 is where pallet2 is not located, distributor1 is where pallet3 is not located, distributor1 is where pallet5 is not located, distributor2 is where pallet0 is not located, distributor2 is where pallet4 is not located, distributor2 is where truck0 is not located, hoist0 is not at distributor1, hoist0 is not at distributor2, hoist0 is not elevating crate0, hoist0 is not elevating crate2, hoist0 is not elevating crate3, hoist0 is not lifting crate1, hoist0 is not located at depot1, hoist0 is not located at distributor0, hoist1 is not at depot0, hoist1 is not at depot2, hoist1 is not at distributor2, hoist1 is not elevating crate0, hoist1 is not elevating crate3, hoist1 is not lifting crate1, hoist1 is not located at distributor0, hoist1 is not located at distributor1, hoist1 is not raising crate2, hoist2 cannot be found located at distributor2, hoist2 is not elevating crate1, hoist2 is not lifting crate0, hoist2 is not lifting crate2, hoist2 is not raising crate3, hoist3 is not at depot0, hoist3 is not at distributor2, hoist3 is not elevating crate1, hoist3 is not elevating crate2, hoist3 is not located at depot1, hoist3 is not located at distributor1, hoist3 is not raising crate0, hoist3 is not raising crate3, hoist4 cannot be found located at depot0, hoist4 cannot be found located at depot1, hoist4 cannot be found located at distributor2, hoist4 is not at distributor0, hoist4 is not elevating crate1, hoist4 is not lifting crate0, hoist4 is not lifting crate2, hoist4 is not raising crate3, hoist5 is not at depot2, hoist5 is not at distributor1, hoist5 is not available, hoist5 is not elevating crate1, hoist5 is not elevating crate2, hoist5 is not located at depot0, hoist5 is not raising crate3, pallet0 does not have crate0 on it, pallet0 does not have crate2 on it, pallet0 is not at depot1, pallet0 is not located at distributor1, pallet1 cannot be found located at distributor1, pallet1 does not have crate0 on it, pallet1 is not at depot0, pallet1 is not at depot2, pallet1 is not at distributor0, pallet1 is not at distributor2, pallet2 cannot be found located at distributor0, pallet2 does not have crate2 on it, pallet2 is not at depot0, pallet2 is not at depot1, pallet2 is not at distributor2, pallet3 cannot be found located at depot1, pallet3 does not have crate0 on it, pallet3 is not at depot0, pallet3 is not clear, pallet3 is not located at depot2, pallet3 is not located at distributor2, pallet4 does not have crate3 on it, pallet4 is not at depot0, pallet4 is not clear of any crates, pallet4 is not located at distributor0, pallet5 cannot be found located at distributor0, pallet5 does not have crate2 on it, pallet5 is not at depot2, pallet5 is not clear, truck0 is not at distributor1, truck1 cannot be found located at distributor1, truck1 cannot be found located at distributor2, truck1 does not contain crate1, truck1 is not at depot0, truck1 is not at distributor0, truck2 cannot be found located at depot2, truck2 does not contain crate0, truck2 is not located at depot0, truck2 is not located at distributor0 and truck2 is not located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate1 is located at depot2, crate1 is on pallet2, crate2 is on crate1, crate3 is clear of any crates, crate3 is located at depot2, crate3 is on crate2, depot0 is where pallet0 is located, depot2 is where crate2 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is at distributor0, hoist3 is available for work, hoist4 is available, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available for work, pallet0 is clear, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 has crate0 on it, pallet3 is located at distributor0, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 travels from depot0 to depot2, at depot2, hoist2 first lifts crate3 off crate2, then at depot2, hoist2 loads crate3 onto truck2, hoist2 lifts crate2 off crate1 at depot2, at depot2, hoist2 loads crate2 onto truck2, hoist2 lifts crate1 off pallet2 at depot2, and crate1 is loaded by hoist2 into truck2 at depot2, truck2 then travels from depot2 to distributor0, at distributor0, hoist3 lifts crate0 off pallet3, at distributor0, hoist3 loads crate0 into truck2, at distributor0, hoist3 unloads crate1 from truck2, truck2 then travels from distributor0 to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 then travels to distributor2 from distributor1, hoist5 unloads crate3 from truck2 at distributor2, at distributor0, hoist3 places crate1 on pallet3, at distributor1, hoist4 places crate2 on pallet4, and hoist5 places crate3 on pallet5 at distributor2, and crate0 is unloaded by hoist5 from truck2 at distributor2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0 and is not stacked on any other crate. Crate1 is positioned at depot2 and is placed on pallet2, while crate2 is stacked on crate1. Crate3 is located at depot2, clear of any other crates, and is placed on crate2. Depot0 is the location of pallet0. Depot2 is where crate2 is situated. Hoist0 is positioned at depot0 and is available for use. Hoist1 is located at depot1 and is accessible. Hoist2 is situated at depot2 and is also accessible. Hoist3 is at distributor0 and is available for work. Hoist4 is available and located at distributor1. Hoist5 is positioned at distributor2 and is available for use. Pallet0 is empty. Pallet1 is clear of any crates and is located at depot1. Pallet2 is situated at depot2. Pallet3 has crate0 on it and is located at distributor0. Pallet4 is empty and positioned at distributor1. Pallet5 is empty and located at distributor2. Truck0 is situated at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "065a3086-6fcb-4924-9c46-1d114c6835de", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven from depot2 to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, at distributor0, hoist3 drops crate1 on pallet3, at distributor1, hoist4 lifts crate0 off pallet4 and hoist4 loads crate0 into truck2 at distributor1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 is in truck2, crate1 can be found located at distributor0, crate1 is clear of any crates, crate1 is on pallet3, crate2 is inside truck2, crate3 is clear, crate3 is located at distributor2, crate3 is on top of pallet5, distributor1 is where truck2 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is at depot2, hoist2 is available for work, hoist3 can be found located at distributor0, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet2 is clear, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, truck0 is located at distributor0 and truck1 is at distributor2", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is on pallet4, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear, crate3 is located at distributor2, crate3 is on pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor1 is where crate0 is located, hoist0 is available, hoist1 is accessible, hoist1 is at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 can be found located at distributor0, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor2, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet3 can be found located at distributor0, pallet3 is clear, pallet4 is at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 is at distributor2 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0. Next, truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2. Then, truck0 is driven from depot2 to distributor0. At distributor0, crate1 is unloaded from truck0 by hoist3 and placed on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. This sequence of actions results in the current state. In this state, list all valid properties that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has no crates on top, it is positioned on pallet4. Crate1 is also clear of any crates and is placed on pallet2. Similarly, crate2 has no crates on it and is situated on pallet0. Crate3 is clear and is located at distributor2, where it is placed on pallet5. Depot0 is the location of crate2 and also houses hoist0. Depot2 is the location of crate1 and pallet2. Crate0 is situated at distributor1. Hoist0 is currently available for use. Hoist1 is accessible and can be found at depot1. Hoist2 is available and located at depot2. Hoist3 is available for work and is situated at distributor0. Hoist4 is also available for work and can be found at distributor1. Hoist5 is accessible and located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear and can be found at depot1. Pallet3 is clear and located at distributor0. Pallet4 is situated at distributor1. Pallet5 is located at distributor2. Truck0 is currently at depot2, truck1 is at distributor2, and truck2 is at depot0."}
{"question_id": "28491a5b-2c86-4c8c-bad7-6ecf15b031cb", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven from depot2 to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, crate3 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven to distributor1 from distributor2, hoist4 unloads crate3 from truck1 at distributor1, crate3 is dropped on pallet4 at distributor1 by hoist4, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, hoist1 unloads crate0 from truck2 at depot1 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 can be found located at depot1, crate0 is clear of any crates, crate0 is on pallet1, crate1 is at distributor0, crate1 is clear, crate1 is on top of pallet3, crate3 is clear of any crates, crate3 is located at distributor1, crate3 is on top of pallet4, depot1 is where pallet1 is located, depot1 is where truck2 is located, distributor0 is where hoist3 is located, distributor0 is where truck0 is located, distributor2 is where hoist5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 is at depot1, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is lifting crate2, hoist5 is available for work, pallet0 can be found located at depot0, pallet0 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 is located at distributor0, pallet4 is located at distributor1, pallet5 can be found located at distributor2, pallet5 is clear and truck1 can be found located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate0 is on pallet4, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear, crate3 is located at distributor2, crate3 is on pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor1 is where crate0 is located, hoist0 is available, hoist1 is accessible, hoist1 is at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 can be found located at distributor0, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor2, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet3 can be found located at distributor0, pallet3 is clear, pallet4 is at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 is at distributor2 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which then travels from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0, which is then driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. Additionally, hoist5 lifts crate3 from pallet5 at distributor2 and loads it into truck1, which is then driven to distributor1. Upon arrival, hoist4 unloads crate3 from truck1 and places it on pallet4 at distributor1. Hoist4 also unloads crate2 from truck2 at distributor1, and truck2 is then driven to depot1. Finally, hoist1 unloads crate0 from truck2 at depot1 and places it on pallet1, resulting in the current state. In this state, list all valid properties that do not involve negations. If none exist, state None.", "initial_state_nl_paraphrased": "Crate0 has no crates on top, it is positioned on pallet4. Crate1 is also clear of any crates and is placed on pallet2. Similarly, crate2 has no crates on it and is situated on pallet0. Crate3 is clear and is located at distributor2, where it is placed on pallet5. Depot0 is the location of crate2 and also houses hoist0. Depot2 is the location of crate1 and pallet2. Crate0 is situated at distributor1. Hoist0 is currently available, while hoist1 is accessible and located at depot1. Hoist2 is available and situated at depot2. Hoist3 is available for work and can be found at distributor0, and hoist4 is also available for work, located at distributor1. Hoist5 is accessible and situated at distributor2. Pallet0 is located at depot0, while pallet1 is clear and situated at depot1. Pallet3 is clear and located at distributor0. Pallet4 is situated at distributor1, and pallet5 is at distributor2. Truck0 is currently at depot2, truck1 is at distributor2, and truck2 is at depot0."}
{"question_id": "eb01124c-33c2-4e82-b572-baed579d6e57", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet3 at depot3 by hoist3, hoist5 lifts crate3 from pallet5 at distributor1, crate3 is loaded by hoist5 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1 and hoist5 drops crate2 on pallet5 at distributor1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 is clear, crate0 is on top of pallet6, crate2 is clear, crate2 is on top of pallet5, depot0 is where pallet0 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor1 is where crate2 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is elevating crate1, hoist4 is accessible, hoist4 is located at distributor0, hoist5 is available for work, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is clear, pallet1 is clear of any crates, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet3 is clear of any crates, pallet4 is at distributor0, pallet4 is clear, pallet5 is at distributor1, pallet6 can be found located at distributor2, truck0 can be found located at depot1, truck1 is at distributor0, truck2 contains crate3 and truck2 is at distributor2", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor2, crate1 is clear, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is clear, crate3 is on top of pallet5, depot2 is where hoist2 is located, depot3 is where crate1 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor2 is where pallet6 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is at depot3, hoist4 can be found located at distributor0, hoist4 is accessible, hoist5 can be found located at distributor1, hoist5 is available, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear, pallet3 has crate1 on it, pallet3 is at depot3, pallet4 is clear, pallet5 is located at distributor1, pallet6 has crate0 on it, truck0 can be found located at depot1, truck1 is located at distributor0 and truck2 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck2 at depot0, truck2 proceeds from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 then travels from distributor1 to distributor2, and finally, hoist5 places crate2 on pallet5 at distributor1 to achieve the current state. In this state, list all valid properties that do not involve negations. If there are no such properties, state None.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor2, crate1 is empty, crate2 is positioned at depot0, crate2 is empty, crate2 is stacked on pallet0, crate3 is empty, crate3 is stacked on pallet5, depot2 is the location of hoist2, depot3 is the location of crate1, distributor0 is the location of pallet4, distributor1 is the location of crate3, distributor2 is the location of pallet6, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is situated at depot3, hoist4 is located at distributor0, hoist4 is accessible, hoist5 is located at distributor1, hoist5 is available, hoist6 is located at distributor2, hoist6 is available for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, pallet3 has crate1 on it, pallet3 is situated at depot3, pallet4 is empty, pallet5 is situated at distributor1, pallet6 has crate0 on it, truck0 is located at depot1, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "5b66ca43-6176-4717-8672-b1c43e4ac4b1", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, hoist1 unloads crate2 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear, crate1 is on top of crate0, crate2 is clear, crate2 is on pallet1, depot1 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where truck0 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is at depot1, hoist1 is available for work, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available, hoist5 is accessible, hoist5 is located at distributor2, hoist6 can be found located at distributor3, hoist6 is elevating crate3, pallet0 is clear, pallet0 is located at depot0, pallet2 is at depot2, pallet2 is clear of any crates, pallet3 can be found located at distributor0, pallet3 is clear, pallet4 is clear, pallet5 is at distributor2, pallet6 is clear, pallet6 is located at distributor3, truck1 is located at depot1 and truck2 is located at distributor3", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck1, after which truck1 is driven from depot0 to depot1. At depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven from depot2 to distributor3. Upon arrival, hoist6 unloads crate3 from truck2, resulting in the current state. In this state, list all valid properties that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is positioned on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 is home to crate3 and truck2. Distributor1 houses hoist4 and pallet4, while distributor2 is the location of crate1, pallet5, and truck0. Distributor3 is where hoist6 and pallet6 can be found. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0 and clear of any crates, pallet4 is clear of any crates, and pallet6 is clear. Lastly, truck1 is situated at depot1."}
{"question_id": "ba092610-612f-4a19-a3b3-25ccfd2573b1", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 is located at distributor2, crate0 is on top of pallet5, crate1 is clear, crate1 is on crate0, crate2 is at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 can be found located at depot2, crate3 is clear, crate3 is on top of pallet2, depot0 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor2 is where crate1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is at distributor1, pallet4 is clear, pallet5 is located at distributor2, pallet6 is at distributor3, pallet6 is clear, truck0 is located at distributor2 and truck2 can be found located at depot2", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: truck1 is driven from depot1 to depot0 to attain the current state. In this state, identify all valid properties that do not include negations, or specify None if none exist.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is positioned on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 is home to crate3 and truck2. Distributor1 houses hoist4 and pallet4, while distributor2 is the location of crate1, pallet5, and truck0. Distributor3 is where hoist6 and pallet6 can be found. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear of any crates and situated at distributor0, pallet4 is clear of any crates, pallet6 is clear, and truck1 is positioned at depot1."}
{"question_id": "f6ed160f-d752-4c62-9ada-0785cf3ed5a9", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet3 at depot3 by hoist3, at distributor1, hoist5 lifts crate3 off pallet5, at distributor1, hoist5 loads crate3 into truck2, hoist5 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, at distributor1, hoist5 drops crate2 on pallet5, hoist6 lifts crate0 from pallet6 at distributor2, crate0 is loaded by hoist6 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, hoist3 loads crate1 into truck2 at depot3, hoist3 unloads crate0 from truck2 at depot3, truck2 is driven to distributor0 from depot3, at distributor0, hoist4 unloads crate3 from truck2, crate0 is dropped on pallet3 at depot3 by hoist3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at distributor0, crate0 does not have crate0 on it, crate0 does not have crate1 on it, crate0 is not at depot0, crate0 is not at depot1, crate0 is not at depot2, crate0 is not in truck2, crate0 is not inside truck0, crate0 is not inside truck1, crate0 is not on crate1, crate0 is not on crate2, crate0 is not on pallet2, crate0 is not on pallet4, crate0 is not on top of pallet1, crate1 cannot be found located at depot2, crate1 is not at depot3, crate1 is not at distributor1, crate1 is not clear of any crates, crate1 is not in truck0, crate1 is not in truck1, crate1 is not located at depot1, crate1 is not on crate1, crate1 is not on crate2, crate1 is not on pallet1, crate1 is not on pallet6, crate1 is not on top of pallet0, crate1 is not on top of pallet3, crate1 is not on top of pallet4, crate2 cannot be found located at depot0, crate2 cannot be found located at depot2, crate2 does not have crate2 on it, crate2 is not at distributor0, crate2 is not in truck1, crate2 is not located at distributor2, crate2 is not on crate0, crate2 is not on crate1, crate2 is not on crate3, crate2 is not on pallet1, crate2 is not on pallet6, crate2 is not on top of pallet2, crate2 is not on top of pallet4, crate3 cannot be found located at distributor2, crate3 does not have crate0 on it, crate3 does not have crate1 on it, crate3 is not at depot0, crate3 is not inside truck2, crate3 is not located at depot2, crate3 is not located at distributor1, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet0, crate3 is not on pallet1, crate3 is not on pallet3, crate3 is not on pallet5, crate3 is not on top of crate0, crate3 is not on top of crate3, crate3 is not on top of pallet2, depot0 is where crate1 is not located, depot0 is where hoist3 is not located, depot0 is where hoist4 is not located, depot0 is where pallet1 is not located, depot0 is where pallet2 is not located, depot0 is where pallet3 is not located, depot0 is where truck0 is not located, depot0 is where truck2 is not located, depot1 is where crate2 is not located, depot1 is where crate3 is not located, depot1 is where hoist0 is not located, depot1 is where hoist2 is not located, depot1 is where hoist5 is not located, depot1 is where truck1 is not located, depot2 is where hoist0 is not located, depot2 is where hoist5 is not located, depot2 is where pallet4 is not located, depot2 is where pallet6 is not located, depot2 is where truck2 is not located, depot3 is where crate2 is not located, depot3 is where crate3 is not located, depot3 is where hoist1 is not located, depot3 is where hoist5 is not located, depot3 is where pallet2 is not located, distributor0 is where crate1 is not located, distributor0 is where hoist3 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet6 is not located, distributor1 is where crate0 is not located, distributor1 is where pallet4 is not located, distributor2 is where crate0 is not located, distributor2 is where crate1 is not located, distributor2 is where hoist2 is not located, distributor2 is where pallet3 is not located, hoist0 cannot be found located at distributor0, hoist0 is not at distributor1, hoist0 is not lifting crate0, hoist0 is not lifting crate2, hoist0 is not lifting crate3, hoist0 is not located at depot3, hoist0 is not located at distributor2, hoist0 is not raising crate1, hoist1 cannot be found located at depot0, hoist1 cannot be found located at distributor0, hoist1 cannot be found located at distributor1, hoist1 is not elevating crate1, hoist1 is not lifting crate0, hoist1 is not lifting crate2, hoist1 is not lifting crate3, hoist1 is not located at depot2, hoist1 is not located at distributor2, hoist2 cannot be found located at depot0, hoist2 is not at distributor1, hoist2 is not elevating crate1, hoist2 is not elevating crate2, hoist2 is not elevating crate3, hoist2 is not lifting crate0, hoist2 is not located at depot3, hoist2 is not located at distributor0, hoist3 cannot be found located at depot1, hoist3 cannot be found located at depot2, hoist3 is not at distributor2, hoist3 is not elevating crate0, hoist3 is not lifting crate1, hoist3 is not lifting crate2, hoist3 is not located at distributor1, hoist3 is not raising crate3, hoist4 cannot be found located at depot3, hoist4 cannot be found located at distributor1, hoist4 is not at distributor2, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not lifting crate1, hoist4 is not located at depot1, hoist4 is not located at depot2, hoist4 is not raising crate3, hoist5 cannot be found located at depot0, hoist5 cannot be found located at distributor2, hoist5 is not elevating crate0, hoist5 is not elevating crate2, hoist5 is not elevating crate3, hoist5 is not located at distributor0, hoist5 is not raising crate1, hoist6 cannot be found located at depot1, hoist6 cannot be found located at distributor0, hoist6 is not at depot3, hoist6 is not at distributor1, hoist6 is not elevating crate1, hoist6 is not lifting crate0, hoist6 is not lifting crate2, hoist6 is not lifting crate3, hoist6 is not located at depot0, hoist6 is not located at depot2, pallet0 cannot be found located at depot3, pallet0 cannot be found located at distributor1, pallet0 does not have crate0 on it, pallet0 does not have crate2 on it, pallet0 is not at depot2, pallet0 is not located at depot1, pallet0 is not located at distributor0, pallet0 is not located at distributor2, pallet1 cannot be found located at distributor2, pallet1 is not at depot3, pallet1 is not at distributor1, pallet1 is not located at depot2, pallet1 is not located at distributor0, pallet2 cannot be found located at distributor2, pallet2 does not have crate1 on it, pallet2 is not located at depot1, pallet2 is not located at distributor1, pallet3 cannot be found located at depot1, pallet3 cannot be found located at depot2, pallet3 cannot be found located at distributor0, pallet3 does not have crate2 on it, pallet3 is not clear of any crates, pallet3 is not located at distributor1, pallet4 cannot be found located at depot0, pallet4 cannot be found located at depot1, pallet4 is not clear of any crates, pallet4 is not located at depot3, pallet4 is not located at distributor2, pallet5 cannot be found located at depot3, pallet5 cannot be found located at distributor0, pallet5 cannot be found located at distributor2, pallet5 does not have crate0 on it, pallet5 does not have crate1 on it, pallet5 is not at depot0, pallet5 is not at depot1, pallet5 is not at depot2, pallet5 is not clear, pallet6 cannot be found located at depot0, pallet6 cannot be found located at distributor1, pallet6 does not have crate0 on it, pallet6 does not have crate3 on it, pallet6 is not located at depot1, pallet6 is not located at depot3, truck0 cannot be found located at depot3, truck0 cannot be found located at distributor1, truck0 does not contain crate2, truck0 does not contain crate3, truck0 is not at distributor0, truck0 is not at distributor2, truck0 is not located at depot2, truck1 cannot be found located at depot0, truck1 cannot be found located at depot2, truck1 does not contain crate3, truck1 is not at depot3, truck1 is not at distributor1, truck1 is not at distributor2, truck2 cannot be found located at depot1, truck2 does not contain crate2, truck2 is not at depot3, truck2 is not at distributor1 and truck2 is not at distributor2", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor2, crate1 is clear, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is clear, crate3 is on top of pallet5, depot2 is where hoist2 is located, depot3 is where crate1 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor2 is where pallet6 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is at depot3, hoist4 can be found located at distributor0, hoist4 is accessible, hoist5 can be found located at distributor1, hoist5 is available, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear, pallet3 has crate1 on it, pallet3 is at depot3, pallet4 is clear, pallet5 is located at distributor1, pallet6 has crate0 on it, truck0 can be found located at depot1, truck1 is located at distributor0 and truck2 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 retrieves crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck2 at depot0, truck2 proceeds to distributor1 from depot0, at distributor1, hoist5 lifts crate3 off pallet5, meanwhile, hoist3 lifts crate1 from pallet3 at depot3, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 then travels from distributor1 to distributor2, at distributor1, hoist5 places crate2 on pallet5, at distributor2, hoist6 lifts crate0 from pallet6, hoist6 loads crate0 into truck2 at distributor2, truck2 then travels from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2, truck2 then travels to distributor0 from depot3, at distributor0, hoist4 unloads crate3 from truck2, and finally, hoist3 places crate0 on pallet3 at depot3 and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor2, crate1 is empty, crate2 is positioned at depot0, crate2 is empty, crate2 is stacked on pallet0, crate3 is empty, crate3 is stacked on pallet5, depot2 is the location of hoist2, depot3 is the location of crate1, distributor0 is the location of pallet4, distributor1 is the location of crate3, distributor2 is the location of pallet6, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is situated at depot3, hoist4 is located at distributor0, hoist4 is accessible, hoist5 is located at distributor1, hoist5 is available, hoist6 is located at distributor2, hoist6 is available for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, pallet3 has crate1 on it, pallet3 is situated at depot3, pallet4 is empty, pallet5 is situated at distributor1, pallet6 has crate0 on it, truck0 is located at depot1, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "c390c1df-976e-42b9-874b-37905c5bca16", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 is not clear, crate0 is not in truck0, crate0 is not inside truck1, crate0 is not located at depot0, crate0 is not located at distributor1, crate0 is not located at distributor3, crate0 is not on crate1, crate0 is not on pallet0, crate0 is not on pallet1, crate0 is not on pallet4, crate0 is not on top of crate0, crate0 is not on top of pallet2, crate1 cannot be found located at distributor1, crate1 does not have crate2 on it, crate1 does not have crate3 on it, crate1 is not inside truck2, crate1 is not located at depot0, crate1 is not located at depot2, crate1 is not located at distributor0, crate1 is not located at distributor3, crate1 is not on pallet0, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet3, crate1 is not on top of crate1, crate1 is not on top of crate3, crate1 is not on top of pallet4, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 cannot be found located at depot2, crate2 cannot be found located at distributor2, crate2 does not have crate0 on it, crate2 does not have crate1 on it, crate2 does not have crate2 on it, crate2 does not have crate3 on it, crate2 is not at depot0, crate2 is not at distributor1, crate2 is not in truck2, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not located at distributor0, crate2 is not located at distributor3, crate2 is not on crate0, crate2 is not on crate3, crate2 is not on pallet2, crate2 is not on pallet3, crate2 is not on top of pallet4, crate2 is not on top of pallet5, crate3 cannot be found located at depot1, crate3 cannot be found located at depot2, crate3 cannot be found located at distributor0, crate3 cannot be found located at distributor2, crate3 does not have crate0 on it, crate3 is not clear, crate3 is not in truck0, crate3 is not in truck2, crate3 is not located at distributor1, crate3 is not located at distributor3, crate3 is not on crate0, crate3 is not on pallet2, crate3 is not on pallet4, crate3 is not on pallet6, crate3 is not on top of crate3, crate3 is not on top of pallet0, crate3 is not on top of pallet3, depot0 is where crate3 is not located, depot0 is where hoist1 is not located, depot0 is where pallet2 is not located, depot0 is where pallet6 is not located, depot0 is where truck0 is not located, depot0 is where truck2 is not located, depot1 is where crate0 is not located, depot1 is where crate1 is not located, depot1 is where hoist2 is not located, depot1 is where hoist3 is not located, depot1 is where pallet0 is not located, depot1 is where pallet3 is not located, depot1 is where pallet5 is not located, depot1 is where pallet6 is not located, depot1 is where truck0 is not located, depot2 is where crate0 is not located, depot2 is where hoist5 is not located, depot2 is where truck2 is not located, distributor0 is where crate0 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist4 is not located, distributor0 is where hoist5 is not located, distributor1 is where hoist2 is not located, distributor1 is where hoist6 is not located, distributor1 is where pallet2 is not located, distributor1 is where truck1 is not located, distributor2 is where hoist2 is not located, distributor2 is where pallet6 is not located, distributor2 is where truck2 is not located, distributor3 is where hoist2 is not located, distributor3 is where hoist4 is not located, distributor3 is where hoist5 is not located, distributor3 is where pallet3 is not located, hoist0 cannot be found located at depot1, hoist0 cannot be found located at distributor0, hoist0 cannot be found located at distributor2, hoist0 is not at distributor1, hoist0 is not lifting crate0, hoist0 is not lifting crate2, hoist0 is not lifting crate3, hoist0 is not located at depot2, hoist0 is not located at distributor3, hoist0 is not raising crate1, hoist1 cannot be found located at distributor1, hoist1 cannot be found located at distributor3, hoist1 is not at distributor2, hoist1 is not elevating crate3, hoist1 is not lifting crate1, hoist1 is not located at depot2, hoist1 is not raising crate0, hoist1 is not raising crate2, hoist2 is not elevating crate1, hoist2 is not elevating crate2, hoist2 is not lifting crate3, hoist2 is not located at depot0, hoist2 is not raising crate0, hoist3 cannot be found located at depot2, hoist3 cannot be found located at distributor2, hoist3 is not at distributor1, hoist3 is not at distributor3, hoist3 is not elevating crate0, hoist3 is not elevating crate2, hoist3 is not lifting crate1, hoist3 is not located at depot0, hoist3 is not raising crate3, hoist4 cannot be found located at depot2, hoist4 is not at distributor2, hoist4 is not elevating crate1, hoist4 is not lifting crate2, hoist4 is not located at depot0, hoist4 is not located at depot1, hoist4 is not raising crate0, hoist4 is not raising crate3, hoist5 cannot be found located at depot0, hoist5 is not elevating crate1, hoist5 is not lifting crate2, hoist5 is not lifting crate3, hoist5 is not located at depot1, hoist5 is not located at distributor1, hoist5 is not raising crate0, hoist6 cannot be found located at depot0, hoist6 cannot be found located at depot1, hoist6 cannot be found located at depot2, hoist6 is not available for work, hoist6 is not elevating crate0, hoist6 is not elevating crate2, hoist6 is not located at distributor0, hoist6 is not located at distributor2, hoist6 is not raising crate1, pallet0 cannot be found located at distributor1, pallet0 does not have crate2 on it, pallet0 is not at distributor0, pallet0 is not located at depot2, pallet0 is not located at distributor2, pallet0 is not located at distributor3, pallet1 cannot be found located at depot0, pallet1 does not have crate3 on it, pallet1 is not at depot2, pallet1 is not clear, pallet1 is not located at distributor0, pallet1 is not located at distributor1, pallet1 is not located at distributor2, pallet1 is not located at distributor3, pallet2 cannot be found located at distributor0, pallet2 cannot be found located at distributor2, pallet2 is not at depot1, pallet2 is not located at distributor3, pallet3 cannot be found located at depot0, pallet3 cannot be found located at distributor1, pallet3 cannot be found located at distributor2, pallet3 does not have crate0 on it, pallet3 is not at depot2, pallet4 cannot be found located at depot0, pallet4 cannot be found located at depot1, pallet4 is not at distributor0, pallet4 is not located at depot2, pallet4 is not located at distributor2, pallet4 is not located at distributor3, pallet5 cannot be found located at depot0, pallet5 does not have crate3 on it, pallet5 is not at distributor3, pallet5 is not clear, pallet5 is not located at depot2, pallet5 is not located at distributor0, pallet5 is not located at distributor1, pallet6 cannot be found located at depot2, pallet6 cannot be found located at distributor1, pallet6 does not have crate0 on it, pallet6 does not have crate2 on it, pallet6 is not located at distributor0, truck0 does not contain crate1, truck0 is not at depot2, truck0 is not at distributor3, truck0 is not located at distributor0, truck0 is not located at distributor1, truck1 cannot be found located at depot2, truck1 cannot be found located at distributor2, truck1 does not contain crate1, truck1 does not contain crate3, truck1 is not at depot0, truck1 is not at distributor3, truck1 is not located at distributor0, truck2 does not contain crate0, truck2 is not located at depot1, truck2 is not located at distributor0 and truck2 is not located at distributor1", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven back to depot1, at depot1, hoist1 unloads crate2 from truck1, and hoist1 places crate2 on pallet1 at depot1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3, and finally, hoist6 unloads crate3 from truck2 at distributor3, resulting in the current state. In this state, list all valid properties that involve negations; if there are none, indicate None.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is placed on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 houses crate3 and truck2. Distributor1 is home to hoist4 and pallet4, while distributor2 is where crate1, pallet5, and truck0 can be found. Distributor3 is the location of hoist6 and pallet6. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is at depot2, pallet3 is at distributor0 and clear of any crates, pallet4 is clear of any crates, and pallet6 is clear. Lastly, truck1 is situated at depot1."}
{"question_id": "004315ad-10c4-46aa-a437-ff4744ce6860", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, at distributor1, hoist5 loads crate3 into truck2, hoist5 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, crate0 is loaded by hoist6 into truck2 at distributor2, from distributor2, truck2 is driven to depot3, hoist3 loads crate1 into truck2 at depot3, crate0 is unloaded by hoist3 from truck2 at depot3, from depot3, truck2 is driven to distributor0, crate3 is unloaded by hoist4 from truck2 at distributor0, at depot3, hoist3 drops crate0 on pallet3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "crate0 can be found located at depot3, crate0 cannot be found located at distributor0, crate0 does not have crate2 on it, crate0 is clear, crate0 is not at depot2, crate0 is not at distributor1, crate0 is not at distributor2, crate0 is not in truck0, crate0 is not inside truck1, crate0 is not inside truck2, crate0 is not located at depot0, crate0 is not on crate0, crate0 is not on crate1, crate0 is not on crate2, crate0 is not on pallet0, crate0 is not on pallet1, crate0 is not on pallet2, crate0 is not on pallet5, crate0 is not on top of crate3, crate0 is not on top of pallet6, crate0 is on pallet3, crate1 cannot be found located at depot1, crate1 cannot be found located at distributor2, crate1 does not have crate1 on it, crate1 does not have crate2 on it, crate1 is in truck2, crate1 is not at depot0, crate1 is not at depot3, crate1 is not clear of any crates, crate1 is not in truck1, crate1 is not inside truck0, crate1 is not located at depot2, crate1 is not located at distributor0, crate1 is not located at distributor1, crate1 is not on crate0, crate1 is not on crate2, crate1 is not on crate3, crate1 is not on pallet4, crate1 is not on pallet6, crate1 is not on top of pallet1, crate1 is not on top of pallet3, crate2 cannot be found located at depot1, crate2 cannot be found located at distributor2, crate2 is clear of any crates, crate2 is located at distributor1, crate2 is not at depot0, crate2 is not at depot3, crate2 is not in truck1, crate2 is not inside truck2, crate2 is not located at distributor0, crate2 is not on crate2, crate2 is not on pallet3, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet2, crate2 is not on top of pallet4, crate3 cannot be found located at depot0, crate3 is at distributor0, crate3 is clear, crate3 is not at depot1, crate3 is not at depot2, crate3 is not at distributor1, crate3 is not in truck0, crate3 is not in truck1, crate3 is not inside truck2, crate3 is not located at distributor2, crate3 is not on crate1, crate3 is not on crate3, crate3 is not on pallet2, crate3 is not on pallet5, crate3 is not on pallet6, crate3 is not on top of crate0, crate3 is not on top of crate2, crate3 is not on top of pallet0, crate3 is not on top of pallet1, crate3 is not on top of pallet3, depot0 is where hoist6 is not located, depot1 is where crate0 is not located, depot1 is where hoist0 is not located, depot1 is where hoist2 is not located, depot1 is where pallet0 is not located, depot2 is where crate2 is not located, depot2 is where hoist5 is not located, depot2 is where hoist6 is not located, depot2 is where pallet1 is not located, depot2 is where pallet2 is located, depot2 is where pallet3 is not located, depot3 is where crate3 is not located, depot3 is where hoist0 is not located, depot3 is where pallet1 is not located, depot3 is where pallet5 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist5 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet1 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet3 is not located, distributor0 is where pallet5 is not located, distributor0 is where truck0 is not located, distributor1 is where hoist1 is not located, distributor1 is where hoist2 is not located, distributor1 is where pallet0 is not located, distributor1 is where pallet2 is not located, distributor1 is where truck1 is not located, distributor2 is where hoist0 is not located, distributor2 is where hoist2 is not located, distributor2 is where hoist6 is located, distributor2 is where pallet4 is not located, distributor2 is where pallet5 is not located, hoist0 can be found located at depot0, hoist0 cannot be found located at distributor0, hoist0 is available for work, hoist0 is not at distributor1, hoist0 is not elevating crate1, hoist0 is not elevating crate2, hoist0 is not lifting crate3, hoist0 is not located at depot2, hoist0 is not raising crate0, hoist1 can be found located at depot1, hoist1 cannot be found located at depot2, hoist1 cannot be found located at depot3, hoist1 is available for work, hoist1 is not at depot0, hoist1 is not at distributor0, hoist1 is not lifting crate0, hoist1 is not lifting crate2, hoist1 is not located at distributor2, hoist1 is not raising crate1, hoist1 is not raising crate3, hoist2 is available, hoist2 is located at depot2, hoist2 is not at depot0, hoist2 is not elevating crate2, hoist2 is not lifting crate0, hoist2 is not lifting crate1, hoist2 is not located at depot3, hoist2 is not raising crate3, hoist3 can be found located at depot3, hoist3 cannot be found located at depot2, hoist3 is available, hoist3 is not at distributor1, hoist3 is not elevating crate0, hoist3 is not lifting crate1, hoist3 is not lifting crate2, hoist3 is not lifting crate3, hoist3 is not located at depot0, hoist3 is not located at depot1, hoist3 is not located at distributor0, hoist3 is not located at distributor2, hoist4 can be found located at distributor0, hoist4 cannot be found located at depot0, hoist4 cannot be found located at depot1, hoist4 is available, hoist4 is not at distributor1, hoist4 is not at distributor2, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not located at depot2, hoist4 is not located at depot3, hoist4 is not raising crate1, hoist4 is not raising crate3, hoist5 cannot be found located at depot0, hoist5 cannot be found located at depot3, hoist5 is available for work, hoist5 is located at distributor1, hoist5 is not at depot1, hoist5 is not at distributor2, hoist5 is not elevating crate0, hoist5 is not elevating crate1, hoist5 is not elevating crate2, hoist5 is not raising crate3, hoist6 cannot be found located at distributor1, hoist6 is accessible, hoist6 is not at depot1, hoist6 is not at depot3, hoist6 is not elevating crate0, hoist6 is not lifting crate3, hoist6 is not located at distributor0, hoist6 is not raising crate1, hoist6 is not raising crate2, pallet0 can be found located at depot0, pallet0 does not have crate1 on it, pallet0 is clear of any crates, pallet0 is not at distributor2, pallet0 is not located at depot2, pallet0 is not located at depot3, pallet1 can be found located at depot1, pallet1 cannot be found located at depot0, pallet1 cannot be found located at distributor2, pallet1 does not have crate2 on it, pallet1 is clear, pallet1 is not located at distributor1, pallet2 cannot be found located at depot0, pallet2 does not have crate1 on it, pallet2 is clear of any crates, pallet2 is not at depot3, pallet2 is not located at depot1, pallet2 is not located at distributor2, pallet3 can be found located at depot3, pallet3 cannot be found located at depot0, pallet3 is not at depot1, pallet3 is not at distributor1, pallet3 is not at distributor2, pallet3 is not clear, pallet4 can be found located at distributor0, pallet4 does not have crate0 on it, pallet4 has crate3 on it, pallet4 is not at depot1, pallet4 is not at depot2, pallet4 is not at depot3, pallet4 is not at distributor1, pallet4 is not clear of any crates, pallet4 is not located at depot0, pallet5 does not have crate1 on it, pallet5 has crate2 on it, pallet5 is located at distributor1, pallet5 is not at depot0, pallet5 is not at depot1, pallet5 is not clear, pallet5 is not located at depot2, pallet6 cannot be found located at depot1, pallet6 does not have crate2 on it, pallet6 is clear, pallet6 is located at distributor2, pallet6 is not at depot0, pallet6 is not at depot3, pallet6 is not at distributor0, pallet6 is not located at depot2, pallet6 is not located at distributor1, truck0 cannot be found located at depot2, truck0 does not contain crate2, truck0 is located at depot1, truck0 is not at depot0, truck0 is not at depot3, truck0 is not at distributor1, truck0 is not located at distributor2, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor2, truck1 is at distributor0, truck1 is not at depot1, truck1 is not at depot2, truck1 is not at depot3, truck2 cannot be found located at depot1, truck2 cannot be found located at distributor2, truck2 is at distributor0, truck2 is not at depot2, truck2 is not located at depot0, truck2 is not located at depot3 and truck2 is not located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor2, crate1 is clear, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is clear, crate3 is on top of pallet5, depot2 is where hoist2 is located, depot3 is where crate1 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor2 is where pallet6 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is at depot3, hoist4 can be found located at distributor0, hoist4 is accessible, hoist5 can be found located at distributor1, hoist5 is available, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear, pallet3 has crate1 on it, pallet3 is at depot3, pallet4 is clear, pallet5 is located at distributor1, pallet6 has crate0 on it, truck0 can be found located at depot1, truck1 is located at distributor0 and truck2 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck2 at depot0, truck2 is then driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, and unloads crate2 from truck2 at distributor1, truck2 then proceeds from distributor1 to distributor2, hoist5 drops crate2 onto pallet5 at distributor1, hoist6 lifts crate0 from pallet6 at distributor2, hoist6 loads crate0 into truck2 at distributor2, truck2 then travels from distributor2 to depot3, hoist3 loads crate1 into truck2 at depot3, and unloads crate0 from truck2 at depot3, truck2 then proceeds from depot3 to distributor0, hoist4 unloads crate3 from truck2 at distributor0, hoist3 drops crate0 onto pallet3 at depot3, and hoist4 drops crate3 onto pallet4 at distributor0, resulting in the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor2, crate1 is empty, crate2 is positioned at depot0, crate2 is empty, crate2 is stacked on pallet0, crate3 is empty, crate3 is stacked on pallet5, depot2 is the location of hoist2, depot3 is the location of crate1, distributor0 is the location of pallet4, distributor1 is the location of crate3, distributor2 is the location of pallet6, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is situated at depot3, hoist4 is located at distributor0, hoist4 is accessible, hoist5 is located at distributor1, hoist5 is available, hoist6 is located at distributor2, hoist6 is available for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, pallet3 has crate1 on it, pallet3 is situated at depot3, pallet4 is empty, pallet5 is situated at distributor1, pallet6 has crate0 on it, truck0 is located at depot1, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "a20855ef-4e34-4136-98bc-e0961c9a331e", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, at depot1, hoist1 drops crate2 on pallet1, crate3 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, from depot2, truck2 is driven to distributor3, at distributor3, hoist6 unloads crate3 from truck2, crate3 is dropped on pallet6 at distributor3 by hoist6, at distributor2, hoist5 lifts crate1 off crate0, crate1 is loaded by hoist5 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate0 into truck0 at distributor2, at distributor2, hoist5 unloads crate1 from truck0, truck0 is driven from distributor2 to distributor0, hoist3 unloads crate0 from truck0 at distributor0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate2 can be found located at depot1, crate2 is clear of any crates, crate2 is on pallet1, crate3 is clear, crate3 is on top of pallet6, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, distributor3 is where crate3 is located, distributor3 is where hoist6 is located, distributor3 is where truck2 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is at distributor1, hoist4 is available, hoist5 is lifting crate1, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet2 is clear of any crates, pallet4 is clear of any crates, pallet5 is clear, pallet6 is located at distributor3, truck0 can be found located at distributor0 and truck1 is at depot1", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: truck1 travels from depot1 to depot0, where hoist0 lifts crate2 off pallet0, then loads it into truck1. Truck1 then proceeds from depot0 to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which then drives to distributor3. Upon arrival, hoist6 unloads crate3 and drops it on pallet6. At distributor2, hoist5 lifts crate1 off crate0, loads crate1 into truck0, and then lifts crate0 and loads it into truck0 as well. However, hoist5 then unloads crate1 from truck0. Truck0 then travels from distributor2 to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is positioned on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 is home to crate3 and truck2. Distributor1 houses hoist4 and pallet4, while distributor2 is the location of crate1, pallet5, and truck0. Distributor3 is where hoist6 and pallet6 can be found. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0 and clear of any crates, pallet4 is clear of any crates, and pallet6 is clear. Lastly, truck1 is situated at depot1."}
{"question_id": "29872542-b096-4f91-b0b4-237f034b7645", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven to depot1 from depot0, crate2 is unloaded by hoist1 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, hoist2 lifts crate3 from pallet2 at depot2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3, hoist6 unloads crate3 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, hoist5 lifts crate1 from crate0 at distributor2, at distributor2, hoist5 loads crate1 into truck0, at distributor2, hoist5 lifts crate0 off pallet5, crate0 is loaded by hoist5 into truck0 at distributor2, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, crate0 is unloaded by hoist3 from truck0 at distributor0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "crate0 cannot be found located at depot2, crate0 does not have crate1 on it, crate0 does not have crate2 on it, crate0 does not have crate3 on it, crate0 is clear, crate0 is not at distributor1, crate0 is not at distributor2, crate0 is not at distributor3, crate0 is not in truck0, crate0 is not in truck1, crate0 is not inside truck2, crate0 is not located at depot0, crate0 is not located at depot1, crate0 is not on crate0, crate0 is not on crate3, crate0 is not on top of crate2, crate0 is not on top of pallet0, crate0 is not on top of pallet1, crate0 is not on top of pallet4, crate0 is not on top of pallet6, crate1 cannot be found located at depot2, crate1 does not have crate0 on it, crate1 is not at depot1, crate1 is not at distributor3, crate1 is not clear, crate1 is not in truck2, crate1 is not located at depot0, crate1 is not located at distributor0, crate1 is not located at distributor2, crate1 is not on crate1, crate1 is not on pallet1, crate1 is not on top of crate2, crate1 is not on top of crate3, crate1 is not on top of pallet2, crate1 is not on top of pallet3, crate1 is not on top of pallet4, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor0, crate2 cannot be found located at distributor1, crate2 cannot be found located at distributor3, crate2 is clear, crate2 is located at depot1, crate2 is not in truck1, crate2 is not in truck2, crate2 is not inside truck0, crate2 is not on pallet2, crate2 is not on pallet3, crate2 is not on pallet6, crate2 is not on top of crate1, crate2 is not on top of crate2, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet4, crate2 is not on top of pallet5, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor0, crate3 does not have crate3 on it, crate3 is at distributor3, crate3 is clear, crate3 is not at depot0, crate3 is not at distributor1, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not inside truck2, crate3 is not located at depot2, crate3 is not on top of crate1, crate3 is not on top of crate2, crate3 is not on top of pallet0, crate3 is not on top of pallet2, crate3 is not on top of pallet3, crate3 is not on top of pallet5, crate3 is on top of pallet6, depot0 is where hoist2 is not located, depot0 is where hoist4 is not located, depot0 is where pallet0 is located, depot0 is where pallet2 is not located, depot0 is where truck0 is not located, depot1 is where pallet1 is located, depot1 is where pallet4 is not located, depot1 is where truck1 is located, depot2 is where crate2 is not located, depot2 is where hoist4 is not located, depot2 is where pallet0 is not located, distributor0 is where crate0 is located, distributor0 is where hoist5 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet5 is not located, distributor1 is where crate1 is not located, distributor2 is where crate2 is not located, distributor2 is where crate3 is not located, distributor2 is where hoist1 is not located, distributor2 is where pallet4 is not located, distributor2 is where pallet5 is located, distributor2 is where truck1 is not located, distributor2 is where truck2 is not located, distributor3 is where hoist1 is not located, distributor3 is where hoist5 is not located, distributor3 is where truck1 is not located, hoist0 can be found located at depot0, hoist0 cannot be found located at distributor1, hoist0 cannot be found located at distributor3, hoist0 is available, hoist0 is not at depot1, hoist0 is not elevating crate1, hoist0 is not elevating crate3, hoist0 is not located at depot2, hoist0 is not located at distributor0, hoist0 is not located at distributor2, hoist0 is not raising crate0, hoist0 is not raising crate2, hoist1 is at depot1, hoist1 is available for work, hoist1 is not at distributor1, hoist1 is not elevating crate0, hoist1 is not elevating crate2, hoist1 is not elevating crate3, hoist1 is not located at depot0, hoist1 is not located at depot2, hoist1 is not located at distributor0, hoist1 is not raising crate1, hoist2 can be found located at depot2, hoist2 cannot be found located at distributor0, hoist2 cannot be found located at distributor1, hoist2 cannot be found located at distributor2, hoist2 cannot be found located at distributor3, hoist2 is available, hoist2 is not elevating crate3, hoist2 is not located at depot1, hoist2 is not raising crate0, hoist2 is not raising crate1, hoist2 is not raising crate2, hoist3 cannot be found located at depot2, hoist3 is available, hoist3 is located at distributor0, hoist3 is not at depot1, hoist3 is not at distributor1, hoist3 is not at distributor2, hoist3 is not at distributor3, hoist3 is not elevating crate0, hoist3 is not elevating crate1, hoist3 is not elevating crate2, hoist3 is not lifting crate3, hoist3 is not located at depot0, hoist4 cannot be found located at depot1, hoist4 cannot be found located at distributor3, hoist4 is available, hoist4 is located at distributor1, hoist4 is not at distributor0, hoist4 is not at distributor2, hoist4 is not elevating crate2, hoist4 is not lifting crate0, hoist4 is not raising crate1, hoist4 is not raising crate3, hoist5 cannot be found located at depot0, hoist5 is lifting crate1, hoist5 is located at distributor2, hoist5 is not accessible, hoist5 is not at depot1, hoist5 is not at depot2, hoist5 is not at distributor1, hoist5 is not elevating crate3, hoist5 is not raising crate0, hoist5 is not raising crate2, hoist6 cannot be found located at depot1, hoist6 is available for work, hoist6 is located at distributor3, hoist6 is not at depot0, hoist6 is not at distributor1, hoist6 is not elevating crate2, hoist6 is not lifting crate1, hoist6 is not located at depot2, hoist6 is not located at distributor0, hoist6 is not located at distributor2, hoist6 is not raising crate0, hoist6 is not raising crate3, pallet0 cannot be found located at depot1, pallet0 cannot be found located at distributor3, pallet0 does not have crate1 on it, pallet0 is clear of any crates, pallet0 is not at distributor1, pallet0 is not located at distributor2, pallet1 cannot be found located at depot0, pallet1 cannot be found located at distributor0, pallet1 does not have crate3 on it, pallet1 has crate2 on it, pallet1 is not at depot2, pallet1 is not at distributor1, pallet1 is not at distributor2, pallet1 is not at distributor3, pallet1 is not clear of any crates, pallet2 cannot be found located at distributor3, pallet2 does not have crate0 on it, pallet2 is at depot2, pallet2 is clear of any crates, pallet2 is not at depot1, pallet2 is not at distributor1, pallet2 is not at distributor2, pallet3 cannot be found located at depot2, pallet3 has crate0 on it, pallet3 is located at distributor0, pallet3 is not at depot0, pallet3 is not at depot1, pallet3 is not at distributor2, pallet3 is not clear of any crates, pallet3 is not located at distributor1, pallet3 is not located at distributor3, pallet4 can be found located at distributor1, pallet4 does not have crate3 on it, pallet4 is clear, pallet4 is not at depot0, pallet4 is not at depot2, pallet4 is not at distributor3, pallet4 is not located at distributor0, pallet5 cannot be found located at depot0, pallet5 does not have crate0 on it, pallet5 is clear of any crates, pallet5 is not at depot1, pallet5 is not at depot2, pallet5 is not at distributor1, pallet5 is not at distributor3, pallet6 can be found located at distributor3, pallet6 cannot be found located at depot0, pallet6 cannot be found located at depot1, pallet6 cannot be found located at distributor0, pallet6 cannot be found located at distributor1, pallet6 cannot be found located at distributor2, pallet6 is not clear of any crates, pallet6 is not located at depot2, truck0 can be found located at distributor0, truck0 cannot be found located at depot1, truck0 does not contain crate1, truck0 is not at distributor2, truck0 is not located at depot2, truck0 is not located at distributor1, truck0 is not located at distributor3, truck1 cannot be found located at depot0, truck1 does not contain crate1, truck1 is not at depot2, truck1 is not at distributor0, truck1 is not at distributor1, truck2 cannot be found located at depot1, truck2 cannot be found located at distributor0, truck2 is located at distributor3, truck2 is not at depot0, truck2 is not at depot2 and truck2 is not located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, and at depot1, hoist1 places crate2 on pallet1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven from depot2 to distributor3. At distributor3, hoist6 unloads crate3 from truck2 and drops it on pallet6. At distributor2, hoist5 lifts crate1 from crate0, loads crate1 into truck0, lifts crate0 off pallet5, and loads crate0 into truck0. However, crate1 is immediately unloaded from truck0 by hoist5 at distributor2. Truck0 then travels from distributor2 to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, list all valid properties (both affirmative and negative) of the state. If there are none, indicate None.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is positioned on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 is home to crate3 and truck2. Distributor1 houses hoist4 and pallet4, while distributor2 is the location of crate1, pallet5, and truck0. Distributor3 is where hoist6 and pallet6 can be found. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0 and clear of any crates, pallet4 is clear of any crates, and pallet6 is clear. Lastly, truck1 is situated at depot1."}
{"question_id": "a36a2c84-b087-4809-b222-84b7c21e01dd", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate0 is clear, crate0 is located at distributor0, crate1 can be found located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 is clear, depot0 is where hoist0 is located, depot2 is where crate2 is located, depot2 is where crate3 is located, distributor0 is where hoist3 is located, distributor0 is where truck0 is located, distributor2 is where hoist5 is located, hoist0 is available, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available, hoist4 is at distributor1, hoist4 is available, hoist5 is accessible, pallet0 is at depot0, pallet0 is clear, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet3 has crate0 on it, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear, truck1 is at depot1 and truck2 can be found located at depot2", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate1 is located at depot2, crate1 is on pallet2, crate2 is on crate1, crate3 is clear of any crates, crate3 is located at depot2, crate3 is on crate2, depot0 is where pallet0 is located, depot2 is where crate2 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is at distributor0, hoist3 is available for work, hoist4 is available, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available for work, pallet0 is clear, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 has crate0 on it, pallet3 is located at distributor0, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is located at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: truck2 is driven from depot0 to depot2 to attain the current state. In this state, identify all valid properties that do not include negations, or specify None if none exist.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0 and has no crates on it. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 is stacked on crate1. Crate3 is located at depot2, has no crates on it, and is placed on crate2. Depot0 is the location of pallet0. Depot2 is where crate2 is situated. Hoist0 is positioned at depot0 and is available for use. Hoist1 is located at depot1 and is accessible. Hoist2 is situated at depot2 and is accessible. Hoist3 is positioned at distributor0 and is available for work. Hoist4 is available and located at distributor1. Hoist5 is positioned at distributor2 and is available for work. Pallet0 is empty. Pallet1 is clear of crates and is located at depot1. Pallet2 is situated at depot2. Pallet3 has crate0 on it and is located at distributor0. Pallet4 is empty and located at distributor1. Pallet5 is empty and situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "6c979051-acef-4af4-ba7a-7c26bff794b9", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, from depot0, truck1 is driven to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, hoist2 lifts crate3 from pallet2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, at distributor2, hoist5 lifts crate1 off crate0, at distributor2, hoist5 loads crate1 into truck0, crate0 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate0 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, from distributor2, truck0 is driven to distributor0, hoist3 unloads crate0 from truck0 at distributor0 and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at distributor1, crate0 does not have crate2 on it, crate0 is not at depot0, crate0 is not at depot2, crate0 is not in truck1, crate0 is not located at distributor2, crate0 is not located at distributor3, crate0 is not on crate2, crate0 is not on pallet1, crate0 is not on pallet5, crate0 is not on top of crate0, crate0 is not on top of crate1, crate0 is not on top of pallet0, crate0 is not on top of pallet2, crate0 is not on top of pallet4, crate1 cannot be found located at distributor0, crate1 cannot be found located at distributor2, crate1 is not at depot0, crate1 is not at distributor3, crate1 is not clear, crate1 is not inside truck1, crate1 is not located at depot2, crate1 is not on crate0, crate1 is not on crate1, crate1 is not on pallet0, crate1 is not on pallet4, crate1 is not on top of crate2, crate1 is not on top of crate3, crate1 is not on top of pallet1, crate1 is not on top of pallet2, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 is not at depot2, crate2 is not at distributor2, crate2 is not at distributor3, crate2 is not in truck1, crate2 is not inside truck2, crate2 is not located at distributor1, crate2 is not on crate3, crate2 is not on pallet2, crate2 is not on pallet4, crate2 is not on top of crate1, crate2 is not on top of crate2, crate2 is not on top of pallet6, crate3 cannot be found located at depot1, crate3 does not have crate0 on it, crate3 does not have crate3 on it, crate3 is not at depot2, crate3 is not at distributor0, crate3 is not at distributor2, crate3 is not in truck0, crate3 is not in truck2, crate3 is not on crate0, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet0, crate3 is not on top of pallet4, crate3 is not on top of pallet5, depot0 is where crate2 is not located, depot0 is where crate3 is not located, depot0 is where hoist3 is not located, depot0 is where hoist4 is not located, depot0 is where pallet6 is not located, depot1 is where crate0 is not located, depot1 is where crate1 is not located, depot1 is where hoist6 is not located, depot1 is where pallet5 is not located, depot1 is where pallet6 is not located, depot1 is where truck0 is not located, depot2 is where hoist0 is not located, depot2 is where hoist5 is not located, depot2 is where pallet1 is not located, depot2 is where pallet3 is not located, depot2 is where pallet5 is not located, depot2 is where truck2 is not located, distributor0 is where crate2 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist2 is not located, distributor0 is where pallet6 is not located, distributor1 is where crate1 is not located, distributor1 is where crate3 is not located, distributor1 is where hoist0 is not located, distributor1 is where hoist2 is not located, distributor1 is where hoist5 is not located, distributor1 is where pallet3 is not located, distributor2 is where pallet0 is not located, distributor2 is where pallet1 is not located, distributor2 is where truck2 is not located, distributor3 is where hoist1 is not located, distributor3 is where hoist4 is not located, distributor3 is where hoist5 is not located, distributor3 is where pallet1 is not located, distributor3 is where truck1 is not located, hoist0 cannot be found located at distributor3, hoist0 is not at distributor0, hoist0 is not lifting crate2, hoist0 is not located at depot1, hoist0 is not located at distributor2, hoist0 is not raising crate0, hoist0 is not raising crate1, hoist0 is not raising crate3, hoist1 cannot be found located at distributor1, hoist1 cannot be found located at distributor2, hoist1 is not elevating crate1, hoist1 is not elevating crate2, hoist1 is not elevating crate3, hoist1 is not located at depot0, hoist1 is not located at depot2, hoist1 is not raising crate0, hoist2 cannot be found located at depot1, hoist2 cannot be found located at distributor3, hoist2 is not at depot0, hoist2 is not at distributor2, hoist2 is not elevating crate2, hoist2 is not elevating crate3, hoist2 is not lifting crate0, hoist2 is not raising crate1, hoist3 cannot be found located at depot2, hoist3 is not at distributor1, hoist3 is not elevating crate0, hoist3 is not elevating crate1, hoist3 is not located at depot1, hoist3 is not located at distributor2, hoist3 is not located at distributor3, hoist3 is not raising crate2, hoist3 is not raising crate3, hoist4 is not at depot1, hoist4 is not at depot2, hoist4 is not at distributor0, hoist4 is not elevating crate2, hoist4 is not lifting crate0, hoist4 is not located at distributor2, hoist4 is not raising crate1, hoist4 is not raising crate3, hoist5 cannot be found located at distributor0, hoist5 is not at depot1, hoist5 is not available for work, hoist5 is not elevating crate3, hoist5 is not lifting crate0, hoist5 is not located at depot0, hoist5 is not raising crate2, hoist6 cannot be found located at depot2, hoist6 cannot be found located at distributor0, hoist6 cannot be found located at distributor1, hoist6 is not at distributor2, hoist6 is not elevating crate2, hoist6 is not lifting crate3, hoist6 is not located at depot0, hoist6 is not raising crate0, hoist6 is not raising crate1, pallet0 cannot be found located at depot1, pallet0 cannot be found located at distributor3, pallet0 does not have crate2 on it, pallet0 is not at distributor1, pallet0 is not located at depot2, pallet0 is not located at distributor0, pallet1 does not have crate3 on it, pallet1 is not at depot0, pallet1 is not clear, pallet1 is not located at distributor0, pallet1 is not located at distributor1, pallet2 cannot be found located at depot0, pallet2 cannot be found located at depot1, pallet2 cannot be found located at distributor2, pallet2 cannot be found located at distributor3, pallet2 does not have crate3 on it, pallet2 is not at distributor0, pallet2 is not located at distributor1, pallet3 cannot be found located at depot1, pallet3 does not have crate1 on it, pallet3 does not have crate2 on it, pallet3 does not have crate3 on it, pallet3 is not at distributor2, pallet3 is not at distributor3, pallet3 is not clear of any crates, pallet3 is not located at depot0, pallet4 cannot be found located at depot2, pallet4 cannot be found located at distributor0, pallet4 cannot be found located at distributor2, pallet4 is not at depot0, pallet4 is not at depot1, pallet4 is not located at distributor3, pallet5 cannot be found located at depot0, pallet5 cannot be found located at distributor0, pallet5 does not have crate2 on it, pallet5 is not at distributor3, pallet5 is not located at distributor1, pallet6 cannot be found located at depot2, pallet6 cannot be found located at distributor1, pallet6 cannot be found located at distributor2, pallet6 does not have crate0 on it, pallet6 is not clear, truck0 cannot be found located at depot0, truck0 cannot be found located at distributor1, truck0 cannot be found located at distributor2, truck0 does not contain crate0, truck0 does not contain crate1, truck0 does not contain crate2, truck0 is not at depot2, truck0 is not located at distributor3, truck1 cannot be found located at distributor0, truck1 does not contain crate3, truck1 is not at depot0, truck1 is not at depot2, truck1 is not located at distributor1, truck1 is not located at distributor2, truck2 does not contain crate0, truck2 does not contain crate1, truck2 is not at depot1, truck2 is not located at depot0, truck2 is not located at distributor0 and truck2 is not located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck1, after which truck1 is driven from depot0 back to depot1. At depot1, crate2 is unloaded from truck1 by hoist1 and placed on pallet1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven to distributor3. At distributor3, crate3 is unloaded from truck2 by hoist6 and dropped on pallet6. At distributor2, hoist5 lifts crate1 off crate0, loads crate1 into truck0, lifts crate0 from pallet5, and loads crate0 into truck0. However, hoist5 then unloads crate1 from truck0. Finally, truck0 is driven from distributor2 to distributor0, where hoist3 unloads crate0 and drops it on pallet3 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is positioned on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 is home to crate3 and truck2. Distributor1 houses hoist4 and pallet4, while distributor2 is the location of crate1, pallet5, and truck0. Distributor3 is where hoist6 and pallet6 can be found. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0 and clear of any crates, pallet4 is clear of any crates, and pallet6 is clear. Lastly, truck1 is situated at depot1."}
{"question_id": "a3a39178-ec99-4f62-818b-6e9f354d1931", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, at depot2, hoist2 lifts crate3 off crate2, crate3 is loaded by hoist2 into truck2 at depot2, crate2 is lifted from crate1 at depot2 by hoist2, at depot2, hoist2 loads crate2 into truck2, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3, at distributor0, hoist3 loads crate0 into truck2, hoist3 unloads crate1 from truck2 at distributor0, from distributor0, truck2 is driven to distributor1, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven from distributor1 to distributor2, at distributor2, hoist5 unloads crate3 from truck2, at distributor0, hoist3 drops crate1 on pallet3, crate2 is dropped on pallet4 at distributor1 by hoist4, hoist5 drops crate3 on pallet5 at distributor2 and at distributor2, hoist5 unloads crate0 from truck2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "crate1 can be found located at distributor0, crate1 is clear of any crates, crate1 is on pallet3, crate2 can be found located at distributor1, crate2 is clear, crate3 is clear, crate3 is located at distributor2, crate3 is on pallet5, depot1 is where truck1 is located, distributor0 is where pallet3 is located, distributor0 is where truck0 is located, distributor1 is where pallet4 is located, distributor2 is where pallet5 is located, distributor2 is where truck2 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is available, hoist1 is located at depot1, hoist2 is accessible, hoist2 is located at depot2, hoist3 is at distributor0, hoist3 is available, hoist4 is at distributor1, hoist4 is available for work, hoist5 can be found located at distributor2, hoist5 is lifting crate0, pallet0 can be found located at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet2 is at depot2, pallet2 is clear of any crates and pallet4 has crate2 on it", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate1 is located at depot2, crate1 is on pallet2, crate2 is on crate1, crate3 is clear of any crates, crate3 is located at depot2, crate3 is on crate2, depot0 is where pallet0 is located, depot2 is where crate2 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is at distributor0, hoist3 is available for work, hoist4 is available, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available for work, pallet0 is clear, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 has crate0 on it, pallet3 is located at distributor0, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is located at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot0 to depot2, where hoist2 lifts crate3 off crate2, then loads crate3 into truck2, lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 off pallet2, and loads crate1 into truck2. Subsequently, truck2 is driven from depot2 to distributor0, where hoist3 lifts crate0 off pallet3, loads crate0 into truck2, and unloads crate1 from truck2. Truck2 is then driven to distributor1, where hoist4 unloads crate2, and then to distributor2, where hoist5 unloads crate3. Finally, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, and hoist5 drops crate3 on pallet5 at distributor2, and unloads crate0 from truck2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0 and has no crates on it. Crate1 is positioned at depot2 and is placed on pallet2, while crate2 is stacked on crate1. Crate3, located at depot2, is clear of any other crates and is positioned on crate2. Depot0 is the location of pallet0, and depot2 is where crate2 is situated. Hoist0 is stationed at depot0 and is available for use. Hoist1 is located at depot1 and is accessible, whereas hoist2 is situated at depot2 and is also accessible. Hoist3 is positioned at distributor0 and is available for work, while hoist4 is available and located at distributor1. Hoist5 is stationed at distributor2 and is available for work. Pallet0 is empty, and pallet1, located at depot1, has no crates on it. Pallet2 is situated at depot2, and pallet3, which has crate0 on it, is located at distributor0. Pallet4 is clear of crates and positioned at distributor1, and pallet5 is empty and located at distributor2. Truck0 is situated at distributor0, truck1 is at depot1, and truck2 is stationed at depot0."}
{"question_id": "bd62107c-3013-4699-886a-e5079496ce1c", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "crate0 cannot be found located at depot0, crate0 cannot be found located at depot1, crate0 cannot be found located at depot2, crate0 does not have crate3 on it, crate0 is at distributor2, crate0 is not clear of any crates, crate0 is not in truck2, crate0 is not inside truck1, crate0 is not located at distributor1, crate0 is not located at distributor3, crate0 is not on pallet1, crate0 is not on pallet3, crate0 is not on top of crate0, crate0 is not on top of crate3, crate0 is not on top of pallet4, crate0 is not on top of pallet6, crate1 does not have crate0 on it, crate1 is clear of any crates, crate1 is not at distributor1, crate1 is not inside truck0, crate1 is not inside truck1, crate1 is not located at distributor3, crate1 is not on crate3, crate1 is not on pallet3, crate1 is not on pallet4, crate1 is not on pallet5, crate1 is not on pallet6, crate1 is not on top of crate1, crate1 is not on top of pallet0, crate1 is not on top of pallet1, crate1 is on top of crate0, crate2 can be found located at depot0, crate2 cannot be found located at depot1, crate2 does not have crate0 on it, crate2 does not have crate1 on it, crate2 is clear of any crates, crate2 is not at distributor0, crate2 is not at distributor1, crate2 is not at distributor3, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not inside truck2, crate2 is not located at depot2, crate2 is not located at distributor2, crate2 is not on crate0, crate2 is not on crate2, crate2 is not on pallet2, crate2 is not on pallet5, crate2 is not on top of crate1, crate2 is not on top of pallet6, crate2 is on pallet0, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor1, crate3 does not have crate2 on it, crate3 does not have crate3 on it, crate3 is at depot2, crate3 is clear, crate3 is not inside truck0, crate3 is not located at distributor3, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet0, crate3 is not on pallet4, crate3 is not on top of pallet3, depot0 is where crate1 is not located, depot0 is where crate3 is not located, depot0 is where hoist1 is not located, depot0 is where pallet2 is not located, depot0 is where pallet5 is not located, depot0 is where truck1 is located, depot0 is where truck2 is not located, depot1 is where crate1 is not located, depot1 is where hoist1 is located, depot1 is where hoist5 is not located, depot1 is where pallet1 is located, depot1 is where pallet3 is not located, depot1 is where pallet5 is not located, depot2 is where crate1 is not located, depot2 is where hoist1 is not located, depot2 is where hoist5 is not located, depot2 is where pallet0 is not located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where crate0 is not located, distributor0 is where crate1 is not located, distributor0 is where crate3 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist4 is not located, distributor0 is where pallet1 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet3 is located, distributor0 is where pallet4 is not located, distributor0 is where pallet6 is not located, distributor1 is where hoist0 is not located, distributor1 is where hoist2 is not located, distributor1 is where hoist3 is not located, distributor1 is where hoist4 is located, distributor1 is where hoist5 is not located, distributor1 is where pallet0 is not located, distributor1 is where pallet2 is not located, distributor1 is where pallet4 is located, distributor1 is where truck1 is not located, distributor1 is where truck2 is not located, distributor2 is where crate1 is located, distributor2 is where crate3 is not located, distributor2 is where hoist1 is not located, distributor2 is where hoist5 is located, distributor2 is where pallet3 is not located, distributor2 is where truck1 is not located, distributor3 is where hoist0 is not located, distributor3 is where hoist2 is not located, distributor3 is where pallet0 is not located, distributor3 is where pallet2 is not located, distributor3 is where truck0 is not located, hoist0 cannot be found located at depot1, hoist0 cannot be found located at depot2, hoist0 cannot be found located at distributor0, hoist0 is at depot0, hoist0 is available, hoist0 is not elevating crate0, hoist0 is not elevating crate2, hoist0 is not lifting crate1, hoist0 is not lifting crate3, hoist0 is not located at distributor2, hoist1 cannot be found located at distributor0, hoist1 cannot be found located at distributor3, hoist1 is accessible, hoist1 is not elevating crate0, hoist1 is not elevating crate3, hoist1 is not lifting crate1, hoist1 is not lifting crate2, hoist1 is not located at distributor1, hoist2 can be found located at depot2, hoist2 cannot be found located at depot0, hoist2 cannot be found located at depot1, hoist2 is accessible, hoist2 is not elevating crate2, hoist2 is not located at distributor2, hoist2 is not raising crate0, hoist2 is not raising crate1, hoist2 is not raising crate3, hoist3 cannot be found located at depot1, hoist3 is accessible, hoist3 is at distributor0, hoist3 is not at depot0, hoist3 is not at depot2, hoist3 is not at distributor3, hoist3 is not elevating crate1, hoist3 is not elevating crate2, hoist3 is not lifting crate0, hoist3 is not lifting crate3, hoist3 is not located at distributor2, hoist4 is available, hoist4 is not at depot2, hoist4 is not elevating crate2, hoist4 is not elevating crate3, hoist4 is not lifting crate1, hoist4 is not located at depot0, hoist4 is not located at depot1, hoist4 is not located at distributor2, hoist4 is not located at distributor3, hoist4 is not raising crate0, hoist5 is accessible, hoist5 is not at distributor0, hoist5 is not at distributor3, hoist5 is not elevating crate2, hoist5 is not elevating crate3, hoist5 is not lifting crate0, hoist5 is not lifting crate1, hoist5 is not located at depot0, hoist6 is at distributor3, hoist6 is available for work, hoist6 is not at depot2, hoist6 is not at distributor2, hoist6 is not elevating crate0, hoist6 is not lifting crate1, hoist6 is not located at depot0, hoist6 is not located at depot1, hoist6 is not located at distributor0, hoist6 is not located at distributor1, hoist6 is not raising crate2, hoist6 is not raising crate3, pallet0 can be found located at depot0, pallet0 cannot be found located at distributor0, pallet0 does not have crate0 on it, pallet0 is not clear, pallet0 is not located at depot1, pallet0 is not located at distributor2, pallet1 cannot be found located at distributor2, pallet1 does not have crate2 on it, pallet1 does not have crate3 on it, pallet1 is clear of any crates, pallet1 is not at depot0, pallet1 is not located at depot2, pallet1 is not located at distributor1, pallet1 is not located at distributor3, pallet2 does not have crate0 on it, pallet2 does not have crate1 on it, pallet2 has crate3 on it, pallet2 is not at depot1, pallet2 is not clear of any crates, pallet2 is not located at distributor2, pallet3 cannot be found located at depot2, pallet3 cannot be found located at distributor1, pallet3 does not have crate2 on it, pallet3 is clear, pallet3 is not located at depot0, pallet3 is not located at distributor3, pallet4 cannot be found located at depot1, pallet4 cannot be found located at depot2, pallet4 does not have crate2 on it, pallet4 is clear, pallet4 is not located at depot0, pallet4 is not located at distributor2, pallet4 is not located at distributor3, pallet5 does not have crate3 on it, pallet5 has crate0 on it, pallet5 is located at distributor2, pallet5 is not at distributor0, pallet5 is not at distributor1, pallet5 is not clear of any crates, pallet5 is not located at depot2, pallet5 is not located at distributor3, pallet6 can be found located at distributor3, pallet6 cannot be found located at depot0, pallet6 cannot be found located at distributor2, pallet6 does not have crate3 on it, pallet6 is clear of any crates, pallet6 is not at depot2, pallet6 is not at distributor1, pallet6 is not located at depot1, truck0 cannot be found located at depot2, truck0 does not contain crate0, truck0 is located at distributor2, truck0 is not at depot0, truck0 is not located at depot1, truck0 is not located at distributor0, truck0 is not located at distributor1, truck1 cannot be found located at distributor3, truck1 does not contain crate3, truck1 is not at depot2, truck1 is not at distributor0, truck1 is not located at depot1, truck2 cannot be found located at distributor0, truck2 cannot be found located at distributor2, truck2 does not contain crate1, truck2 does not contain crate3, truck2 is not at depot1 and truck2 is not at distributor3", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 has crate1 on it, crate0 is on pallet5, crate1 is clear of any crates, crate2 can be found located at depot0, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot2 is where crate3 is located, depot2 is where truck2 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, distributor2 is where crate1 is located, distributor2 is where pallet5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, distributor3 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is accessible, hoist2 is accessible, hoist2 is located at depot2, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0, pallet3 is clear of any crates, pallet4 is clear of any crates, pallet6 is clear and truck1 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: truck1 is moved from depot1 to depot0 to achieve the current state. In this state, enumerate all valid properties (including both affirmative and negated properties). If there are no properties, indicate None.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, with crate1 placed on top of it, and crate0 itself is positioned on pallet5. Crate1 has no other crates on it. Crate2 is located at depot0 and is clear of any other crates. Similarly, crate3 is also clear of any crates and is positioned on top of pallet2. Depot1 is the location of both hoist1 and pallet1. Depot2 is where crate3 and truck2 can be found. Distributor1 houses hoist4 and pallet4, while distributor2 is home to crate1, pallet5, and truck0. Distributor3 is the location of hoist6 and pallet6. Hoist0 is available for work and is situated at depot0. Hoist1 is accessible, hoist2 is accessible and located at depot2, and hoist3 is accessible and located at distributor0. Hoist4 is available for work, hoist5 is available and located at distributor2, and hoist6 is available. Pallet0 has crate2 on it and is located at depot0. Pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is at distributor0 and clear of any crates, pallet4 is clear of any crates, and pallet6 is clear. Truck1 is situated at depot1."}
{"question_id": "8f64dfcf-0467-41f2-842a-76b5047171fe", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at depot2, crate0 cannot be found located at distributor1, crate0 does not have crate1 on it, crate0 does not have crate2 on it, crate0 does not have crate3 on it, crate0 is not at depot1, crate0 is not at distributor0, crate0 is not in truck0, crate0 is not in truck1, crate0 is not inside truck2, crate0 is not on crate0, crate0 is not on crate3, crate0 is not on pallet1, crate0 is not on pallet2, crate0 is not on pallet3, crate0 is not on top of crate1, crate0 is not on top of pallet4, crate1 cannot be found located at distributor0, crate1 does not have crate1 on it, crate1 is not at depot1, crate1 is not at distributor1, crate1 is not in truck1, crate1 is not in truck2, crate1 is not inside truck0, crate1 is not located at depot2, crate1 is not located at distributor2, crate1 is not on crate3, crate1 is not on pallet0, crate1 is not on pallet4, crate1 is not on top of pallet1, crate1 is not on top of pallet2, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 cannot be found located at depot1, crate2 cannot be found located at distributor0, crate2 cannot be found located at distributor1, crate2 does not have crate0 on it, crate2 does not have crate1 on it, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not inside truck2, crate2 is not located at depot3, crate2 is not on pallet4, crate2 is not on pallet5, crate2 is not on top of crate1, crate2 is not on top of crate2, crate2 is not on top of pallet2, crate3 cannot be found located at depot0, crate3 cannot be found located at distributor0, crate3 cannot be found located at distributor2, crate3 does not have crate2 on it, crate3 does not have crate3 on it, crate3 is not in truck2, crate3 is not located at depot1, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet6, crate3 is not on top of pallet3, crate3 is not on top of pallet4, depot0 is where crate0 is not located, depot0 is where crate1 is not located, depot0 is where hoist1 is not located, depot0 is where pallet3 is not located, depot1 is where hoist0 is not located, depot1 is where hoist6 is not located, depot1 is where pallet3 is not located, depot2 is where crate2 is not located, depot2 is where crate3 is not located, depot2 is where hoist0 is not located, depot2 is where hoist1 is not located, depot2 is where hoist3 is not located, depot2 is where pallet1 is not located, depot2 is where pallet6 is not located, depot3 is where crate0 is not located, depot3 is where crate3 is not located, depot3 is where hoist4 is not located, depot3 is where pallet2 is not located, depot3 is where pallet4 is not located, depot3 is where pallet6 is not located, distributor0 is where pallet2 is not located, distributor1 is where hoist1 is not located, distributor1 is where hoist4 is not located, distributor1 is where pallet6 is not located, distributor1 is where truck2 is not located, distributor2 is where crate2 is not located, distributor2 is where hoist0 is not located, distributor2 is where truck1 is not located, distributor2 is where truck2 is not located, hoist0 is not at depot3, hoist0 is not at distributor0, hoist0 is not lifting crate0, hoist0 is not lifting crate3, hoist0 is not located at distributor1, hoist0 is not raising crate1, hoist0 is not raising crate2, hoist1 is not at distributor2, hoist1 is not elevating crate0, hoist1 is not elevating crate1, hoist1 is not lifting crate2, hoist1 is not located at depot3, hoist1 is not located at distributor0, hoist1 is not raising crate3, hoist2 cannot be found located at distributor0, hoist2 is not at depot0, hoist2 is not at distributor2, hoist2 is not lifting crate0, hoist2 is not lifting crate2, hoist2 is not located at depot1, hoist2 is not located at depot3, hoist2 is not located at distributor1, hoist2 is not raising crate1, hoist2 is not raising crate3, hoist3 cannot be found located at depot1, hoist3 cannot be found located at distributor1, hoist3 cannot be found located at distributor2, hoist3 is not elevating crate0, hoist3 is not located at depot0, hoist3 is not located at distributor0, hoist3 is not raising crate1, hoist3 is not raising crate2, hoist3 is not raising crate3, hoist4 is not at depot2, hoist4 is not at distributor2, hoist4 is not elevating crate3, hoist4 is not lifting crate2, hoist4 is not located at depot0, hoist4 is not located at depot1, hoist4 is not raising crate0, hoist4 is not raising crate1, hoist5 cannot be found located at depot1, hoist5 cannot be found located at depot2, hoist5 cannot be found located at depot3, hoist5 is not at depot0, hoist5 is not at distributor0, hoist5 is not at distributor2, hoist5 is not elevating crate3, hoist5 is not raising crate0, hoist5 is not raising crate1, hoist5 is not raising crate2, hoist6 is not at depot0, hoist6 is not at depot2, hoist6 is not elevating crate3, hoist6 is not lifting crate1, hoist6 is not located at depot3, hoist6 is not located at distributor0, hoist6 is not located at distributor1, hoist6 is not raising crate0, hoist6 is not raising crate2, pallet0 does not have crate0 on it, pallet0 does not have crate3 on it, pallet0 is not at depot1, pallet0 is not at distributor0, pallet0 is not at distributor1, pallet0 is not at distributor2, pallet0 is not clear, pallet0 is not located at depot2, pallet0 is not located at depot3, pallet1 cannot be found located at depot0, pallet1 cannot be found located at depot3, pallet1 cannot be found located at distributor2, pallet1 does not have crate2 on it, pallet1 does not have crate3 on it, pallet1 is not at distributor0, pallet1 is not located at distributor1, pallet2 cannot be found located at distributor2, pallet2 does not have crate3 on it, pallet2 is not located at depot0, pallet2 is not located at depot1, pallet2 is not located at distributor1, pallet3 cannot be found located at distributor2, pallet3 does not have crate2 on it, pallet3 is not at depot2, pallet3 is not at distributor0, pallet3 is not clear of any crates, pallet3 is not located at distributor1, pallet4 cannot be found located at depot0, pallet4 cannot be found located at depot1, pallet4 cannot be found located at depot2, pallet4 is not at distributor2, pallet4 is not located at distributor1, pallet5 cannot be found located at depot1, pallet5 does not have crate0 on it, pallet5 is not at depot2, pallet5 is not at depot3, pallet5 is not at distributor0, pallet5 is not clear of any crates, pallet5 is not located at depot0, pallet5 is not located at distributor2, pallet6 cannot be found located at depot1, pallet6 cannot be found located at distributor0, pallet6 does not have crate2 on it, pallet6 is not at depot0, pallet6 is not clear, truck0 cannot be found located at depot2, truck0 cannot be found located at distributor0, truck0 cannot be found located at distributor1, truck0 does not contain crate3, truck0 is not at depot3, truck0 is not at distributor2, truck0 is not located at depot0, truck1 cannot be found located at depot3, truck1 does not contain crate3, truck1 is not located at depot0, truck1 is not located at depot1, truck1 is not located at depot2, truck1 is not located at distributor1, truck2 cannot be found located at depot1, truck2 cannot be found located at distributor0, truck2 is not at depot2 and truck2 is not at depot3", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor2, crate1 is clear, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is clear, crate3 is on top of pallet5, depot2 is where hoist2 is located, depot3 is where crate1 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor2 is where pallet6 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is at depot3, hoist4 can be found located at distributor0, hoist4 is accessible, hoist5 can be found located at distributor1, hoist5 is available, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear, pallet3 has crate1 on it, pallet3 is at depot3, pallet4 is clear, pallet5 is located at distributor1, pallet6 has crate0 on it, truck0 can be found located at depot1, truck1 is located at distributor0 and truck2 is at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: truck2 is driven from depot1 to depot0 to achieve the current state. In this state, identify all valid properties of the state that include negations. If there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor2, crate1 is empty, crate2 is positioned at depot0, crate2 is empty, crate2 is stacked on pallet0, crate3 is empty, crate3 is stacked on pallet5, depot2 is the location of hoist2, depot3 is the location of crate1, distributor0 is the location of pallet4, distributor1 is the location of crate3, distributor2 is the location of pallet6, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is accessible, hoist3 is accessible, hoist3 is situated at depot3, hoist4 is located at distributor0, hoist4 is accessible, hoist5 is located at distributor1, hoist5 is available, hoist6 is located at distributor2, hoist6 is available for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, pallet3 has crate1 on it, pallet3 is situated at depot3, pallet4 is empty, pallet5 is situated at distributor1, pallet6 has crate0 on it, truck0 is located at depot1, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "83c4b2d1-3f7e-4c5b-a7d0-9feb3de5260e", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck0, from depot2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate1 from truck0, at distributor0, hoist3 drops crate1 on pallet3, crate0 is lifted from pallet4 at distributor1 by hoist4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at depot2, crate0 cannot be found located at distributor2, crate0 does not have crate1 on it, crate0 does not have crate3 on it, crate0 is not at distributor1, crate0 is not clear, crate0 is not inside truck1, crate0 is not located at depot1, crate0 is not located at distributor0, crate0 is not on crate0, crate0 is not on crate3, crate0 is not on pallet0, crate0 is not on pallet5, crate0 is not on top of crate1, crate0 is not on top of crate2, crate0 is not on top of pallet1, crate0 is not on top of pallet3, crate0 is not on top of pallet4, crate1 cannot be found located at depot0, crate1 cannot be found located at depot1, crate1 cannot be found located at distributor2, crate1 does not have crate2 on it, crate1 is not at distributor1, crate1 is not in truck0, crate1 is not in truck2, crate1 is not located at depot2, crate1 is not on pallet1, crate1 is not on pallet2, crate1 is not on pallet5, crate1 is not on top of crate1, crate1 is not on top of crate3, crate1 is not on top of pallet0, crate1 is not on top of pallet4, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor1, crate2 does not have crate1 on it, crate2 is not at distributor2, crate2 is not clear of any crates, crate2 is not in truck1, crate2 is not located at distributor0, crate2 is not on crate0, crate2 is not on top of crate2, crate2 is not on top of crate3, crate2 is not on top of pallet1, crate2 is not on top of pallet3, crate2 is not on top of pallet4, crate2 is not on top of pallet5, crate3 cannot be found located at depot1, crate3 is not at depot2, crate3 is not in truck0, crate3 is not in truck1, crate3 is not inside truck2, crate3 is not located at depot0, crate3 is not on crate3, crate3 is not on top of crate1, crate3 is not on top of crate2, depot0 is where crate0 is not located, depot0 is where hoist1 is not located, depot0 is where hoist3 is not located, depot0 is where pallet2 is not located, depot1 is where crate2 is not located, depot1 is where hoist4 is not located, depot1 is where pallet3 is not located, depot1 is where pallet5 is not located, depot2 is where crate2 is not located, depot2 is where hoist4 is not located, depot2 is where pallet1 is not located, depot2 is where truck0 is not located, distributor0 is where crate3 is not located, distributor0 is where hoist0 is not located, distributor0 is where hoist1 is not located, distributor0 is where hoist5 is not located, distributor0 is where pallet0 is not located, distributor0 is where pallet1 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet4 is not located, distributor0 is where pallet5 is not located, distributor0 is where truck1 is not located, distributor0 is where truck2 is not located, distributor1 is where crate3 is not located, distributor1 is where hoist2 is not located, distributor1 is where pallet0 is not located, distributor2 is where pallet1 is not located, distributor2 is where truck0 is not located, distributor2 is where truck2 is not located, hoist0 cannot be found located at depot2, hoist0 is not elevating crate2, hoist0 is not elevating crate3, hoist0 is not located at depot1, hoist0 is not located at distributor1, hoist0 is not located at distributor2, hoist0 is not raising crate0, hoist0 is not raising crate1, hoist1 cannot be found located at depot2, hoist1 is not lifting crate1, hoist1 is not located at distributor1, hoist1 is not located at distributor2, hoist1 is not raising crate0, hoist1 is not raising crate2, hoist1 is not raising crate3, hoist2 cannot be found located at depot1, hoist2 cannot be found located at distributor0, hoist2 is not lifting crate0, hoist2 is not lifting crate2, hoist2 is not located at depot0, hoist2 is not located at distributor2, hoist2 is not raising crate1, hoist2 is not raising crate3, hoist3 cannot be found located at depot1, hoist3 is not at distributor2, hoist3 is not elevating crate0, hoist3 is not elevating crate2, hoist3 is not lifting crate1, hoist3 is not located at depot2, hoist3 is not located at distributor1, hoist3 is not raising crate3, hoist4 is not elevating crate0, hoist4 is not elevating crate1, hoist4 is not lifting crate3, hoist4 is not located at depot0, hoist4 is not located at distributor0, hoist4 is not located at distributor2, hoist4 is not raising crate2, hoist5 cannot be found located at depot1, hoist5 cannot be found located at distributor1, hoist5 is not at depot2, hoist5 is not elevating crate1, hoist5 is not lifting crate0, hoist5 is not lifting crate2, hoist5 is not lifting crate3, hoist5 is not located at depot0, pallet0 cannot be found located at distributor2, pallet0 does not have crate2 on it, pallet0 does not have crate3 on it, pallet0 is not at depot2, pallet0 is not located at depot1, pallet1 cannot be found located at depot0, pallet1 does not have crate3 on it, pallet1 is not located at distributor1, pallet2 cannot be found located at distributor2, pallet2 does not have crate0 on it, pallet2 does not have crate2 on it, pallet2 does not have crate3 on it, pallet2 is not at depot1, pallet2 is not located at distributor1, pallet3 cannot be found located at depot0, pallet3 cannot be found located at depot2, pallet3 does not have crate3 on it, pallet3 is not clear, pallet3 is not located at distributor1, pallet3 is not located at distributor2, pallet4 cannot be found located at depot1, pallet4 cannot be found located at depot2, pallet4 does not have crate3 on it, pallet4 is not at depot0, pallet4 is not located at distributor2, pallet5 cannot be found located at depot2, pallet5 is not at depot0, pallet5 is not at distributor1, pallet5 is not clear of any crates, truck0 does not contain crate0, truck0 does not contain crate2, truck0 is not at depot0, truck0 is not at depot1, truck0 is not located at distributor1, truck1 does not contain crate1, truck1 is not at depot0, truck1 is not at depot2, truck1 is not located at depot1, truck1 is not located at distributor1, truck2 is not at depot2, truck2 is not located at depot0 and truck2 is not located at depot1", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is on pallet4, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear, crate3 is located at distributor2, crate3 is on pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor1 is where crate0 is located, hoist0 is available, hoist1 is accessible, hoist1 is at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 can be found located at distributor0, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor2, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet3 can be found located at distributor0, pallet3 is clear, pallet4 is at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 is at distributor2 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0, after which truck2 is driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. Additionally, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has no crates on top, it is positioned on pallet4. Crate1 is also clear of any crates and is placed on pallet2. Similarly, crate2 has no crates on it and is situated on pallet0. Crate3 is clear and is located at distributor2, where it is placed on pallet5. Depot0 is the location of crate2 and also houses hoist0. Depot2 is where crate1 and pallet2 are located. Crate0 is situated at distributor1. Hoist0 is available for use, while hoist1 is accessible and located at depot1. Hoist2 is available and situated at depot2. Hoist3 is available for work and can be found at distributor0, and hoist4 is also available for work, located at distributor1. Hoist5 is accessible and situated at distributor2. Pallet0 is located at depot0, pallet1 is clear and situated at depot1, and pallet3 is clear and located at distributor0. Pallet4 is positioned at distributor1, and pallet5 is situated at distributor2. The locations of the trucks are as follows: truck0 is at depot2, truck1 is at distributor2, and truck2 is at depot0."}
{"question_id": "6aab63a6-a24d-4bb7-883a-cbdef3ee68af", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, at depot2, hoist2 lifts crate3 off crate2, at depot2, hoist2 loads crate3 into truck2, hoist2 lifts crate2 from crate1 at depot2, crate2 is loaded by hoist2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor0, hoist3 lifts crate0 from pallet3 at distributor0 and hoist3 loads crate0 into truck2 at distributor0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at depot0, crate0 is not at distributor0, crate0 is not clear, crate0 is not in truck0, crate0 is not located at depot1, crate0 is not located at depot2, crate0 is not on crate0, crate0 is not on crate2, crate0 is not on crate3, crate0 is not on pallet3, crate0 is not on pallet4, crate0 is not on top of pallet0, crate0 is not on top of pallet2, crate0 is not on top of pallet5, crate1 cannot be found located at depot0, crate1 cannot be found located at distributor0, crate1 does not have crate0 on it, crate1 does not have crate3 on it, crate1 is not at distributor1, crate1 is not clear, crate1 is not inside truck1, crate1 is not located at depot1, crate1 is not located at depot2, crate1 is not on crate1, crate1 is not on crate2, crate1 is not on crate3, crate1 is not on pallet0, crate1 is not on pallet1, crate1 is not on pallet4, crate1 is not on pallet5, crate1 is not on top of crate0, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor0, crate2 cannot be found located at distributor2, crate2 does not have crate2 on it, crate2 does not have crate3 on it, crate2 is not clear, crate2 is not in truck0, crate2 is not inside truck1, crate2 is not located at depot1, crate2 is not located at depot2, crate2 is not on crate3, crate2 is not on top of crate0, crate2 is not on top of crate1, crate2 is not on top of pallet0, crate2 is not on top of pallet1, crate2 is not on top of pallet2, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor0, crate3 is not clear of any crates, crate3 is not in truck1, crate3 is not inside truck0, crate3 is not located at depot0, crate3 is not located at depot2, crate3 is not located at distributor1, crate3 is not located at distributor2, crate3 is not on crate0, crate3 is not on crate3, crate3 is not on pallet1, crate3 is not on pallet2, crate3 is not on pallet3, crate3 is not on pallet4, crate3 is not on pallet5, depot0 is where hoist3 is not located, depot0 is where pallet2 is not located, depot0 is where pallet3 is not located, depot1 is where hoist5 is not located, depot1 is where pallet4 is not located, depot2 is where pallet0 is not located, depot2 is where pallet3 is not located, depot2 is where truck0 is not located, distributor0 is where truck1 is not located, distributor1 is where crate0 is not located, distributor1 is where crate2 is not located, distributor1 is where hoist2 is not located, distributor1 is where pallet0 is not located, distributor1 is where truck1 is not located, distributor2 is where crate0 is not located, distributor2 is where crate1 is not located, distributor2 is where hoist3 is not located, distributor2 is where pallet4 is not located, hoist0 cannot be found located at depot2, hoist0 cannot be found located at distributor1, hoist0 cannot be found located at distributor2, hoist0 is not at distributor0, hoist0 is not elevating crate0, hoist0 is not elevating crate2, hoist0 is not lifting crate1, hoist0 is not lifting crate3, hoist0 is not located at depot1, hoist1 cannot be found located at depot2, hoist1 cannot be found located at distributor2, hoist1 is not at depot0, hoist1 is not at distributor0, hoist1 is not at distributor1, hoist1 is not elevating crate0, hoist1 is not lifting crate3, hoist1 is not raising crate1, hoist1 is not raising crate2, hoist2 cannot be found located at depot0, hoist2 cannot be found located at depot1, hoist2 cannot be found located at distributor2, hoist2 is not lifting crate1, hoist2 is not lifting crate3, hoist2 is not located at distributor0, hoist2 is not raising crate0, hoist2 is not raising crate2, hoist3 cannot be found located at depot1, hoist3 cannot be found located at depot2, hoist3 is not at distributor1, hoist3 is not lifting crate1, hoist3 is not lifting crate2, hoist3 is not raising crate0, hoist3 is not raising crate3, hoist4 is not at depot0, hoist4 is not at depot2, hoist4 is not at distributor0, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not located at depot1, hoist4 is not located at distributor2, hoist4 is not raising crate1, hoist4 is not raising crate3, hoist5 cannot be found located at depot2, hoist5 is not at distributor1, hoist5 is not elevating crate0, hoist5 is not elevating crate1, hoist5 is not elevating crate3, hoist5 is not located at depot0, hoist5 is not located at distributor0, hoist5 is not raising crate2, pallet0 cannot be found located at distributor0, pallet0 does not have crate3 on it, pallet0 is not at distributor2, pallet0 is not located at depot1, pallet1 cannot be found located at depot2, pallet1 cannot be found located at distributor1, pallet1 does not have crate0 on it, pallet1 is not at distributor0, pallet1 is not at distributor2, pallet1 is not located at depot0, pallet2 cannot be found located at depot1, pallet2 cannot be found located at distributor0, pallet2 does not have crate1 on it, pallet2 is not at distributor2, pallet2 is not located at distributor1, pallet3 cannot be found located at distributor2, pallet3 does not have crate1 on it, pallet3 does not have crate2 on it, pallet3 is not at depot1, pallet3 is not at distributor1, pallet4 cannot be found located at depot2, pallet4 does not have crate2 on it, pallet4 is not at depot0, pallet4 is not located at distributor0, pallet5 cannot be found located at depot2, pallet5 cannot be found located at distributor0, pallet5 does not have crate2 on it, pallet5 is not at depot1, pallet5 is not located at depot0, pallet5 is not located at distributor1, truck0 does not contain crate1, truck0 is not at depot0, truck0 is not at depot1, truck0 is not at distributor2, truck0 is not located at distributor1, truck1 does not contain crate0, truck1 is not at depot0, truck1 is not located at depot2, truck1 is not located at distributor2, truck2 cannot be found located at depot0, truck2 cannot be found located at depot2, truck2 cannot be found located at distributor1, truck2 cannot be found located at distributor2 and truck2 is not at depot1", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate1 is located at depot2, crate1 is on pallet2, crate2 is on crate1, crate3 is clear of any crates, crate3 is located at depot2, crate3 is on crate2, depot0 is where pallet0 is located, depot2 is where crate2 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is at distributor0, hoist3 is available for work, hoist4 is available, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available for work, pallet0 is clear, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 has crate0 on it, pallet3 is located at distributor0, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: truck2 travels from depot0 to depot2, where hoist2 unloads crate3 from crate2, then loads crate3 into truck2, subsequently lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2, all at depot2. Then, truck2 travels from depot2 to distributor0. At distributor0, hoist3 unloads crate0 from pallet3 and loads crate0 into truck2, resulting in the current state. In this state, list all valid properties that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0 and has no crates on it. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 is stacked on crate1. Crate3 is located at depot2, has no crates on it, and is placed on crate2. Depot0 is the location of pallet0. Depot2 is where crate2 is situated. Hoist0 is positioned at depot0 and is available for use. Hoist1 is located at depot1 and is accessible. Hoist2 is situated at depot2 and is accessible. Hoist3 is at distributor0 and is available for work. Hoist4 is available and located at distributor1. Hoist5 is positioned at distributor2 and is available for work. Pallet0 is empty. Pallet1 is clear of crates and is located at depot1. Pallet2 is situated at depot2. Pallet3 has crate0 on it and is located at distributor0. Pallet4 is empty and located at distributor1. Pallet5 is empty and situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "3522a286-6daf-44ef-aece-718bd1714e58", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "crate0 cannot be found located at depot2, crate0 cannot be found located at distributor1, crate0 does not have crate0 on it, crate0 does not have crate1 on it, crate0 does not have crate3 on it, crate0 is not in truck1, crate0 is not in truck2, crate0 is not located at distributor2, crate0 is not on crate1, crate0 is not on crate3, crate0 is not on pallet0, crate0 is not on top of pallet2, crate0 is not on top of pallet5, crate1 cannot be found located at depot0, crate1 is not at distributor1, crate1 is not clear of any crates, crate1 is not in truck1, crate1 is not located at distributor0, crate1 is not on top of crate1, crate1 is not on top of crate2, crate1 is not on top of crate3, crate1 is not on top of pallet0, crate1 is not on top of pallet1, crate1 is not on top of pallet3, crate2 does not have crate0 on it, crate2 is not at depot0, crate2 is not at distributor0, crate2 is not at distributor1, crate2 is not clear, crate2 is not in truck0, crate2 is not in truck2, crate2 is not located at depot1, crate2 is not located at distributor2, crate2 is not on crate0, crate2 is not on crate2, crate2 is not on crate3, crate2 is not on pallet1, crate2 is not on top of pallet0, crate2 is not on top of pallet2, crate2 is not on top of pallet5, crate3 is not at distributor0, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not located at depot0, crate3 is not located at depot1, crate3 is not on crate1, crate3 is not on crate3, crate3 is not on pallet3, crate3 is not on top of pallet0, crate3 is not on top of pallet1, crate3 is not on top of pallet4, depot0 is where crate0 is not located, depot0 is where pallet1 is not located, depot0 is where pallet5 is not located, depot1 is where crate0 is not located, depot1 is where crate1 is not located, depot1 is where hoist0 is not located, depot1 is where hoist4 is not located, depot1 is where hoist5 is not located, depot1 is where pallet2 is not located, depot1 is where truck2 is not located, depot2 is where hoist0 is not located, depot2 is where hoist1 is not located, depot2 is where hoist3 is not located, depot2 is where truck0 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist4 is not located, distributor0 is where pallet1 is not located, distributor0 is where truck1 is not located, distributor1 is where crate3 is not located, distributor1 is where pallet5 is not located, distributor1 is where truck0 is not located, distributor1 is where truck1 is not located, distributor2 is where crate1 is not located, distributor2 is where crate3 is not located, distributor2 is where hoist2 is not located, distributor2 is where truck2 is not located, hoist0 is not elevating crate1, hoist0 is not lifting crate0, hoist0 is not located at distributor0, hoist0 is not located at distributor1, hoist0 is not located at distributor2, hoist0 is not raising crate2, hoist0 is not raising crate3, hoist1 is not at distributor0, hoist1 is not at distributor1, hoist1 is not elevating crate0, hoist1 is not lifting crate2, hoist1 is not located at depot0, hoist1 is not located at distributor2, hoist1 is not raising crate1, hoist1 is not raising crate3, hoist2 cannot be found located at distributor1, hoist2 is not at depot1, hoist2 is not elevating crate0, hoist2 is not elevating crate1, hoist2 is not elevating crate2, hoist2 is not located at depot0, hoist2 is not raising crate3, hoist3 cannot be found located at distributor2, hoist3 is not at depot0, hoist3 is not at depot1, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist3 is not located at distributor1, hoist3 is not raising crate0, hoist3 is not raising crate2, hoist4 is not at depot0, hoist4 is not at distributor2, hoist4 is not elevating crate0, hoist4 is not elevating crate2, hoist4 is not elevating crate3, hoist4 is not lifting crate1, hoist4 is not located at depot2, hoist5 cannot be found located at depot0, hoist5 cannot be found located at depot2, hoist5 is not at distributor0, hoist5 is not at distributor1, hoist5 is not elevating crate0, hoist5 is not elevating crate1, hoist5 is not elevating crate2, hoist5 is not elevating crate3, pallet0 cannot be found located at depot1, pallet0 cannot be found located at distributor1, pallet0 is not at depot2, pallet0 is not at distributor0, pallet0 is not located at distributor2, pallet1 cannot be found located at distributor1, pallet1 does not have crate0 on it, pallet1 is not at distributor2, pallet1 is not located at depot2, pallet2 cannot be found located at distributor1, pallet2 cannot be found located at distributor2, pallet2 does not have crate3 on it, pallet2 is not at depot0, pallet2 is not clear of any crates, pallet2 is not located at distributor0, pallet3 cannot be found located at depot0, pallet3 cannot be found located at depot2, pallet3 does not have crate2 on it, pallet3 is not at distributor1, pallet3 is not clear of any crates, pallet3 is not located at depot1, pallet3 is not located at distributor2, pallet4 cannot be found located at depot1, pallet4 cannot be found located at distributor0, pallet4 cannot be found located at distributor2, pallet4 does not have crate0 on it, pallet4 does not have crate1 on it, pallet4 does not have crate2 on it, pallet4 is not located at depot0, pallet4 is not located at depot2, pallet5 cannot be found located at depot2, pallet5 cannot be found located at distributor0, pallet5 does not have crate1 on it, pallet5 does not have crate3 on it, pallet5 is not located at depot1, truck0 does not contain crate0, truck0 does not contain crate1, truck0 is not at depot0, truck0 is not at depot1, truck0 is not located at distributor2, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor2, truck1 does not contain crate2, truck1 is not at depot2, truck2 cannot be found located at distributor1, truck2 does not contain crate1, truck2 does not contain crate3, truck2 is not at distributor0 and truck2 is not located at depot0", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate1 is located at depot2, crate1 is on pallet2, crate2 is on crate1, crate3 is clear of any crates, crate3 is located at depot2, crate3 is on crate2, depot0 is where pallet0 is located, depot2 is where crate2 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is at distributor0, hoist3 is available for work, hoist4 is available, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available for work, pallet0 is clear, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 has crate0 on it, pallet3 is located at distributor0, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is located at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: truck2 is driven from depot0 to depot2 to achieve the current state. In this state, identify all valid properties that include negations, or state None if none exist.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0 and has no crates on it. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 is stacked on crate1. Crate3 is located at depot2, has no crates on it, and is placed on crate2. Depot0 is the location of pallet0. Depot2 is where crate2 is situated. Hoist0 is positioned at depot0 and is available for use. Hoist1 is located at depot1 and is accessible. Hoist2 is situated at depot2 and is accessible. Hoist3 is at distributor0 and is available for work. Hoist4 is available and located at distributor1. Hoist5 is positioned at distributor2 and is available for work. Pallet0 is empty. Pallet1 is clear of crates and located at depot1. Pallet2 is situated at depot2. Pallet3 has crate0 on it and is located at distributor0. Pallet4 is empty and located at distributor1. Pallet5 is empty and situated at distributor2. Truck0 is located at distributor0. Truck1 is positioned at depot1, and truck2 is situated at depot0."}
