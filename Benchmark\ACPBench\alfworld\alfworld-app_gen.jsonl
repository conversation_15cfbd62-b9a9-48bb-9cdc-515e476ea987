{"id": 4273841870964801883, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. sinkbasin1 is at location16. cabinet3 is at location8. cabinet4 is at location15. toiletpaperhanger1 is at location10. countertop1 is at location3. garbagecan1 is at location2. handtowelholder2 is at location17. handtowelholder1 is at location18. cabinet1 is at location4. toilet1 is at location7. towelholder1 is at location5. cabinet2 is at location11. sinkbasin2 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. handtowel1 is at location18. soapbar1 and toiletpaper1 are at location7. spraybottle1 and soapbottle2 are at location4. showerdoor1 is at location1. showerglass1 and towel1 are at location5. handtowel2 is at location17. sink1 is at location12. sink2 is at location14. soapbottle1 and spraybottle2 are at location15. candle1 and cloth1 are at location3. lightswitch1 is at location13. mirror1 is at location9. cloth2 is at location11. spraybottle3 is at location2. toiletpaper2 is at location8. agent agent1 is at location location3. The objects are in/on receptacle as follows. candle1 and cloth1 are on countertop1. spraybottle1 and soapbottle2 are in cabinet1. toiletpaper1 and soapbar1 are in toilet1. spraybottle2 and soapbottle1 are in cabinet4. spraybottle3 is in garbagecan1. towel1 is on towelholder1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. toiletpaper2 is in cabinet3. handtowel2 is on handtowelholder2. cabinet3, cabinet4, and cabinet2 are closed. soapbar2 is clean. cabinet1 is checked. cabinet1 is open. Nothing has been validated. agent1 is holding object soapbar2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a goes to receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a turns on object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - validate that togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is chilled and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(validate_pick_and_place_in_receptacle toiletpaper1 toiletpapertype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle spraybottle1 spraybottletype cabinet1 cabinettype)", "(go_to_location agent1 location3 location10 toiletpaperhanger1)", "(go_to_location agent1 location3 location8 cabinet3)", "(validate_pick_and_place_in_receptacle toiletpaper2 toiletpapertype cabinet3 cabinettype)", "(go_to_location agent1 location3 location5 towelholder1)", "(go_to_location agent1 location3 location17 handtowelholder2)", "(validate_pick_and_place_in_receptacle handtowel2 handtoweltype handtowelholder2 handtowelholdertype)", "(go_to_location agent1 location3 location15 cabinet4)", "(go_to_location agent1 location3 location18 handtowelholder1)", "(go_to_location agent1 location3 location4 cabinet1)", "(validate_pick_and_place_in_receptacle spraybottle2 spraybottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle candle1 candletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle soapbar1 soapbartype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle handtowel1 handtoweltype handtowelholder1 handtowelholdertype)", "(put_object_on_not_openable_receptacle agent1 location3 soapbar2 countertop1 soapbartype countertoptype)", "(go_to_location agent1 location3 location11 cabinet2)", "(go_to_location agent1 location3 location6 sinkbasin2)", "(go_to_location agent1 location3 location16 sinkbasin1)", "(validate_pick_and_place_in_receptacle cloth1 clothtype countertop1 countertoptype)", "(go_to_location agent1 location3 location2 garbagecan1)", "(validate_pick_and_place_in_receptacle towel1 toweltype towelholder1 towelholdertype)", "(validate_pick_and_place_in_receptacle spraybottle3 spraybottletype garbagecan1 garbagecantype)", "(go_to_location agent1 location3 location7 toilet1)", "(validate_pick_and_place_in_receptacle cloth2 clothtype cabinet2 cabinettype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype cabinet1 cabinettype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_214946_567644)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location3 location4 location5 location6 location7 location8 location9 - location alarmclock apple baseballbat basketball bathtub blinds book boots bowl box bread butterknife candle candle1 cd cellphone chair cloth cloth1 cloth2 creditcard cup curtains desklamp dishsponge egg faucet1 faucet2 faucet3 floorlamp footstool fork glassbottle handtowel handtowel1 handtowel2 houseplant kettle keychain knife ladle laptop laundryhamperlid lettuce lightswitch lightswitch1 mirror mirror1 mug newspaper painting pan papertowel papertowelroll pen pencil peppershaker pillow plate plunger plunger1 poster pot potato remotecontrol saltshaker scrubbrush scrubbrush1 showerdoor showerdoor1 showerglass showerglass1 sink sink1 sink2 soapbar soapbar1 soapbar2 soapbottle soapbottle1 soapbottle2 spatula spoon spraybottle spraybottle1 spraybottle2 spraybottle3 statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaper1 toiletpaper2 toiletpaperroll tomato towel towel1 vase watch wateringcan window winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 countertop1 garbagecan1 handtowelholder1 handtowelholder2 sinkbasin1 sinkbasin2 toilet1 toiletpaperhanger1 towelholder1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location3) (cancontain cabinettype candletype) (cancontain cabinettype clothtype) (cancontain cabinettype handtoweltype) (cancontain cabinettype plungertype) (cancontain cabinettype soapbartype) (cancontain cabinettype soapbottletype) (cancontain cabinettype spraybottletype) (cancontain cabinettype toiletpapertype) (cancontain countertoptype candletype) (cancontain countertoptype clothtype) (cancontain countertoptype handtoweltype) (cancontain countertoptype soapbartype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spraybottletype) (cancontain countertoptype toiletpapertype) (cancontain garbagecantype clothtype) (cancontain garbagecantype handtoweltype) (cancontain garbagecantype soapbartype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype spraybottletype) (cancontain garbagecantype toiletpapertype) (cancontain handtowelholdertype handtoweltype) (cancontain sinkbasintype clothtype) (cancontain sinkbasintype handtoweltype) (cancontain sinkbasintype soapbartype) (cancontain toiletpaperhangertype toiletpapertype) (cancontain toilettype candletype) (cancontain toilettype clothtype) (cancontain toilettype handtoweltype) (cancontain toilettype soapbartype) (cancontain toilettype soapbottletype) (cancontain toilettype spraybottletype) (cancontain toilettype toiletpapertype) (cancontain towelholdertype toweltype) (checked cabinet1) (cleanable cloth1) (cleanable cloth2) (cleanable soapbar1) (cleanable soapbar2) (closed cabinet2) (closed cabinet3) (closed cabinet4) (holds agent1 soapbar2) (inreceptacle candle1 countertop1) (inreceptacle cloth1 countertop1) (inreceptacle cloth2 cabinet2) (inreceptacle handtowel1 handtowelholder1) (inreceptacle handtowel2 handtowelholder2) (inreceptacle soapbar1 toilet1) (inreceptacle soapbottle1 cabinet4) (inreceptacle soapbottle2 cabinet1) (inreceptacle spraybottle1 cabinet1) (inreceptacle spraybottle2 cabinet4) (inreceptacle spraybottle3 garbagecan1) (inreceptacle toiletpaper1 toilet1) (inreceptacle toiletpaper2 cabinet3) (inreceptacle towel1 towelholder1) (isclean soapbar2) (notopenable countertop1) (notopenable garbagecan1) (notopenable handtowelholder1) (notopenable handtowelholder2) (notopenable sinkbasin1) (notopenable sinkbasin2) (notopenable toilet1) (notopenable toiletpaperhanger1) (notopenable towelholder1) (notvalidated) (objectatlocation candle1 location3) (objectatlocation cloth1 location3) (objectatlocation cloth2 location11) (objectatlocation handtowel1 location18) (objectatlocation handtowel2 location17) (objectatlocation lightswitch1 location13) (objectatlocation mirror1 location9) (objectatlocation plunger1 location10) (objectatlocation scrubbrush1 location10) (objectatlocation showerdoor1 location1) (objectatlocation showerglass1 location5) (objectatlocation sink1 location12) (objectatlocation sink2 location14) (objectatlocation soapbar1 location7) (objectatlocation soapbottle1 location15) (objectatlocation soapbottle2 location4) (objectatlocation spraybottle1 location4) (objectatlocation spraybottle2 location15) (objectatlocation spraybottle3 location2) (objectatlocation toiletpaper1 location7) (objectatlocation toiletpaper2 location8) (objectatlocation towel1 location5) (objecttype candle1 candletype) (objecttype cloth1 clothtype) (objecttype cloth2 clothtype) (objecttype handtowel1 handtoweltype) (objecttype handtowel2 handtoweltype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype plunger1 plungertype) (objecttype scrubbrush1 scrubbrushtype) (objecttype showerdoor1 showerdoortype) (objecttype showerglass1 showerglasstype) (objecttype sink1 sinktype) (objecttype sink2 sinktype) (objecttype soapbar1 soapbartype) (objecttype soapbar2 soapbartype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spraybottle1 spraybottletype) (objecttype spraybottle2 spraybottletype) (objecttype spraybottle3 spraybottletype) (objecttype toiletpaper1 toiletpapertype) (objecttype toiletpaper2 toiletpapertype) (objecttype towel1 toweltype) (openable cabinet1) (openable cabinet2) (openable cabinet3) (openable cabinet4) (opened cabinet1) (pickupable candle1) (pickupable cloth1) (pickupable cloth2) (pickupable handtowel1) (pickupable handtowel2) (pickupable plunger1) (pickupable scrubbrush1) (pickupable soapbar1) (pickupable soapbar2) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spraybottle1) (pickupable spraybottle2) (pickupable spraybottle3) (pickupable toiletpaper1) (pickupable toiletpaper2) (pickupable towel1) (receptacleatlocation cabinet1 location4) (receptacleatlocation cabinet2 location11) (receptacleatlocation cabinet3 location8) (receptacleatlocation cabinet4 location15) (receptacleatlocation countertop1 location3) (receptacleatlocation garbagecan1 location2) (receptacleatlocation handtowelholder1 location18) (receptacleatlocation handtowelholder2 location17) (receptacleatlocation sinkbasin1 location16) (receptacleatlocation sinkbasin2 location6) (receptacleatlocation toilet1 location7) (receptacleatlocation toiletpaperhanger1 location10) (receptacleatlocation towelholder1 location5) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype countertop1 countertoptype) (receptacletype garbagecan1 garbagecantype) (receptacletype handtowelholder1 handtowelholdertype) (receptacletype handtowelholder2 handtowelholdertype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype sinkbasin2 sinkbasintype) (receptacletype toilet1 toilettype) (receptacletype toiletpaperhanger1 toiletpaperhangertype) (receptacletype towelholder1 towelholdertype))\n    (:goal (validatecleanandplace soapbartype cabinettype))\n)"}
{"id": -6799268985430334462, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. fridge1 is at location10. cabinet5 is at location13. sinkbasin1 is at location6. countertop2 and coffeemachine1 are at location11. shelf1 is at location17. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. drawer2 is at location15. cabinet4 is at location14. countertop1 is at location2. shelf2 is at location29. stoveburner1 and stoveburner3 are at location5. garbagecan1 is at location8. countertop3 is at location3. shelf3 is at location26. drawer3 is at location27. drawer1 is at location20. cabinet1 is at location19. cabinet2 is at location22. toaster1 is at location9. cabinet3 is at location16. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. dishsponge1 is at location15. potato2, potato1, cup2, cup1, apple1, and lettuce1 are at location10. cellphone3, creditcard1, mug1, lettuce2, bread1, creditcard2, spoon1, and statue1 are at location2. houseplant1, spoon3, lettuce3, glassbottle3, soapbottle1, knife1, fork2, saltshaker2, tomato1, and butterknife1 are at location3. vase1 is at location17. vase2 and papertowelroll1 are at location29. peppershaker1, cellphone1, and pan1 are at location11. plate1 is at location4. stoveknob3 and stoveknob2 are at location12. fork1, sink1, spoon2, and spatula2 are at location6. saltshaker1 is at location16. stoveknob4 is at location7. window1 is at location30. chair2 is at location1. bowl1 and glassbottle2 are at location14. apple2 and egg1 are at location8. saltshaker3 is at location26. lightswitch1 is at location25. pot1 is at location5. cellphone2 and peppershaker2 are at location20. chair1 is at location23. stoveknob1 is at location18. window2 is at location28. glassbottle1 is at location19. spatula1 is at location27. agent agent1 is at location location13. The objects are in/on receptacle as follows. vase1 is on shelf1. spoon1, creditcard1, statue1, creditcard2, mug1, lettuce2, bread1, and cellphone3 are on countertop1. pan1 is on stoveburner2. lettuce1, potato1, cup2, cup1, apple1, and potato2 are in fridge1. soapbottle1, houseplant1, butterknife1, fork2, knife1, saltshaker2, tomato1, glassbottle3, lettuce3, and spoon3 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. pan1 is on stoveburner4. cellphone2 and peppershaker2 are in drawer1. saltshaker1 is in cabinet3. bowl1 and glassbottle2 are in cabinet4. spatula1 is in drawer3. plate1 is in cabinet6. egg1 and apple2 are in garbagecan1. vase2 and papertowelroll1 are on shelf2. pot1 is on stoveburner3. glassbottle1 is in cabinet1. peppershaker1, cellphone1, and pan1 are on countertop2. pot1 is on stoveburner1. saltshaker3 is on shelf3. dishsponge1 is in drawer2. drawer3, drawer1, drawer2, cabinet5, cabinet1, microwave1, fridge1, and cabinet2 are closed. Nothing has been validated. agent1 is holding object egg2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in a receptacle ?r that is at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o with a receptacle ?r that is in location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(go_to_location agent1 location13 location11 countertop2)", "(go_to_location agent1 location13 location17 shelf1)", "(validate_pick_and_place_in_receptacle spoon3 spoontype countertop3 countertoptype)", "(go_to_location agent1 location13 location21 stoveburner2)", "(go_to_location agent1 location13 location5 stoveburner1)", "(go_to_location agent1 location13 location21 stoveburner4)", "(go_to_location agent1 location13 location24 microwave1)", "(validate_pick_and_place_in_receptacle saltshaker2 saltshakertype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle creditcard2 creditcard1 creditcardtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle dishsponge1 dishspongetype drawer2 drawertype)", "(go_to_location agent1 location13 location8 garbagecan1)", "(validate_pick_and_place_in_receptacle potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle vase2 vasetype shelf2 shelftype)", "(validate_pick_and_place_in_receptacle cup2 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle saltshaker1 saltshakertype cabinet3 cabinettype)", "(go_to_location agent1 location13 location3 countertop3)", "(validate_pick_and_place_in_receptacle houseplant1 houseplanttype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle cellphone1 cellphonetype countertop2 countertoptype)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner3 stoveburnertype)", "(go_to_location agent1 location13 location26 shelf3)", "(go_to_location agent1 location13 location20 drawer1)", "(validate_pick_and_place_in_receptacle tomato1 tomatotype countertop3 countertoptype)", "(go_to_location agent1 location13 location6 sinkbasin1)", "(validate_pick_and_place_in_receptacle mug1 mugtype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle potato1 potato2 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle vase1 vasetype shelf1 shelftype)", "(validate_pick_and_place_in_receptacle glassbottle1 glassbottletype cabinet1 cabinettype)", "(go_to_location agent1 location13 location22 cabinet2)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner1 stoveburnertype)", "(validate_pick_and_place_in_receptacle spoon2 spoontype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle creditcard1 creditcardtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle lettuce1 lettucetype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle saltshaker3 saltshakertype shelf3 shelftype)", "(validate_pick_and_place_in_receptacle cup1 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle papertowelroll1 papertowelrolltype shelf2 shelftype)", "(go_to_location agent1 location13 location19 cabinet1)", "(go_to_location agent1 location13 location29 shelf2)", "(go_to_location agent1 location13 location14 cabinet4)", "(validate_pick_and_place_in_receptacle knife1 knifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle bread1 breadtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle creditcard2 creditcardtype countertop1 countertoptype)", "(go_to_location agent1 location13 location10 fridge1)", "(validate_pick_and_place_in_receptacle lettuce2 lettucetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle cup2 cup1 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle peppershaker1 peppershakertype countertop2 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype countertop2 countertoptype)", "(validate_pick_two_and_place_in_receptacle creditcard1 creditcard2 creditcardtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle spoon1 spoontype countertop1 countertoptype)", "(go_to_location agent1 location13 location27 drawer3)", "(go_to_location agent1 location13 location2 countertop1)", "(go_to_location agent1 location13 location5 stoveburner3)", "(validate_pick_and_place_in_receptacle cellphone2 cellphonetype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle fork1 forktype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle apple1 appletype fridge1 fridgetype)", "(go_to_location agent1 location13 location11 coffeemachine1)", "(validate_pick_two_and_place_in_receptacle cup1 cup2 cuptype fridge1 fridgetype)", "(go_to_location agent1 location13 location9 toaster1)", "(go_to_location agent1 location13 location16 cabinet3)", "(validate_pick_and_place_in_receptacle glassbottle2 glassbottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle egg1 eggtype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle lettuce3 lettucetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle glassbottle3 glassbottletype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle bowl1 bowltype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle statue1 statuetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle apple2 appletype garbagecan1 garbagecantype)", "(go_to_location agent1 location13 location4 cabinet6)", "(validate_pick_and_place_in_receptacle cellphone3 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle plate1 platetype cabinet6 cabinettype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner4 stoveburnertype)", "(validate_pick_and_place_in_receptacle fork2 forktype countertop3 countertoptype)", "(go_to_location agent1 location13 location15 drawer2)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle potato2 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle peppershaker2 peppershakertype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner2 stoveburnertype)", "(open_receptacle agent1 location13 cabinet5)", "(validate_pick_two_and_place_in_receptacle potato2 potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle spatula2 spatulatype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle spatula1 spatulatype drawer3 drawertype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_113523_123938)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 cup cup1 cup2 curtains desklamp dishsponge dishsponge1 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lettuce3 lightswitch lightswitch1 mirror mug mug1 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate1 plunger poster pot pot1 potato potato1 potato2 remotecontrol saltshaker saltshaker1 saltshaker2 saltshaker3 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 spatula spatula1 spatula2 spoon spoon1 spoon2 spoon3 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location13) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable butterknife1) (cleanable cup1) (cleanable cup2) (cleanable dishsponge1) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable lettuce2) (cleanable lettuce3) (cleanable mug1) (cleanable pan1) (cleanable plate1) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable spatula1) (cleanable spatula2) (cleanable spoon1) (cleanable spoon2) (cleanable spoon3) (cleanable tomato1) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable lettuce3) (coolable mug1) (coolable pan1) (coolable plate1) (coolable pot1) (coolable potato1) (coolable potato2) (coolable tomato1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable cup2) (heatable egg1) (heatable egg2) (heatable mug1) (heatable plate1) (heatable potato1) (heatable potato2) (heatable tomato1) (holds agent1 egg2) (inreceptacle apple1 fridge1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 cabinet4) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop2) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop1) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 countertop1) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle dishsponge1 drawer2) (inreceptacle egg1 garbagecan1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 cabinet1) (inreceptacle glassbottle2 cabinet4) (inreceptacle glassbottle3 countertop3) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle lettuce2 countertop1) (inreceptacle lettuce3 countertop3) (inreceptacle mug1 countertop1) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 shelf2) (inreceptacle peppershaker1 countertop2) (inreceptacle peppershaker2 drawer1) (inreceptacle plate1 cabinet6) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle saltshaker1 cabinet3) (inreceptacle saltshaker2 countertop3) (inreceptacle saltshaker3 shelf3) (inreceptacle soapbottle1 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spoon1 countertop1) (inreceptacle spoon2 sinkbasin1) (inreceptacle spoon3 countertop3) (inreceptacle statue1 countertop1) (inreceptacle tomato1 countertop3) (inreceptacle vase1 shelf1) (inreceptacle vase2 shelf2) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location10) (objectatlocation apple2 location8) (objectatlocation bowl1 location14) (objectatlocation bread1 location2) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location11) (objectatlocation cellphone2 location20) (objectatlocation cellphone3 location2) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location2) (objectatlocation creditcard2 location2) (objectatlocation cup1 location10) (objectatlocation cup2 location10) (objectatlocation dishsponge1 location15) (objectatlocation egg1 location8) (objectatlocation fork1 location6) (objectatlocation fork2 location3) (objectatlocation glassbottle1 location19) (objectatlocation glassbottle2 location14) (objectatlocation glassbottle3 location3) (objectatlocation houseplant1 location3) (objectatlocation knife1 location3) (objectatlocation lettuce1 location10) (objectatlocation lettuce2 location2) (objectatlocation lettuce3 location3) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location2) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location29) (objectatlocation peppershaker1 location11) (objectatlocation peppershaker2 location20) (objectatlocation plate1 location4) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location10) (objectatlocation saltshaker1 location16) (objectatlocation saltshaker2 location3) (objectatlocation saltshaker3 location26) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location3) (objectatlocation spatula1 location27) (objectatlocation spatula2 location6) (objectatlocation spoon1 location2) (objectatlocation spoon2 location6) (objectatlocation spoon3 location3) (objectatlocation statue1 location2) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location3) (objectatlocation vase1 location17) (objectatlocation vase2 location29) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lettuce3 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype saltshaker2 saltshakertype) (objecttype saltshaker3 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype spoon3 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable cup1) (pickupable cup2) (pickupable dishsponge1) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable lettuce1) (pickupable lettuce2) (pickupable lettuce3) (pickupable mug1) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable saltshaker1) (pickupable saltshaker2) (pickupable saltshaker3) (pickupable soapbottle1) (pickupable spatula1) (pickupable spatula2) (pickupable spoon1) (pickupable spoon2) (pickupable spoon3) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable lettuce3) (sliceable potato1) (sliceable potato2) (sliceable tomato1))\n    (:goal (validateheatandplace eggtype garbagecantype))\n)"}
{"id": -685556877892875815, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. sinkbasin1 is at location16. cabinet3 is at location8. cabinet4 is at location15. toiletpaperhanger1 is at location10. countertop1 is at location3. garbagecan1 is at location2. handtowelholder2 is at location17. handtowelholder1 is at location18. cabinet1 is at location4. toilet1 is at location7. towelholder1 is at location5. cabinet2 is at location11. sinkbasin2 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. handtowel1 is at location18. soapbar1 and toiletpaper1 are at location7. spraybottle1 and soapbottle2 are at location4. showerdoor1 is at location1. showerglass1 and towel1 are at location5. handtowel2 is at location17. soapbar2 and toiletpaper2 are at location8. sink1 is at location12. sink2 is at location14. soapbottle1 and spraybottle2 are at location15. candle1 and cloth1 are at location3. lightswitch1 is at location13. mirror1 is at location9. cloth2 is at location11. spraybottle3 is at location2. agent agent1 is at location location16. The objects are in/on receptacle as follows. candle1 and cloth1 are on countertop1. spraybottle1 and soapbottle2 are in cabinet1. soapbar2 and toiletpaper2 are in cabinet3. toiletpaper1 and soapbar1 are in toilet1. spraybottle2 and soapbottle1 are in cabinet4. spraybottle3 is in garbagecan1. towel1 is on towelholder1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. handtowel2 is on handtowelholder2. cabinet4, cabinet2, and cabinet1 are closed. soapbar2 is clean. cabinet3 is checked. cabinet3 is open. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a moves from the current position ?lstart to the next position ?lend that has the receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot on a not openable receptacle ?r of type ?rt while at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in a receptacle ?r that is at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(go_to_location agent1 location16 location3 countertop1)", "(validate_pick_and_place_in_receptacle toiletpaper1 toiletpapertype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle spraybottle1 spraybottletype cabinet1 cabinettype)", "(validate_pick_and_place_in_receptacle toiletpaper2 toiletpapertype cabinet3 cabinettype)", "(go_to_location agent1 location16 location10 toiletpaperhanger1)", "(go_to_location agent1 location16 location11 cabinet2)", "(go_to_location agent1 location16 location17 handtowelholder2)", "(validate_pick_and_place_in_receptacle handtowel2 handtoweltype handtowelholder2 handtowelholdertype)", "(validate_clean_and_place_in_receptacle soapbar2 soapbartype cabinet3 cabinettype)", "(validate_pick_and_place_in_receptacle spraybottle2 spraybottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle candle1 candletype countertop1 countertoptype)", "(go_to_location agent1 location16 location8 cabinet3)", "(go_to_location agent1 location16 location18 handtowelholder1)", "(go_to_location agent1 location16 location7 toilet1)", "(go_to_location agent1 location16 location4 cabinet1)", "(validate_pick_and_place_in_receptacle soapbar1 soapbartype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle handtowel1 handtoweltype handtowelholder1 handtowelholdertype)", "(go_to_location agent1 location16 location6 sinkbasin2)", "(go_to_location agent1 location16 location5 towelholder1)", "(validate_pick_and_place_in_receptacle soapbar2 soapbartype cabinet3 cabinettype)", "(validate_pick_and_place_in_receptacle cloth1 clothtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle towel1 toweltype towelholder1 towelholdertype)", "(go_to_location agent1 location16 location2 garbagecan1)", "(validate_pick_and_place_in_receptacle spraybottle3 spraybottletype garbagecan1 garbagecantype)", "(go_to_location agent1 location16 location15 cabinet4)", "(validate_pick_and_place_in_receptacle cloth2 clothtype cabinet2 cabinettype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype cabinet1 cabinettype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_214946_567644)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location3 location4 location5 location6 location7 location8 location9 - location alarmclock apple baseballbat basketball bathtub blinds book boots bowl box bread butterknife candle candle1 cd cellphone chair cloth cloth1 cloth2 creditcard cup curtains desklamp dishsponge egg faucet1 faucet2 faucet3 floorlamp footstool fork glassbottle handtowel handtowel1 handtowel2 houseplant kettle keychain knife ladle laptop laundryhamperlid lettuce lightswitch lightswitch1 mirror mirror1 mug newspaper painting pan papertowel papertowelroll pen pencil peppershaker pillow plate plunger plunger1 poster pot potato remotecontrol saltshaker scrubbrush scrubbrush1 showerdoor showerdoor1 showerglass showerglass1 sink sink1 sink2 soapbar soapbar1 soapbar2 soapbottle soapbottle1 soapbottle2 spatula spoon spraybottle spraybottle1 spraybottle2 spraybottle3 statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaper1 toiletpaper2 toiletpaperroll tomato towel towel1 vase watch wateringcan window winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 countertop1 garbagecan1 handtowelholder1 handtowelholder2 sinkbasin1 sinkbasin2 toilet1 toiletpaperhanger1 towelholder1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location16) (cancontain cabinettype candletype) (cancontain cabinettype clothtype) (cancontain cabinettype handtoweltype) (cancontain cabinettype plungertype) (cancontain cabinettype soapbartype) (cancontain cabinettype soapbottletype) (cancontain cabinettype spraybottletype) (cancontain cabinettype toiletpapertype) (cancontain countertoptype candletype) (cancontain countertoptype clothtype) (cancontain countertoptype handtoweltype) (cancontain countertoptype soapbartype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spraybottletype) (cancontain countertoptype toiletpapertype) (cancontain garbagecantype clothtype) (cancontain garbagecantype handtoweltype) (cancontain garbagecantype soapbartype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype spraybottletype) (cancontain garbagecantype toiletpapertype) (cancontain handtowelholdertype handtoweltype) (cancontain sinkbasintype clothtype) (cancontain sinkbasintype handtoweltype) (cancontain sinkbasintype soapbartype) (cancontain toiletpaperhangertype toiletpapertype) (cancontain toilettype candletype) (cancontain toilettype clothtype) (cancontain toilettype handtoweltype) (cancontain toilettype soapbartype) (cancontain toilettype soapbottletype) (cancontain toilettype spraybottletype) (cancontain toilettype toiletpapertype) (cancontain towelholdertype toweltype) (checked cabinet3) (cleanable cloth1) (cleanable cloth2) (cleanable soapbar1) (cleanable soapbar2) (closed cabinet1) (closed cabinet2) (closed cabinet4) (handempty agent1) (inreceptacle candle1 countertop1) (inreceptacle cloth1 countertop1) (inreceptacle cloth2 cabinet2) (inreceptacle handtowel1 handtowelholder1) (inreceptacle handtowel2 handtowelholder2) (inreceptacle soapbar1 toilet1) (inreceptacle soapbar2 cabinet3) (inreceptacle soapbottle1 cabinet4) (inreceptacle soapbottle2 cabinet1) (inreceptacle spraybottle1 cabinet1) (inreceptacle spraybottle2 cabinet4) (inreceptacle spraybottle3 garbagecan1) (inreceptacle toiletpaper1 toilet1) (inreceptacle toiletpaper2 cabinet3) (inreceptacle towel1 towelholder1) (isclean soapbar2) (notopenable countertop1) (notopenable garbagecan1) (notopenable handtowelholder1) (notopenable handtowelholder2) (notopenable sinkbasin1) (notopenable sinkbasin2) (notopenable toilet1) (notopenable toiletpaperhanger1) (notopenable towelholder1) (notvalidated) (objectatlocation candle1 location3) (objectatlocation cloth1 location3) (objectatlocation cloth2 location11) (objectatlocation handtowel1 location18) (objectatlocation handtowel2 location17) (objectatlocation lightswitch1 location13) (objectatlocation mirror1 location9) (objectatlocation plunger1 location10) (objectatlocation scrubbrush1 location10) (objectatlocation showerdoor1 location1) (objectatlocation showerglass1 location5) (objectatlocation sink1 location12) (objectatlocation sink2 location14) (objectatlocation soapbar1 location7) (objectatlocation soapbar2 location8) (objectatlocation soapbottle1 location15) (objectatlocation soapbottle2 location4) (objectatlocation spraybottle1 location4) (objectatlocation spraybottle2 location15) (objectatlocation spraybottle3 location2) (objectatlocation toiletpaper1 location7) (objectatlocation toiletpaper2 location8) (objectatlocation towel1 location5) (objecttype candle1 candletype) (objecttype cloth1 clothtype) (objecttype cloth2 clothtype) (objecttype handtowel1 handtoweltype) (objecttype handtowel2 handtoweltype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype plunger1 plungertype) (objecttype scrubbrush1 scrubbrushtype) (objecttype showerdoor1 showerdoortype) (objecttype showerglass1 showerglasstype) (objecttype sink1 sinktype) (objecttype sink2 sinktype) (objecttype soapbar1 soapbartype) (objecttype soapbar2 soapbartype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spraybottle1 spraybottletype) (objecttype spraybottle2 spraybottletype) (objecttype spraybottle3 spraybottletype) (objecttype toiletpaper1 toiletpapertype) (objecttype toiletpaper2 toiletpapertype) (objecttype towel1 toweltype) (openable cabinet1) (openable cabinet2) (openable cabinet3) (openable cabinet4) (opened cabinet3) (pickupable candle1) (pickupable cloth1) (pickupable cloth2) (pickupable handtowel1) (pickupable handtowel2) (pickupable plunger1) (pickupable scrubbrush1) (pickupable soapbar1) (pickupable soapbar2) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spraybottle1) (pickupable spraybottle2) (pickupable spraybottle3) (pickupable toiletpaper1) (pickupable toiletpaper2) (pickupable towel1) (receptacleatlocation cabinet1 location4) (receptacleatlocation cabinet2 location11) (receptacleatlocation cabinet3 location8) (receptacleatlocation cabinet4 location15) (receptacleatlocation countertop1 location3) (receptacleatlocation garbagecan1 location2) (receptacleatlocation handtowelholder1 location18) (receptacleatlocation handtowelholder2 location17) (receptacleatlocation sinkbasin1 location16) (receptacleatlocation sinkbasin2 location6) (receptacleatlocation toilet1 location7) (receptacleatlocation toiletpaperhanger1 location10) (receptacleatlocation towelholder1 location5) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype countertop1 countertoptype) (receptacletype garbagecan1 garbagecantype) (receptacletype handtowelholder1 handtowelholdertype) (receptacletype handtowelholder2 handtowelholdertype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype sinkbasin2 sinkbasintype) (receptacletype toilet1 toilettype) (receptacletype toiletpaperhanger1 toiletpaperhangertype) (receptacletype towelholder1 towelholdertype))\n    (:goal (validatecleanandplace soapbartype cabinettype))\n)"}
{"id": 1355058713747467566, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. sinkbasin1 is at location16. cabinet3 is at location8. cabinet4 is at location15. toiletpaperhanger1 is at location10. countertop1 is at location3. garbagecan1 is at location2. handtowelholder2 is at location17. handtowelholder1 is at location18. cabinet1 is at location4. toilet1 is at location7. towelholder1 is at location5. cabinet2 is at location11. sinkbasin2 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. handtowel1 is at location18. soapbar1 and toiletpaper1 are at location7. spraybottle1 and soapbottle2 are at location4. showerdoor1 is at location1. showerglass1 and towel1 are at location5. handtowel2 is at location17. sink1 is at location12. sink2 is at location14. soapbottle1 and spraybottle2 are at location15. candle1, soapbar2, and cloth1 are at location3. lightswitch1 is at location13. mirror1 is at location9. cloth2 is at location11. spraybottle3 is at location2. toiletpaper2 is at location8. agent agent1 is at location location8. The objects are in/on receptacle as follows. soapbar2, candle1, and cloth1 are on countertop1. spraybottle1 and soapbottle2 are in cabinet1. toiletpaper1 and soapbar1 are in toilet1. spraybottle2 and soapbottle1 are in cabinet4. spraybottle3 is in garbagecan1. towel1 is on towelholder1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. toiletpaper2 is in cabinet3. handtowel2 is on handtowelholder2. cabinet3, cabinet4, cabinet2, and cabinet1 are closed. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r while at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans an object ?o in a sink ?r that is in location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a turns on object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(go_to_location agent1 location8 location6 sinkbasin2)", "(validate_pick_and_place_in_receptacle toiletpaper1 toiletpapertype toilet1 toilettype)", "(go_to_location agent1 location8 location16 sinkbasin1)", "(validate_pick_and_place_in_receptacle spraybottle1 spraybottletype cabinet1 cabinettype)", "(validate_pick_and_place_in_receptacle toiletpaper2 toiletpapertype cabinet3 cabinettype)", "(validate_pick_and_place_in_receptacle handtowel2 handtoweltype handtowelholder2 handtowelholdertype)", "(go_to_location agent1 location8 location18 handtowelholder1)", "(validate_pick_and_place_in_receptacle spraybottle2 spraybottletype cabinet4 cabinettype)", "(open_receptacle agent1 location8 cabinet3)", "(go_to_location agent1 location8 location4 cabinet1)", "(go_to_location agent1 location8 location5 towelholder1)", "(validate_pick_and_place_in_receptacle candle1 candletype countertop1 countertoptype)", "(go_to_location agent1 location8 location3 countertop1)", "(validate_pick_and_place_in_receptacle soapbar1 soapbartype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle handtowel1 handtoweltype handtowelholder1 handtowelholdertype)", "(go_to_location agent1 location8 location15 cabinet4)", "(go_to_location agent1 location8 location17 handtowelholder2)", "(go_to_location agent1 location8 location2 garbagecan1)", "(validate_pick_and_place_in_receptacle cloth1 clothtype countertop1 countertoptype)", "(go_to_location agent1 location8 location10 toiletpaperhanger1)", "(go_to_location agent1 location8 location11 cabinet2)", "(validate_pick_and_place_in_receptacle towel1 toweltype towelholder1 towelholdertype)", "(go_to_location agent1 location8 location7 toilet1)", "(validate_pick_and_place_in_receptacle soapbar2 soapbartype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle spraybottle3 spraybottletype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle cloth2 clothtype cabinet2 cabinettype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype cabinet1 cabinettype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_214946_567644)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location3 location4 location5 location6 location7 location8 location9 - location alarmclock apple baseballbat basketball bathtub blinds book boots bowl box bread butterknife candle candle1 cd cellphone chair cloth cloth1 cloth2 creditcard cup curtains desklamp dishsponge egg faucet1 faucet2 faucet3 floorlamp footstool fork glassbottle handtowel handtowel1 handtowel2 houseplant kettle keychain knife ladle laptop laundryhamperlid lettuce lightswitch lightswitch1 mirror mirror1 mug newspaper painting pan papertowel papertowelroll pen pencil peppershaker pillow plate plunger plunger1 poster pot potato remotecontrol saltshaker scrubbrush scrubbrush1 showerdoor showerdoor1 showerglass showerglass1 sink sink1 sink2 soapbar soapbar1 soapbar2 soapbottle soapbottle1 soapbottle2 spatula spoon spraybottle spraybottle1 spraybottle2 spraybottle3 statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaper1 toiletpaper2 toiletpaperroll tomato towel towel1 vase watch wateringcan window winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 countertop1 garbagecan1 handtowelholder1 handtowelholder2 sinkbasin1 sinkbasin2 toilet1 toiletpaperhanger1 towelholder1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location8) (cancontain cabinettype candletype) (cancontain cabinettype clothtype) (cancontain cabinettype handtoweltype) (cancontain cabinettype plungertype) (cancontain cabinettype soapbartype) (cancontain cabinettype soapbottletype) (cancontain cabinettype spraybottletype) (cancontain cabinettype toiletpapertype) (cancontain countertoptype candletype) (cancontain countertoptype clothtype) (cancontain countertoptype handtoweltype) (cancontain countertoptype soapbartype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spraybottletype) (cancontain countertoptype toiletpapertype) (cancontain garbagecantype clothtype) (cancontain garbagecantype handtoweltype) (cancontain garbagecantype soapbartype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype spraybottletype) (cancontain garbagecantype toiletpapertype) (cancontain handtowelholdertype handtoweltype) (cancontain sinkbasintype clothtype) (cancontain sinkbasintype handtoweltype) (cancontain sinkbasintype soapbartype) (cancontain toiletpaperhangertype toiletpapertype) (cancontain toilettype candletype) (cancontain toilettype clothtype) (cancontain toilettype handtoweltype) (cancontain toilettype soapbartype) (cancontain toilettype soapbottletype) (cancontain toilettype spraybottletype) (cancontain toilettype toiletpapertype) (cancontain towelholdertype toweltype) (cleanable cloth1) (cleanable cloth2) (cleanable soapbar1) (cleanable soapbar2) (closed cabinet1) (closed cabinet2) (closed cabinet3) (closed cabinet4) (handempty agent1) (inreceptacle candle1 countertop1) (inreceptacle cloth1 countertop1) (inreceptacle cloth2 cabinet2) (inreceptacle handtowel1 handtowelholder1) (inreceptacle handtowel2 handtowelholder2) (inreceptacle soapbar1 toilet1) (inreceptacle soapbar2 countertop1) (inreceptacle soapbottle1 cabinet4) (inreceptacle soapbottle2 cabinet1) (inreceptacle spraybottle1 cabinet1) (inreceptacle spraybottle2 cabinet4) (inreceptacle spraybottle3 garbagecan1) (inreceptacle toiletpaper1 toilet1) (inreceptacle toiletpaper2 cabinet3) (inreceptacle towel1 towelholder1) (notopenable countertop1) (notopenable garbagecan1) (notopenable handtowelholder1) (notopenable handtowelholder2) (notopenable sinkbasin1) (notopenable sinkbasin2) (notopenable toilet1) (notopenable toiletpaperhanger1) (notopenable towelholder1) (notvalidated) (objectatlocation candle1 location3) (objectatlocation cloth1 location3) (objectatlocation cloth2 location11) (objectatlocation handtowel1 location18) (objectatlocation handtowel2 location17) (objectatlocation lightswitch1 location13) (objectatlocation mirror1 location9) (objectatlocation plunger1 location10) (objectatlocation scrubbrush1 location10) (objectatlocation showerdoor1 location1) (objectatlocation showerglass1 location5) (objectatlocation sink1 location12) (objectatlocation sink2 location14) (objectatlocation soapbar1 location7) (objectatlocation soapbar2 location3) (objectatlocation soapbottle1 location15) (objectatlocation soapbottle2 location4) (objectatlocation spraybottle1 location4) (objectatlocation spraybottle2 location15) (objectatlocation spraybottle3 location2) (objectatlocation toiletpaper1 location7) (objectatlocation toiletpaper2 location8) (objectatlocation towel1 location5) (objecttype candle1 candletype) (objecttype cloth1 clothtype) (objecttype cloth2 clothtype) (objecttype handtowel1 handtoweltype) (objecttype handtowel2 handtoweltype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype plunger1 plungertype) (objecttype scrubbrush1 scrubbrushtype) (objecttype showerdoor1 showerdoortype) (objecttype showerglass1 showerglasstype) (objecttype sink1 sinktype) (objecttype sink2 sinktype) (objecttype soapbar1 soapbartype) (objecttype soapbar2 soapbartype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spraybottle1 spraybottletype) (objecttype spraybottle2 spraybottletype) (objecttype spraybottle3 spraybottletype) (objecttype toiletpaper1 toiletpapertype) (objecttype toiletpaper2 toiletpapertype) (objecttype towel1 toweltype) (openable cabinet1) (openable cabinet2) (openable cabinet3) (openable cabinet4) (pickupable candle1) (pickupable cloth1) (pickupable cloth2) (pickupable handtowel1) (pickupable handtowel2) (pickupable plunger1) (pickupable scrubbrush1) (pickupable soapbar1) (pickupable soapbar2) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spraybottle1) (pickupable spraybottle2) (pickupable spraybottle3) (pickupable toiletpaper1) (pickupable toiletpaper2) (pickupable towel1) (receptacleatlocation cabinet1 location4) (receptacleatlocation cabinet2 location11) (receptacleatlocation cabinet3 location8) (receptacleatlocation cabinet4 location15) (receptacleatlocation countertop1 location3) (receptacleatlocation garbagecan1 location2) (receptacleatlocation handtowelholder1 location18) (receptacleatlocation handtowelholder2 location17) (receptacleatlocation sinkbasin1 location16) (receptacleatlocation sinkbasin2 location6) (receptacleatlocation toilet1 location7) (receptacleatlocation toiletpaperhanger1 location10) (receptacleatlocation towelholder1 location5) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype countertop1 countertoptype) (receptacletype garbagecan1 garbagecantype) (receptacletype handtowelholder1 handtowelholdertype) (receptacletype handtowelholder2 handtowelholdertype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype sinkbasin2 sinkbasintype) (receptacletype toilet1 toilettype) (receptacletype toiletpaperhanger1 toiletpaperhangertype) (receptacletype towelholder1 towelholdertype))\n    (:goal (validatecleanandplace soapbartype cabinettype))\n)"}
{"id": -*******************, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location9. toaster1 is at location10. shelf2 is at location30. stoveburner2 and stoveburner4 are at location22. cabinet6 is at location5. stoveburner1 and stoveburner3 are at location6. countertop2 and coffeemachine1 are at location12. shelf3 is at location27. cabinet5 is at location14. drawer1 is at location21. countertop3 is at location4. cabinet1 is at location20. cabinet2 is at location23. shelf1 is at location18. microwave1 is at location25. cabinet4 is at location15. countertop1 is at location3. drawer2 is at location16. sinkbasin1 is at location7. cabinet3 is at location17. drawer3 is at location28. fridge1 is at location11. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. vase2 is at location18. mug1 is at location25. apple3, apple1, creditcard1, dishsponge3, cellphone1, bread1, spatula2, apple2, and statue1 are at location3. egg1, cup1, potato2, potato1, cup2, lettuce1, tomato1, and tomato2 are at location11. butterknife2, butterknife1, spatula3, peppershaker2, knife1, statue2, soapbottle3, cellphone3, spoon2, fork2, and houseplant1 are at location4. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. stoveknob4 is at location8. stoveknob1 is at location19. creditcard3 and saltshaker1 are at location27. window1 is at location31. pot1 is at location22. bowl1, mug2, glassbottle2, and creditcard2 are at location30. fork1, egg2, tomato3, cup3, potato3, and sink1 are at location7. soapbottle2, papertowelroll1, and glassbottle1 are at location9. chair1 is at location24. chair2 is at location1. stoveknob3 and stoveknob2 are at location13. spatula1 is at location28. pan1 is at location6. plate2 and plate1 are at location14. cellphone2 is at location21. dishsponge2 is at location2. vase1 and soapbottle1 are at location5. peppershaker1 is at location20. agent agent1 is at location location11. The objects are in/on receptacle as follows. peppershaker1 is in cabinet1. cellphone3, houseplant1, butterknife1, butterknife2, fork2, soapbottle3, spatula3, knife1, statue2, peppershaker2, and spoon2 are on countertop3. soapbottle1 and vase1 are in cabinet6. glassbottle2, mug2, bowl1, and creditcard2 are on shelf2. apple1, creditcard1, statue1, spatula2, cellphone1, apple3, dishsponge3, bread1, and apple2 are on countertop1. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. lettuce1, tomato1, potato1, cup2, tomato2, cup1, egg1, and potato2 are in fridge1. pot1 is on stoveburner4. creditcard3 and saltshaker1 are on shelf3. cup3, fork1, potato3, tomato3, and egg2 are in sinkbasin1. plate2, dishsponge2, and plate1 are in cabinet5. pan1 is on stoveburner1. pot1 is on stoveburner2. cellphone2 is in drawer1. vase2 is on shelf1. dishsponge2 is on plate1. spatula1 is in drawer3. spoon1 and dishsponge1 are in drawer2. pan1 is on stoveburner3. mug1 is in microwave1. drawer3, drawer1, drawer2, cabinet5, cabinet1, microwave1, fridge1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r while at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a grasps object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans an object ?o in a sink ?r that is in location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a turns on object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is colded down and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(go_to_location agent1 location11 location7 sinkbasin1)", "(validate_pick_and_place_in_receptacle creditcard3 creditcardtype shelf3 shelftype)", "(validate_pick_two_and_place_in_receptacle apple2 apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle3 soapbottletype countertop3 countertoptype)", "(go_to_location agent1 location11 location20 cabinet1)", "(go_to_location agent1 location11 location25 microwave1)", "(validate_pick_and_place_in_receptacle dishsponge1 dishspongetype drawer2 drawertype)", "(validate_pick_and_place_in_receptacle potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle egg2 eggtype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle peppershaker2 peppershakertype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle apple3 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle cellphone1 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle tomato2 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle mug1 mugtype microwave1 microwavetype)", "(validate_pick_and_place_in_receptacle cup2 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle tomato3 tomatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle glassbottle1 glassbottletype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle peppershaker1 peppershakertype cabinet1 cabinettype)", "(validate_pick_and_place_in_receptacle dishsponge3 dishspongetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle plate1 plate2 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle houseplant1 houseplanttype countertop3 countertoptype)", "(go_to_location agent1 location11 location17 cabinet3)", "(validate_pick_two_and_place_in_receptacle potato1 potato2 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle apple1 appletype countertop1 countertoptype)", "(go_to_location agent1 location11 location4 countertop3)", "(go_to_location agent1 location11 location9 garbagecan1)", "(go_to_location agent1 location11 location5 cabinet6)", "(go_to_location agent1 location11 location15 cabinet4)", "(validate_pick_and_place_in_receptacle dishsponge2 dishspongetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle creditcard1 creditcardtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle vase1 vasetype cabinet6 cabinettype)", "(validate_pick_two_and_place_in_receptacle tomato1 tomato2 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle lettuce1 lettucetype fridge1 fridgetype)", "(go_to_location agent1 location11 location10 toaster1)", "(validate_pick_and_place_in_receptacle papertowelroll1 papertowelrolltype garbagecan1 garbagecantype)", "(go_to_location agent1 location11 location28 drawer3)", "(validate_pick_and_place_in_receptacle cellphone3 cellphonetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle cup1 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle tomato1 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle glassbottle2 glassbottletype shelf2 shelftype)", "(go_to_location agent1 location11 location22 stoveburner4)", "(validate_pick_two_and_place_in_receptacle apple3 apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife2 butterknifetype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle apple2 apple3 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle cup3 cuptype sinkbasin1 sinkbasintype)", "(go_to_location agent1 location11 location6 stoveburner1)", "(go_to_location agent1 location11 location22 stoveburner2)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner4 stoveburnertype)", "(go_to_location agent1 location11 location12 coffeemachine1)", "(validate_pick_and_place_in_receptacle knife1 knifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner3 stoveburnertype)", "(validate_pick_and_place_in_receptacle spoon2 spoontype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet6 cabinettype)", "(validate_pick_and_place_in_receptacle bread1 breadtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner1 stoveburnertype)", "(validate_pick_two_and_place_in_receptacle apple3 apple2 appletype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle apple1 apple3 appletype countertop1 countertoptype)", "(go_to_location agent1 location11 location16 drawer2)", "(validate_pick_and_place_in_receptacle apple2 appletype countertop1 countertoptype)", "(go_to_location agent1 location11 location14 cabinet5)", "(open_receptacle agent1 location11 fridge1)", "(validate_pick_two_and_place_in_receptacle cup2 cup1 cuptype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle butterknife1 butterknife2 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle saltshaker1 saltshakertype shelf3 shelftype)", "(go_to_location agent1 location11 location23 cabinet2)", "(go_to_location agent1 location11 location30 shelf2)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner2 stoveburnertype)", "(validate_pick_and_place_in_receptacle cellphone2 cellphonetype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle fork1 forktype sinkbasin1 sinkbasintype)", "(go_to_location agent1 location11 location18 shelf1)", "(go_to_location agent1 location11 location12 countertop2)", "(validate_pick_two_and_place_in_receptacle cup1 cup2 cuptype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle apple1 apple2 appletype countertop1 countertoptype)", "(go_to_location agent1 location11 location27 shelf3)", "(validate_pick_and_place_in_receptacle spoon1 spoontype drawer2 drawertype)", "(validate_pick_and_place_in_receptacle statue1 statuetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle tomato2 tomato1 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle mug2 mugtype shelf2 shelftype)", "(validate_pick_and_place_in_receptacle plate2 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle bowl1 bowltype shelf2 shelftype)", "(validate_pick_and_place_in_receptacle spatula3 spatulatype countertop3 countertoptype)", "(go_to_location agent1 location11 location21 drawer1)", "(validate_pick_and_place_in_receptacle potato3 potatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle spatula2 spatulatype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle vase2 vasetype shelf1 shelftype)", "(validate_pick_and_place_in_receptacle plate1 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle fork2 forktype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location11 location6 stoveburner3)", "(validate_pick_and_place_in_receptacle statue2 statuetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle potato2 potatotype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle plate2 plate1 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle creditcard2 creditcardtype shelf2 shelftype)", "(validate_pick_two_and_place_in_receptacle butterknife2 butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle potato2 potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle spatula1 spatulatype drawer3 drawertype)", "(go_to_location agent1 location11 location3 countertop1)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190906_191445_723170)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location32 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 apple3 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 butterknife2 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 creditcard3 cup cup1 cup2 cup3 curtains desklamp dishsponge dishsponge1 dishsponge2 dishsponge3 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate2 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 soapbottle3 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 statue2 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 tomato2 tomato3 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location11) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable apple3) (cleanable bowl1) (cleanable butterknife1) (cleanable butterknife2) (cleanable cup1) (cleanable cup2) (cleanable cup3) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable dishsponge3) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (cleanable tomato2) (cleanable tomato3) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable apple3) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable cup3) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (coolable tomato2) (coolable tomato3) (handempty agent1) (heatable apple1) (heatable apple2) (heatable apple3) (heatable bread1) (heatable cup1) (heatable cup2) (heatable cup3) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (heatable tomato2) (heatable tomato3) (inreceptacle apple1 countertop1) (inreceptacle apple2 countertop1) (inreceptacle apple3 countertop1) (inreceptacle bowl1 shelf2) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle butterknife2 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 shelf2) (inreceptacle creditcard3 shelf3) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle cup3 sinkbasin1) (inreceptacle dishsponge1 drawer2) (inreceptacle dishsponge2 cabinet5) (inreceptacle dishsponge2 plate1) (inreceptacle dishsponge3 countertop1) (inreceptacle egg1 fridge1) (inreceptacle egg2 sinkbasin1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 garbagecan1) (inreceptacle glassbottle2 shelf2) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle mug1 microwave1) (inreceptacle mug2 shelf2) (inreceptacle pan1 stoveburner1) (inreceptacle pan1 stoveburner3) (inreceptacle papertowelroll1 garbagecan1) (inreceptacle peppershaker1 cabinet1) (inreceptacle peppershaker2 countertop3) (inreceptacle plate1 cabinet5) (inreceptacle plate2 cabinet5) (inreceptacle pot1 stoveburner2) (inreceptacle pot1 stoveburner4) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle potato3 sinkbasin1) (inreceptacle saltshaker1 shelf3) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 garbagecan1) (inreceptacle soapbottle3 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 countertop1) (inreceptacle spatula3 countertop3) (inreceptacle spoon1 drawer2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 countertop1) (inreceptacle statue2 countertop3) (inreceptacle tomato1 fridge1) (inreceptacle tomato2 fridge1) (inreceptacle tomato3 sinkbasin1) (inreceptacle vase1 cabinet6) (inreceptacle vase2 shelf1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location3) (objectatlocation apple2 location3) (objectatlocation apple3 location3) (objectatlocation bowl1 location30) (objectatlocation bread1 location3) (objectatlocation butterknife1 location4) (objectatlocation butterknife2 location4) (objectatlocation cellphone1 location3) (objectatlocation cellphone2 location21) (objectatlocation cellphone3 location4) (objectatlocation chair1 location24) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation creditcard2 location30) (objectatlocation creditcard3 location27) (objectatlocation cup1 location11) (objectatlocation cup2 location11) (objectatlocation cup3 location7) (objectatlocation dishsponge1 location16) (objectatlocation dishsponge2 location2) (objectatlocation dishsponge3 location3) (objectatlocation egg1 location11) (objectatlocation egg2 location7) (objectatlocation fork1 location7) (objectatlocation fork2 location4) (objectatlocation glassbottle1 location9) (objectatlocation glassbottle2 location30) (objectatlocation houseplant1 location4) (objectatlocation knife1 location4) (objectatlocation lettuce1 location11) (objectatlocation lightswitch1 location26) (objectatlocation mug1 location25) (objectatlocation mug2 location30) (objectatlocation pan1 location6) (objectatlocation papertowelroll1 location9) (objectatlocation peppershaker1 location20) (objectatlocation peppershaker2 location4) (objectatlocation plate1 location14) (objectatlocation plate2 location14) (objectatlocation pot1 location22) (objectatlocation potato1 location11) (objectatlocation potato2 location11) (objectatlocation potato3 location7) (objectatlocation saltshaker1 location27) (objectatlocation sink1 location7) (objectatlocation soapbottle1 location5) (objectatlocation soapbottle2 location9) (objectatlocation soapbottle3 location4) (objectatlocation spatula1 location28) (objectatlocation spatula2 location3) (objectatlocation spatula3 location4) (objectatlocation spoon1 location16) (objectatlocation spoon2 location4) (objectatlocation statue1 location3) (objectatlocation statue2 location4) (objectatlocation stoveknob1 location19) (objectatlocation stoveknob2 location13) (objectatlocation stoveknob3 location13) (objectatlocation stoveknob4 location8) (objectatlocation tomato1 location11) (objectatlocation tomato2 location11) (objectatlocation tomato3 location7) (objectatlocation vase1 location5) (objectatlocation vase2 location18) (objectatlocation window1 location31) (objectatlocation window2 location29) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype apple3 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype butterknife2 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype creditcard3 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype cup3 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype dishsponge3 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype soapbottle3 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype statue2 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype tomato2 tomatotype) (objecttype tomato3 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable apple3) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable butterknife2) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable creditcard3) (pickupable cup1) (pickupable cup2) (pickupable cup3) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable dishsponge3) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable knife1) (pickupable lettuce1) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable plate2) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable soapbottle3) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable statue2) (pickupable tomato1) (pickupable tomato2) (pickupable tomato3) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location20) (receptacleatlocation cabinet2 location23) (receptacleatlocation cabinet3 location17) (receptacleatlocation cabinet4 location15) (receptacleatlocation cabinet5 location14) (receptacleatlocation cabinet6 location5) (receptacleatlocation coffeemachine1 location12) (receptacleatlocation countertop1 location3) (receptacleatlocation countertop2 location12) (receptacleatlocation countertop3 location4) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location16) (receptacleatlocation drawer3 location28) (receptacleatlocation fridge1 location11) (receptacleatlocation garbagecan1 location9) (receptacleatlocation microwave1 location25) (receptacleatlocation shelf1 location18) (receptacleatlocation shelf2 location30) (receptacleatlocation shelf3 location27) (receptacleatlocation sinkbasin1 location7) (receptacleatlocation stoveburner1 location6) (receptacleatlocation stoveburner2 location22) (receptacleatlocation stoveburner3 location6) (receptacleatlocation stoveburner4 location22) (receptacleatlocation toaster1 location10) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable apple3) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1) (sliceable tomato2) (sliceable tomato3))\n    (:goal (validatepickandplace saltshakertype cabinettype))\n)"}
{"id": 6198797412606130776, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location9. toaster1 is at location10. shelf2 is at location30. stoveburner2 and stoveburner4 are at location22. cabinet6 is at location5. stoveburner1 and stoveburner3 are at location6. countertop2 and coffeemachine1 are at location12. shelf3 is at location27. cabinet5 is at location14. drawer1 is at location21. countertop3 is at location4. cabinet1 is at location20. cabinet2 is at location23. shelf1 is at location18. microwave1 is at location25. cabinet4 is at location15. countertop1 is at location3. drawer2 is at location16. sinkbasin1 is at location7. cabinet3 is at location17. drawer3 is at location28. fridge1 is at location11. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. vase2 is at location18. mug1 is at location25. apple3, apple1, creditcard1, dishsponge3, cellphone1, bread1, spatula2, apple2, and statue1 are at location3. egg1, cup1, potato2, potato1, cup2, lettuce1, tomato1, and tomato2 are at location11. butterknife2, butterknife1, spatula3, peppershaker2, knife1, statue2, soapbottle3, cellphone3, spoon2, fork2, and houseplant1 are at location4. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. stoveknob4 is at location8. stoveknob1 is at location19. creditcard3 and saltshaker1 are at location27. window1 is at location31. pot1 is at location22. bowl1, mug2, glassbottle2, and creditcard2 are at location30. fork1, egg2, tomato3, cup3, potato3, and sink1 are at location7. soapbottle2, papertowelroll1, and glassbottle1 are at location9. chair1 is at location24. chair2 is at location1. stoveknob3 and stoveknob2 are at location13. spatula1 is at location28. pan1 is at location6. plate2 and plate1 are at location14. cellphone2 is at location21. dishsponge2 is at location2. vase1 and soapbottle1 are at location5. peppershaker1 is at location20. agent agent1 is at location location32. The objects are in/on receptacle as follows. peppershaker1 is in cabinet1. cellphone3, houseplant1, butterknife1, butterknife2, fork2, soapbottle3, spatula3, knife1, statue2, peppershaker2, and spoon2 are on countertop3. soapbottle1 and vase1 are in cabinet6. glassbottle2, mug2, bowl1, and creditcard2 are on shelf2. apple1, creditcard1, statue1, spatula2, cellphone1, apple3, dishsponge3, bread1, and apple2 are on countertop1. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. lettuce1, tomato1, potato1, cup2, tomato2, cup1, egg1, and potato2 are in fridge1. pot1 is on stoveburner4. creditcard3 and saltshaker1 are on shelf3. cup3, fork1, potato3, tomato3, and egg2 are in sinkbasin1. plate2, dishsponge2, and plate1 are in cabinet5. pan1 is on stoveburner1. pot1 is on stoveburner2. cellphone2 is in drawer1. vase2 is on shelf1. dishsponge2 is on plate1. spatula1 is in drawer3. spoon1 and dishsponge1 are in drawer2. pan1 is on stoveburner3. mug1 is in microwave1. drawer3, drawer1, drawer2, cabinet5, cabinet1, microwave1, fridge1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot on a not openable receptacle ?r of type ?rt that is at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - validate that togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(go_to_location agent1 location32 location22 stoveburner2)", "(validate_pick_and_place_in_receptacle creditcard3 creditcardtype shelf3 shelftype)", "(validate_pick_two_and_place_in_receptacle apple2 apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle3 soapbottletype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle dishsponge1 dishspongetype drawer2 drawertype)", "(validate_pick_and_place_in_receptacle potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle egg2 eggtype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle peppershaker2 peppershakertype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle apple3 appletype countertop1 countertoptype)", "(go_to_location agent1 location32 location12 countertop2)", "(validate_pick_and_place_in_receptacle cellphone1 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle tomato2 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle mug1 mugtype microwave1 microwavetype)", "(validate_pick_and_place_in_receptacle cup2 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle tomato3 tomatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle glassbottle1 glassbottletype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle peppershaker1 peppershakertype cabinet1 cabinettype)", "(go_to_location agent1 location32 location4 countertop3)", "(validate_pick_and_place_in_receptacle dishsponge3 dishspongetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle plate1 plate2 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle houseplant1 houseplanttype countertop3 countertoptype)", "(go_to_location agent1 location32 location15 cabinet4)", "(go_to_location agent1 location32 location12 coffeemachine1)", "(go_to_location agent1 location32 location14 cabinet5)", "(validate_pick_two_and_place_in_receptacle potato1 potato2 potatotype fridge1 fridgetype)", "(go_to_location agent1 location32 location27 shelf3)", "(validate_pick_and_place_in_receptacle apple1 appletype countertop1 countertoptype)", "(go_to_location agent1 location32 location6 stoveburner3)", "(validate_pick_and_place_in_receptacle dishsponge2 dishspongetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle creditcard1 creditcardtype countertop1 countertoptype)", "(go_to_location agent1 location32 location10 toaster1)", "(validate_pick_and_place_in_receptacle vase1 vasetype cabinet6 cabinettype)", "(validate_pick_two_and_place_in_receptacle tomato1 tomato2 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle lettuce1 lettucetype fridge1 fridgetype)", "(go_to_location agent1 location32 location25 microwave1)", "(validate_pick_and_place_in_receptacle papertowelroll1 papertowelrolltype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle cellphone3 cellphonetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle cup1 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle tomato1 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle glassbottle2 glassbottletype shelf2 shelftype)", "(validate_pick_two_and_place_in_receptacle apple3 apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife2 butterknifetype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle apple2 apple3 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle cup3 cuptype sinkbasin1 sinkbasintype)", "(go_to_location agent1 location32 location30 shelf2)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner4 stoveburnertype)", "(validate_pick_and_place_in_receptacle knife1 knifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner3 stoveburnertype)", "(validate_pick_and_place_in_receptacle spoon2 spoontype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet6 cabinettype)", "(go_to_location agent1 location32 location21 drawer1)", "(validate_pick_and_place_in_receptacle bread1 breadtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner1 stoveburnertype)", "(validate_pick_two_and_place_in_receptacle apple3 apple2 appletype countertop1 countertoptype)", "(go_to_location agent1 location32 location3 countertop1)", "(validate_pick_two_and_place_in_receptacle apple1 apple3 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle apple2 appletype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle cup2 cup1 cuptype fridge1 fridgetype)", "(go_to_location agent1 location32 location28 drawer3)", "(validate_pick_two_and_place_in_receptacle butterknife1 butterknife2 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle saltshaker1 saltshakertype shelf3 shelftype)", "(go_to_location agent1 location32 location23 cabinet2)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner2 stoveburnertype)", "(go_to_location agent1 location32 location9 garbagecan1)", "(go_to_location agent1 location32 location5 cabinet6)", "(go_to_location agent1 location32 location17 cabinet3)", "(validate_pick_and_place_in_receptacle cellphone2 cellphonetype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle fork1 forktype sinkbasin1 sinkbasintype)", "(validate_pick_two_and_place_in_receptacle cup1 cup2 cuptype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle apple1 apple2 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle spoon1 spoontype drawer2 drawertype)", "(validate_pick_and_place_in_receptacle statue1 statuetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle tomato2 tomato1 tomatotype fridge1 fridgetype)", "(go_to_location agent1 location32 location7 sinkbasin1)", "(validate_pick_and_place_in_receptacle mug2 mugtype shelf2 shelftype)", "(validate_pick_and_place_in_receptacle plate2 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle bowl1 bowltype shelf2 shelftype)", "(validate_pick_and_place_in_receptacle spatula3 spatulatype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle potato3 potatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle spatula2 spatulatype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle vase2 vasetype shelf1 shelftype)", "(validate_pick_and_place_in_receptacle plate1 platetype cabinet5 cabinettype)", "(go_to_location agent1 location32 location16 drawer2)", "(validate_pick_and_place_in_receptacle fork2 forktype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location32 location18 shelf1)", "(go_to_location agent1 location32 location20 cabinet1)", "(go_to_location agent1 location32 location11 fridge1)", "(validate_pick_and_place_in_receptacle statue2 statuetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle potato2 potatotype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle plate2 plate1 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle creditcard2 creditcardtype shelf2 shelftype)", "(go_to_location agent1 location32 location6 stoveburner1)", "(validate_pick_two_and_place_in_receptacle butterknife2 butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle spatula1 spatulatype drawer3 drawertype)", "(validate_pick_two_and_place_in_receptacle potato2 potato1 potatotype fridge1 fridgetype)", "(go_to_location agent1 location32 location22 stoveburner4)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190906_191445_723170)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location32 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 apple3 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 butterknife2 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 creditcard3 cup cup1 cup2 cup3 curtains desklamp dishsponge dishsponge1 dishsponge2 dishsponge3 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate2 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 soapbottle3 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 statue2 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 tomato2 tomato3 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location32) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable apple3) (cleanable bowl1) (cleanable butterknife1) (cleanable butterknife2) (cleanable cup1) (cleanable cup2) (cleanable cup3) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable dishsponge3) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (cleanable tomato2) (cleanable tomato3) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable apple3) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable cup3) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (coolable tomato2) (coolable tomato3) (handempty agent1) (heatable apple1) (heatable apple2) (heatable apple3) (heatable bread1) (heatable cup1) (heatable cup2) (heatable cup3) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (heatable tomato2) (heatable tomato3) (inreceptacle apple1 countertop1) (inreceptacle apple2 countertop1) (inreceptacle apple3 countertop1) (inreceptacle bowl1 shelf2) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle butterknife2 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 shelf2) (inreceptacle creditcard3 shelf3) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle cup3 sinkbasin1) (inreceptacle dishsponge1 drawer2) (inreceptacle dishsponge2 cabinet5) (inreceptacle dishsponge2 plate1) (inreceptacle dishsponge3 countertop1) (inreceptacle egg1 fridge1) (inreceptacle egg2 sinkbasin1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 garbagecan1) (inreceptacle glassbottle2 shelf2) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle mug1 microwave1) (inreceptacle mug2 shelf2) (inreceptacle pan1 stoveburner1) (inreceptacle pan1 stoveburner3) (inreceptacle papertowelroll1 garbagecan1) (inreceptacle peppershaker1 cabinet1) (inreceptacle peppershaker2 countertop3) (inreceptacle plate1 cabinet5) (inreceptacle plate2 cabinet5) (inreceptacle pot1 stoveburner2) (inreceptacle pot1 stoveburner4) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle potato3 sinkbasin1) (inreceptacle saltshaker1 shelf3) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 garbagecan1) (inreceptacle soapbottle3 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 countertop1) (inreceptacle spatula3 countertop3) (inreceptacle spoon1 drawer2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 countertop1) (inreceptacle statue2 countertop3) (inreceptacle tomato1 fridge1) (inreceptacle tomato2 fridge1) (inreceptacle tomato3 sinkbasin1) (inreceptacle vase1 cabinet6) (inreceptacle vase2 shelf1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location3) (objectatlocation apple2 location3) (objectatlocation apple3 location3) (objectatlocation bowl1 location30) (objectatlocation bread1 location3) (objectatlocation butterknife1 location4) (objectatlocation butterknife2 location4) (objectatlocation cellphone1 location3) (objectatlocation cellphone2 location21) (objectatlocation cellphone3 location4) (objectatlocation chair1 location24) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation creditcard2 location30) (objectatlocation creditcard3 location27) (objectatlocation cup1 location11) (objectatlocation cup2 location11) (objectatlocation cup3 location7) (objectatlocation dishsponge1 location16) (objectatlocation dishsponge2 location2) (objectatlocation dishsponge3 location3) (objectatlocation egg1 location11) (objectatlocation egg2 location7) (objectatlocation fork1 location7) (objectatlocation fork2 location4) (objectatlocation glassbottle1 location9) (objectatlocation glassbottle2 location30) (objectatlocation houseplant1 location4) (objectatlocation knife1 location4) (objectatlocation lettuce1 location11) (objectatlocation lightswitch1 location26) (objectatlocation mug1 location25) (objectatlocation mug2 location30) (objectatlocation pan1 location6) (objectatlocation papertowelroll1 location9) (objectatlocation peppershaker1 location20) (objectatlocation peppershaker2 location4) (objectatlocation plate1 location14) (objectatlocation plate2 location14) (objectatlocation pot1 location22) (objectatlocation potato1 location11) (objectatlocation potato2 location11) (objectatlocation potato3 location7) (objectatlocation saltshaker1 location27) (objectatlocation sink1 location7) (objectatlocation soapbottle1 location5) (objectatlocation soapbottle2 location9) (objectatlocation soapbottle3 location4) (objectatlocation spatula1 location28) (objectatlocation spatula2 location3) (objectatlocation spatula3 location4) (objectatlocation spoon1 location16) (objectatlocation spoon2 location4) (objectatlocation statue1 location3) (objectatlocation statue2 location4) (objectatlocation stoveknob1 location19) (objectatlocation stoveknob2 location13) (objectatlocation stoveknob3 location13) (objectatlocation stoveknob4 location8) (objectatlocation tomato1 location11) (objectatlocation tomato2 location11) (objectatlocation tomato3 location7) (objectatlocation vase1 location5) (objectatlocation vase2 location18) (objectatlocation window1 location31) (objectatlocation window2 location29) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype apple3 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype butterknife2 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype creditcard3 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype cup3 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype dishsponge3 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype soapbottle3 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype statue2 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype tomato2 tomatotype) (objecttype tomato3 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable apple3) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable butterknife2) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable creditcard3) (pickupable cup1) (pickupable cup2) (pickupable cup3) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable dishsponge3) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable knife1) (pickupable lettuce1) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable plate2) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable soapbottle3) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable statue2) (pickupable tomato1) (pickupable tomato2) (pickupable tomato3) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location20) (receptacleatlocation cabinet2 location23) (receptacleatlocation cabinet3 location17) (receptacleatlocation cabinet4 location15) (receptacleatlocation cabinet5 location14) (receptacleatlocation cabinet6 location5) (receptacleatlocation coffeemachine1 location12) (receptacleatlocation countertop1 location3) (receptacleatlocation countertop2 location12) (receptacleatlocation countertop3 location4) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location16) (receptacleatlocation drawer3 location28) (receptacleatlocation fridge1 location11) (receptacleatlocation garbagecan1 location9) (receptacleatlocation microwave1 location25) (receptacleatlocation shelf1 location18) (receptacleatlocation shelf2 location30) (receptacleatlocation shelf3 location27) (receptacleatlocation sinkbasin1 location7) (receptacleatlocation stoveburner1 location6) (receptacleatlocation stoveburner2 location22) (receptacleatlocation stoveburner3 location6) (receptacleatlocation stoveburner4 location22) (receptacleatlocation toaster1 location10) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable apple3) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1) (sliceable tomato2) (sliceable tomato3))\n    (:goal (validatepickandplace saltshakertype cabinettype))\n)"}
{"id": 4842894206362840644, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. sinkbasin1 is at location16. cabinet3 is at location8. cabinet4 is at location15. toiletpaperhanger1 is at location10. countertop1 is at location3. garbagecan1 is at location2. handtowelholder2 is at location17. handtowelholder1 is at location18. cabinet1 is at location4. toilet1 is at location7. towelholder1 is at location5. cabinet2 is at location11. sinkbasin2 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. handtowel1 is at location18. soapbar1 and toiletpaper1 are at location7. spraybottle1 and soapbottle2 are at location4. showerdoor1 is at location1. showerglass1 and towel1 are at location5. handtowel2 is at location17. sink1 is at location12. sink2 is at location14. soapbottle1 and spraybottle2 are at location15. candle1 and cloth1 are at location3. lightswitch1 is at location13. mirror1 is at location9. cloth2 is at location11. spraybottle3 is at location2. toiletpaper2 is at location8. agent agent1 is at location location6. The objects are in/on receptacle as follows. candle1 and cloth1 are on countertop1. spraybottle1 and soapbottle2 are in cabinet1. toiletpaper1 and soapbar1 are in toilet1. spraybottle2 and soapbottle1 are in cabinet4. spraybottle3 is in garbagecan1. towel1 is on towelholder1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. toiletpaper2 is in cabinet3. handtowel2 is on handtowelholder2. cabinet3, cabinet4, cabinet2, and cabinet1 are closed. soapbar2 is clean. Nothing has been validated. agent1 is holding object soapbar2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a that is at location ?l opens receptacle ?r, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o with a receptacle ?r that is in location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - validate that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(go_to_location agent1 location6 location10 toiletpaperhanger1)", "(validate_pick_and_place_in_receptacle toiletpaper1 toiletpapertype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle spraybottle1 spraybottletype cabinet1 cabinettype)", "(go_to_location agent1 location6 location5 towelholder1)", "(validate_pick_and_place_in_receptacle toiletpaper2 toiletpapertype cabinet3 cabinettype)", "(put_object_on_not_openable_receptacle agent1 location6 soapbar2 sinkbasin2 soapbartype sinkbasintype)", "(validate_pick_and_place_in_receptacle handtowel2 handtoweltype handtowelholder2 handtowelholdertype)", "(go_to_location agent1 location6 location16 sinkbasin1)", "(go_to_location agent1 location6 location11 cabinet2)", "(validate_pick_and_place_in_receptacle spraybottle2 spraybottletype cabinet4 cabinettype)", "(go_to_location agent1 location6 location3 countertop1)", "(validate_pick_and_place_in_receptacle candle1 candletype countertop1 countertoptype)", "(go_to_location agent1 location6 location8 cabinet3)", "(validate_pick_and_place_in_receptacle soapbar1 soapbartype toilet1 toilettype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet4 cabinettype)", "(validate_pick_and_place_in_receptacle handtowel1 handtoweltype handtowelholder1 handtowelholdertype)", "(go_to_location agent1 location6 location17 handtowelholder2)", "(go_to_location agent1 location6 location2 garbagecan1)", "(go_to_location agent1 location6 location4 cabinet1)", "(go_to_location agent1 location6 location15 cabinet4)", "(validate_pick_and_place_in_receptacle cloth1 clothtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle towel1 toweltype towelholder1 towelholdertype)", "(go_to_location agent1 location6 location18 handtowelholder1)", "(clean_object agent1 location6 sinkbasin2 soapbar2)", "(validate_pick_and_place_in_receptacle spraybottle3 spraybottletype garbagecan1 garbagecantype)", "(go_to_location agent1 location6 location7 toilet1)", "(validate_pick_and_place_in_receptacle cloth2 clothtype cabinet2 cabinettype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype cabinet1 cabinettype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_214946_567644)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location3 location4 location5 location6 location7 location8 location9 - location alarmclock apple baseballbat basketball bathtub blinds book boots bowl box bread butterknife candle candle1 cd cellphone chair cloth cloth1 cloth2 creditcard cup curtains desklamp dishsponge egg faucet1 faucet2 faucet3 floorlamp footstool fork glassbottle handtowel handtowel1 handtowel2 houseplant kettle keychain knife ladle laptop laundryhamperlid lettuce lightswitch lightswitch1 mirror mirror1 mug newspaper painting pan papertowel papertowelroll pen pencil peppershaker pillow plate plunger plunger1 poster pot potato remotecontrol saltshaker scrubbrush scrubbrush1 showerdoor showerdoor1 showerglass showerglass1 sink sink1 sink2 soapbar soapbar1 soapbar2 soapbottle soapbottle1 soapbottle2 spatula spoon spraybottle spraybottle1 spraybottle2 spraybottle3 statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaper1 toiletpaper2 toiletpaperroll tomato towel towel1 vase watch wateringcan window winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 countertop1 garbagecan1 handtowelholder1 handtowelholder2 sinkbasin1 sinkbasin2 toilet1 toiletpaperhanger1 towelholder1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location6) (cancontain cabinettype candletype) (cancontain cabinettype clothtype) (cancontain cabinettype handtoweltype) (cancontain cabinettype plungertype) (cancontain cabinettype soapbartype) (cancontain cabinettype soapbottletype) (cancontain cabinettype spraybottletype) (cancontain cabinettype toiletpapertype) (cancontain countertoptype candletype) (cancontain countertoptype clothtype) (cancontain countertoptype handtoweltype) (cancontain countertoptype soapbartype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spraybottletype) (cancontain countertoptype toiletpapertype) (cancontain garbagecantype clothtype) (cancontain garbagecantype handtoweltype) (cancontain garbagecantype soapbartype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype spraybottletype) (cancontain garbagecantype toiletpapertype) (cancontain handtowelholdertype handtoweltype) (cancontain sinkbasintype clothtype) (cancontain sinkbasintype handtoweltype) (cancontain sinkbasintype soapbartype) (cancontain toiletpaperhangertype toiletpapertype) (cancontain toilettype candletype) (cancontain toilettype clothtype) (cancontain toilettype handtoweltype) (cancontain toilettype soapbartype) (cancontain toilettype soapbottletype) (cancontain toilettype spraybottletype) (cancontain toilettype toiletpapertype) (cancontain towelholdertype toweltype) (cleanable cloth1) (cleanable cloth2) (cleanable soapbar1) (cleanable soapbar2) (closed cabinet1) (closed cabinet2) (closed cabinet3) (closed cabinet4) (holds agent1 soapbar2) (inreceptacle candle1 countertop1) (inreceptacle cloth1 countertop1) (inreceptacle cloth2 cabinet2) (inreceptacle handtowel1 handtowelholder1) (inreceptacle handtowel2 handtowelholder2) (inreceptacle soapbar1 toilet1) (inreceptacle soapbottle1 cabinet4) (inreceptacle soapbottle2 cabinet1) (inreceptacle spraybottle1 cabinet1) (inreceptacle spraybottle2 cabinet4) (inreceptacle spraybottle3 garbagecan1) (inreceptacle toiletpaper1 toilet1) (inreceptacle toiletpaper2 cabinet3) (inreceptacle towel1 towelholder1) (isclean soapbar2) (notopenable countertop1) (notopenable garbagecan1) (notopenable handtowelholder1) (notopenable handtowelholder2) (notopenable sinkbasin1) (notopenable sinkbasin2) (notopenable toilet1) (notopenable toiletpaperhanger1) (notopenable towelholder1) (notvalidated) (objectatlocation candle1 location3) (objectatlocation cloth1 location3) (objectatlocation cloth2 location11) (objectatlocation handtowel1 location18) (objectatlocation handtowel2 location17) (objectatlocation lightswitch1 location13) (objectatlocation mirror1 location9) (objectatlocation plunger1 location10) (objectatlocation scrubbrush1 location10) (objectatlocation showerdoor1 location1) (objectatlocation showerglass1 location5) (objectatlocation sink1 location12) (objectatlocation sink2 location14) (objectatlocation soapbar1 location7) (objectatlocation soapbottle1 location15) (objectatlocation soapbottle2 location4) (objectatlocation spraybottle1 location4) (objectatlocation spraybottle2 location15) (objectatlocation spraybottle3 location2) (objectatlocation toiletpaper1 location7) (objectatlocation toiletpaper2 location8) (objectatlocation towel1 location5) (objecttype candle1 candletype) (objecttype cloth1 clothtype) (objecttype cloth2 clothtype) (objecttype handtowel1 handtoweltype) (objecttype handtowel2 handtoweltype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype plunger1 plungertype) (objecttype scrubbrush1 scrubbrushtype) (objecttype showerdoor1 showerdoortype) (objecttype showerglass1 showerglasstype) (objecttype sink1 sinktype) (objecttype sink2 sinktype) (objecttype soapbar1 soapbartype) (objecttype soapbar2 soapbartype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spraybottle1 spraybottletype) (objecttype spraybottle2 spraybottletype) (objecttype spraybottle3 spraybottletype) (objecttype toiletpaper1 toiletpapertype) (objecttype toiletpaper2 toiletpapertype) (objecttype towel1 toweltype) (openable cabinet1) (openable cabinet2) (openable cabinet3) (openable cabinet4) (pickupable candle1) (pickupable cloth1) (pickupable cloth2) (pickupable handtowel1) (pickupable handtowel2) (pickupable plunger1) (pickupable scrubbrush1) (pickupable soapbar1) (pickupable soapbar2) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spraybottle1) (pickupable spraybottle2) (pickupable spraybottle3) (pickupable toiletpaper1) (pickupable toiletpaper2) (pickupable towel1) (receptacleatlocation cabinet1 location4) (receptacleatlocation cabinet2 location11) (receptacleatlocation cabinet3 location8) (receptacleatlocation cabinet4 location15) (receptacleatlocation countertop1 location3) (receptacleatlocation garbagecan1 location2) (receptacleatlocation handtowelholder1 location18) (receptacleatlocation handtowelholder2 location17) (receptacleatlocation sinkbasin1 location16) (receptacleatlocation sinkbasin2 location6) (receptacleatlocation toilet1 location7) (receptacleatlocation toiletpaperhanger1 location10) (receptacleatlocation towelholder1 location5) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype countertop1 countertoptype) (receptacletype garbagecan1 garbagecantype) (receptacletype handtowelholder1 handtowelholdertype) (receptacletype handtowelholder2 handtowelholdertype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype sinkbasin2 sinkbasintype) (receptacletype toilet1 toilettype) (receptacletype toiletpaperhanger1 toiletpaperhangertype) (receptacletype towelholder1 towelholdertype))\n    (:goal (validatecleanandplace soapbartype cabinettype))\n)"}
{"id": 7615299986125888127, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. fridge1 is at location10. cabinet5 is at location13. sinkbasin1 is at location6. countertop2 and coffeemachine1 are at location11. shelf1 is at location17. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. drawer2 is at location15. cabinet4 is at location14. countertop1 is at location2. shelf2 is at location29. stoveburner1 and stoveburner3 are at location5. garbagecan1 is at location8. countertop3 is at location3. shelf3 is at location26. drawer3 is at location27. drawer1 is at location20. cabinet1 is at location19. cabinet2 is at location22. toaster1 is at location9. cabinet3 is at location16. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. cellphone1, knife1, lettuce1, lettuce2, peppershaker1, apple1, plate1, and cellphone2 are at location2. fork1 and dishsponge1 are at location27. potato1, tomato1, mug1, egg1, plate2, bowl1, and egg2 are at location10. glassbottle2, spatula3, potato2, sink1, and spatula2 are at location6. houseplant1, creditcard1, knife2, papertowelroll1, spoon2, mug2, spatula1, glassbottle1, cellphone3, bread1, plate3, and butterknife1 are at location3. cup1 is at location24. bowl2 is at location29. stoveknob3 and stoveknob2 are at location12. potato3, apple2, and glassbottle3 are at location8. stoveknob4 is at location7. window1 is at location30. vase1 and dishsponge2 are at location13. chair2 is at location1. pan1 and spoon1 are at location11. lightswitch1 is at location25. pot1 is at location5. peppershaker3 is at location16. chair1 is at location23. stoveknob1 is at location18. soapbottle1 is at location4. window2 is at location28. soapbottle2 and statue1 are at location26. peppershaker2 is at location20. saltshaker1 is at location15. vase2 is at location17. agent agent1 is at location location11. The objects are in/on receptacle as follows. cellphone3, spatula1, knife2, bread1, houseplant1, papertowelroll1, glassbottle1, mug2, butterknife1, creditcard1, plate3, and spoon2 are on countertop3. soapbottle1 is in cabinet6. glassbottle2, spatula3, potato2, and spatula2 are in sinkbasin1. apple1, cellphone2, lettuce1, cellphone1, knife1, peppershaker1, lettuce2, and plate1 are on countertop1. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner2. soapbottle2 and statue1 are on shelf3. tomato1, potato1, egg2, plate2, mug1, bowl1, and egg1 are in fridge1. pan1 is on stoveburner4. vase2 is on shelf1. pot1 is on stoveburner3. spoon1 and pan1 are on countertop2. peppershaker2 is in drawer1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. bowl2 is on shelf2. pot1 is on stoveburner1. cup1 is in microwave1. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cellphone2 is on plate1. drawer3, drawer1, drawer2, cabinet5, cabinet1, microwave1, fridge1, and cabinet2 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r while at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans an object ?o in a sink ?r that is in location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o with a receptacle ?r that is in location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is colded down and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - validate that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(validate_pick_and_place_in_receptacle spoon1 spoontype countertop2 countertoptype)", "(validate_pick_and_place_in_receptacle spatula3 spatulatype sinkbasin1 sinkbasintype)", "(go_to_location agent1 location11 location17 shelf1)", "(validate_pick_and_place_in_receptacle mug1 mugtype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle spatula1 spatulatype countertop3 countertoptype)", "(go_to_location agent1 location11 location24 microwave1)", "(go_to_location agent1 location11 location20 drawer1)", "(validate_pick_two_and_place_in_receptacle egg1 egg2 eggtype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle potato1 potatotype fridge1 fridgetype)", "(go_to_location agent1 location11 location22 cabinet2)", "(validate_pick_and_place_in_receptacle cellphone1 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle bowl1 bowltype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle cellphone2 cellphone1 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle dishsponge1 dishspongetype drawer3 drawertype)", "(validate_pick_two_and_place_in_receptacle lettuce2 lettuce1 lettucetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle peppershaker1 peppershakertype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle peppershaker3 peppershakertype cabinet3 cabinettype)", "(validate_pick_and_place_in_receptacle houseplant1 houseplanttype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle plate1 platetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle potato2 potatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner3 stoveburnertype)", "(validate_pick_and_place_in_receptacle statue1 statuetype shelf3 shelftype)", "(validate_pick_and_place_in_receptacle creditcard1 creditcardtype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle egg2 egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location11 location4 cabinet6)", "(validate_pick_and_place_in_receptacle apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype shelf3 shelftype)", "(go_to_location agent1 location11 location6 sinkbasin1)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner1 stoveburnertype)", "(validate_pick_and_place_in_receptacle dishsponge2 dishspongetype cabinet5 cabinettype)", "(go_to_location agent1 location11 location5 stoveburner1)", "(validate_pick_and_place_in_receptacle plate2 platetype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle papertowelroll1 papertowelrolltype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle spatula2 spatulatype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle glassbottle2 glassbottletype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle fork1 forktype drawer3 drawertype)", "(validate_pick_and_place_in_receptacle cellphone3 cellphonetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle tomato1 tomatotype fridge1 fridgetype)", "(go_to_location agent1 location11 location19 cabinet1)", "(validate_pick_and_place_in_receptacle spoon2 spoontype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet6 cabinettype)", "(go_to_location agent1 location11 location21 stoveburner4)", "(validate_cool_and_place_in_receptacle mug1 mugtype fridge1 fridgetype)", "(go_to_location agent1 location11 location26 shelf3)", "(validate_pick_and_place_in_receptacle glassbottle3 glassbottletype garbagecan1 garbagecantype)", "(go_to_location agent1 location11 location5 stoveburner3)", "(validate_pick_and_place_in_receptacle cellphone2 cellphonetype countertop1 countertoptype)", "(pickup_object_from_not_openable_receptacle agent1 location11 spoon1 countertop2)", "(validate_pick_and_place_in_receptacle bread1 breadtype countertop3 countertoptype)", "(go_to_location agent1 location11 location13 cabinet5)", "(validate_pick_and_place_in_receptacle lettuce2 lettucetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype countertop2 countertoptype)", "(validate_pick_and_place_in_receptacle potato3 potatotype garbagecan1 garbagecantype)", "(go_to_location agent1 location11 location8 garbagecan1)", "(validate_pick_and_place_in_receptacle saltshaker1 saltshakertype drawer2 drawertype)", "(go_to_location agent1 location11 location10 fridge1)", "(go_to_location agent1 location11 location21 stoveburner2)", "(go_to_location agent1 location11 location29 shelf2)", "(validate_pick_and_place_in_receptacle bowl2 bowltype shelf2 shelftype)", "(validate_pick_two_and_place_in_receptacle cellphone1 cellphone2 cellphonetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle spatula3 spatula2 spatulatype sinkbasin1 sinkbasintype)", "(pickup_object_from_not_openable_receptacle agent1 location11 pan1 countertop2)", "(validate_pick_and_place_in_receptacle apple2 appletype garbagecan1 garbagecantype)", "(go_to_location agent1 location11 location14 cabinet4)", "(validate_pick_and_place_in_receptacle glassbottle1 glassbottletype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg2 eggtype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle lettuce1 lettucetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle mug2 mugtype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle plate3 platetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner4 stoveburnertype)", "(validate_pick_and_place_in_receptacle vase2 vasetype shelf1 shelftype)", "(validate_pick_and_place_in_receptacle cup1 cuptype microwave1 microwavetype)", "(go_to_location agent1 location11 location15 drawer2)", "(go_to_location agent1 location11 location9 toaster1)", "(go_to_location agent1 location11 location27 drawer3)", "(go_to_location agent1 location11 location2 countertop1)", "(validate_pick_and_place_in_receptacle knife2 knifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location11 location16 cabinet3)", "(validate_pick_two_and_place_in_receptacle lettuce1 lettuce2 lettucetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle spatula2 spatula3 spatulatype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle peppershaker2 peppershakertype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle knife1 knifetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner2 stoveburnertype)", "(go_to_location agent1 location11 location3 countertop3)", "(validate_pick_and_place_in_receptacle vase1 vasetype cabinet5 cabinettype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190907_171933_349922)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 bowl2 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup cup1 curtains desklamp dishsponge dishsponge1 dishsponge2 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 knife2 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 peppershaker3 pillow plate plate2 plate3 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location11) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable bowl2) (cleanable butterknife1) (cleanable cup1) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable knife1) (cleanable knife2) (cleanable lettuce1) (cleanable lettuce2) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable plate3) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bowl2) (coolable bread1) (coolable cup1) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable plate3) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (handempty agent1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable plate3) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (inreceptacle apple1 countertop1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 fridge1) (inreceptacle bowl2 shelf2) (inreceptacle bread1 countertop3) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 countertop1) (inreceptacle cellphone2 plate1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop3) (inreceptacle cup1 microwave1) (inreceptacle dishsponge1 drawer3) (inreceptacle dishsponge2 cabinet5) (inreceptacle egg1 fridge1) (inreceptacle egg2 fridge1) (inreceptacle fork1 drawer3) (inreceptacle glassbottle1 countertop3) (inreceptacle glassbottle2 sinkbasin1) (inreceptacle glassbottle3 garbagecan1) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop1) (inreceptacle knife2 countertop3) (inreceptacle lettuce1 countertop1) (inreceptacle lettuce2 countertop1) (inreceptacle mug1 fridge1) (inreceptacle mug2 countertop3) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 countertop3) (inreceptacle peppershaker1 countertop1) (inreceptacle peppershaker2 drawer1) (inreceptacle peppershaker3 cabinet3) (inreceptacle plate1 countertop1) (inreceptacle plate2 fridge1) (inreceptacle plate3 countertop3) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 sinkbasin1) (inreceptacle potato3 garbagecan1) (inreceptacle saltshaker1 drawer2) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 shelf3) (inreceptacle spatula1 countertop3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spatula3 sinkbasin1) (inreceptacle spoon1 countertop2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 shelf3) (inreceptacle tomato1 fridge1) (inreceptacle vase1 cabinet5) (inreceptacle vase2 shelf1) (iscool mug1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location2) (objectatlocation apple2 location8) (objectatlocation bowl1 location10) (objectatlocation bowl2 location29) (objectatlocation bread1 location3) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location2) (objectatlocation cellphone2 location2) (objectatlocation cellphone3 location3) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation cup1 location24) (objectatlocation dishsponge1 location27) (objectatlocation dishsponge2 location13) (objectatlocation egg1 location10) (objectatlocation egg2 location10) (objectatlocation fork1 location27) (objectatlocation glassbottle1 location3) (objectatlocation glassbottle2 location6) (objectatlocation glassbottle3 location8) (objectatlocation houseplant1 location3) (objectatlocation knife1 location2) (objectatlocation knife2 location3) (objectatlocation lettuce1 location2) (objectatlocation lettuce2 location2) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location10) (objectatlocation mug2 location3) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location3) (objectatlocation peppershaker1 location2) (objectatlocation peppershaker2 location20) (objectatlocation peppershaker3 location16) (objectatlocation plate1 location2) (objectatlocation plate2 location10) (objectatlocation plate3 location3) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location6) (objectatlocation potato3 location8) (objectatlocation saltshaker1 location15) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location4) (objectatlocation soapbottle2 location26) (objectatlocation spatula1 location3) (objectatlocation spatula2 location6) (objectatlocation spatula3 location6) (objectatlocation spoon1 location11) (objectatlocation spoon2 location3) (objectatlocation statue1 location26) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location10) (objectatlocation vase1 location13) (objectatlocation vase2 location17) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype cup1 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype knife2 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype peppershaker3 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype plate3 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bowl2) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable cup1) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable knife2) (pickupable lettuce1) (pickupable lettuce2) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable peppershaker3) (pickupable plate1) (pickupable plate2) (pickupable plate3) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1))\n    (:goal (validateheatandplace mugtype coffeemachinetype))\n)"}
{"id": 2401154628213944267, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. fridge1 is at location10. cabinet5 is at location13. sinkbasin1 is at location6. countertop2 and coffeemachine1 are at location11. shelf1 is at location17. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. drawer2 is at location15. cabinet4 is at location14. countertop1 is at location2. shelf2 is at location29. stoveburner1 and stoveburner3 are at location5. garbagecan1 is at location8. countertop3 is at location3. shelf3 is at location26. drawer3 is at location27. drawer1 is at location20. cabinet1 is at location19. cabinet2 is at location22. toaster1 is at location9. cabinet3 is at location16. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. cellphone1, knife1, lettuce1, lettuce2, peppershaker1, apple1, plate1, and cellphone2 are at location2. fork1 and dishsponge1 are at location27. potato1, tomato1, mug1, egg1, plate2, bowl1, and egg2 are at location10. glassbottle2, spatula3, potato2, sink1, and spatula2 are at location6. houseplant1, creditcard1, knife2, papertowelroll1, spoon2, mug2, spatula1, glassbottle1, cellphone3, bread1, plate3, and butterknife1 are at location3. cup1 is at location24. bowl2 is at location29. stoveknob3 and stoveknob2 are at location12. potato3, apple2, and glassbottle3 are at location8. stoveknob4 is at location7. window1 is at location30. vase1 and dishsponge2 are at location13. chair2 is at location1. pan1 and spoon1 are at location11. lightswitch1 is at location25. pot1 is at location5. peppershaker3 is at location16. chair1 is at location23. stoveknob1 is at location18. soapbottle1 is at location4. window2 is at location28. soapbottle2 and statue1 are at location26. peppershaker2 is at location20. saltshaker1 is at location15. vase2 is at location17. agent agent1 is at location location21. The objects are in/on receptacle as follows. cellphone3, spatula1, knife2, bread1, houseplant1, papertowelroll1, glassbottle1, mug2, butterknife1, creditcard1, plate3, and spoon2 are on countertop3. soapbottle1 is in cabinet6. glassbottle2, spatula3, potato2, and spatula2 are in sinkbasin1. apple1, cellphone2, lettuce1, cellphone1, knife1, peppershaker1, lettuce2, and plate1 are on countertop1. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner2. soapbottle2 and statue1 are on shelf3. tomato1, potato1, egg2, plate2, mug1, bowl1, and egg1 are in fridge1. pan1 is on stoveburner4. vase2 is on shelf1. pot1 is on stoveburner3. spoon1 and pan1 are on countertop2. peppershaker2 is in drawer1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. bowl2 is on shelf2. pot1 is on stoveburner1. cup1 is in microwave1. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cellphone2 is on plate1. drawer3, drawer1, drawer2, cabinet5, cabinet1, microwave1, fridge1, and cabinet2 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates to the receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a that is at location ?l opens receptacle ?r, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot on a not openable receptacle ?r of type ?rt while at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans an object ?o in a sink ?r that is in location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is colded down and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(validate_pick_and_place_in_receptacle spoon1 spoontype countertop2 countertoptype)", "(validate_pick_and_place_in_receptacle spatula3 spatulatype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle mug1 mugtype fridge1 fridgetype)", "(go_to_location agent1 location21 location20 drawer1)", "(validate_pick_and_place_in_receptacle spatula1 spatulatype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle egg1 egg2 eggtype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle cellphone1 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle bowl1 bowltype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle cellphone2 cellphone1 cellphonetype countertop1 countertoptype)", "(pickup_object_from_not_openable_receptacle agent1 location21 pan1 stoveburner2)", "(validate_pick_and_place_in_receptacle dishsponge1 dishspongetype drawer3 drawertype)", "(validate_pick_two_and_place_in_receptacle lettuce2 lettuce1 lettucetype countertop1 countertoptype)", "(go_to_location agent1 location21 location5 stoveburner1)", "(validate_pick_and_place_in_receptacle peppershaker1 peppershakertype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle peppershaker3 peppershakertype cabinet3 cabinettype)", "(validate_pick_and_place_in_receptacle houseplant1 houseplanttype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle plate1 platetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle potato2 potatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner3 stoveburnertype)", "(go_to_location agent1 location21 location17 shelf1)", "(go_to_location agent1 location21 location13 cabinet5)", "(validate_pick_and_place_in_receptacle statue1 statuetype shelf3 shelftype)", "(validate_pick_and_place_in_receptacle creditcard1 creditcardtype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle egg2 egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location21 location26 shelf3)", "(go_to_location agent1 location21 location2 countertop1)", "(validate_pick_and_place_in_receptacle apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype shelf3 shelftype)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner1 stoveburnertype)", "(validate_pick_and_place_in_receptacle dishsponge2 dishspongetype cabinet5 cabinettype)", "(go_to_location agent1 location21 location29 shelf2)", "(validate_pick_and_place_in_receptacle plate2 platetype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle papertowelroll1 papertowelrolltype countertop3 countertoptype)", "(go_to_location agent1 location21 location8 garbagecan1)", "(validate_pick_and_place_in_receptacle glassbottle2 glassbottletype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle fork1 forktype drawer3 drawertype)", "(validate_pick_and_place_in_receptacle cellphone3 cellphonetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle tomato1 tomatotype fridge1 fridgetype)", "(go_to_location agent1 location21 location15 drawer2)", "(go_to_location agent1 location21 location27 drawer3)", "(validate_pick_and_place_in_receptacle spoon2 spoontype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet6 cabinettype)", "(validate_cool_and_place_in_receptacle mug1 mugtype fridge1 fridgetype)", "(go_to_location agent1 location21 location11 coffeemachine1)", "(validate_pick_and_place_in_receptacle glassbottle3 glassbottletype garbagecan1 garbagecantype)", "(go_to_location agent1 location21 location3 countertop3)", "(go_to_location agent1 location21 location14 cabinet4)", "(validate_pick_and_place_in_receptacle cellphone2 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle bread1 breadtype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle lettuce2 lettucetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife1 butterknifetype countertop3 countertoptype)", "(go_to_location agent1 location21 location11 countertop2)", "(validate_pick_and_place_in_receptacle pan1 pantype countertop2 countertoptype)", "(validate_pick_and_place_in_receptacle potato3 potatotype garbagecan1 garbagecantype)", "(go_to_location agent1 location21 location16 cabinet3)", "(validate_pick_and_place_in_receptacle saltshaker1 saltshakertype drawer2 drawertype)", "(go_to_location agent1 location21 location22 cabinet2)", "(go_to_location agent1 location21 location24 microwave1)", "(validate_pick_and_place_in_receptacle bowl2 bowltype shelf2 shelftype)", "(validate_pick_two_and_place_in_receptacle cellphone1 cellphone2 cellphonetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle spatula3 spatula2 spatulatype sinkbasin1 sinkbasintype)", "(go_to_location agent1 location21 location19 cabinet1)", "(validate_pick_and_place_in_receptacle apple2 appletype garbagecan1 garbagecantype)", "(go_to_location agent1 location21 location9 toaster1)", "(validate_pick_and_place_in_receptacle glassbottle1 glassbottletype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg2 eggtype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle lettuce1 lettucetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle mug2 mugtype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle plate3 platetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner4 stoveburnertype)", "(validate_pick_and_place_in_receptacle vase2 vasetype shelf1 shelftype)", "(validate_pick_and_place_in_receptacle cup1 cuptype microwave1 microwavetype)", "(validate_pick_and_place_in_receptacle knife2 knifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location21 location4 cabinet6)", "(pickup_object_from_not_openable_receptacle agent1 location21 pan1 stoveburner4)", "(validate_pick_two_and_place_in_receptacle lettuce1 lettuce2 lettucetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle spatula2 spatula3 spatulatype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle peppershaker2 peppershakertype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle knife1 knifetype countertop1 countertoptype)", "(go_to_location agent1 location21 location5 stoveburner3)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner2 stoveburnertype)", "(go_to_location agent1 location21 location6 sinkbasin1)", "(go_to_location agent1 location21 location10 fridge1)", "(validate_pick_and_place_in_receptacle spatula2 spatulatype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle vase1 vasetype cabinet5 cabinettype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190907_171933_349922)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 bowl2 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup cup1 curtains desklamp dishsponge dishsponge1 dishsponge2 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 knife2 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 peppershaker3 pillow plate plate2 plate3 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location21) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable bowl2) (cleanable butterknife1) (cleanable cup1) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable knife1) (cleanable knife2) (cleanable lettuce1) (cleanable lettuce2) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable plate3) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bowl2) (coolable bread1) (coolable cup1) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable plate3) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (handempty agent1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable plate3) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (inreceptacle apple1 countertop1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 fridge1) (inreceptacle bowl2 shelf2) (inreceptacle bread1 countertop3) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 countertop1) (inreceptacle cellphone2 plate1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop3) (inreceptacle cup1 microwave1) (inreceptacle dishsponge1 drawer3) (inreceptacle dishsponge2 cabinet5) (inreceptacle egg1 fridge1) (inreceptacle egg2 fridge1) (inreceptacle fork1 drawer3) (inreceptacle glassbottle1 countertop3) (inreceptacle glassbottle2 sinkbasin1) (inreceptacle glassbottle3 garbagecan1) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop1) (inreceptacle knife2 countertop3) (inreceptacle lettuce1 countertop1) (inreceptacle lettuce2 countertop1) (inreceptacle mug1 fridge1) (inreceptacle mug2 countertop3) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 countertop3) (inreceptacle peppershaker1 countertop1) (inreceptacle peppershaker2 drawer1) (inreceptacle peppershaker3 cabinet3) (inreceptacle plate1 countertop1) (inreceptacle plate2 fridge1) (inreceptacle plate3 countertop3) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 sinkbasin1) (inreceptacle potato3 garbagecan1) (inreceptacle saltshaker1 drawer2) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 shelf3) (inreceptacle spatula1 countertop3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spatula3 sinkbasin1) (inreceptacle spoon1 countertop2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 shelf3) (inreceptacle tomato1 fridge1) (inreceptacle vase1 cabinet5) (inreceptacle vase2 shelf1) (iscool mug1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location2) (objectatlocation apple2 location8) (objectatlocation bowl1 location10) (objectatlocation bowl2 location29) (objectatlocation bread1 location3) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location2) (objectatlocation cellphone2 location2) (objectatlocation cellphone3 location3) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation cup1 location24) (objectatlocation dishsponge1 location27) (objectatlocation dishsponge2 location13) (objectatlocation egg1 location10) (objectatlocation egg2 location10) (objectatlocation fork1 location27) (objectatlocation glassbottle1 location3) (objectatlocation glassbottle2 location6) (objectatlocation glassbottle3 location8) (objectatlocation houseplant1 location3) (objectatlocation knife1 location2) (objectatlocation knife2 location3) (objectatlocation lettuce1 location2) (objectatlocation lettuce2 location2) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location10) (objectatlocation mug2 location3) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location3) (objectatlocation peppershaker1 location2) (objectatlocation peppershaker2 location20) (objectatlocation peppershaker3 location16) (objectatlocation plate1 location2) (objectatlocation plate2 location10) (objectatlocation plate3 location3) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location6) (objectatlocation potato3 location8) (objectatlocation saltshaker1 location15) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location4) (objectatlocation soapbottle2 location26) (objectatlocation spatula1 location3) (objectatlocation spatula2 location6) (objectatlocation spatula3 location6) (objectatlocation spoon1 location11) (objectatlocation spoon2 location3) (objectatlocation statue1 location26) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location10) (objectatlocation vase1 location13) (objectatlocation vase2 location17) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype cup1 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype knife2 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype peppershaker3 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype plate3 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bowl2) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable cup1) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable knife2) (pickupable lettuce1) (pickupable lettuce2) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable peppershaker3) (pickupable plate1) (pickupable plate2) (pickupable plate3) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1))\n    (:goal (validateheatandplace mugtype coffeemachinetype))\n)"}
{"id": -7103851462027105498, "group": "applicable_actions_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location9. toaster1 is at location10. shelf2 is at location30. stoveburner2 and stoveburner4 are at location22. cabinet6 is at location5. stoveburner1 and stoveburner3 are at location6. countertop2 and coffeemachine1 are at location12. shelf3 is at location27. cabinet5 is at location14. drawer1 is at location21. countertop3 is at location4. cabinet1 is at location20. cabinet2 is at location23. shelf1 is at location18. microwave1 is at location25. cabinet4 is at location15. countertop1 is at location3. drawer2 is at location16. sinkbasin1 is at location7. cabinet3 is at location17. drawer3 is at location28. fridge1 is at location11. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. vase2 is at location18. mug1 is at location25. apple3, apple1, creditcard1, dishsponge3, cellphone1, bread1, spatula2, apple2, and statue1 are at location3. egg1, cup1, potato2, potato1, cup2, lettuce1, tomato1, and tomato2 are at location11. butterknife2, butterknife1, spatula3, peppershaker2, knife1, statue2, soapbottle3, cellphone3, spoon2, fork2, and houseplant1 are at location4. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. stoveknob4 is at location8. stoveknob1 is at location19. creditcard3 and saltshaker1 are at location27. window1 is at location31. pot1 is at location22. bowl1, mug2, glassbottle2, and creditcard2 are at location30. fork1, egg2, tomato3, cup3, potato3, and sink1 are at location7. soapbottle2, papertowelroll1, and glassbottle1 are at location9. chair1 is at location24. chair2 is at location1. stoveknob3 and stoveknob2 are at location13. spatula1 is at location28. pan1 is at location6. plate2 and plate1 are at location14. cellphone2 is at location21. dishsponge2 is at location2. vase1 and soapbottle1 are at location5. peppershaker1 is at location20. agent agent1 is at location location16. The objects are in/on receptacle as follows. peppershaker1 is in cabinet1. cellphone3, houseplant1, butterknife1, butterknife2, fork2, soapbottle3, spatula3, knife1, statue2, peppershaker2, and spoon2 are on countertop3. soapbottle1 and vase1 are in cabinet6. glassbottle2, mug2, bowl1, and creditcard2 are on shelf2. apple1, creditcard1, statue1, spatula2, cellphone1, apple3, dishsponge3, bread1, and apple2 are on countertop1. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. lettuce1, tomato1, potato1, cup2, tomato2, cup1, egg1, and potato2 are in fridge1. pot1 is on stoveburner4. creditcard3 and saltshaker1 are on shelf3. cup3, fork1, potato3, tomato3, and egg2 are in sinkbasin1. plate2, dishsponge2, and plate1 are in cabinet5. pan1 is on stoveburner1. pot1 is on stoveburner2. cellphone2 is in drawer1. vase2 is on shelf1. dishsponge2 is on plate1. spatula1 is in drawer3. spoon1 and dishsponge1 are in drawer2. pan1 is on stoveburner3. mug1 is in microwave1. drawer3, drawer1, drawer2, cabinet5, cabinet1, microwave1, fridge1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates to the receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r while at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot in an openable receptacle ?r of type ?rt that is at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up a heatable object ?o with a receptacle ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a turns off object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - validate that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(validate_pick_and_place_in_receptacle creditcard3 creditcardtype shelf3 shelftype)", "(validate_pick_two_and_place_in_receptacle apple2 apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle3 soapbottletype countertop3 countertoptype)", "(go_to_location agent1 location16 location6 stoveburner1)", "(go_to_location agent1 location16 location12 coffeemachine1)", "(open_receptacle agent1 location16 drawer2)", "(validate_pick_and_place_in_receptacle dishsponge1 dishspongetype drawer2 drawertype)", "(validate_pick_and_place_in_receptacle potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle egg2 eggtype sinkbasin1 sinkbasintype)", "(go_to_location agent1 location16 location11 fridge1)", "(validate_pick_and_place_in_receptacle peppershaker2 peppershakertype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle apple3 appletype countertop1 countertoptype)", "(go_to_location agent1 location16 location9 garbagecan1)", "(validate_pick_and_place_in_receptacle cellphone1 cellphonetype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle tomato2 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle mug1 mugtype microwave1 microwavetype)", "(validate_pick_and_place_in_receptacle cup2 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle tomato3 tomatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle glassbottle1 glassbottletype garbagecan1 garbagecantype)", "(go_to_location agent1 location16 location28 drawer3)", "(validate_pick_and_place_in_receptacle peppershaker1 peppershakertype cabinet1 cabinettype)", "(go_to_location agent1 location16 location22 stoveburner4)", "(validate_pick_and_place_in_receptacle dishsponge3 dishspongetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle plate1 plate2 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle houseplant1 houseplanttype countertop3 countertoptype)", "(go_to_location agent1 location16 location3 countertop1)", "(validate_pick_two_and_place_in_receptacle potato1 potato2 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle apple1 appletype countertop1 countertoptype)", "(go_to_location agent1 location16 location5 cabinet6)", "(validate_pick_and_place_in_receptacle dishsponge2 dishspongetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle creditcard1 creditcardtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle vase1 vasetype cabinet6 cabinettype)", "(validate_pick_two_and_place_in_receptacle tomato1 tomato2 tomatotype fridge1 fridgetype)", "(go_to_location agent1 location16 location12 countertop2)", "(validate_pick_and_place_in_receptacle lettuce1 lettucetype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle papertowelroll1 papertowelrolltype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle cellphone3 cellphonetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle cup1 cuptype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle tomato1 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle glassbottle2 glassbottletype shelf2 shelftype)", "(validate_pick_two_and_place_in_receptacle apple3 apple1 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife2 butterknifetype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle apple2 apple3 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle cup3 cuptype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner4 stoveburnertype)", "(validate_pick_and_place_in_receptacle knife1 knifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner3 stoveburnertype)", "(validate_pick_and_place_in_receptacle spoon2 spoontype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle soapbottle1 soapbottletype cabinet6 cabinettype)", "(go_to_location agent1 location16 location6 stoveburner3)", "(go_to_location agent1 location16 location18 shelf1)", "(validate_pick_and_place_in_receptacle bread1 breadtype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle pan1 pantype stoveburner1 stoveburnertype)", "(go_to_location agent1 location16 location25 microwave1)", "(validate_pick_two_and_place_in_receptacle apple3 apple2 appletype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle apple1 apple3 appletype countertop1 countertoptype)", "(go_to_location agent1 location16 location14 cabinet5)", "(go_to_location agent1 location16 location7 sinkbasin1)", "(validate_pick_and_place_in_receptacle apple2 appletype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle cup2 cup1 cuptype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle butterknife1 butterknife2 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle saltshaker1 saltshakertype shelf3 shelftype)", "(go_to_location agent1 location16 location10 toaster1)", "(go_to_location agent1 location16 location17 cabinet3)", "(go_to_location agent1 location16 location4 countertop3)", "(validate_pick_and_place_in_receptacle pot1 pottype stoveburner2 stoveburnertype)", "(go_to_location agent1 location16 location15 cabinet4)", "(validate_pick_and_place_in_receptacle cellphone2 cellphonetype drawer1 drawertype)", "(validate_pick_and_place_in_receptacle fork1 forktype sinkbasin1 sinkbasintype)", "(validate_pick_two_and_place_in_receptacle cup1 cup2 cuptype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle apple1 apple2 appletype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle spoon1 spoontype drawer2 drawertype)", "(validate_pick_and_place_in_receptacle statue1 statuetype countertop1 countertoptype)", "(validate_pick_two_and_place_in_receptacle tomato2 tomato1 tomatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle mug2 mugtype shelf2 shelftype)", "(go_to_location agent1 location16 location20 cabinet1)", "(validate_pick_and_place_in_receptacle plate2 platetype cabinet5 cabinettype)", "(go_to_location agent1 location16 location22 stoveburner2)", "(go_to_location agent1 location16 location30 shelf2)", "(validate_pick_and_place_in_receptacle soapbottle2 soapbottletype garbagecan1 garbagecantype)", "(validate_pick_and_place_in_receptacle bowl1 bowltype shelf2 shelftype)", "(validate_pick_and_place_in_receptacle spatula3 spatulatype countertop3 countertoptype)", "(go_to_location agent1 location16 location27 shelf3)", "(validate_pick_and_place_in_receptacle potato3 potatotype sinkbasin1 sinkbasintype)", "(validate_pick_and_place_in_receptacle spatula2 spatulatype countertop1 countertoptype)", "(validate_pick_and_place_in_receptacle vase2 vasetype shelf1 shelftype)", "(validate_pick_and_place_in_receptacle plate1 platetype cabinet5 cabinettype)", "(validate_pick_and_place_in_receptacle fork2 forktype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle egg1 eggtype fridge1 fridgetype)", "(go_to_location agent1 location16 location23 cabinet2)", "(validate_pick_and_place_in_receptacle statue2 statuetype countertop3 countertoptype)", "(validate_pick_and_place_in_receptacle potato2 potatotype fridge1 fridgetype)", "(validate_pick_two_and_place_in_receptacle plate2 plate1 platetype cabinet5 cabinettype)", "(go_to_location agent1 location16 location21 drawer1)", "(validate_pick_and_place_in_receptacle creditcard2 creditcardtype shelf2 shelftype)", "(validate_pick_two_and_place_in_receptacle butterknife2 butterknife1 butterknifetype countertop3 countertoptype)", "(validate_pick_two_and_place_in_receptacle potato2 potato1 potatotype fridge1 fridgetype)", "(validate_pick_and_place_in_receptacle spatula1 spatulatype drawer3 drawertype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190906_191445_723170)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location32 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 apple3 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 butterknife2 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 creditcard3 cup cup1 cup2 cup3 curtains desklamp dishsponge dishsponge1 dishsponge2 dishsponge3 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate2 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 soapbottle3 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 statue2 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 tomato2 tomato3 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location16) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable apple3) (cleanable bowl1) (cleanable butterknife1) (cleanable butterknife2) (cleanable cup1) (cleanable cup2) (cleanable cup3) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable dishsponge3) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (cleanable tomato2) (cleanable tomato3) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable apple3) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable cup3) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (coolable tomato2) (coolable tomato3) (handempty agent1) (heatable apple1) (heatable apple2) (heatable apple3) (heatable bread1) (heatable cup1) (heatable cup2) (heatable cup3) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (heatable tomato2) (heatable tomato3) (inreceptacle apple1 countertop1) (inreceptacle apple2 countertop1) (inreceptacle apple3 countertop1) (inreceptacle bowl1 shelf2) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle butterknife2 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 shelf2) (inreceptacle creditcard3 shelf3) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle cup3 sinkbasin1) (inreceptacle dishsponge1 drawer2) (inreceptacle dishsponge2 cabinet5) (inreceptacle dishsponge2 plate1) (inreceptacle dishsponge3 countertop1) (inreceptacle egg1 fridge1) (inreceptacle egg2 sinkbasin1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 garbagecan1) (inreceptacle glassbottle2 shelf2) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle mug1 microwave1) (inreceptacle mug2 shelf2) (inreceptacle pan1 stoveburner1) (inreceptacle pan1 stoveburner3) (inreceptacle papertowelroll1 garbagecan1) (inreceptacle peppershaker1 cabinet1) (inreceptacle peppershaker2 countertop3) (inreceptacle plate1 cabinet5) (inreceptacle plate2 cabinet5) (inreceptacle pot1 stoveburner2) (inreceptacle pot1 stoveburner4) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle potato3 sinkbasin1) (inreceptacle saltshaker1 shelf3) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 garbagecan1) (inreceptacle soapbottle3 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 countertop1) (inreceptacle spatula3 countertop3) (inreceptacle spoon1 drawer2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 countertop1) (inreceptacle statue2 countertop3) (inreceptacle tomato1 fridge1) (inreceptacle tomato2 fridge1) (inreceptacle tomato3 sinkbasin1) (inreceptacle vase1 cabinet6) (inreceptacle vase2 shelf1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location3) (objectatlocation apple2 location3) (objectatlocation apple3 location3) (objectatlocation bowl1 location30) (objectatlocation bread1 location3) (objectatlocation butterknife1 location4) (objectatlocation butterknife2 location4) (objectatlocation cellphone1 location3) (objectatlocation cellphone2 location21) (objectatlocation cellphone3 location4) (objectatlocation chair1 location24) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation creditcard2 location30) (objectatlocation creditcard3 location27) (objectatlocation cup1 location11) (objectatlocation cup2 location11) (objectatlocation cup3 location7) (objectatlocation dishsponge1 location16) (objectatlocation dishsponge2 location2) (objectatlocation dishsponge3 location3) (objectatlocation egg1 location11) (objectatlocation egg2 location7) (objectatlocation fork1 location7) (objectatlocation fork2 location4) (objectatlocation glassbottle1 location9) (objectatlocation glassbottle2 location30) (objectatlocation houseplant1 location4) (objectatlocation knife1 location4) (objectatlocation lettuce1 location11) (objectatlocation lightswitch1 location26) (objectatlocation mug1 location25) (objectatlocation mug2 location30) (objectatlocation pan1 location6) (objectatlocation papertowelroll1 location9) (objectatlocation peppershaker1 location20) (objectatlocation peppershaker2 location4) (objectatlocation plate1 location14) (objectatlocation plate2 location14) (objectatlocation pot1 location22) (objectatlocation potato1 location11) (objectatlocation potato2 location11) (objectatlocation potato3 location7) (objectatlocation saltshaker1 location27) (objectatlocation sink1 location7) (objectatlocation soapbottle1 location5) (objectatlocation soapbottle2 location9) (objectatlocation soapbottle3 location4) (objectatlocation spatula1 location28) (objectatlocation spatula2 location3) (objectatlocation spatula3 location4) (objectatlocation spoon1 location16) (objectatlocation spoon2 location4) (objectatlocation statue1 location3) (objectatlocation statue2 location4) (objectatlocation stoveknob1 location19) (objectatlocation stoveknob2 location13) (objectatlocation stoveknob3 location13) (objectatlocation stoveknob4 location8) (objectatlocation tomato1 location11) (objectatlocation tomato2 location11) (objectatlocation tomato3 location7) (objectatlocation vase1 location5) (objectatlocation vase2 location18) (objectatlocation window1 location31) (objectatlocation window2 location29) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype apple3 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype butterknife2 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype creditcard3 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype cup3 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype dishsponge3 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype soapbottle3 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype statue2 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype tomato2 tomatotype) (objecttype tomato3 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable apple3) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable butterknife2) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable creditcard3) (pickupable cup1) (pickupable cup2) (pickupable cup3) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable dishsponge3) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable knife1) (pickupable lettuce1) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable plate2) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable soapbottle3) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable statue2) (pickupable tomato1) (pickupable tomato2) (pickupable tomato3) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location20) (receptacleatlocation cabinet2 location23) (receptacleatlocation cabinet3 location17) (receptacleatlocation cabinet4 location15) (receptacleatlocation cabinet5 location14) (receptacleatlocation cabinet6 location5) (receptacleatlocation coffeemachine1 location12) (receptacleatlocation countertop1 location3) (receptacleatlocation countertop2 location12) (receptacleatlocation countertop3 location4) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location16) (receptacleatlocation drawer3 location28) (receptacleatlocation fridge1 location11) (receptacleatlocation garbagecan1 location9) (receptacleatlocation microwave1 location25) (receptacleatlocation shelf1 location18) (receptacleatlocation shelf2 location30) (receptacleatlocation shelf3 location27) (receptacleatlocation sinkbasin1 location7) (receptacleatlocation stoveburner1 location6) (receptacleatlocation stoveburner2 location22) (receptacleatlocation stoveburner3 location6) (receptacleatlocation stoveburner4 location22) (receptacleatlocation toaster1 location10) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable apple3) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1) (sliceable tomato2) (sliceable tomato3))\n    (:goal (validatepickandplace saltshakertype cabinettype))\n)"}
