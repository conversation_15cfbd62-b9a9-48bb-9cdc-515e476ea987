{"id": 2376773130592837971, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf1 is at location20. bed1 is at location13. garbagecan1 is at location2. shelf5 is at location22. drawer5 and drawer4 are at location12. drawer2 is at location18. drawer6 is at location1. laundryhamper1 is at location8. shelf4 is at location23. shelf2 is at location25. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer3 is at location17. shelf6 is at location24. shelf3 is at location11. desk2 is at location10.  Currently, the objects are at locations as follows. pillow1, cellphone1, laptop1, book1, pillow2, and laptop2 are at location13. pencil3, pen1, mug2, and cd3 are at location10. window2 is at location4. bowl1, alarmclock1, pencil1, cd1, and mug1 are at location3. creditcard1 and pencil2 are at location22. cellphone3 is at location12. keychain2 and keychain1 are at location6. desklamp1, bowl2, and alarmclock3 are at location23. chair2 is at location26. blinds2 is at location15. baseballbat1 is at location9. mirror1 is at location19. cd2 is at location2. laundryhamperlid1 is at location8. bowl3 is at location24. alarmclock2 is at location11. blinds1 is at location16. window1 is at location5. basketball1 is at location7. lightswitch1 is at location14. chair1 is at location21. agent agent1 is at location location10. The objects are in/on receptacle as follows. alarmclock3, bowl2, pencil3, desklamp1, cd3, pen1, and mug2 are on desk2. desklamp1, bowl2, and alarmclock3 are on shelf4. alarmclock2 is on shelf3. cd1, alarmclock1, pencil1, bowl1, and mug1 are on desk1. creditcard1 and pencil2 are on shelf5. keychain1 and keychain2 are in safe1. pillow2, laptop1, cellphone1, pillow1, laptop2, and book1 are in bed1. bowl3 is on shelf6. cellphone3 is in drawer5. cd2 is in garbagecan1. drawer6, safe1, drawer3, and drawer1 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object cellphone2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type windowtype is cool and is in a receptacle of type handtowelholdertype and pencil3 is on desk2. B. It has been validated that two objects of type curtainstype are in a receptacle of type fridgetype and cd2 is at location2. C. It has been validated that an object of type pentype is in a receptacle of type drawertype and Nothing has been validated. D. It has been validated that an object of type bowltype is in a receptacle of type desktype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type windowtype is cool and is in a receptacle of type handtowelholdertype and pencil3 is on desk2", "It has been validated that two objects of type curtainstype are in a receptacle of type fridgetype and cd2 is at location2", "It has been validated that an object of type pentype is in a receptacle of type drawertype and Nothing has been validated", "It has been validated that an object of type bowltype is in a receptacle of type desktype"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 2453498690548128359, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. egg2 and apple2 are at location8. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location24. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg2 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. egg1 is hot. Nothing has been validated. agent1 is holding object egg1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. spatula2 is hot. B. It has been validated that an object of type blindstype is examined under an object of type sinktype and pan1 is at location11. C. It has been validated that an object of type peppershakertype is in a receptacle of type shelftype and Nothing has been validated. D. It has been validated that an object of type spoontype is in a receptacle of type countertoptype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["spatula2 is hot", "It has been validated that an object of type blindstype is examined under an object of type sinktype and pan1 is at location11", "It has been validated that an object of type peppershakertype is in a receptacle of type shelftype and Nothing has been validated", "It has been validated that an object of type spoontype is in a receptacle of type countertoptype"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -685050466740604210, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. egg2 and apple2 are at location8. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location24. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg2 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, drawer2, and fridge1 are closed. microwave1 is checked. egg1 is hot. microwave1 is open. Nothing has been validated. agent1 is holding object egg1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that two objects of type cuptype are in a receptacle of type fridgetype. B. It has been validated that an object of type tissueboxtype is in a receptacle of type sofatype. C. It has been validated that an object of type platetype is hot and is in a receptacle of type cabinettype and It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype. D. carttype is on stoveburner3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that two objects of type cuptype are in a receptacle of type fridgetype", "It has been validated that an object of type tissueboxtype is in a receptacle of type sofatype", "It has been validated that an object of type platetype is hot and is in a receptacle of type cabinettype and It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype", "carttype is on stoveburner3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -1324058002387925924, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. apple2 and egg1 are at location8. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location16. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg1 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. Nothing has been validated. agent1 is holding object egg2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. location21 is off and fridge1 is closed. B. egg1 is in garbagecan1 and It has been validated that an object of type saltshakertype is examined under an object of type desklamptype. C. It has been validated that an object of type cuptype is in a receptacle of type fridgetype. D. It has been validated that an object of type tomatotype is in a receptacle of type sinkbasintype and Nothing has been validated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["location21 is off and fridge1 is closed", "egg1 is in garbagecan1 and It has been validated that an object of type saltshakertype is examined under an object of type desklamptype", "It has been validated that an object of type cuptype is in a receptacle of type fridgetype", "It has been validated that an object of type tomatotype is in a receptacle of type sinkbasintype and Nothing has been validated"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": *******************, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. apple2 and egg1 are at location8. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location20. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg1 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. egg2 is hot. Nothing has been validated. agent1 is holding object egg2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type teddybeartype is in a receptacle of type bedtype and lettuce3 is at location3. B. It has been validated that an object of type vasetype is cool and is in a receptacle of type tvstandtype. C. It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type spoontype is clean and is in a receptacle of type sinkbasintype. D. It has been validated that an object of type potatotype is in a receptacle of type fridgetype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type teddybeartype is in a receptacle of type bedtype and lettuce3 is at location3", "It has been validated that an object of type vasetype is cool and is in a receptacle of type tvstandtype", "It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type spoontype is clean and is in a receptacle of type sinkbasintype", "It has been validated that an object of type potatotype is in a receptacle of type fridgetype"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 8195910789613811634, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. egg2 and apple2 are at location8. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location20. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg2 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, cabinet1, microwave1, drawer2, and fridge1 are closed. drawer1 is checked. drawer1 is open. Nothing has been validated. agent1 is holding object egg1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type platetype is cool and is in a receptacle of type ottomantype and glassbottle2 is at location14. B. agent1 is holding object faucet1 and creditcard2 is on countertop1. C. It has been validated that an object of type potatotype is clean and is in a receptacle of type fridgetype and It has been validated that an object of type pantype is in a receptacle of type sinkbasintype. D. agent agent1 is at location location14.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type platetype is cool and is in a receptacle of type ottomantype and glassbottle2 is at location14", "agent1 is holding object faucet1 and creditcard2 is on countertop1", "It has been validated that an object of type potatotype is clean and is in a receptacle of type fridgetype and It has been validated that an object of type pantype is in a receptacle of type sinkbasintype", "agent agent1 is at location location14"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -8377900159947648989, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf1 is at location20. bed1 is at location13. garbagecan1 is at location2. shelf5 is at location22. drawer5 and drawer4 are at location12. drawer2 is at location18. drawer6 is at location1. laundryhamper1 is at location8. shelf4 is at location23. shelf2 is at location25. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer3 is at location17. shelf6 is at location24. shelf3 is at location11. desk2 is at location10.  Currently, the objects are at locations as follows. pillow1, cellphone1, laptop1, book1, pillow2, and laptop2 are at location13. pencil3, cellphone2, pen1, and cd3 are at location10. window2 is at location4. bowl1, alarmclock1, pencil1, cd1, and mug1 are at location3. creditcard1 and pencil2 are at location22. cellphone3 is at location12. keychain2 and keychain1 are at location6. desklamp1, bowl2, and alarmclock3 are at location23. chair2 is at location26. window1 is at location5. blinds2 is at location15. baseballbat1 is at location9. mirror1 is at location19. cd2 is at location2. laundryhamperlid1 is at location8. bowl3 is at location24. alarmclock2 is at location11. blinds1 is at location16. basketball1 is at location7. lightswitch1 is at location14. chair1 is at location21. agent agent1 is at location location10. The objects are in/on receptacle as follows. alarmclock3, bowl2, pencil3, desklamp1, cellphone2, cd3, and pen1 are on desk2. desklamp1, bowl2, and alarmclock3 are on shelf4. alarmclock2 is on shelf3. cd1, alarmclock1, pencil1, bowl1, and mug1 are on desk1. creditcard1 and pencil2 are on shelf5. keychain1 and keychain2 are in safe1. pillow2, laptop1, cellphone1, pillow1, laptop2, and book1 are in bed1. bowl3 is on shelf6. cellphone3 is in drawer5. cd2 is in garbagecan1. drawer6, safe1, drawer3, and drawer1 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object mug2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. shelf1 is closed. B. showerdoortype is on desk2 and keychain1 is in safe1. C. blinds1 is at location16 and drawer2 is open. D. It has been validated that an object of type alarmclocktype is in a receptacle of type shelftype and agent agent1 is at location location12.", "choices": {"label": ["A", "B", "C", "D"], "text": ["shelf1 is closed", "showerdoortype is on desk2 and keychain1 is in safe1", "blinds1 is at location16 and drawer2 is open", "It has been validated that an object of type alarmclocktype is in a receptacle of type shelftype and agent agent1 is at location location12"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 371853594057797496, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. cellphone1, lettuce1, lettuce2, knife1, cellphone2, peppershaker1, plate1, and apple1 are at location2. saltshaker1 is at location15. fork1 and dishsponge1 are at location27. potato1, mug1, bowl1, egg1, tomato1, egg2, and plate2 are at location10. knife2, cellphone3, glassbottle1, papertowelroll1, houseplant1, spatula1, butterknife1, creditcard1, bread1, plate3, and spoon2 are at location3. soapbottle2 and statue1 are at location26. dishsponge2 and vase1 are at location13. peppershaker3 is at location16. stoveknob4 is at location7. glassbottle3, apple2, and potato3 are at location8. window2 is at location28. spatula3, glassbottle2, sink1, spatula2, and potato2 are at location6. bowl2 is at location29. peppershaker2 is at location20. cup1 is at location24. chair1 is at location23. pan1 and spoon1 are at location11. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. soapbottle1 is at location4. vase2 is at location17. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. stoveknob1 is at location18. agent agent1 is at location location26. The objects are in/on receptacle as follows. apple2, potato3, and glassbottle3 are in garbagecan1. houseplant1, glassbottle1, spoon2, cellphone3, knife2, papertowelroll1, bread1, creditcard1, spatula1, plate3, and butterknife1 are on countertop3. cellphone2, plate1, lettuce1, peppershaker1, knife1, apple1, cellphone1, and lettuce2 are on countertop1. egg2, egg1, potato1, plate2, tomato1, mug1, and bowl1 are in fridge1. cellphone2 is on plate1. spoon1 and pan1 are on countertop2. vase1 and dishsponge2 are in cabinet5. fork1 and dishsponge1 are in drawer3. potato2, glassbottle2, spatula2, and spatula3 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. peppershaker2 is in drawer1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. peppershaker3 is in cabinet3. saltshaker1 is in drawer2. pan1 is on stoveburner2. bowl2 is on shelf2. cup1 is in microwave1. soapbottle1 is in cabinet6. vase2 is on shelf1. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type breadtype is hot and is in a receptacle of type garbagecantype. B. It has been validated that an object of type bowltype is in a receptacle of type shelftype. C. basketball is cool. D. agent1 is holding object location26.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type breadtype is hot and is in a receptacle of type garbagecantype", "It has been validated that an object of type bowltype is in a receptacle of type shelftype", "basketball is cool", "agent1 is holding object location26"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 600376431830331291, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. cellphone1, lettuce1, lettuce2, knife1, cellphone2, peppershaker1, plate1, and apple1 are at location2. saltshaker1 is at location15. fork1 and dishsponge1 are at location27. potato1, mug1, bowl1, egg1, tomato1, egg2, and plate2 are at location10. knife2, cellphone3, glassbottle1, papertowelroll1, houseplant1, spatula1, butterknife1, creditcard1, bread1, plate3, and spoon2 are at location3. soapbottle2 and statue1 are at location26. dishsponge2 and vase1 are at location13. peppershaker3 is at location16. stoveknob4 is at location7. glassbottle3, apple2, and potato3 are at location8. window2 is at location28. spatula3, glassbottle2, sink1, spatula2, and potato2 are at location6. bowl2 is at location29. peppershaker2 is at location20. cup1 is at location24. chair1 is at location23. pan1 and spoon1 are at location11. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. soapbottle1 is at location4. vase2 is at location17. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. stoveknob1 is at location18. agent agent1 is at location location27. The objects are in/on receptacle as follows. apple2, potato3, and glassbottle3 are in garbagecan1. houseplant1, glassbottle1, spoon2, cellphone3, knife2, papertowelroll1, bread1, creditcard1, spatula1, plate3, and butterknife1 are on countertop3. cellphone2, plate1, lettuce1, peppershaker1, knife1, apple1, cellphone1, and lettuce2 are on countertop1. egg2, egg1, potato1, plate2, tomato1, mug1, and bowl1 are in fridge1. cellphone2 is on plate1. spoon1 and pan1 are on countertop2. vase1 and dishsponge2 are in cabinet5. fork1 and dishsponge1 are in drawer3. potato2, glassbottle2, spatula2, and spatula3 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. peppershaker2 is in drawer1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. peppershaker3 is in cabinet3. saltshaker1 is in drawer2. pan1 is on stoveburner2. bowl2 is on shelf2. cup1 is in microwave1. soapbottle1 is in cabinet6. vase2 is on shelf1. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype and It has been validated that two objects of type eggtype are in a receptacle of type garbagecantype. B. It has been validated that an object of type cuptype is in a receptacle of type microwavetype and agent agent1 is at location location20. C. pottype is hot. D. lightswitch is at location4 and potato3 is in garbagecan1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype and It has been validated that two objects of type eggtype are in a receptacle of type garbagecantype", "It has been validated that an object of type cuptype is in a receptacle of type microwavetype and agent agent1 is at location location20", "pottype is hot", "lightswitch is at location4 and potato3 is in garbagecan1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -******************, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner2 and stoveburner4 are at location22. drawer3 is at location28. countertop3 is at location4. cabinet4 is at location15. stoveburner1 and stoveburner3 are at location6. fridge1 is at location11. shelf3 is at location27. countertop1 is at location3. cabinet5 is at location14. cabinet1 is at location20. coffeemachine1 and countertop2 are at location12. microwave1 is at location25. drawer1 is at location21. shelf2 is at location30. sinkbasin1 is at location7. drawer2 is at location16. shelf1 is at location18. garbagecan1 is at location9. toaster1 is at location10. cabinet2 is at location23. cabinet3 is at location17. cabinet6 is at location5.  Currently, the objects are at locations as follows. peppershaker2, cellphone3, butterknife1, butterknife2, fork2, spatula3, knife1, soapbottle3, spoon2, houseplant1, and statue2 are at location4. papertowelroll1, soapbottle2, and glassbottle1 are at location9. apple3, cellphone1, spatula2, statue1, creditcard1, apple2, bread1, apple1, and dishsponge3 are at location3. stoveknob2 and stoveknob3 are at location13. chair1 is at location24. cellphone2 is at location21. plate2 and plate1 are at location14. spoon1 and dishsponge1 are at location16. vase2 is at location18. stoveknob4 is at location8. window2 is at location29. tomato1, cup1, lettuce1, potato1, potato2, egg1, tomato2, and cup2 are at location11. pot1 is at location22. mug2, bowl1, creditcard2, and glassbottle2 are at location30. vase1 and soapbottle1 are at location5. tomato3, egg2, sink1, fork1, potato3, and cup3 are at location7. window1 is at location31. pan1 is at location6. mug1 is at location25. lightswitch1 is at location26. creditcard3 is at location27. dishsponge2 is at location2. peppershaker1 is at location20. stoveknob1 is at location19. chair2 is at location1. spatula1 is at location28. agent agent1 is at location location27. The objects are in/on receptacle as follows. creditcard2, glassbottle2, mug2, and bowl1 are on shelf2. houseplant1, spoon2, spatula3, cellphone3, butterknife2, knife1, fork2, soapbottle3, statue2, peppershaker2, and butterknife1 are on countertop3. creditcard3 is on shelf3. spoon1 and dishsponge1 are in drawer2. apple2, creditcard1, bread1, statue1, dishsponge3, apple3, apple1, spatula2, and cellphone1 are on countertop1. egg1, potato1, cup2, tomato1, lettuce1, cup1, tomato2, and potato2 are in fridge1. cellphone2 is in drawer1. pot1 is on stoveburner4. plate1, plate2, and dishsponge2 are in cabinet5. fork1, tomato3, potato3, egg2, and cup3 are in sinkbasin1. mug1 is in microwave1. dishsponge2 is on plate1. papertowelroll1, glassbottle1, and soapbottle2 are in garbagecan1. peppershaker1 is in cabinet1. pot1 is on stoveburner2. spatula1 is in drawer3. pan1 is on stoveburner1. vase1 and soapbottle1 are in cabinet6. pan1 is on stoveburner3. vase2 is on shelf1. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. Nothing has been validated. agent1 is holding object saltshaker1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type newspapertype is examined under an object of type bathtubtype. B. agent agent1 is at location location21. C. It has been validated that an object of type bowltype is clean and is in a receptacle of type fridgetype and It has been validated that two objects of type cuptype are in a receptacle of type cabinettype. D. It has been validated that two objects of type lettucetype are in a receptacle of type microwavetype and knife1 is at location4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type newspapertype is examined under an object of type bathtubtype", "agent agent1 is at location location21", "It has been validated that an object of type bowltype is clean and is in a receptacle of type fridgetype and It has been validated that two objects of type cuptype are in a receptacle of type cabinettype", "It has been validated that two objects of type lettucetype are in a receptacle of type microwavetype and knife1 is at location4"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
