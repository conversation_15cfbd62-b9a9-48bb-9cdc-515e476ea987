{"question_id": "3bdded84-9ac2-42c2-b999-2db2901c312b", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c8 is loaded in vehicle v1 with space s2 and space s1 at location l1, cargo c0 is loaded in vehicle v1 with space s2 and space s1 at location l1, cargo c3 is loaded in vehicle v1 with space s2 and space s1 at location l1, at location l0, cargo c9 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is loaded in vehicle v1 with space s2 and space s1 at location l1, cargo c2 is loaded in vehicle v1 with space s2 and space s1 at location l1, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and at location l1, cargo c1 is loaded in vehicle v1 with spaces s2 and s1", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels of f3 and f2, to location l0. Upon arrival at location l0, cargo c0 is loaded onto vehicle v0, occupying spaces s2 and s1. Subsequently, at location l0, cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels of f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Cargo c10 is then loaded onto vehicle v0 at location l1, occupying spaces s1 and s0. Additionally, at location l1, cargo c2 is unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then proceeds to move from location l1, which has fuel levels of f2 and f1, to location l0. At location l0, cargo c10 is unloaded from vehicle v0, vacating spaces s1 and s2. Cargo c3 is then loaded onto vehicle v0 at location l0, occupying spaces s2 and s1, followed by the loading of cargo c4, which utilizes spaces s1 and s0. Vehicle v0 then relocates from location l0, which has fuel levels of f3 and f2, to location l1. At location l1, cargo c3 is unloaded from vehicle v0, freeing up spaces s0 and s1. Furthermore, at location l1, cargo c4 is unloaded from vehicle v0, releasing spaces s1 and s2. Cargo c5 is then loaded onto vehicle v0 at location l1, occupying spaces s2 and s1, and cargo c9 is loaded, utilizing spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels of f1 and f0, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, vacating spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, ultimately reaching the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are connected, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 is equipped with space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "a9bda5a5-1f54-4bf1-b49e-23ffd29288b7", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "at location l0, cargo c5 is loaded in vehicle v1 with spaces s1 and s0, at location l0, cargo c2 is loaded in vehicle v1 with spaces s1 and s0, at location l0, cargo c6 is loaded in vehicle v1 with spaces s1 and s0, cargo c9 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l0, cargo c3 is loaded in vehicle v1 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0 using spaces s0 and s1. Next, at location l0, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1, where cargo c0 is unloaded from vehicle v0 using spaces s0 and s1. This process continues: at location l1, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels to location l0, with fuel levels f7 and f6, where cargo c3 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1, where cargo c1 is unloaded from vehicle v0 using spaces s0 and s1. The sequence continues with cargo c5 being loaded onto vehicle v0 at location l1, then unloaded at location l0, and cargo c7 being loaded onto vehicle v0 at location l0, then unloaded at location l1, where cargo c9 is loaded onto vehicle v0. To reach the current state, these actions are performed. Now, list all possible actions that can be executed from this state. If there are no possible actions, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also present at l0. Fuel f3 is available at l0, and fuel f8 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at l1, and also within vehicle v1, which is present at l0."}
{"question_id": "713a6832-4f76-4be0-8177-7a96847b7116", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f0 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "vehicle v0 moves from location l0 which has fuel-levels f6 and f0 to location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0, which has fuel levels f6 and f0, to location l1 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at l0, and fuel f8 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at l1, and also within vehicle v1, which is present at l0."}
{"question_id": "536c1db1-7925-4521-991b-dfff843ebc9a", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "vehicle v0 moves from location l1 which has fuel-levels f8 and f0 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f4, cargo c3 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l1 which has fuel-levels f7 and f5 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f8, vehicle v0 moves from location l0 which has fuel-levels f7 and f8 to location l1, vehicle v0 moves from location l0 which has fuel-levels f1 and f6 to location l1, at location l1, cargo c9 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f7, cargo c6 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f5, vehicle v0 moves from location l0 which has fuel-levels f1 and f3 to location l1, cargo c0 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f1, cargo c1 is unloaded from vehicle v0 with space s1 and space s0 at location l0, at location l1, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f1 to location l0, vehicle v0 moves from location l0 which has fuel-levels f0 and f8 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f8, vehicle v0 moves from location l1 which has fuel-levels f5 and f7 to location l0, vehicle v0 moves from location l1 which has fuel-levels f1 and f5 to location l0, vehicle v0 moves from location l0 which has fuel-levels f0 and f7 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f0 to location l1, at location l1, cargo c10 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f1, vehicle v0 moves from location l0 which has fuel-levels f6 and f1 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f8 and f1 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f8 and f6 to location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f5 to location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f8 to location l1, cargo c6 is unloaded from vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f0, vehicle v0 moves from location l0 which has fuel-levels f6 and f8 to location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f6 to location l0, vehicle v0 moves from location l1 which has fuel-levels f7 and f8 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f6, vehicle v0 moves from location l1 which has fuel-levels f0 and f1 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f4, vehicle v0 moves from location l1 which has fuel-levels f3 and f8 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c1 is loaded in vehicle v0 with spaces s0 and s1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f3, vehicle v0 moves from location l1 which has fuel-levels f7 and f4 to location l0, cargo c5 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f3, vehicle v0 moves from location l0 which has fuel-levels f8 and f4 to location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f8 to location l0, at location l1, cargo c4 is loaded in vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s1 and s0, at location l0, cargo c7 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f0 and f5 to location l0, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l1 which has fuel-levels f3 and f7 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f8, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c3 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f1, vehicle v0 moves from location l0 which has fuel-levels f3 and f0 to location l1, vehicle v0 moves from location l0 which has fuel-levels f8 and f2 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f6, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c8 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f5, vehicle v0 moves from location l0 which has fuel-levels f8 and f0 to location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f4 to location l1, vehicle v0 moves from location l0 which has fuel-levels f7 and f2 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f1, vehicle v0 moves from location l0 which has fuel-levels f3 and f7 to location l1, vehicle v0 moves from location l1 which has fuel-levels f0 and f3 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c2 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f1 to location l0, vehicle v0 moves from location l1 which has fuel-levels f3 and f5 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l1 which has fuel-levels f7 and f0 to location l0, at location l0, cargo c8 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f7, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f0 and f5 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f4, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f2 and f6 to location l1, at location l0, cargo c5 is loaded in vehicle v0 with spaces s0 and s1, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f4, at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, vehicle v0 moves from location l1 which has fuel-levels f2 and f7 to location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f5 to location l1, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f0, at location l0, cargo c10 is loaded in vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f0 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f8, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f2, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f4 to location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f3, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, vehicle v0 moves from location l0 which has fuel-levels f8 and f7 to location l1, at location l0, cargo c9 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f3 to location l1, at location l1, cargo c2 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f0 and f2 to location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f2 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, vehicle v0 moves from location l1 which has fuel-levels f4 and f7 to location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c6 is loaded in vehicle v0 with space s0 and space s1 at location l0, cargo c9 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c8 is loaded in vehicle v0 with spaces s0 and s1, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l1 which has fuel-levels f7 and f2 to location l0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c7 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f2, vehicle v0 moves from location l1 which has fuel-levels f0 and f6 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f8, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f5, vehicle v0 moves from location l0 which has fuel-levels f1 and f7 to location l1, vehicle v0 moves from location l0 which has fuel-levels f4 and f2 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f4, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f3 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f7, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f6, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f2, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f8 to location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f0 to location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f0 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f4 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is loaded in vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f7, cargo c5 is unloaded from vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c6 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f4, at location l1, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f5 to location l1, vehicle v0 moves from location l1 which has fuel-levels f3 and f0 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f3, vehicle v0 moves from location l0 which has fuel-levels f3 and f4 to location l1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f6 to location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f6 to location l0, vehicle v0 moves from location l1 which has fuel-levels f5 and f2 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f8, vehicle v0 moves from location l0 which has fuel-levels f6 and f7 to location l1, vehicle v0 moves from location l0 which has fuel-levels f2 and f7 to location l1, cargo c8 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f3, cargo c7 is unloaded from vehicle v0 with space s1 and space s0 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is unloaded from vehicle v0 with spaces s1 and s0, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f3, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f3, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c4 is unloaded from vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f6, vehicle v0 moves from location l0 which has fuel-levels f4 and f8 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f3, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f2, vehicle v0 moves from location l0 which has fuel-levels f2 and f0 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f4, vehicle v0 moves from location l1 which has fuel-levels f4 and f5 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f7, vehicle v0 moves from location l0 which has fuel-levels f2 and f5 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f6, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f2 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f6 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f2, cargo c9 is loaded in vehicle v0 with space s0 and space s1 at location l1, cargo c4 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f0, at location l0, cargo c9 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f1, cargo c0 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f6, vehicle v0 moves from location l0 which has fuel-levels f8 and f3 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f4, cargo c9 is unloaded from vehicle v0 with space s1 and space s0 at location l0, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f2, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f8, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f0 and vehicle v0 moves from location l0 which has fuel-levels f1 and f5 to location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Vehicle v0 then travels from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. This is followed by vehicle v0 moving from location l0, with fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f4 and f3, to location l0. Upon arrival at location l0, cargo c6 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f5 and f4, back to location l1. At location l1, cargo c7 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f3 and f2, to location l0. At location l0, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1. This is followed by vehicle v0 moving from location l0, with fuel levels f4 and f3, back to location l1. At location l1, cargo c8 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f2 and f1, to location l0, where cargo c8 is unloaded from vehicle v0, which has spaces s0 and s1, resulting in the current state. In this state, list all inexecutable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, fuel levels f1 and f2 are neighbors, as are f2 and f3, and f4 and f5. Location l0 has fuel f7 and is connected to location l1, which is also connected to l0. Space s0 is adjacent to space s1, and space s1 is part of vehicle v0, which is currently situated at location l1."}
{"question_id": "b95746cb-e7e2-4ab7-8ea2-82b79c6d1ee0", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, vehicle v0 moves from location l1 which has fuel-levels f8 and f2 to location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "vehicle v0 moves from location l1 which has fuel-levels f8 and f2 to location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is performed: cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. This sequence of actions is repeated for cargo c6 and c7. However, the sequence of actions for cargo c8 is different. Vehicle v0 moves from location l1, which has fuel levels f8 and f2, to location l0, and then from location l0, which has fuel levels f4 and f3, back to location l1. Then, cargo c8 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Finally, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0, and at location l0, cargo c8 is unloaded from vehicle v0, which has spaces s0 and s1, to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is found at l0, cargo c2 is situated at l0, cargo c3 is positioned at l0, cargo c4 is situated at l1, cargo c5 is present at l1, cargo c6 is also present at l1, cargo c7 is located at l1, cargo c8 is present at l1, cargo c9 is present at l1, fuel f6 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel level f7 is adjacent to fuel level f8, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f4 and f5 are adjacent, location l0 is supplied with fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is positioned at location l1."}
{"question_id": "665f32fc-67d0-4416-a41d-fd57e21c3c2b", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v1 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c2 is loaded in vehicle v1 with space s1 and space s0 at location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c0 is loaded in vehicle v1 with spaces s1 and s0, at location l1, cargo c6 is loaded in vehicle v1 with spaces s1 and s0, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c4 is loaded in vehicle v1 with spaces s1 and s0, cargo c7 is loaded in vehicle v1 with space s1 and space s0 at location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v1 moves from location l1 which has fuel-levels f7 and f6 to location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l0, which has fuel levels of f5 and f4, to location l1, resulting in the current state. In this state, list all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "1ea6ca31-bcd4-4b8b-87f7-a5d8968f9a5d", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c3 is also unloaded from vehicle v0, which has spaces s0 and s1. Cargo c1 is then loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Then, cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1. Cargo c7 is then loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "4e2ace6c-7cc7-4a42-a121-7a93cffde7cb", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2 and at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "at location l1, cargo c8 is loaded in vehicle v1 with spaces s2 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, cargo c0 is loaded in vehicle v1 with space s2 and space s1 at location l1, cargo c5 is loaded in vehicle v1 with space s2 and space s1 at location l1, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s1 and s2, cargo c9 is loaded in vehicle v1 with space s2 and space s1 at location l1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v1 moves from location l1 which has fuel-levels f1 and f0 to location l0, cargo c2 is loaded in vehicle v1 with space s2 and space s1 at location l1 and cargo c1 is loaded in vehicle v1 with space s2 and space s1 at location l1", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded into vehicle v0, utilizing spaces s2 and s1. Subsequently, at location l0, cargo c2 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. Upon arrival, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0. At location l1, cargo c2 is then unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, back to location l0. At location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 is loaded into vehicle v0, utilizing spaces s2 and s1, resulting in the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "ea09f098-f6e7-4951-aaf9-6970ccfa7b38", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l0, cargo c7 is loaded in vehicle v1 with spaces s1 and s0, cargo c2 is loaded in vehicle v1 with space s1 and space s0 at location l0, vehicle v1 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c6 is loaded in vehicle v1 with space s1 and space s0 at location l0, at location l0, cargo c3 is loaded in vehicle v1 with spaces s1 and s0 and cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l1, cargo c3 is present at location l1, cargo c4 is present at location l1, cargo c5 is present at location l1, cargo c6 is present at location l0, cargo c7 is at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has available spaces s1 and s0. Vehicle v0 then moves from location l1, where the fuel levels are f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1 available. Next, at location l0, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then travels from location l0, which has fuel levels f3 and f2, back to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1 available. Subsequently, at location l1, cargo c3 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l1, where the fuel levels are f7 and f6, to location l0. Upon arrival at location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1 available, and cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, cargo c3, cargo c4, cargo c5, cargo c8, and cargo c9 are all situated at location l1, with cargo c6 and cargo c7 also being present at l0. Fuel f3 is available at location l0, and fuel f8 is available at location l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f6 is adjacent to f7, and f7 is adjacent to f8. Additionally, f2 and f3 are neighboring fuel levels, as are f3 and f4, f4 and f5, and f5 and f6. Locations l0 and l1 are interconnected. Furthermore, space s0 is adjacent to space s1, which is contained within vehicle v0, currently situated at location l1, and also within vehicle v1, which is present at location l0."}
{"question_id": "f867c83c-40b7-4111-8b9d-4192e2aec7ec", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "vehicle v0 moves from location l1 which has fuel-levels f8 and f0 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f4, cargo c3 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f5, vehicle v0 moves from location l1 which has fuel-levels f4 and f8 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f8, vehicle v0 moves from location l0 which has fuel-levels f1 and f6 to location l1, cargo c9 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f7 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f5, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f7 and f1 to location l1, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s1 and s0, at location l1, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f1 to location l0, vehicle v0 moves from location l0 which has fuel-levels f0 and f8 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f8, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f7, vehicle v0 moves from location l1 which has fuel-levels f1 and f5 to location l0, vehicle v0 moves from location l0 which has fuel-levels f0 and f7 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f0 to location l1, cargo c10 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f1, at location l0, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c1 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f6, vehicle v0 moves from location l0 which has fuel-levels f3 and f5 to location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f8, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f8, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l1 which has fuel-levels f5 and f6 to location l0, vehicle v0 moves from location l1 which has fuel-levels f7 and f8 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f1, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f4, vehicle v0 moves from location l1 which has fuel-levels f3 and f8 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s0 and s1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f3 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f4, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c5 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f3, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f4, vehicle v0 moves from location l1 which has fuel-levels f2 and f8 to location l0, cargo c4 is loaded in vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f0, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s1 and s0, cargo c7 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f5, at location l0, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f7, vehicle v0 moves from location l1 which has fuel-levels f5 and f8 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, cargo c3 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f0, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f2, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f5, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f0, cargo c3 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f1 to location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f7 to location l1, vehicle v0 moves from location l1 which has fuel-levels f0 and f3 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c2 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f1 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f5, vehicle v0 moves from location l0 which has fuel-levels f4 and f1 to location l1, cargo c10 is unloaded from vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f0, at location l0, cargo c8 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f0 and f7, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f5, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f4, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f6, cargo c5 is loaded in vehicle v0 with space s0 and space s1 at location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l1 which has fuel-levels f3 and f4 to location l0, at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, vehicle v0 moves from location l1 which has fuel-levels f2 and f7 to location l0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f5 to location l1, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f0 to location l0, cargo c10 is loaded in vehicle v0 with space s0 and space s1 at location l0, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f3, cargo c1 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l1 which has fuel-levels f0 and f8 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f2, cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f4 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f3, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f7, cargo c9 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f3, cargo c2 is loaded in vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l0 which has fuel-levels f0 and f2 to location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f2 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f7, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c6 is loaded in vehicle v0 with space s0 and space s1 at location l0, at location l1, cargo c9 is unloaded from vehicle v0 with spaces s0 and s1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c8 is loaded in vehicle v0 with space s0 and space s1 at location l1, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f7 and f2 to location l0, at location l0, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f2, vehicle v0 moves from location l1 which has fuel-levels f0 and f6 to location l0, vehicle v0 moves from location l1 which has fuel-levels f6 and f8 to location l0, vehicle v0 moves from location l1 which has fuel-levels f2 and f5 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f7, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f2, vehicle v0 moves from location l1 which has fuel-levels f1 and f4 to location l0, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f6 and f3 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f7, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f6, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f2, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f8 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f4, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s0 and s1, cargo c2 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f7, at location l1, cargo c5 is unloaded from vehicle v0 with spaces s1 and s0, at location l1, cargo c6 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f4 to location l1, at location l1, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l0 which has fuel-levels f4 and f5 to location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f0, vehicle v0 moves from location l0 which has fuel-levels f2 and f3 to location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f4 to location l1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, vehicle v0 moves from location l0 which has fuel-levels f5 and f6 to location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f6 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f2, vehicle v0 moves from location l0 which has fuel-levels f2 and f8 to location l1, vehicle v0 moves from location l0 which has fuel-levels f6 and f7 to location l1, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f7, at location l1, cargo c8 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f7 and f3 to location l1, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s1 and s0, cargo c0 is loaded in vehicle v0 with space s0 and space s1 at location l0, at location l1, cargo c10 is unloaded from vehicle v0 with spaces s1 and s0, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f3, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f3, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f0 and f6, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f8, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f3, vehicle v0 moves from location l1 which has fuel-levels f8 and f2 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f4, vehicle v0 moves from location l1 which has fuel-levels f4 and f5 to location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f7, vehicle v0 moves from location l0 which has fuel-levels f2 and f5 to location l1, vehicle v0 moves from location l0 which has fuel-levels f3 and f6 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f2, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f6, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f2, at location l1, cargo c9 is loaded in vehicle v0 with spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f0, cargo c9 is loaded in vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f1, vehicle v0 moves from location l0 which has fuel-levels f3 and f1 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f6, vehicle v0 moves to location l1 from location l0 that has fuel level f8 and f3, vehicle v0 moves from location l1 which has fuel-levels f2 and f4 to location l0, at location l0, cargo c9 is unloaded from vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f0 and f2 to location l0, vehicle v0 moves from location l1 which has fuel-levels f1 and f8 to location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f0 to location l1 and vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f5", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1. Finally, at location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 moves to location l0 from location l1, where the fuel levels are f4 and f3, resulting in the current state. In this state, list all inexecutable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10 is positioned at l0, cargo c2 is situated at l0, cargo c3 is at l0, and cargo c4, c5, c6, c7, c8, and c9 are all located at l1. Additionally, fuel f6 is present at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f1 and f2 are neighboring fuel levels, f2 and f3 are also neighbors, and f4 and f5 are adjacent. Location l0 contains fuel f7 and is connected to l1, which in turn is connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at location l1."}
{"question_id": "7c8f324d-c620-408e-ad54-3a5584fef1f6", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v1 moves from location l1 which has fuel-levels f7 and f2 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "vehicle v1 moves to location l0 from location l1 that has fuel level f7 and f2", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is performed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves from location l1, which has fuel levels f7 and f6, back to location l0, where cargo c0 is unloaded from vehicle v0, freeing spaces s0 and s1. Next, vehicle v0 returns to location l1 from location l0, which has fuel levels f4 and f3, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0 at location l1. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, back to location l0, where cargo c1 is unloaded from vehicle v0, freeing spaces s0 and s1. This sequence continues with vehicle v0 moving from location l0, which has fuel levels f3 and f2, to location l1, where cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0. Meanwhile, vehicle v1 moves from location l1, which has fuel levels f7 and f2, to location l0, and cargo c2 is unloaded from vehicle v0, freeing spaces s0 and s1 at location l0. The sequence proceeds with vehicle v0 moving from location l0, which has fuel levels f2 and f1, to location l1, where cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves to location l0 from location l1, which has fuel level f4 and f3, and cargo c4 is unloaded from vehicle v0, freeing spaces s0 and s1 at location l0. Next, vehicle v0 returns to location l1 from location l0, which has fuel level f1 and f0, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0 at location l1. Finally, vehicle v0 moves to location l0 from location l1, which has fuel level f3 and f2, to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
{"question_id": "a870b25a-22a0-4d2e-9e98-54a36e3dfe31", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1 and vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps involve loading cargo c1 into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. Now, list all possible actions that can be executed from this state, or indicate None if there are no options.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f4 and f5. Location l0 is equipped with fuel f7 and is connected to location l1, which in turn is connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at location l1."}
{"question_id": "5523b64f-6935-4989-9241-bbee936b190a", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l0, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l0 and at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is present at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f7, location l0 is connected to location l1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Vehicle v0 then moves back to location l1, which has fuel levels f7 and f6. Upon arrival at location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then proceeds to location l0 from location l1, which has fuel levels f5 and f4. At location l0, cargo c4 is unloaded from vehicle v0, which still has spaces s0 and s1. Vehicle v0 then returns to location l1, which has fuel levels f6 and f5. At location l1, cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves to location l0 from location l1, which has fuel levels f4 and f3. At location l0, cargo c6 is unloaded from vehicle v0, which still has spaces s0 and s1. Vehicle v0 then moves back to location l1, which has fuel levels f5 and f4. Upon arrival at location l1, cargo c7 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then proceeds to location l0 from location l1, which has fuel levels f3 and f2. At location l0, cargo c7 is unloaded from vehicle v0, which still has spaces s0 and s1. Vehicle v0 then returns to location l1, which has fuel levels f4 and f3. At location l1, cargo c8 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves to location l0 from location l1, which has fuel levels f2 and f1, and finally, cargo c8 is unloaded from vehicle v0, which still has spaces s0 and s1, to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all present at location l1. Furthermore, fuel f6 is available at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Moreover, fuel levels f1 and f2 are neighboring, as are f2 and f3, and f4 and f5. Location l0 is equipped with fuel f7 and is connected to location l1, which is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is situated at location l1."}
{"question_id": "e09ee82b-0615-4a8b-9609-61140e8cc056", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c0 is loaded in vehicle v1 with space s2 and space s1 at location l0 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "at location l0, cargo c0 is loaded in vehicle v1 with spaces s2 and s1", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has fuel f4, location l1 has a fuel-level of f3, space s0 neighbors space s1, space s1 neighbors space s2, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the subsequent actions taken are as follows: cargo c0 is loaded into vehicle v1, which has spaces s2 and s1, at location l0, resulting in the current state. What is the first action in the sequence that cannot be executed? If there are no such actions, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is located at l0, cargo c4 is situated at l0, cargo c5 is situated at l1, cargo c6 is located at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is located at l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, locations l0 and l1 are linked, location l0 is equipped with fuel f4, location l1 has a fuel level of f3, space s0 is adjacent to space s1, space s1 is adjacent to space s2, locations l1 and l0 are connected, vehicle v0 occupies space s2, vehicle v0 is located at l1, vehicle v1 contains space s2 and vehicle v1 is located at l1."}
{"question_id": "add8614e-30d0-471f-80c3-7d4d09fa2abc", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "cargo c7 is loaded in vehicle v1 with space s1 and space s0 at location l1, vehicle v1 moves from location l1 which has fuel-levels f2 and f1 to location l0 and cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0 utilizing spaces s1 and s0. Then, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0 using spaces s0 and s1. Next, vehicle v0 returns to location l1, which has fuel levels f4 and f3, and cargo c1 is loaded onto vehicle v0 using spaces s1 and s0. This is followed by vehicle v0 moving back to location l0, which now has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0 using spaces s0 and s1. The sequence continues with vehicle v0 moving to location l1, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0 using spaces s1 and s0. Then, vehicle v0 moves back to location l0, which now has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0 using spaces s0 and s1. Vehicle v0 then relocates from location l0, which has fuel levels f2 and f1, to location l1, where cargo c4 is loaded onto vehicle v0 using spaces s1 and s0. Next, vehicle v0 moves back to location l0, which now has fuel levels f4 and f3, and cargo c4 is unloaded from vehicle v0 using spaces s0 and s1. The sequence continues with vehicle v0 moving from location l0, which has fuel levels f1 and f0, to location l1, where cargo c6 is loaded onto vehicle v0 using spaces s1 and s0. Finally, vehicle v0 moves to location l0, which has fuel levels f3 and f2, to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is found at l1, cargo c3 is positioned at l0, cargo c4 is positioned at l1, cargo c5 is positioned at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is located at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, locations l0 and l1 are connected, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 has space s1 and vehicle v1 is at location l1."}
