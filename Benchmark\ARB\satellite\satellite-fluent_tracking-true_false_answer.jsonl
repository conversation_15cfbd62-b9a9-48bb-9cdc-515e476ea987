{"question_id": "c9e90b68-a654-4ecf-aa72-c885db14b371", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: power is not available for satellite1 and satellite0 does not have power available?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, are the following properties that involve negations True or False: satellite1 does not have power available and power is not available for satellite0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently directed towards star3."}
{"question_id": "e8d77c07-a197-40e6-b3f7-5deaf75e37ba", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, is it True or False that instrument3 is not switched on?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. In this state, is it True or False that instrument3 is not activated?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "81c594df-5540-4d79-ba63-d77b610d18e4", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, from phenomenon10, satellite1 turns to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, from phenomenon17, satellite1 turns to planet11 and instrument3 which is on satellite1 takes an image of planet11 in image3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 is incomplete, calibration of instrument1 is incomplete, image of groundstation0 does not exist in image0, image of groundstation2 does not exist in image2, image of groundstation2 does not exist in image3, image of groundstation2 does not exist in infrared1, image of groundstation3 does not exist in image0, image of groundstation3 does not exist in image2, image of groundstation3 does not exist in infrared1, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image2, image of groundstation4 does not exist in image3, image of groundstation4 does not exist in infrared1, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image2, image of groundstation7 does not exist in image3, image of phenomenon10 does not exist in image0, image of phenomenon10 does not exist in image3, image of phenomenon16 does not exist in image2, image of phenomenon16 does not exist in infrared1, image of phenomenon17 does not exist in image0, image of planet11 does not exist in image0, image of planet11 does not exist in image2, image of planet11 does not exist in infrared1, image of planet12 does not exist in image3, image of planet12 does not exist in infrared1, image of star1 does not exist in image0, image of star15 does not exist in image3, image of star15 does not exist in infrared1, image of star6 does not exist in image0, image of star6 does not exist in infrared1, image of star8 does not exist in image0, image of star8 does not exist in image2, image of star8 does not exist in image3, image of star8 does not exist in infrared1, image of star9 does not exist in image2, image of star9 does not exist in infrared1, instrument1 is not powered on, instrument2 is not calibrated, instrument2 is not powered on, there is no image of direction groundstation0 in image2, there is no image of direction groundstation0 in image3, there is no image of direction groundstation0 in infrared1, there is no image of direction groundstation2 in image0, there is no image of direction groundstation3 in image3, there is no image of direction groundstation5 in image3, there is no image of direction groundstation5 in infrared1, there is no image of direction groundstation7 in image0, there is no image of direction groundstation7 in image2, there is no image of direction groundstation7 in infrared1, there is no image of direction phenomenon10 in image2, there is no image of direction phenomenon10 in infrared1, there is no image of direction phenomenon16 in image0, there is no image of direction phenomenon17 in image2, there is no image of direction phenomenon17 in infrared1, there is no image of direction planet12 in image0, there is no image of direction planet12 in image2, there is no image of direction planet13 in image0, there is no image of direction planet13 in image2, there is no image of direction planet13 in image3, there is no image of direction planet13 in infrared1, there is no image of direction planet14 in image0, there is no image of direction planet14 in image2, there is no image of direction planet14 in image3, there is no image of direction planet14 in infrared1, there is no image of direction star1 in image2, there is no image of direction star1 in image3, there is no image of direction star1 in infrared1, there is no image of direction star15 in image0, there is no image of direction star15 in image2, there is no image of direction star6 in image2, there is no image of direction star6 in image3, there is no image of direction star9 in image0 and there is no image of direction star9 in image3?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured by instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured by instrument3 on satellite1 and stored in image3, and finally, satellite1 reorients from phenomenon17 to planet11, and instrument3 on satellite1 captures an image of planet11, which is stored in image3, resulting in the current state. In this state, are the following properties that involve negations True or False: instrument0's calibration is incomplete, instrument1's calibration is incomplete, there is no image of groundstation0 in image0, groundstation2's image does not exist in image2, groundstation2's image does not exist in image3, groundstation2's image does not exist in infrared1, groundstation3's image does not exist in image0, groundstation3's image does not exist in image2, groundstation3's image does not exist in infrared1, groundstation4's image does not exist in image0, groundstation4's image does not exist in image2, groundstation4's image does not exist in image3, groundstation4's image does not exist in infrared1, groundstation5's image does not exist in image0, groundstation5's image does not exist in image2, groundstation7's image does not exist in image3, phenomenon10's image does not exist in image0, phenomenon10's image does not exist in image3, phenomenon16's image does not exist in image2, phenomenon16's image does not exist in infrared1, phenomenon17's image does not exist in image0, planet11's image does not exist in image0, planet11's image does not exist in image2, planet11's image does not exist in infrared1, planet12's image does not exist in image3, planet12's image does not exist in infrared1, star1's image does not exist in image0, star15's image does not exist in image3, star15's image does not exist in infrared1, star6's image does not exist in image0, star6's image does not exist in infrared1, star8's image does not exist in image0, star8's image does not exist in image2, star8's image does not exist in image3, star8's image does not exist in infrared1, star9's image does not exist in image2, star9's image does not exist in infrared1, instrument1 is not powered on, instrument2 is not calibrated, instrument2 is not powered on, there is no image of the direction towards groundstation0 in image2, there is no image of the direction towards groundstation0 in image3, there is no image of the direction towards groundstation0 in infrared1, there is no image of the direction towards groundstation2 in image0, there is no image of the direction towards groundstation3 in image3, there is no image of the direction towards groundstation5 in image3, there is no image of the direction towards groundstation5 in infrared1, there is no image of the direction towards groundstation7 in image0, there is no image of the direction towards groundstation7 in image2, there is no image of the direction towards groundstation7 in infrared1, there is no image of the direction towards phenomenon10 in image2, there is no image of the direction towards phenomenon10 in infrared1, there is no image of the direction towards phenomenon16 in image0, there is no image of the direction towards phenomenon17 in image2, there is no image of the direction towards phenomenon17 in infrared1, there is no image of the direction towards planet12 in image0, there is no image of the direction towards planet12 in image2, there is no image of the direction towards planet13 in image0, there is no image of the direction towards planet13 in image2, there is no image of the direction towards planet13 in image3, there is no image of the direction towards planet13 in infrared1, there is no image of the direction towards planet14 in image0, there is no image of the direction towards planet14 in image2, there is no image of the direction towards planet14 in image3, there is no image of the direction towards planet14 in infrared1, there is no image of the direction towards star1 in image2, there is no image of the direction towards star1 in image3, there is no image of the direction towards star1 in infrared1, there is no image of the direction towards star15 in image0, there is no image of the direction towards star15 in image2, there is no image of the direction towards star6 in image2, there is no image of the direction towards star6 in image3, there is no image of the direction towards star9 in image0, and there is no image of the direction towards star9 in image3?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current pointing direction being groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "4699705a-79f9-4812-bd28-4efe5614c47f", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, satellite0's instrument1 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, is it True or False that satellite1 has power available?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: the instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 then reorients from planet11 to planet13, an image of planet13 is captured by instrument1 on satellite0 and stored in image5, and instrument1 on satellite0 captures an image of planet13 in spectrograph2, finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, is it True or False that satellite1 has power available?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, with instrument1 also on board satellite0."}
{"question_id": "64219dfd-ec72-4838-9651-1e8b0a5283a2", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to star6 from groundstation4, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, from planet14, satellite1 turns to star10, image of star10 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns to star12 from star10 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 is incomplete, calibration of instrument2 is incomplete, image of groundstation2 does not exist in spectrograph0, image of groundstation2 does not exist in spectrograph2, image of groundstation3 does not exist in infrared3, image of groundstation3 does not exist in spectrograph2, image of groundstation4 does not exist in spectrograph0, image of groundstation4 does not exist in spectrograph2, image of groundstation5 does not exist in spectrograph2, image of groundstation5 does not exist in thermograph4, image of groundstation9 does not exist in spectrograph0, image of groundstation9 does not exist in spectrograph1, image of phenomenon15 does not exist in spectrograph2, image of phenomenon15 does not exist in thermograph4, image of planet14 does not exist in infrared3, image of planet14 does not exist in spectrograph0, image of planet14 does not exist in spectrograph2, image of planet14 does not exist in thermograph4, image of star0 does not exist in infrared3, image of star0 does not exist in spectrograph0, image of star0 does not exist in spectrograph1, image of star1 does not exist in infrared3, image of star10 does not exist in infrared3, image of star10 does not exist in spectrograph2, image of star11 does not exist in spectrograph2, image of star12 does not exist in infrared3, image of star12 does not exist in spectrograph0, image of star12 does not exist in thermograph4, image of star13 does not exist in spectrograph2, image of star16 does not exist in spectrograph0, image of star16 does not exist in spectrograph2, image of star6 does not exist in infrared3, image of star6 does not exist in spectrograph2, image of star7 does not exist in infrared3, image of star7 does not exist in spectrograph2, image of star8 does not exist in infrared3, image of star8 does not exist in spectrograph0, image of star8 does not exist in spectrograph1, instrument1 is not calibrated, instrument1 is not turned on, instrument2 is not turned on, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph1, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation3 in spectrograph0, there is no image of direction groundstation3 in spectrograph1, there is no image of direction groundstation3 in thermograph4, there is no image of direction groundstation4 in infrared3, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in thermograph4, there is no image of direction groundstation5 in infrared3, there is no image of direction groundstation5 in spectrograph0, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph2, there is no image of direction groundstation9 in thermograph4, there is no image of direction phenomenon15 in infrared3, there is no image of direction phenomenon15 in spectrograph0, there is no image of direction phenomenon15 in spectrograph1, there is no image of direction star0 in spectrograph2, there is no image of direction star0 in thermograph4, there is no image of direction star1 in spectrograph0, there is no image of direction star1 in spectrograph1, there is no image of direction star1 in spectrograph2, there is no image of direction star1 in thermograph4, there is no image of direction star10 in spectrograph0, there is no image of direction star10 in thermograph4, there is no image of direction star11 in infrared3, there is no image of direction star11 in spectrograph0, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in thermograph4, there is no image of direction star12 in spectrograph2, there is no image of direction star13 in infrared3, there is no image of direction star13 in spectrograph0, there is no image of direction star13 in spectrograph1, there is no image of direction star13 in thermograph4, there is no image of direction star16 in infrared3, there is no image of direction star16 in spectrograph1, there is no image of direction star16 in thermograph4, there is no image of direction star6 in spectrograph0, there is no image of direction star6 in spectrograph1, there is no image of direction star6 in thermograph4, there is no image of direction star7 in spectrograph0, there is no image of direction star7 in spectrograph1, there is no image of direction star7 in thermograph4, there is no image of direction star8 in spectrograph2 and there is no image of direction star8 in thermograph4?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from groundstation4 to star6, the calibration of instrument3 on satellite1 to star6 is completed, satellite1 then reorients from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, and finally, satellite1 reorients from star10 to star12 and captures an image of star12 using instrument3 on satellite1 in spectrograph1, resulting in the current state. In this state, are the following properties that involve negations True or False: the calibration of instrument0 is incomplete, the calibration of instrument2 is incomplete, no image of groundstation2 exists in spectrograph0, no image of groundstation2 exists in spectrograph2, no image of groundstation3 exists in infrared3, no image of groundstation3 exists in spectrograph2, no image of groundstation4 exists in spectrograph0, no image of groundstation4 exists in spectrograph2, no image of groundstation5 exists in spectrograph2, no image of groundstation5 exists in thermograph4, no image of groundstation9 exists in spectrograph0, no image of groundstation9 exists in spectrograph1, no image of phenomenon15 exists in spectrograph2, no image of phenomenon15 exists in thermograph4, no image of planet14 exists in infrared3, no image of planet14 exists in spectrograph0, no image of planet14 exists in spectrograph2, no image of planet14 exists in thermograph4, no image of star0 exists in infrared3, no image of star0 exists in spectrograph0, no image of star0 exists in spectrograph1, no image of star1 exists in infrared3, no image of star10 exists in infrared3, no image of star10 exists in spectrograph2, no image of star11 exists in spectrograph2, no image of star12 exists in infrared3, no image of star12 exists in spectrograph0, no image of star12 exists in thermograph4, no image of star13 exists in spectrograph2, no image of star16 exists in spectrograph0, no image of star16 exists in spectrograph2, no image of star6 exists in infrared3, no image of star6 exists in spectrograph2, no image of star7 exists in infrared3, no image of star7 exists in spectrograph2, no image of star8 exists in infrared3, no image of star8 exists in spectrograph0, no image of star8 exists in spectrograph1, instrument1 is not calibrated, instrument1 is not activated, instrument2 is not activated, there is no image of groundstation2 in infrared3, there is no image of groundstation2 in spectrograph1, there is no image of groundstation2 in thermograph4, there is no image of groundstation3 in spectrograph0, there is no image of groundstation3 in spectrograph1, there is no image of groundstation3 in thermograph4, there is no image of groundstation4 in infrared3, there is no image of groundstation4 in spectrograph1, there is no image of groundstation4 in thermograph4, there is no image of groundstation5 in infrared3, there is no image of groundstation5 in spectrograph0, there is no image of groundstation5 in spectrograph1, there is no image of groundstation9 in infrared3, there is no image of groundstation9 in spectrograph2, there is no image of groundstation9 in thermograph4, there is no image of phenomenon15 in infrared3, there is no image of phenomenon15 in spectrograph0, there is no image of phenomenon15 in spectrograph1, there is no image of star0 in spectrograph2, there is no image of star0 in thermograph4, there is no image of star1 in spectrograph0, there is no image of star1 in spectrograph1, there is no image of star1 in spectrograph2, there is no image of star1 in thermograph4, there is no image of star10 in spectrograph0, there is no image of star10 in thermograph4, there is no image of star11 in infrared3, there is no image of star11 in spectrograph0, there is no image of star11 in spectrograph1, there is no image of star11 in thermograph4, there is no image of star12 in spectrograph2, there is no image of star13 in infrared3, there is no image of star13 in spectrograph0, there is no image of star13 in spectrograph1, there is no image of star13 in thermograph4, there is no image of star16 in infrared3, there is no image of star16 in spectrograph1, there is no image of star16 in thermograph4, there is no image of star6 in spectrograph0, there is no image of star6 in spectrograph1, there is no image of star6 in thermograph4, there is no image of star7 in spectrograph0, there is no image of star7 in spectrograph1, there is no image of star7 in thermograph4, there is no image of star8 in spectrograph2, and there is no image of star8 in thermograph4?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3, spectrograph0, and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 and spectrograph1 are compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "4b5c4783-4116-4e02-80fe-894bec7bacb0", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, satellite1 turns from star3 to star1, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns from star1 to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, image of phenomenon5 is taken with instrument1 on satellite1 in image5, from phenomenon5, satellite1 turns to phenomenon7, image of phenomenon7 is taken with instrument1 on satellite1 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, satellite1 turns from phenomenon7 to phenomenon9, instrument1 which is on satellite1 takes an image of phenomenon9 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: satellite1 does not have power available?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then reorients from star1 to phenomenon10, satellite1's instrument1 captures an image of phenomenon10 in image5, instrument1 on satellite1 also captures an image of phenomenon10 in spectrograph3, satellite1 reorients from phenomenon10 to phenomenon11, an image of phenomenon11 is captured with instrument1 on satellite1 in spectrograph1, satellite1 then reorients from phenomenon11 to phenomenon5, instrument1 on satellite1 captures an image of phenomenon5 in image4, another image of phenomenon5 is captured with instrument1 on satellite1 in image5, from phenomenon5, satellite1 reorients to phenomenon7, an image of phenomenon7 is captured with instrument1 on satellite1 in image0, another image of phenomenon7 is captured with instrument1 on satellite1 in image4, satellite1 reorients from phenomenon7 to phenomenon9, instrument1 on satellite1 captures an image of phenomenon9 in image5, satellite1's instrument1 also captures an image of phenomenon9 in spectrograph1, and finally, from phenomenon9, satellite1 reorients to planet8 and instrument1 on satellite1 captures an image of planet8 in image5 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: is it true that satellite1 does not have power available?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on board satellite1. Satellite0 and satellite1 both have available power. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently pointed towards star3."}
{"question_id": "6c1fc871-e08a-4612-a2c3-9db86e581dfc", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: phenomenon15 is not where satellite0 is pointed, planet14 is not where satellite0 is pointed, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation3, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards star0, satellite0 is not aimed towards star10, satellite0 is not aimed towards star12, satellite0 is not aimed towards star7, satellite0 is not pointing to star11, satellite0 is not pointing to star6, satellite0 is not pointing to star8, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards planet14, satellite1 is not aimed towards star1, satellite1 is not aimed towards star8, satellite1 is not pointing to groundstation9, satellite1 is not pointing to star10, satellite1 is not pointing to star13, star0 is not where satellite1 is pointed, star11 is not where satellite1 is pointed, star12 is not where satellite1 is pointed, star13 is not where satellite0 is pointed, star16 is not where satellite0 is pointed, star16 is not where satellite1 is pointed, star6 is not where satellite1 is pointed and star7 is not where satellite1 is pointed?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, are the following properties that involve negations True or False: satellite0 is not pointing at phenomenon15, satellite0 is not pointing at planet14, satellite0 is not aimed at groundstation2, groundstation3, groundstation4, groundstation5, or groundstation9, and satellite0 is not aimed at star0, star10, star12, or star7, satellite0 is not pointing towards star11, star6, or star8, satellite1 is not aimed at groundstation2, groundstation3, groundstation5, phenomenon15, planet14, star1, or star8, satellite1 is not pointing at groundstation9, star10, or star13, satellite1 is not pointed at star0, star11, star12, star6, or star7, and satellite0 is not pointed at star13 or star16, and satellite1 is not pointed at star16.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "74e57b12-4372-4ab6-bcf5-e1f8925749d4", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_11", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, from star3, satellite1 turns to star1, calibration of instrument1 which is on satellite1 to star1 is complete, from star1, satellite1 turns to phenomenon10, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5, image of phenomenon5 is taken with instrument1 on satellite1 in image4, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1 turns to phenomenon7 from phenomenon5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, satellite1's instrument1 takes an image of phenomenon7 in image4, satellite1 turns from phenomenon7 to phenomenon9, satellite1's instrument1 takes an image of phenomenon9 in image5, instrument1 which is on satellite1 takes an image of phenomenon9 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and satellite1's instrument1 takes an image of planet8 in image5 to reach the current state. In this state, is it True or False that satellite0 is not aimed towards phenomenon7?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: the instrument1 on satellite1 is activated, satellite1 shifts its orientation from star3 to star1, and instrument1 on satellite1 is calibrated to star1. Then, satellite1 turns from star1 to phenomenon10, and instrument1 on satellite1 captures an image of phenomenon10 in both image5 and spectrograph3. Satellite1 then reorients from phenomenon10 to phenomenon11, and instrument1 on satellite1 takes an image of phenomenon11 in spectrograph1. Next, satellite1 turns from phenomenon11 to phenomenon5, and instrument1 on satellite1 captures an image of phenomenon5 in both image4 and image5. Satellite1 then shifts its orientation from phenomenon5 to phenomenon7, and instrument1 on satellite1 takes an image of phenomenon7 in both image0 and image4. After that, satellite1 turns from phenomenon7 to phenomenon9, and instrument1 on satellite1 captures an image of phenomenon9 in both image5 and spectrograph1. Finally, satellite1 reorients from phenomenon9 to planet8, and instrument1 on satellite1 takes an image of planet8 in image5, resulting in the current state. In this state, is it True or False that satellite0 is not aimed towards phenomenon7?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on board satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently pointed towards star3."}
{"question_id": "ee0a8ec1-990d-40a2-9679-7d4f712a9ee4", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 on satellite0 is switched off, instrument2 on satellite0 is switched on, satellite0 turns to star4 from star10, instrument2 is calibrated on satellite0 to star4, satellite0 turns to star16 from star4, instrument2 which is on satellite0 takes an image of star16 in image0 and instrument2 that is on satellite0 is turned off to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument1 is incomplete, calibration of instrument2 is incomplete, calibration of instrument4 is incomplete, image of groundstation0 does not exist in image6, image of groundstation0 does not exist in spectrograph2, image of groundstation0 does not exist in spectrograph4, image of groundstation1 does not exist in image1, image of groundstation1 does not exist in image5, image of groundstation1 does not exist in image6, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in spectrograph2, image of groundstation1 does not exist in spectrograph4, image of groundstation1 does not exist in thermograph3, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image1, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in infrared7, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in infrared7, image of groundstation7 does not exist in image1, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in infrared7, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in spectrograph4, image of groundstation8 does not exist in image0, image of groundstation8 does not exist in infrared7, image of groundstation8 does not exist in spectrograph4, image of groundstation9 does not exist in image0, image of groundstation9 does not exist in image6, image of groundstation9 does not exist in infrared7, image of groundstation9 does not exist in spectrograph2, image of phenomenon14 does not exist in image6, image of phenomenon14 does not exist in infrared7, image of phenomenon14 does not exist in spectrograph4, image of phenomenon15 does not exist in infrared7, image of planet11 does not exist in image1, image of planet11 does not exist in image6, image of planet11 does not exist in spectrograph2, image of planet11 does not exist in thermograph3, image of planet13 does not exist in image6, image of planet13 does not exist in spectrograph2, image of star10 does not exist in image0, image of star10 does not exist in image6, image of star12 does not exist in image0, image of star12 does not exist in image1, image of star12 does not exist in infrared7, image of star12 does not exist in spectrograph4, image of star16 does not exist in image1, image of star16 does not exist in infrared7, image of star16 does not exist in spectrograph4, image of star16 does not exist in thermograph3, image of star3 does not exist in image1, image of star3 does not exist in image5, image of star3 does not exist in image6, image of star3 does not exist in infrared7, image of star3 does not exist in spectrograph2, image of star4 does not exist in image0, image of star4 does not exist in image6, image of star4 does not exist in spectrograph2, instrument0 is not calibrated, instrument0 is not powered on, instrument1 is not turned on, instrument2 is not turned on, instrument3 is not calibrated, instrument3 is not switched on, instrument4 is not powered on, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image1, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image0, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image6, there is no image of direction groundstation5 in image5, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation6 in spectrograph4, there is no image of direction groundstation6 in thermograph3, there is no image of direction groundstation7 in image0, there is no image of direction groundstation7 in image5, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in image1, there is no image of direction groundstation8 in image5, there is no image of direction groundstation8 in image6, there is no image of direction groundstation8 in spectrograph2, there is no image of direction groundstation8 in thermograph3, there is no image of direction groundstation9 in image1, there is no image of direction groundstation9 in image5, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image0, there is no image of direction phenomenon14 in image1, there is no image of direction phenomenon14 in image5, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon14 in thermograph3, there is no image of direction phenomenon15 in image0, there is no image of direction phenomenon15 in image1, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in image6, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in spectrograph4, there is no image of direction phenomenon15 in thermograph3, there is no image of direction planet11 in image0, there is no image of direction planet11 in image5, there is no image of direction planet11 in infrared7, there is no image of direction planet11 in spectrograph4, there is no image of direction planet13 in image0, there is no image of direction planet13 in image1, there is no image of direction planet13 in image5, there is no image of direction planet13 in infrared7, there is no image of direction planet13 in spectrograph4, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image1, there is no image of direction star10 in image5, there is no image of direction star10 in infrared7, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in spectrograph4, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image5, there is no image of direction star12 in image6, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image0, there is no image of direction star16 in image5, there is no image of direction star16 in image6, there is no image of direction star16 in spectrograph2, there is no image of direction star3 in image0, there is no image of direction star3 in spectrograph4, there is no image of direction star3 in thermograph3, there is no image of direction star4 in image1, there is no image of direction star4 in image5, there is no image of direction star4 in infrared7, there is no image of direction star4 in spectrograph4 and there is no image of direction star4 in thermograph3?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated, satellite0 changes its orientation from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then turns to planet11 from groundstation0, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 changes its orientation from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 and stores it in image5, satellite0's instrument1 takes a spectrograph of planet13 in spectrograph2, satellite0 then turns to star10 from planet13, instrument1 on satellite0 captures an image of star10 and stores it in image6, satellite0's instrument1 takes a spectrograph of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 changes its orientation from star10 to star4, instrument2 is calibrated on satellite0 to star4, satellite0 then turns to star16 from star4, instrument2 on satellite0 captures an image of star16 and stores it in image0, and instrument2 on satellite0 is deactivated to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument1 is incomplete, calibration of instrument2 is incomplete, calibration of instrument4 is incomplete, image of groundstation0 does not exist in image6, image of groundstation0 does not exist in spectrograph2, image of groundstation0 does not exist in spectrograph4, image of groundstation1 does not exist in image1, image of groundstation1 does not exist in image5, image of groundstation1 does not exist in image6, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in spectrograph2, image of groundstation1 does not exist in spectrograph4, image of groundstation1 does not exist in thermograph3, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image1, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in infrared7, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in infrared7, image of groundstation7 does not exist in image1, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in infrared7, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in spectrograph4, image of groundstation8 does not exist in image0, image of groundstation8 does not exist in infrared7, image of groundstation8 does not exist in spectrograph4, image of groundstation9 does not exist in image0, image of groundstation9 does not exist in image6, image of groundstation9 does not exist in infrared7, image of groundstation9 does not exist in spectrograph2, image of phenomenon14 does not exist in image6, image of phenomenon14 does not exist in infrared7, image of phenomenon14 does not exist in spectrograph4, image of phenomenon15 does not exist in infrared7, image of planet11 does not exist in image1, image of planet11 does not exist in image6, image of planet11 does not exist in spectrograph2, image of planet11 does not exist in thermograph3, image of planet13 does not exist in image6, image of planet13 does not exist in spectrograph2, image of star10 does not exist in image0, image of star10 does not exist in image6, image of star12 does not exist in image0, image of star12 does not exist in image1, image of star12 does not exist in infrared7, image of star12 does not exist in spectrograph4, image of star16 does not exist in image1, image of star16 does not exist in infrared7, image of star16 does not exist in spectrograph4, image of star16 does not exist in thermograph3, image of star3 does not exist in image1, image of star3 does not exist in image5, image of star3 does not exist in image6, image of star3 does not exist in infrared7, image of star3 does not exist in spectrograph2, image of star4 does not exist in image0, image of star4 does not exist in image6, image of star4 does not exist in spectrograph2, instrument0 is not calibrated, instrument0 is not powered on, instrument1 is not turned on, instrument2 is not turned on, instrument3 is not calibrated, instrument3 is not switched on, instrument4 is not powered on, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image1, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image0, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image6, there is no image of direction groundstation5 in image5, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation6 in spectrograph4, there is no image of direction groundstation6 in thermograph3, there is no image of direction groundstation7 in image0, there is no image of direction groundstation7 in image5, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in image1, there is no image of direction groundstation8 in image5, there is no image of direction groundstation8 in image6, there is no image of direction groundstation8 in spectrograph2, there is no image of direction groundstation8 in thermograph3, there is no image of direction groundstation9 in image1, there is no image of direction groundstation9 in image5, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image0, there is no image of direction phenomenon14 in image1, there is no image of direction phenomenon14 in image5, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon14 in thermograph3, there is no image of direction phenomenon15 in image0, there is no image of direction phenomenon15 in image1, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in image6, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in spectrograph4, there is no image of direction phenomenon15 in thermograph3, there is no image of direction planet11 in image0, there is no image of direction planet11 in image5, there is no image of direction planet11 in infrared7, there is no image of direction planet11 in spectrograph4, there is no image of direction planet13 in image0, there is no image of direction planet13 in image1, there is no image of direction planet13 in image5, there is no image of direction planet13 in infrared7, there is no image of direction planet13 in spectrograph4, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image1, there is no image of direction star10 in image5, there is no image of direction star10 in infrared7, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in spectrograph4, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image5, there is no image of direction star12 in image6, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image0, there is no image of direction star16 in image5, there is no image of direction star16 in image6, there is no image of direction star16 in spectrograph2, there is no image of direction star3 in image0, there is no image of direction star3 in spectrograph4, there is no image of direction star3 in thermograph3, there is no image of direction star4 in image1, there is no image of direction star4 in image5, there is no image of direction star4 in infrared7, there is no image of direction star4 in spectrograph4 and there is no image of direction star4 in thermograph3?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board."}
{"question_id": "9ab94038-59d4-4238-bfdd-f58d2c502991", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_20", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, from phenomenon16, satellite1 turns to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, satellite1 turns to planet11 from phenomenon17 and instrument3 which is on satellite1 takes an image of planet11 in image3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for planet11 is complete, calibration of instrument0 for star9 is complete, calibration of instrument1 for groundstation0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for groundstation7 is complete, calibration of instrument1 for phenomenon17 is complete, calibration of instrument1 for planet13 is complete, calibration of instrument1 for star15 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation2 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for phenomenon17 is complete, calibration of instrument2 for planet12 is complete, calibration of instrument2 for planet13 is complete, calibration of instrument2 for planet14 is complete, calibration of instrument2 for star15 is complete, calibration of instrument2 for star8 is complete, calibration of instrument3 for groundstation3 is complete, calibration of instrument3 for planet11 is complete, calibration of instrument3 for star15 is complete, calibration of instrument3 for star8 is complete, for groundstation0, instrument0 is calibrated, for groundstation0, instrument2 is calibrated, for groundstation2, instrument3 is calibrated, for groundstation3, instrument0 is calibrated, for groundstation3, instrument1 is calibrated, for groundstation4, instrument1 is calibrated, for groundstation5, instrument2 is calibrated, for phenomenon10, instrument1 is calibrated, for phenomenon10, instrument3 is calibrated, for phenomenon16, instrument0 is calibrated, for phenomenon16, instrument2 is calibrated, for phenomenon16, instrument3 is calibrated, for phenomenon17, instrument0 is calibrated, for phenomenon17, instrument3 is calibrated, for planet11, instrument1 is calibrated, for planet13, instrument0 is calibrated, for planet14, instrument0 is calibrated, for planet14, instrument1 is calibrated, for planet14, instrument3 is calibrated, for star1, instrument1 is calibrated, for star1, instrument2 is calibrated, for star1, instrument3 is calibrated, for star6, instrument3 is calibrated, for star9, instrument1 is calibrated, image0 is compatible with instrument0, image0 is supported by instrument1, image0 is supported by instrument2, image0 is supported by instrument3, image2 is compatible with instrument1, image2 is supported by instrument2, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is compatible with instrument2, infrared1 is compatible with instrument1, infrared1 is supported by instrument0, infrared1 is supported by instrument2, instrument0 is calibrated for groundstation4, instrument0 is calibrated for groundstation5, instrument0 is calibrated for groundstation7, instrument0 is calibrated for phenomenon10, instrument0 is calibrated for planet12, instrument0 is calibrated for star1, instrument0 is calibrated for star15, instrument0 is calibrated for star6, instrument0 is calibrated for star8, instrument0 is on board satellite0, instrument0 supports image2, instrument1 is calibrated for groundstation5, instrument1 is calibrated for phenomenon16, instrument1 is calibrated for planet12, instrument1 is calibrated for star6, instrument1 is on board satellite1, instrument2 is calibrated for groundstation3, instrument2 is calibrated for groundstation4, instrument2 is calibrated for phenomenon10, instrument2 is calibrated for planet11, instrument2 is calibrated for star6, instrument2 is calibrated for star9, instrument3 is calibrated for groundstation0, instrument3 is calibrated for groundstation4, instrument3 is calibrated for groundstation5, instrument3 is calibrated for groundstation7, instrument3 is calibrated for planet12, instrument3 is calibrated for planet13, instrument3 is calibrated for star9, instrument3 is on board satellite0, instrument3 is on board satellite1, instrument3 supports image2, instrument3 supports image3, instrument3 supports infrared1, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite1 carries instrument2 on board and satellite1 has instrument0 on board?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated for groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured using instrument3 on satellite1 and stored in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 using instrument3 on satellite1 and storing it in image3, resulting in the current state. In this state, are the following properties that do not involve negations True or False: instrument0 is calibrated for groundstation2, instrument0 is calibrated for planet11, instrument0 is calibrated for star9, instrument1 is calibrated for groundstation0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation7, instrument1 is calibrated for phenomenon17, instrument1 is calibrated for planet13, instrument1 is calibrated for star15, instrument1 is calibrated for star8, instrument2 is calibrated for groundstation2, instrument2 is calibrated for groundstation7, instrument2 is calibrated for phenomenon17, instrument2 is calibrated for planet12, instrument2 is calibrated for planet13, instrument2 is calibrated for planet14, instrument2 is calibrated for star15, instrument2 is calibrated for star8, instrument3 is calibrated for groundstation3, instrument3 is calibrated for planet11, instrument3 is calibrated for star15, instrument3 is calibrated for star8, instrument0 is calibrated for groundstation0, instrument2 is calibrated for groundstation0, instrument3 is calibrated for groundstation2, instrument0 is calibrated for groundstation3, instrument1 is calibrated for groundstation3, instrument1 is calibrated for groundstation4, instrument2 is calibrated for groundstation5, instrument1 is calibrated for phenomenon10, instrument3 is calibrated for phenomenon10, instrument0 is calibrated for phenomenon16, instrument2 is calibrated for phenomenon16, instrument3 is calibrated for phenomenon16, instrument0 is calibrated for phenomenon17, instrument3 is calibrated for phenomenon17, instrument1 is calibrated for planet11, instrument0 is calibrated for planet13, instrument0 is calibrated for planet14, instrument1 is calibrated for planet14, instrument3 is calibrated for planet14, instrument1 is calibrated for star1, instrument2 is calibrated for star1, instrument3 is calibrated for star1, instrument3 is calibrated for star6, instrument1 is calibrated for star9, image0 is compatible with instrument0, image0 is supported by instrument1, image0 is supported by instrument2, image0 is supported by instrument3, image2 is compatible with instrument1, image2 is supported by instrument2, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is compatible with instrument2, infrared1 is compatible with instrument1, infrared1 is supported by instrument0, infrared1 is supported by instrument2, instrument0 is calibrated for groundstation4, instrument0 is calibrated for groundstation5, instrument0 is calibrated for groundstation7, instrument0 is calibrated for phenomenon10, instrument0 is calibrated for planet12, instrument0 is calibrated for star1, instrument0 is calibrated for star15, instrument0 is calibrated for star6, instrument0 is calibrated for star8, instrument0 is on satellite0, instrument0 supports image2, instrument1 is calibrated for groundstation5, instrument1 is calibrated for phenomenon16, instrument1 is calibrated for planet12, instrument1 is calibrated for star6, instrument1 is on satellite1, instrument2 is calibrated for groundstation3, instrument2 is calibrated for groundstation4, instrument2 is calibrated for phenomenon10, instrument2 is calibrated for planet11, instrument2 is calibrated for star6, instrument2 is calibrated for star9, instrument3 is calibrated for groundstation0, instrument3 is calibrated for groundstation4, instrument3 is calibrated for groundstation5, instrument3 is calibrated for groundstation7, instrument3 is calibrated for planet12, instrument3 is calibrated for planet13, instrument3 is calibrated for star9, instrument3 is on satellite0 and satellite1, instrument3 supports image2, image3, and infrared1, satellite0 has instrument1 and instrument2 on board, satellite1 has instrument2 on board, and satellite1 has instrument0 on board?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. Instrument1 has been calibrated for groundstation0, instrument0 for star1, and instrument3 for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, while image3 is compatible with both instrument2 and instrument3, and also supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 has been calibrated for groundstation7, and instrument3 for both groundstation5 and star6. Instrument3 also supports image2. Satellite0 has instrument0 and instrument2 on board, and it has power available while pointing towards groundstation3. Satellite1, which carries instrument3, has power and is currently pointing towards phenomenon10."}
{"question_id": "17687e10-3644-4d01-8465-7efa55156479", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, from phenomenon10, satellite1 turns to groundstation5, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns to phenomenon17 from phenomenon16, image of phenomenon17 is taken with instrument3 on satellite1 in image3, from phenomenon17, satellite1 turns to planet11 and instrument3 which is on satellite1 takes an image of planet11 in image3 to reach the current state. In this state, is it True or False that instrument3 is not calibrated for groundstation4?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 transitions from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, then satellite1 transitions from groundstation5 to phenomenon16, and instrument3 on satellite1 captures an image of phenomenon16 in image3. Next, satellite1 moves from phenomenon16 to phenomenon17, and instrument3 on satellite1 takes an image of phenomenon17 in image3. Finally, satellite1 transitions from phenomenon17 to planet11, and instrument3 on satellite1 captures an image of planet11 in image3, resulting in the current state. In this state, is it True or False that instrument3 is not calibrated for groundstation4?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current pointing direction being groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "8d1a3cf5-4aca-4b6b-a5d2-abc79ce951a1", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, satellite1 turns from phenomenon10 to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: power is not available for satellite0 and power is not available for satellite1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 with instrument3 in image3, resulting in the current state. In this state, are the following properties that involve negations True or False: is power unavailable for satellite0 and is power unavailable for satellite1?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with star9 has been finalized, instrument2's calibration for groundstation5 is complete, and instrument2's calibration for star9 is also complete. Instrument1 has been calibrated for groundstation0, instrument0 for star1, and instrument3 for star8. Image0 is compatible with instrument3 and supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, as well as supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 has been calibrated for groundstation7, and instrument3 has been calibrated for groundstation5 and star6. Instrument3 supports image2. Satellite0 has instrument0 and instrument2 on board, has available power, and is currently pointing towards groundstation3. Satellite1 carries instrument3 on board, has power available, and is pointing towards phenomenon10."}
{"question_id": "cffa071a-e573-49ce-bf28-1316eab0a71c", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on to reach the current state. In this state, is it True or False that satellite0 does not have power?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is activated to achieve the current state. In this state, is it True or False that satellite0 lacks power?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently directed towards star3."}
{"question_id": "bcd9498a-b96c-47fe-9f93-ab3f1b7b62ae", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: groundstation2 is not where satellite1 is pointed, groundstation3 is not where satellite1 is pointed, planet14 is not where satellite0 is pointed, satellite0 is not aimed towards groundstation3, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards phenomenon15, satellite0 is not aimed towards star1, satellite0 is not aimed towards star11, satellite0 is not aimed towards star7, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation5, satellite0 is not pointing to groundstation9, satellite0 is not pointing to star0, satellite0 is not pointing to star12, satellite0 is not pointing to star13, satellite0 is not pointing to star16, satellite0 is not pointing to star6, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards groundstation9, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards star0, satellite1 is not aimed towards star1, satellite1 is not aimed towards star8, satellite1 is not pointing to groundstation4, satellite1 is not pointing to planet14, satellite1 is not pointing to star10, satellite1 is not pointing to star11, satellite1 is not pointing to star13, satellite1 is not pointing to star7, star10 is not where satellite0 is pointed, star12 is not where satellite1 is pointed, star16 is not where satellite1 is pointed, star6 is not where satellite1 is pointed and star8 is not where satellite0 is pointed?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, are the following properties that involve negations True or False: satellite1 is not oriented towards groundstation2, satellite1 is not oriented towards groundstation3, satellite0 is not directed at planet14, satellite0 is not aimed at groundstation3, satellite0 is not aimed at groundstation4, satellite0 is not aimed at phenomenon15, satellite0 is not aimed at star1, satellite0 is not aimed at star11, satellite0 is not aimed at star7, satellite0 is not pointing towards groundstation2, satellite0 is not pointing towards groundstation5, satellite0 is not pointing towards groundstation9, satellite0 is not pointing towards star0, satellite0 is not pointing towards star12, satellite0 is not pointing towards star13, satellite0 is not pointing towards star16, satellite0 is not pointing towards star6, satellite1 is not aimed at groundstation5, satellite1 is not aimed at groundstation9, satellite1 is not aimed at phenomenon15, satellite1 is not aimed at star0, satellite1 is not aimed at star1, satellite1 is not aimed at star8, satellite1 is not pointing towards groundstation4, satellite1 is not pointing towards planet14, satellite1 is not pointing towards star10, satellite1 is not pointing towards star11, satellite1 is not pointing towards star13, satellite1 is not pointing towards star7, satellite0 is not oriented towards star10, satellite1 is not oriented towards star12, satellite1 is not oriented towards star16, satellite1 is not oriented towards star6, and satellite0 is not oriented towards star8?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is currently aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "1f857cc6-46b1-49f9-8bd7-7c6dbafcadb0", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for phenomenon10 is incomplete, calibration of instrument0 for phenomenon5 is incomplete, calibration of instrument0 for star1 is incomplete, calibration of instrument1 for phenomenon9 is incomplete, for groundstation2, instrument0 is not calibrated, for phenomenon10, instrument1 is not calibrated, for phenomenon11, instrument1 is not calibrated, for phenomenon7, instrument0 is not calibrated, for phenomenon7, instrument1 is not calibrated, for phenomenon9, instrument0 is not calibrated, for star6, instrument1 is not calibrated, image0 is not compatible with instrument0, image0 is not compatible with instrument1, image2 is not compatible with instrument1, image4 is not compatible with instrument1, image5 is not supported by instrument1, instrument0 does not support image2, instrument0 does not support image4, instrument0 does not support image5, instrument0 is not calibrated for groundstation4, instrument0 is not calibrated for phenomenon11, instrument0 is not calibrated for planet8, instrument0 is not calibrated for star3, instrument0 is not calibrated for star6, instrument1 does not support spectrograph3, instrument1 is not calibrated for groundstation0, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation4, instrument1 is not calibrated for phenomenon5, instrument1 is not calibrated for planet8, instrument1 is not calibrated for star1, instrument1 is not calibrated for star3, instrument1 is not on board satellite0, instrument1 is not on board satellite1, satellite0 does not have instrument0 on board, satellite1 does not have instrument0 on board, spectrograph1 is not supported by instrument0, spectrograph1 is not supported by instrument1 and spectrograph3 is not compatible with instrument0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: on satellite1, instrument1 is activated to achieve the current state. In this state, are the following properties that involve negations True or False: the calibration of instrument0 for groundstation0 is not complete, the calibration of instrument0 for phenomenon10 is not complete, the calibration of instrument0 for phenomenon5 is not complete, the calibration of instrument0 for star1 is not complete, the calibration of instrument1 for phenomenon9 is not complete, instrument0 is not calibrated for groundstation2, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for phenomenon11, instrument0 is not calibrated for phenomenon7, instrument1 is not calibrated for phenomenon7, instrument0 is not calibrated for phenomenon9, instrument1 is not calibrated for star6, instrument0 is incompatible with image0, instrument1 is incompatible with image0, instrument1 is incompatible with image2, instrument1 is incompatible with image4, instrument1 does not support image5, instrument0 does not support image2, instrument0 does not support image4, instrument0 does not support image5, instrument0 is not calibrated for groundstation4, instrument0 is not calibrated for phenomenon11, instrument0 is not calibrated for planet8, instrument0 is not calibrated for star3, instrument0 is not calibrated for star6, instrument1 does not support spectrograph3, instrument1 is not calibrated for groundstation0, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation4, instrument1 is not calibrated for phenomenon5, instrument1 is not calibrated for planet8, instrument1 is not calibrated for star1, instrument1 is not calibrated for star3, instrument1 is not installed on satellite0, instrument1 is installed on satellite1, satellite0 does not have instrument0 installed, satellite1 does not have instrument0 installed, spectrograph1 is not supported by instrument0, spectrograph1 is not supported by instrument1, and spectrograph3 is incompatible with instrument0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on board satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently pointed towards star3."}
{"question_id": "9afdf8a6-5416-496b-b16e-3778413d5974", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: for star1, instrument0 is calibrated, image0 is supported by instrument1, image4 is compatible with instrument1, instrument0 supports image2, instrument1 is calibrated for star1, instrument1 is on board satellite1, instrument1 supports image2, instrument1 supports image5, instrument1 supports spectrograph3, satellite0 carries instrument0 on board and spectrograph1 is compatible with instrument1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, are all of the following properties that do not involve negations True or False: for star1, instrument0 is calibrated, image0 is supported by instrument1, image4 is compatible with instrument1, instrument0 supports image2, instrument1 is calibrated for star1, instrument1 is installed on satellite1, instrument1 supports image2, instrument1 supports image5, instrument1 supports spectrograph3, satellite0 has instrument0 on board, and spectrograph1 is compatible with instrument1?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently directed towards star3."}
{"question_id": "8bf3f7d7-23a6-4a31-bdb0-f16f342260a3", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_14", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, is it True or False that for star15, instrument1 is calibrated?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: on satellite1, instrument3 is activated to achieve the current state. In this state, is it True or False that instrument1 is calibrated for star15?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, the calibration of instrument2 for groundstation5 is now complete, and the calibration of instrument2 for star9 has also been completed. Instrument1 has been calibrated for groundstation0, instrument0 has been calibrated for star1, and instrument3 has been calibrated for star8. Image0 is found to be compatible with instrument3, and it is also supported by instrument1. Instrument2 supports image2, while image3 is compatible with both instrument2 and instrument3, and is also supported by instrument1. Instrument0 is compatible with image3 and infrared1, and instrument1 supports infrared1. Instrument1 is installed on satellite0, which also carries instrument0 on board. Instrument3 is calibrated for groundstation5 and star6, and supports image2. Satellite0 has sufficient power and is currently pointing towards groundstation3. Satellite1, which carries instrument3 on board, has power available and is pointing towards phenomenon10."}
{"question_id": "8ff5767c-0439-4e3f-988a-57d7305cefe3", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, from phenomenon10, satellite1 turns to groundstation5, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and satellite1's instrument3 takes an image of planet11 in image3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 for phenomenon10 is incomplete, calibration of instrument0 for phenomenon16 is incomplete, calibration of instrument0 for phenomenon17 is incomplete, calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for planet12 is incomplete, calibration of instrument0 for planet14 is incomplete, calibration of instrument0 for star6 is incomplete, calibration of instrument1 for groundstation4 is incomplete, calibration of instrument1 for phenomenon16 is incomplete, calibration of instrument1 for planet12 is incomplete, calibration of instrument1 for planet14 is incomplete, calibration of instrument1 for star6 is incomplete, calibration of instrument1 for star8 is incomplete, calibration of instrument1 for star9 is incomplete, calibration of instrument2 for groundstation0 is incomplete, calibration of instrument2 for groundstation2 is incomplete, calibration of instrument2 for groundstation4 is incomplete, calibration of instrument2 for phenomenon16 is incomplete, calibration of instrument2 for star6 is incomplete, calibration of instrument2 for star9 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for phenomenon16 is incomplete, calibration of instrument3 for planet14 is incomplete, for groundstation0, instrument3 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation3, instrument0 is not calibrated, for groundstation3, instrument1 is not calibrated, for groundstation3, instrument3 is not calibrated, for groundstation5, instrument3 is not calibrated, for groundstation7, instrument0 is not calibrated, for phenomenon10, instrument2 is not calibrated, for phenomenon10, instrument3 is not calibrated, for planet11, instrument1 is not calibrated, for planet12, instrument3 is not calibrated, for planet13, instrument0 is not calibrated, for planet13, instrument1 is not calibrated, for star1, instrument2 is not calibrated, for star1, instrument3 is not calibrated, for star15, instrument0 is not calibrated, for star15, instrument3 is not calibrated, for star8, instrument0 is not calibrated, for star9, instrument0 is not calibrated, image0 is not compatible with instrument0, image0 is not supported by instrument2, image2 is not compatible with instrument1, image2 is not supported by instrument0, image3 is not compatible with instrument1, image3 is not compatible with instrument3, image3 is not supported by instrument2, infrared1 is not compatible with instrument1, infrared1 is not compatible with instrument2, infrared1 is not supported by instrument0, instrument0 does not support image3, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation4, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for star1, instrument0 is not on board satellite0, instrument1 does not support image0, instrument1 is not calibrated for groundstation0, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for groundstation7, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for phenomenon17, instrument1 is not calibrated for star1, instrument1 is not calibrated for star15, instrument1 is not on board satellite0, instrument2 does not support image2, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation7, instrument2 is not calibrated for phenomenon17, instrument2 is not calibrated for planet11, instrument2 is not calibrated for planet12, instrument2 is not calibrated for planet13, instrument2 is not calibrated for planet14, instrument2 is not calibrated for star15, instrument2 is not calibrated for star8, instrument2 is not on board satellite1, instrument3 does not support image0, instrument3 does not support image2, instrument3 does not support infrared1, instrument3 is not calibrated for groundstation2, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for phenomenon17, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star6, instrument3 is not calibrated for star8, instrument3 is not calibrated for star9, satellite0 does not carry instrument2 on board, satellite0 does not carry instrument3 on board, satellite1 does not carry instrument0 on board, satellite1 does not carry instrument3 on board and satellite1 does not have instrument1 on board?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 turns from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 turns from groundstation5 to phenomenon16, an image of phenomenon16 is captured with instrument3 on satellite1 in image3, satellite1 turns from phenomenon16 to phenomenon17, an image of phenomenon17 is captured with instrument3 on satellite1 in image3, and satellite1 turns from phenomenon17 to planet11, capturing an image of planet11 with instrument3 in image3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 for phenomenon10 is not complete, calibration of instrument0 for phenomenon16 is not complete, calibration of instrument0 for phenomenon17 is not complete, calibration of instrument0 for planet11 is not complete, calibration of instrument0 for planet12 is not complete, calibration of instrument0 for planet14 is not complete, calibration of instrument0 for star6 is not complete, calibration of instrument1 for groundstation4 is not complete, calibration of instrument1 for phenomenon16 is not complete, calibration of instrument1 for planet12 is not complete, calibration of instrument1 for planet14 is not complete, calibration of instrument1 for star6 is not complete, calibration of instrument1 for star8 is not complete, calibration of instrument1 for star9 is not complete, calibration of instrument2 for groundstation0 is not complete, calibration of instrument2 for groundstation2 is not complete, calibration of instrument2 for groundstation4 is not complete, calibration of instrument2 for phenomenon16 is not complete, calibration of instrument2 for star6 is not complete, calibration of instrument2 for star9 is not complete, calibration of instrument3 for groundstation4 is not complete, calibration of instrument3 for phenomenon16 is not complete, calibration of instrument3 for planet14 is not complete, instrument3 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation2, instrument0 is not calibrated for groundstation3, instrument1 is not calibrated for groundstation3, instrument3 is not calibrated for groundstation3, instrument3 is not calibrated for groundstation5, instrument0 is not calibrated for groundstation7, instrument2 is not calibrated for phenomenon10, instrument3 is not calibrated for phenomenon10, instrument1 is not calibrated for planet11, instrument3 is not calibrated for planet12, instrument0 is not calibrated for planet13, instrument1 is not calibrated for planet13, instrument2 is not calibrated for star1, instrument3 is not calibrated for star1, instrument0 is not calibrated for star15, instrument3 is not calibrated for star15, instrument0 is not calibrated for star8, instrument0 is not calibrated for star9, image0 is incompatible with instrument0, image0 is not supported by instrument2, image2 is incompatible with instrument1, image2 is not supported by instrument0, image3 is incompatible with instrument1, image3 is incompatible with instrument3, image3 is not supported by instrument2, infrared1 is incompatible with instrument1, infrared1 is incompatible with instrument2, infrared1 is not supported by instrument0, instrument0 does not support image3, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation4, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for star1, instrument0 is not on board satellite0, instrument1 does not support image0, instrument1 is not calibrated for groundstation0, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for groundstation7, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for phenomenon17, instrument1 is not calibrated for star1, instrument1 is not calibrated for star15, instrument1 is not on board satellite0, instrument2 does not support image2, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation7, instrument2 is not calibrated for phenomenon17, instrument2 is not calibrated for planet11, instrument2 is not calibrated for planet12, instrument2 is not calibrated for planet13, instrument2 is not calibrated for planet14, instrument2 is not calibrated for star15, instrument2 is not calibrated for star8, instrument2 is not on board satellite1, instrument3 does not support image0, instrument3 does not support image2, instrument3 does not support infrared1, instrument3 is not calibrated for groundstation2, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for phenomenon17, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star6, instrument3 is not calibrated for star8, instrument3 is not calibrated for star9, satellite0 does not have instrument2 on board, satellite0 does not have instrument3 on board, satellite1 does not have instrument0 on board, satellite1 does not have instrument3 on board, and satellite1 does not have instrument1 on board?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3, and it is also supported by instrument1. Instrument2 supports image2, image3 is compatible with both instrument2 and instrument3, and it is also supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1 is installed on satellite0 and supports infrared1. Instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5 and star6, and it supports image2. Satellite0 has instrument0 and instrument2 on board, has available power, and is currently pointing towards groundstation3. Satellite1 carries instrument3 on board, has power available, and is pointing towards phenomenon10."}
{"question_id": "7efb00b7-9365-4f0e-8475-a9f5b0883328", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, is it True or False that instrument3 is switched on?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, is it True or False that instrument3 is activated?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3, spectrograph0, and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 and spectrograph1 are compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "4a73b33a-2b30-4ad4-96ef-3350e102a8c7", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 is incomplete, calibration of instrument1 is incomplete, image of groundstation0 does not exist in image3, image of groundstation0 does not exist in infrared1, image of groundstation3 does not exist in image0, image of groundstation3 does not exist in image3, image of groundstation3 does not exist in infrared1, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image2, image of groundstation4 does not exist in image3, image of groundstation5 does not exist in image0, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image3, image of phenomenon10 does not exist in infrared1, image of phenomenon16 does not exist in image0, image of phenomenon16 does not exist in image2, image of phenomenon17 does not exist in image0, image of phenomenon17 does not exist in image3, image of planet11 does not exist in image0, image of planet11 does not exist in image2, image of planet11 does not exist in image3, image of planet11 does not exist in infrared1, image of planet12 does not exist in image2, image of planet13 does not exist in image0, image of planet13 does not exist in image2, image of planet13 does not exist in image3, image of planet13 does not exist in infrared1, image of planet14 does not exist in image0, image of star1 does not exist in image0, image of star1 does not exist in image3, image of star15 does not exist in image2, image of star15 does not exist in image3, image of star15 does not exist in infrared1, image of star6 does not exist in image0, image of star6 does not exist in image3, image of star8 does not exist in image2, image of star8 does not exist in image3, image of star9 does not exist in image0, image of star9 does not exist in image2, image of star9 does not exist in image3, instrument0 is not powered on, instrument1 is not powered on, instrument2 is not calibrated, instrument2 is not powered on, instrument3 is not calibrated, instrument3 is not powered on, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image2, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image2, there is no image of direction groundstation2 in image3, there is no image of direction groundstation2 in infrared1, there is no image of direction groundstation3 in image2, there is no image of direction groundstation4 in infrared1, there is no image of direction groundstation5 in image2, there is no image of direction groundstation5 in image3, there is no image of direction groundstation5 in infrared1, there is no image of direction groundstation7 in image2, there is no image of direction groundstation7 in infrared1, there is no image of direction phenomenon10 in image0, there is no image of direction phenomenon10 in image2, there is no image of direction phenomenon10 in image3, there is no image of direction phenomenon16 in image3, there is no image of direction phenomenon16 in infrared1, there is no image of direction phenomenon17 in image2, there is no image of direction phenomenon17 in infrared1, there is no image of direction planet12 in image0, there is no image of direction planet12 in image3, there is no image of direction planet12 in infrared1, there is no image of direction planet14 in image2, there is no image of direction planet14 in image3, there is no image of direction planet14 in infrared1, there is no image of direction star1 in image2, there is no image of direction star1 in infrared1, there is no image of direction star15 in image0, there is no image of direction star6 in image2, there is no image of direction star6 in infrared1, there is no image of direction star8 in image0, there is no image of direction star8 in infrared1 and there is no image of direction star9 in infrared1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 changes its orientation from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, then satellite1 changes its orientation from groundstation5 to phenomenon16, an image of phenomenon16 is captured by instrument3 on satellite1 and stored in image3, satellite1 then changes its orientation from phenomenon16 to phenomenon17, an image of phenomenon17 is captured by instrument3 on satellite1 and stored in image3, and finally, satellite1 changes its orientation from phenomenon17 to planet11 and an image of planet11 is captured by instrument3 on satellite1 and stored in image3, resulting in the current state. In this state, are the following properties that involve negations True or False: instrument0's calibration is incomplete, instrument1's calibration is incomplete, image3 does not contain an image of groundstation0, infrared1 does not contain an image of groundstation0, image0 does not contain an image of groundstation3, image3 does not contain an image of groundstation3, infrared1 does not contain an image of groundstation3, image0 does not contain an image of groundstation4, image2 does not contain an image of groundstation4, image3 does not contain an image of groundstation4, image0 does not contain an image of groundstation5, image0 does not contain an image of groundstation7, image3 does not contain an image of groundstation7, infrared1 does not contain an image of phenomenon10, image0 does not contain an image of phenomenon16, image2 does not contain an image of phenomenon16, image0 does not contain an image of phenomenon17, image3 does not contain an image of phenomenon17, image0 does not contain an image of planet11, image2 does not contain an image of planet11, image3 does not contain an image of planet11, infrared1 does not contain an image of planet11, image2 does not contain an image of planet12, image0 does not contain an image of planet13, image2 does not contain an image of planet13, image3 does not contain an image of planet13, infrared1 does not contain an image of planet13, image0 does not contain an image of planet14, image0 does not contain an image of star1, image3 does not contain an image of star1, image2 does not contain an image of star15, image3 does not contain an image of star15, infrared1 does not contain an image of star15, image0 does not contain an image of star6, image3 does not contain an image of star6, image2 does not contain an image of star8, image3 does not contain an image of star8, image0 does not contain an image of star9, image2 does not contain an image of star9, image3 does not contain an image of star9, instrument0 is not powered on, instrument1 is not powered on, instrument2 is not calibrated, instrument2 is not powered on, instrument3 is not calibrated, instrument3 is not powered on, image0 does not contain an image of the direction of groundstation0, image2 does not contain an image of the direction of groundstation0, image0 does not contain an image of the direction of groundstation2, image2 does not contain an image of the direction of groundstation2, image3 does not contain an image of the direction of groundstation2, infrared1 does not contain an image of the direction of groundstation2, image2 does not contain an image of the direction of groundstation3, infrared1 does not contain an image of the direction of groundstation4, image2 does not contain an image of the direction of groundstation5, image3 does not contain an image of the direction of groundstation5, infrared1 does not contain an image of the direction of groundstation5, image2 does not contain an image of the direction of groundstation7, infrared1 does not contain an image of the direction of groundstation7, image0 does not contain an image of the direction of phenomenon10, image2 does not contain an image of the direction of phenomenon10, image3 does not contain an image of the direction of phenomenon10, image3 does not contain an image of the direction of phenomenon16, infrared1 does not contain an image of the direction of phenomenon16, image2 does not contain an image of the direction of phenomenon17, infrared1 does not contain an image of the direction of phenomenon17, image0 does not contain an image of the direction of planet12, image3 does not contain an image of the direction of planet12, infrared1 does not contain an image of the direction of planet12, image2 does not contain an image of the direction of planet14, image3 does not contain an image of the direction of planet14, infrared1 does not contain an image of the direction of planet14, image2 does not contain an image of the direction of star1, infrared1 does not contain an image of the direction of star1, image0 does not contain an image of the direction of star15, image2 does not contain an image of the direction of star6, infrared1 does not contain an image of the direction of star6, image0 does not contain an image of the direction of star8, infrared1 does not contain an image of the direction of star8, and infrared1 does not contain an image of the direction of star9?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1 is installed on satellite0 and supports infrared1. Instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5 and star6, and it supports image2. Satellite0 has instrument0 and instrument2 on board, has available power, and is currently pointing towards groundstation3. Satellite1 carries instrument3 on board, has power available, and is pointing towards phenomenon10."}
{"question_id": "802772fb-61c1-44e2-8a22-21861e58005a", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, is it True or False that star13 is where satellite0 is pointed?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 rotates from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then rotates from star6 to planet14, instrument3 on satellite1 captures an image of planet14 using spectrograph1, satellite1 rotates from planet14 to star10, instrument3 on satellite1 captures an image of star10 using spectrograph1, and finally, satellite1 rotates from star10 to star12 and captures an image of star12 using spectrograph1, resulting in the current state. In this state, is it True or False that satellite0 is currently pointing at star13?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "74731572-513b-4b23-afc6-de7ab64b5ecf", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, is it True or False that instrument1 is turned on?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated to achieve the current state. In this state, is it True or False that instrument1 is activated?", "initial_state_nl_paraphrased": "Calibration of instrument3 has been finalized for groundstation9, while instrument1 has been calibrated for groundstation6, and instrument0 for groundstation7. Instrument4 has also been calibrated for groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 is also calibrated for groundstation0 and supports spectrograph2. Instrument2 is calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3, while instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, with instrument1 also on board satellite0."}
{"question_id": "907659c5-cd9b-4df8-9652-f6ba89519d5f", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, is it True or False that there is no image of direction groundstation9 in image6?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated to achieve the current state. In this state, is it True or False that an image of direction groundstation9 does not exist in image6?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board."}
{"question_id": "b8c2dc51-c358-4fa5-b2e4-792d7795a2fd", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, satellite1 turns from star3 to star1, instrument1 is calibrated on satellite1 to star1, satellite1 turns to phenomenon10 from star1, image of phenomenon10 is taken with instrument1 on satellite1 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11 and satellite1's instrument1 takes an image of phenomenon5 in image4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: power is not available for satellite1 and satellite0 does not have power?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then reorients from star1 to phenomenon10, captures an image of phenomenon10 using instrument1 in image5, and also captures an image of phenomenon10 using instrument1 in spectrograph3. Subsequently, satellite1 reorients from phenomenon10 to phenomenon11, captures an image of phenomenon11 using instrument1 in spectrograph1, then reorients from phenomenon11 to phenomenon5, and finally captures an image of phenomenon5 using instrument1 in image4, resulting in the current state. In this state, are the following properties that involve negations True or False: satellite1 does not have power available and satellite0 lacks power?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated. Instrument1 supports image0, and it is also compatible with image2. Image2 is supported by instrument0, while instrument1 supports both image4 and image5. Instrument1 is installed on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Both spectrograph1 and spectrograph3 are compatible with instrument1. Satellite1 is currently directed towards star3."}
{"question_id": "8414a788-c985-4cca-8622-a11cab52a83c", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_14", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, satellite1 turns from star3 to star1, calibration of instrument1 which is on satellite1 to star1 is complete, from star1, satellite1 turns to phenomenon10, image of phenomenon10 is taken with instrument1 on satellite1 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, image of phenomenon5 is taken with instrument1 on satellite1 in image4, instrument1 which is on satellite1 takes an image of phenomenon5 in image5, satellite1 turns to phenomenon7 from phenomenon5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, from phenomenon7, satellite1 turns to phenomenon9, image of phenomenon9 is taken with instrument1 on satellite1 in image5, image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and image of planet8 is taken with instrument1 on satellite1 in image5 to reach the current state. In this state, is it True or False that instrument0 supports image5?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 shifts from star1 to phenomenon10, capturing an image of phenomenon10 using instrument1 on satellite1 in both image5 and spectrograph3, followed by a turn from phenomenon10 to phenomenon11, where an image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, then satellite1 moves from phenomenon11 to phenomenon5, capturing an image of phenomenon5 with instrument1 on satellite1 in image4, and another image of phenomenon5 is taken with instrument1 on satellite1 in image5, satellite1 then turns from phenomenon5 to phenomenon7, capturing images of phenomenon7 with instrument1 on satellite1 in both image0 and image4, next, satellite1 moves from phenomenon7 to phenomenon9, capturing images of phenomenon9 with instrument1 on satellite1 in image5 and spectrograph1, and finally, satellite1 turns from phenomenon9 to planet8, capturing an image of planet8 with instrument1 on satellite1 in image5, resulting in the current state. In this state, is it True or False that instrument0 supports image5?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently pointed towards star3."}
{"question_id": "5bc314ec-50a9-49d9-a6e6-fbf5759acc7f", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: power is available for satellite0 and power is available for satellite1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, are the following properties, which do not involve negations, True or False: power is available for satellite0 and power is available for satellite1?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently directed towards star3."}
{"question_id": "7402fa21-87bc-4942-989e-106363fcfa7f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, satellite1 turns to star6 from groundstation4, calibration of instrument3 which is on satellite1 to star6 is complete, from star6, satellite1 turns to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, image of star10 is taken with instrument3 on satellite1 in spectrograph1, from star10, satellite1 turns to star12 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, is it True or False that instrument3 is turned on?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is switched on, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 reorients from star6 to planet14, captures an image of planet14 using spectrograph1 with instrument3, reorients from planet14 to star10, captures an image of star10 using spectrograph1 with instrument3, and finally reorients from star10 to star12, capturing an image of star12 using spectrograph1 with instrument3, resulting in the current state. In this state, is it True or False that instrument3 is turned on?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "12f8a514-7a6a-40fc-a796-0090fcce6248", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns to planet11 from groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, satellite0's instrument1 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, from planet13, satellite0 turns to star10, instrument1 which is on satellite0 takes an image of star10 in image6, image of star10 is taken with instrument1 on satellite0 in spectrograph2, instrument1 on satellite0 is switched off, instrument2 that is on satellite0 is turned on, satellite0 turns from star10 to star4, calibration of instrument2 which is on satellite0 to star4 is complete, satellite0 turns from star4 to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and instrument2 on satellite0 is switched off to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: calibration of instrument1 is complete, image of groundstation0 exists in image5, image of groundstation0 exists in spectrograph2, image of groundstation0 exists in thermograph3, image of groundstation1 exists in image0, image of groundstation1 exists in image1, image of groundstation1 exists in spectrograph2, image of groundstation1 exists in thermograph3, image of groundstation2 exists in image0, image of groundstation2 exists in image1, image of groundstation2 exists in infrared7, image of groundstation2 exists in spectrograph2, image of groundstation2 exists in thermograph3, image of groundstation5 exists in image0, image of groundstation5 exists in image6, image of groundstation5 exists in infrared7, image of groundstation5 exists in thermograph3, image of groundstation6 exists in image0, image of groundstation6 exists in image6, image of groundstation6 exists in spectrograph2, image of groundstation6 exists in thermograph3, image of groundstation7 exists in image6, image of groundstation7 exists in infrared7, image of groundstation7 exists in spectrograph2, image of groundstation7 exists in spectrograph4, image of groundstation7 exists in thermograph3, image of groundstation8 exists in image0, image of groundstation8 exists in infrared7, image of groundstation8 exists in spectrograph4, image of groundstation9 exists in image0, image of groundstation9 exists in spectrograph2, image of groundstation9 exists in spectrograph4, image of groundstation9 exists in thermograph3, image of phenomenon14 exists in image5, image of phenomenon14 exists in image6, image of phenomenon14 exists in spectrograph2, image of phenomenon14 exists in thermograph3, image of phenomenon15 exists in image0, image of phenomenon15 exists in image6, image of phenomenon15 exists in infrared7, image of phenomenon15 exists in spectrograph4, image of phenomenon15 exists in thermograph3, image of planet11 exists in image5, image of planet13 exists in infrared7, image of planet13 exists in spectrograph2, image of planet13 exists in thermograph3, image of star10 exists in image0, image of star10 exists in image5, image of star10 exists in spectrograph4, image of star12 exists in image0, image of star12 exists in image6, image of star12 exists in infrared7, image of star12 exists in thermograph3, image of star3 exists in image1, image of star3 exists in image6, image of star3 exists in spectrograph2, image of star3 exists in spectrograph4, image of star3 exists in thermograph3, image of star4 exists in spectrograph4, image of star4 exists in thermograph3, instrument0 is calibrated, instrument0 is switched on, instrument1 is powered on, instrument2 is calibrated, instrument2 is switched on, instrument3 is calibrated, instrument3 is turned on, instrument4 is calibrated, instrument4 is powered on, there is an image of groundstation0 in image0, there is an image of groundstation0 in image1, there is an image of groundstation0 in image6, there is an image of groundstation0 in infrared7, there is an image of groundstation0 in spectrograph4, there is an image of groundstation1 in image5, there is an image of groundstation1 in image6, there is an image of groundstation1 in infrared7, there is an image of groundstation1 in spectrograph4, there is an image of groundstation2 in image5, there is an image of groundstation2 in image6, there is an image of groundstation2 in spectrograph4, there is an image of groundstation5 in image1, there is an image of groundstation5 in image5, there is an image of groundstation5 in spectrograph2, there is an image of groundstation5 in spectrograph4, there is an image of groundstation6 in image1, there is an image of groundstation6 in image5, there is an image of groundstation6 in infrared7, there is an image of groundstation6 in spectrograph4, there is an image of groundstation7 in image0, there is an image of groundstation7 in image1, there is an image of groundstation7 in image5, there is an image of groundstation8 in image1, there is an image of groundstation8 in image5, there is an image of groundstation8 in image6, there is an image of groundstation8 in spectrograph2, there is an image of groundstation8 in thermograph3, there is an image of groundstation9 in image1, there is an image of groundstation9 in image5, there is an image of groundstation9 in image6, there is an image of groundstation9 in infrared7, there is an image of phenomenon14 in image0, there is an image of phenomenon14 in image1, there is an image of phenomenon14 in infrared7, there is an image of phenomenon14 in spectrograph4, there is an image of phenomenon15 in image1, there is an image of phenomenon15 in image5, there is an image of phenomenon15 in spectrograph2, there is an image of planet11 in image0, there is an image of planet11 in image1, there is an image of planet11 in image6, there is an image of planet11 in infrared7, there is an image of planet11 in spectrograph2, there is an image of planet11 in spectrograph4, there is an image of planet11 in thermograph3, there is an image of planet13 in image0, there is an image of planet13 in image1, there is an image of planet13 in image5, there is an image of planet13 in image6, there is an image of planet13 in spectrograph4, there is an image of star10 in image1, there is an image of star10 in image6, there is an image of star10 in infrared7, there is an image of star10 in spectrograph2, there is an image of star10 in thermograph3, there is an image of star12 in image1, there is an image of star12 in image5, there is an image of star12 in spectrograph2, there is an image of star12 in spectrograph4, there is an image of star16 in image0, there is an image of star16 in image1, there is an image of star16 in image5, there is an image of star16 in image6, there is an image of star16 in infrared7, there is an image of star16 in spectrograph2, there is an image of star16 in spectrograph4, there is an image of star16 in thermograph3, there is an image of star3 in image0, there is an image of star3 in image5, there is an image of star3 in infrared7, there is an image of star4 in image0, there is an image of star4 in image1, there is an image of star4 in image5, there is an image of star4 in image6, there is an image of star4 in infrared7 and there is an image of star4 in spectrograph2?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the instrument1 on satellite0 is activated, satellite0 rotates from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then turns from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, satellite0's instrument1 also captures an image of planet11 in image6, satellite0 then turns from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 in image5, an image of planet13 is taken with instrument1 on satellite0 in spectrograph2, from planet13, satellite0 turns to star10, instrument1 on satellite0 captures an image of star10 in image6, an image of star10 is taken with instrument1 on satellite0 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 turns from star10 to star4, calibration of instrument2 on satellite0 to star4 is complete, satellite0 then turns from star4 to star16, instrument2 on satellite0 captures an image of star16 in image0, and instrument2 on satellite0 is deactivated to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: instrument1 calibration is complete, an image of groundstation0 exists in image5, an image of groundstation0 exists in spectrograph2, an image of groundstation0 exists in thermograph3, an image of groundstation1 exists in image0, an image of groundstation1 exists in image1, an image of groundstation1 exists in spectrograph2, an image of groundstation1 exists in thermograph3, an image of groundstation2 exists in image0, an image of groundstation2 exists in image1, an image of groundstation2 exists in infrared7, an image of groundstation2 exists in spectrograph2, an image of groundstation2 exists in thermograph3, an image of groundstation5 exists in image0, an image of groundstation5 exists in image6, an image of groundstation5 exists in infrared7, an image of groundstation5 exists in thermograph3, an image of groundstation6 exists in image0, an image of groundstation6 exists in image6, an image of groundstation6 exists in spectrograph2, an image of groundstation6 exists in thermograph3, an image of groundstation7 exists in image6, an image of groundstation7 exists in infrared7, an image of groundstation7 exists in spectrograph2, an image of groundstation7 exists in spectrograph4, an image of groundstation7 exists in thermograph3, an image of groundstation8 exists in image0, an image of groundstation8 exists in infrared7, an image of groundstation8 exists in spectrograph4, an image of groundstation9 exists in image0, an image of groundstation9 exists in spectrograph2, an image of groundstation9 exists in spectrograph4, an image of groundstation9 exists in thermograph3, an image of phenomenon14 exists in image5, an image of phenomenon14 exists in image6, an image of phenomenon14 exists in spectrograph2, an image of phenomenon14 exists in thermograph3, an image of phenomenon15 exists in image0, an image of phenomenon15 exists in image6, an image of phenomenon15 exists in infrared7, an image of phenomenon15 exists in spectrograph4, an image of phenomenon15 exists in thermograph3, an image of planet11 exists in image5, an image of planet13 exists in infrared7, an image of planet13 exists in spectrograph2, an image of planet13 exists in thermograph3, an image of star10 exists in image0, an image of star10 exists in image5, an image of star10 exists in spectrograph4, an image of star12 exists in image0, an image of star12 exists in image6, an image of star12 exists in infrared7, an image of star12 exists in thermograph3, an image of star3 exists in image1, an image of star3 exists in image6, an image of star3 exists in spectrograph2, an image of star3 exists in spectrograph4, an image of star3 exists in thermograph3, an image of star4 exists in spectrograph4, an image of star4 exists in thermograph3, instrument0 is calibrated, instrument0 is activated, instrument1 is powered on, instrument2 is calibrated, instrument2 is activated, instrument3 is calibrated, instrument3 is turned on, instrument4 is calibrated, instrument4 is powered on, there is an image of groundstation0 in image0, there is an image of groundstation0 in image1, there is an image of groundstation0 in image6, there is an image of groundstation0 in infrared7, there is an image of groundstation0 in spectrograph4, there is an image of groundstation1 in image5, there is an image of groundstation1 in image6, there is an image of groundstation1 in infrared7, there is an image of groundstation1 in spectrograph4, there is an image of groundstation2 in image5, there is an image of groundstation2 in image6, there is an image of groundstation2 in spectrograph4, there is an image of groundstation5 in image1, there is an image of groundstation5 in image5, there is an image of groundstation5 in spectrograph2, there is an image of groundstation5 in spectrograph4, there is an image of groundstation6 in image1, there is an image of groundstation6 in image5, there is an image of groundstation6 in infrared7, there is an image of groundstation6 in spectrograph4, there is an image of groundstation7 in image0, there is an image of groundstation7 in image1, there is an image of groundstation7 in image5, there is an image of groundstation8 in image1, there is an image of groundstation8 in image5, there is an image of groundstation8 in image6, there is an image of groundstation8 in spectrograph2, there is an image of groundstation8 in thermograph3, there is an image of groundstation9 in image1, there is an image of groundstation9 in image5, there is an image of groundstation9 in image6, there is an image of groundstation9 in infrared7, there is an image of phenomenon14 in image0, there is an image of phenomenon14 in image1, there is an image of phenomenon14 in infrared7, there is an image of phenomenon14 in spectrograph4, there is an image of phenomenon15 in image1, there is an image of phenomenon15 in image5, there is an image of phenomenon15 in spectrograph2, there is an image of planet11 in image0, there is an image of planet11 in image1, there is an image of planet11 in image6, there is an image of planet11 in infrared7, there is an image of planet11 in spectrograph2, there is an image of planet11 in spectrograph4, there is an image of planet11 in thermograph3, there is an image of planet13 in image0, there is an image of planet13 in image1, there is an image of planet13 in image5, there is an image of planet13 in image6, there is an image of planet13 in spectrograph4, there is an image of star10 in image1, there is an image of star10 in image6, there is an image of star10 in infrared7, there is an image of star10 in spectrograph2, there is an image of star10 in thermograph3, there is an image of star12 in image1, there is an image of star12 in image5, there is an image of star12 in spectrograph2, there is an image of star12 in spectrograph4, there is an image of star16 in image0, there is an image of star16 in image1, there is an image of star16 in image5, there is an image of star16 in image6, there is an image of star16 in infrared7, there is an image of star16 in spectrograph2, there is an image of star16 in spectrograph4, there is an image of star16 in thermograph3, there is an image of star3 in image0, there is an image of star3 in image5, there is an image of star3 in infrared7, there is an image of star4 in image0, there is an image of star4 in image1, there is an image of star4 in image5, there is an image of star4 in image6, there is an image of star4 in infrared7 and there is an image of star4 in spectrograph2?", "initial_state_nl_paraphrased": "The calibration process for instrument3 at groundstation9 has been completed. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available, carries instrument3 on board, and also has instrument0 and instrument1 on board, with its aim directed towards groundstation2. Satellite1 has power available and is aimed towards planet13."}
{"question_id": "0389a041-eae8-4ba9-a8f4-31096438d395", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns from groundstation2 to groundstation0, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns to planet13 from planet11, instrument1 which is on satellite0 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and satellite0 turns from planet13 to star10 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: groundstation2 is not where satellite1 is pointed, groundstation6 is not where satellite0 is pointed, groundstation7 is not where satellite1 is pointed, groundstation8 is not where satellite1 is pointed, phenomenon14 is not where satellite0 is pointed, planet11 is not where satellite1 is pointed, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards planet13, satellite0 is not aimed towards star10, satellite0 is not aimed towards star12, satellite0 is not aimed towards star16, satellite0 is not aimed towards star4, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation1, satellite0 is not pointing to groundstation8, satellite0 is not pointing to groundstation9, satellite0 is not pointing to phenomenon15, satellite0 is not pointing to planet11, satellite0 is not pointing to star3, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards phenomenon14, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards star10, satellite1 is not aimed towards star3, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation1, satellite1 is not pointing to groundstation6, satellite1 is not pointing to groundstation9, satellite1 is not pointing to planet13, satellite1 is not pointing to star12, star16 is not where satellite1 is pointed and star4 is not where satellite1 is pointed?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 is activated on satellite0, satellite0 reorients from groundstation2 to groundstation0, instrument1 is calibrated on satellite0 with respect to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is captured using instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 and stores it in image5, instrument1 on satellite0 captures an image of planet13 using spectrograph2, and finally, satellite0 reorients from planet13 to star10, resulting in the current state. In this state, are the following properties that involve negations True or False: satellite1 is not oriented towards groundstation2, satellite0 is not oriented towards groundstation6, satellite1 is not oriented towards groundstation7, satellite1 is not oriented towards groundstation8, satellite0 is not oriented towards phenomenon14, satellite1 is not oriented towards planet11, satellite0 is not aimed at groundstation2, satellite0 is not aimed at groundstation5, satellite0 is not aimed at groundstation7, satellite0 is not aimed at planet13, satellite0 is aimed at star10 but the statement is negated, satellite0 is not aimed at star12, satellite0 is not aimed at star16, satellite0 is not aimed at star4, satellite0 is not pointing towards groundstation0, satellite0 is not pointing towards groundstation1, satellite0 is not pointing towards groundstation8, satellite0 is not pointing towards groundstation9, satellite0 is not pointing towards phenomenon15, satellite0 is not pointing towards planet11, satellite0 is not pointing towards star3, satellite1 is not aimed at groundstation5, satellite1 is not aimed at phenomenon14, satellite1 is not aimed at phenomenon15, satellite1 is not aimed at star10, satellite1 is not aimed at star3, satellite1 is not pointing towards groundstation0, satellite1 is not pointing towards groundstation1, satellite1 is not pointing towards groundstation6, satellite1 is not pointing towards groundstation9, satellite1 is not pointing towards planet13, satellite1 is not pointing towards star12, satellite1 is not oriented towards star16, and satellite1 is not oriented towards star4.", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, with instrument1 also on board satellite0."}
{"question_id": "c266a143-5ff4-4a43-9426-c766815fbbfa", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_20", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, satellite0's instrument1 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, satellite0's instrument1 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, on satellite0, instrument1 is switched off, on satellite0, instrument2 is switched on, satellite0 turns to star4 from star10, instrument2 is calibrated on satellite0 to star4, satellite0 turns to star16 from star4, satellite0's instrument2 takes an image of star16 in image0 and instrument2 on satellite0 is switched off to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: calibration of instrument0 for groundstation0 is complete, calibration of instrument0 for groundstation1 is complete, calibration of instrument0 for groundstation7 is complete, calibration of instrument0 for groundstation9 is complete, calibration of instrument0 for phenomenon14 is complete, calibration of instrument0 for planet11 is complete, calibration of instrument0 for star10 is complete, calibration of instrument0 for star12 is complete, calibration of instrument0 for star3 is complete, calibration of instrument1 for groundstation5 is complete, calibration of instrument1 for groundstation8 is complete, calibration of instrument1 for groundstation9 is complete, calibration of instrument1 for planet11 is complete, calibration of instrument1 for star12 is complete, calibration of instrument1 for star16 is complete, calibration of instrument1 for star3 is complete, calibration of instrument2 for groundstation0 is complete, calibration of instrument2 for groundstation2 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for groundstation8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for planet11 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation2 is complete, calibration of instrument3 for groundstation8 is complete, calibration of instrument3 for groundstation9 is complete, calibration of instrument3 for star12 is complete, calibration of instrument4 for groundstation6 is complete, calibration of instrument4 for groundstation7 is complete, calibration of instrument4 for groundstation8 is complete, calibration of instrument4 for phenomenon15 is complete, calibration of instrument4 for star12 is complete, for groundstation0, instrument1 is calibrated, for groundstation1, instrument1 is calibrated, for groundstation1, instrument3 is calibrated, for groundstation1, instrument4 is calibrated, for groundstation2, instrument4 is calibrated, for groundstation5, instrument0 is calibrated, for groundstation5, instrument3 is calibrated, for groundstation6, instrument0 is calibrated, for groundstation6, instrument3 is calibrated, for groundstation7, instrument1 is calibrated, for groundstation8, instrument0 is calibrated, for groundstation9, instrument4 is calibrated, for phenomenon14, instrument3 is calibrated, for phenomenon14, instrument4 is calibrated, for phenomenon15, instrument0 is calibrated, for planet11, instrument3 is calibrated, for planet13, instrument0 is calibrated, for planet13, instrument1 is calibrated, for planet13, instrument2 is calibrated, for planet13, instrument4 is calibrated, for star10, instrument1 is calibrated, for star12, instrument2 is calibrated, for star16, instrument0 is calibrated, for star16, instrument4 is calibrated, for star3, instrument4 is calibrated, for star4, instrument1 is calibrated, for star4, instrument3 is calibrated, image0 is compatible with instrument1, image0 is supported by instrument2, image0 is supported by instrument4, image1 is supported by instrument1, image5 is compatible with instrument2, image5 is supported by instrument3, image5 is supported by instrument4, image6 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument4, infrared7 is compatible with instrument3, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star4, instrument0 is on board satellite0, instrument0 supports image0, instrument0 supports image1, instrument0 supports image5, instrument0 supports infrared7, instrument0 supports spectrograph2, instrument0 supports spectrograph4, instrument0 supports thermograph3, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation6, instrument1 is calibrated for phenomenon14, instrument1 is calibrated for phenomenon15, instrument1 is on board satellite0, instrument1 supports image5, instrument1 supports infrared7, instrument1 supports spectrograph2, instrument2 is calibrated for groundstation1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation6, instrument2 is calibrated for phenomenon14, instrument2 is calibrated for phenomenon15, instrument2 is calibrated for star10, instrument2 is calibrated for star16, instrument2 is calibrated for star3, instrument2 is on board satellite1, instrument2 supports image1, instrument2 supports image6, instrument2 supports infrared7, instrument3 is calibrated for groundstation0, instrument3 is calibrated for groundstation7, instrument3 is calibrated for phenomenon15, instrument3 is calibrated for planet13, instrument3 is calibrated for star10, instrument3 is calibrated for star16, instrument3 is calibrated for star3, instrument3 supports image0, instrument3 supports image1, instrument3 supports image6, instrument3 supports spectrograph2, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is calibrated for groundstation0, instrument4 is calibrated for groundstation5, instrument4 is calibrated for planet11, instrument4 is calibrated for star10, instrument4 is calibrated for star4, instrument4 is on board satellite0, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument2 on board, satellite0 has instrument3 on board, satellite1 carries instrument0 on board, satellite1 carries instrument1 on board, satellite1 has instrument3 on board, satellite1 has instrument4 on board, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument4, spectrograph4 is compatible with instrument1, spectrograph4 is compatible with instrument2, spectrograph4 is compatible with instrument4, thermograph3 is compatible with instrument1, thermograph3 is supported by instrument2 and thermograph3 is supported by instrument4?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1, which is located on satellite0, is activated, satellite0 rotates from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated for groundstation0, satellite0 then rotates from groundstation0 to planet11, satellite0's instrument1 captures an image of planet11 in image5, instrument1 on satellite0 also captures an image of planet11 in image6, satellite0 rotates from planet11 to planet13, satellite0's instrument1 captures an image of planet13 in image5, satellite0's instrument1 captures an image of planet13 in spectrograph2, satellite0 then rotates from planet13 to star10, satellite0's instrument1 captures an image of star10 in image6, satellite0's instrument1 captures an image of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 rotates from star10 to star4, instrument2 on satellite0 is calibrated for star4, satellite0 then rotates from star4 to star16, satellite0's instrument2 captures an image of star16 in image0, and instrument2 on satellite0 is deactivated to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: calibration of instrument0 for groundstation0 is complete, calibration of instrument0 for groundstation1 is complete, calibration of instrument0 for groundstation7 is complete, calibration of instrument0 for groundstation9 is complete, calibration of instrument0 for phenomenon14 is complete, calibration of instrument0 for planet11 is complete, calibration of instrument0 for star10 is complete, calibration of instrument0 for star12 is complete, calibration of instrument0 for star3 is complete, calibration of instrument1 for groundstation5 is complete, calibration of instrument1 for groundstation8 is complete, calibration of instrument1 for groundstation9 is complete, calibration of instrument1 for planet11 is complete, calibration of instrument1 for star12 is complete, calibration of instrument1 for star16 is complete, calibration of instrument1 for star3 is complete, calibration of instrument2 for groundstation0 is complete, calibration of instrument2 for groundstation2 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for groundstation8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for planet11 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation2 is complete, calibration of instrument3 for groundstation8 is complete, calibration of instrument3 for groundstation9 is complete, calibration of instrument3 for star12 is complete, calibration of instrument4 for groundstation6 is complete, calibration of instrument4 for groundstation7 is complete, calibration of instrument4 for groundstation8 is complete, calibration of instrument4 for phenomenon15 is complete, calibration of instrument4 for star12 is complete, for groundstation0, instrument1 is calibrated, for groundstation1, instrument1 is calibrated, for groundstation1, instrument3 is calibrated, for groundstation1, instrument4 is calibrated, for groundstation2, instrument4 is calibrated, for groundstation5, instrument0 is calibrated, for groundstation5, instrument3 is calibrated, for groundstation6, instrument0 is calibrated, for groundstation6, instrument3 is calibrated, for groundstation7, instrument1 is calibrated, for groundstation8, instrument0 is calibrated, for groundstation9, instrument4 is calibrated, for phenomenon14, instrument3 is calibrated, for phenomenon14, instrument4 is calibrated, for phenomenon15, instrument0 is calibrated, for planet11, instrument3 is calibrated, for planet13, instrument0 is calibrated, for planet13, instrument1 is calibrated, for planet13, instrument2 is calibrated, for planet13, instrument4 is calibrated, for star10, instrument1 is calibrated, for star12, instrument2 is calibrated, for star16, instrument0 is calibrated, for star16, instrument4 is calibrated, for star3, instrument4 is calibrated, for star4, instrument1 is calibrated, for star4, instrument3 is calibrated, image0 is compatible with instrument1, image0 is supported by instrument2, image0 is supported by instrument4, image1 is supported by instrument1, image5 is compatible with instrument2, image5 is supported by instrument3, image5 is supported by instrument4, image6 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument4, infrared7 is compatible with instrument3, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star4, instrument0 is on board satellite0, instrument0 supports image0, instrument0 supports image1, instrument0 supports image5, instrument0 supports infrared7, instrument0 supports spectrograph2, instrument0 supports spectrograph4, instrument0 supports thermograph3, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation6, instrument1 is calibrated for phenomenon14, instrument1 is calibrated for phenomenon15, instrument1 is on board satellite0, instrument1 supports image5, instrument1 supports infrared7, instrument1 supports spectrograph2, instrument2 is calibrated for groundstation1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation6, instrument2 is calibrated for phenomenon14, instrument2 is calibrated for phenomenon15, instrument2 is calibrated for star10, instrument2 is calibrated for star16, instrument2 is calibrated for star3, instrument2 is on board satellite1, instrument2 supports image1, instrument2 supports image6, instrument2 supports infrared7, instrument3 is calibrated for groundstation0, instrument3 is calibrated for groundstation7, instrument3 is calibrated for phenomenon15, instrument3 is calibrated for planet13, instrument3 is calibrated for star10, instrument3 is calibrated for star16, instrument3 is calibrated for star3, instrument3 supports image0, instrument3 supports image1, instrument3 supports image6, instrument3 supports spectrograph2, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is calibrated for groundstation0, instrument4 is calibrated for groundstation5, instrument4 is calibrated for planet11, instrument4 is calibrated for star10, instrument4 is calibrated for star4, instrument4 is on board satellite0, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument2 on board, satellite0 has instrument3 on board, satellite1 carries instrument0 on board, satellite1 carries instrument1 on board, satellite1 has instrument3 on board, satellite1 has instrument4 on board, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument4, spectrograph4 is compatible with instrument1, spectrograph4 is compatible with instrument2, spectrograph4 is compatible with instrument4, thermograph3 is compatible with instrument1, thermograph3 is supported by instrument2 and thermograph3 is supported by instrument4?", "initial_state_nl_paraphrased": "The calibration process for instrument3 at groundstation9 has been completed. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, a functionality also provided by instrument1, which is also calibrated for groundstation0 and supports spectrograph2. Instrument2 is calibrated for star4 and is on board satellite0, which also carries instrument3 that supports spectrograph4 and thermograph3. Instrument4, on board satellite1, supports image1. Satellite0 has power available and carries instruments 0, 1, and 3 on board, with its aim directed towards groundstation2. Satellite1 has power available and is aimed towards planet13."}
{"question_id": "e602438f-69e9-47e8-b35f-e4bf230dcffe", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, is it True or False that satellite1 has power?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated to achieve the current state. In this state, is it True or False that satellite1 is powered?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board."}
{"question_id": "8bd1b280-5794-4844-a4ff-62b8e67a40ca", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: satellite1 does not have power available?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, are all of the following properties that involve negations True or False: satellite1 lacks available power?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, has available power, and is currently pointing towards groundstation3. Satellite1 carries instrument3 on board, has power available, and is pointing towards phenomenon10."}
{"question_id": "39c080d6-4877-4474-8e42-d1b495d7fe4c", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, satellite1 turns from groundstation4 to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns from star6 to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, from planet14, satellite1 turns to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10 and instrument3 which is on satellite1 takes an image of star12 in spectrograph1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: groundstation3 is where satellite1 is pointed, groundstation4 is where satellite1 is pointed, groundstation9 is where satellite0 is pointed, satellite0 is aimed towards groundstation3, satellite0 is aimed towards groundstation5, satellite0 is aimed towards planet14, satellite0 is aimed towards star0, satellite0 is aimed towards star1, satellite0 is aimed towards star12, satellite0 is aimed towards star13, satellite0 is aimed towards star6, satellite0 is aimed towards star7, satellite0 is pointing to groundstation2, satellite0 is pointing to groundstation4, satellite0 is pointing to phenomenon15, satellite0 is pointing to star10, satellite0 is pointing to star11, satellite0 is pointing to star8, satellite1 is aimed towards groundstation9, satellite1 is aimed towards planet14, satellite1 is aimed towards star0, satellite1 is aimed towards star12, satellite1 is aimed towards star16, satellite1 is pointing to groundstation2, satellite1 is pointing to groundstation5, satellite1 is pointing to phenomenon15, satellite1 is pointing to star10, satellite1 is pointing to star7, star1 is where satellite1 is pointed, star11 is where satellite1 is pointed, star13 is where satellite1 is pointed, star16 is where satellite0 is pointed, star6 is where satellite1 is pointed and star8 is where satellite1 is pointed?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, from planet14, satellite1 reorients to star10, an image of star10 is taken by instrument3 on satellite1 in spectrograph1, and finally, satellite1 reorients from star10 to star12, and instrument3 on satellite1 captures an image of star12 in spectrograph1, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: satellite1 is currently pointing at groundstation3, satellite1 is currently pointing at groundstation4, satellite0 is currently pointing at groundstation9, satellite0 is aimed at groundstation3, satellite0 is aimed at groundstation5, satellite0 is aimed at planet14, satellite0 is aimed at star0, satellite0 is aimed at star1, satellite0 is aimed at star12, satellite0 is aimed at star13, satellite0 is aimed at star6, satellite0 is aimed at star7, satellite0 is pointing towards groundstation2, satellite0 is pointing towards groundstation4, satellite0 is pointing towards phenomenon15, satellite0 is pointing towards star10, satellite0 is pointing towards star11, satellite0 is pointing towards star8, satellite1 is aimed at groundstation9, satellite1 is aimed at planet14, satellite1 is aimed at star0, satellite1 is aimed at star12, satellite1 is aimed at star16, satellite1 is pointing towards groundstation2, satellite1 is pointing towards groundstation5, satellite1 is pointing towards phenomenon15, satellite1 is pointing towards star10, satellite1 is pointing towards star7, star1 is the current target of satellite1, star11 is the current target of satellite1, star13 is the current target of satellite1, star16 is the current target of satellite0, star6 is the current target of satellite1, and star8 is the current target of satellite1?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. The calibration for star6 has been done with instrument3, star7 with instrument2, and star8 with instrument1. Instrument0 has also been calibrated for groundstation2. It is worth noting that instrument0 is currently on board satellite0 and supports spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3, spectrograph0, and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1, on the other hand, has instrument2 and instrument3 on board, has power, and is currently aimed at groundstation4. Spectrograph0 and spectrograph1 are compatible with instrument1. Satellite0 is currently pointed towards star1, and thermograph4 is compatible with instrument0."}
{"question_id": "8825cdc0-29f3-47cf-8749-16624b3489e0", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, satellite1 turns from star3 to star1, calibration of instrument1 which is on satellite1 to star1 is complete, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, satellite1 turns from phenomenon11 to phenomenon5 and instrument1 which is on satellite1 takes an image of phenomenon5 in image4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: calibration of instrument0 is complete, calibration of instrument1 is complete, image of groundstation0 exists in image0, image of groundstation0 exists in image2, image of groundstation2 exists in image2, image of groundstation2 exists in spectrograph1, image of groundstation4 exists in image0, image of groundstation4 exists in image2, image of phenomenon10 exists in image5, image of phenomenon10 exists in spectrograph1, image of phenomenon10 exists in spectrograph3, image of phenomenon11 exists in image0, image of phenomenon11 exists in image2, image of phenomenon11 exists in image4, image of phenomenon11 exists in spectrograph1, image of phenomenon11 exists in spectrograph3, image of phenomenon5 exists in image0, image of phenomenon5 exists in image5, image of phenomenon7 exists in image0, image of phenomenon9 exists in image0, image of phenomenon9 exists in image2, image of phenomenon9 exists in spectrograph1, image of phenomenon9 exists in spectrograph3, image of planet8 exists in image0, image of planet8 exists in image4, image of planet8 exists in image5, image of star1 exists in image0, image of star1 exists in image2, image of star1 exists in image4, image of star1 exists in spectrograph1, image of star1 exists in spectrograph3, image of star3 exists in spectrograph3, image of star6 exists in image0, image of star6 exists in image2, image of star6 exists in image5, image of star6 exists in spectrograph3, instrument0 is powered on, instrument1 is switched on, there is an image of groundstation0 in image4, there is an image of groundstation0 in image5, there is an image of groundstation0 in spectrograph1, there is an image of groundstation0 in spectrograph3, there is an image of groundstation2 in image0, there is an image of groundstation2 in image4, there is an image of groundstation2 in image5, there is an image of groundstation2 in spectrograph3, there is an image of groundstation4 in image4, there is an image of groundstation4 in image5, there is an image of groundstation4 in spectrograph1, there is an image of groundstation4 in spectrograph3, there is an image of phenomenon10 in image0, there is an image of phenomenon10 in image2, there is an image of phenomenon10 in image4, there is an image of phenomenon11 in image5, there is an image of phenomenon5 in image2, there is an image of phenomenon5 in image4, there is an image of phenomenon5 in spectrograph1, there is an image of phenomenon5 in spectrograph3, there is an image of phenomenon7 in image2, there is an image of phenomenon7 in image4, there is an image of phenomenon7 in image5, there is an image of phenomenon7 in spectrograph1, there is an image of phenomenon7 in spectrograph3, there is an image of phenomenon9 in image4, there is an image of phenomenon9 in image5, there is an image of planet8 in image2, there is an image of planet8 in spectrograph1, there is an image of planet8 in spectrograph3, there is an image of star1 in image5, there is an image of star3 in image0, there is an image of star3 in image2, there is an image of star3 in image4, there is an image of star3 in image5, there is an image of star3 in spectrograph1, there is an image of star6 in image4 and there is an image of star6 in spectrograph1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 shifts its orientation from star3 to star1, the calibration of instrument1 on satellite1 for star1 is completed, satellite1 then turns to phenomenon10, and instrument1 on satellite1 captures an image of phenomenon10 in image5, an image of phenomenon10 is also taken with instrument1 on satellite1 in spectrograph3, satellite1 then turns to phenomenon11, instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1, satellite1 then turns to phenomenon5, and instrument1 on satellite1 captures an image of phenomenon5 in image4, resulting in the current state. In this state, are the following properties that do not involve negations True or False: calibration of instrument0 is complete, calibration of instrument1 is complete, an image of groundstation0 exists in image0, an image of groundstation0 exists in image2, an image of groundstation2 exists in image2, an image of groundstation2 exists in spectrograph1, an image of groundstation4 exists in image0, an image of groundstation4 exists in image2, an image of phenomenon10 exists in image5, an image of phenomenon10 exists in spectrograph3, an image of phenomenon11 exists in spectrograph1, an image of phenomenon11 exists in image4, an image of phenomenon5 exists in image4, an image of phenomenon9 exists in image0, an image of phenomenon9 exists in image2, an image of phenomenon9 exists in spectrograph1, an image of planet8 exists in image0, an image of planet8 exists in image4, an image of planet8 exists in image5, an image of star1 exists in image0, an image of star1 exists in image2, an image of star1 exists in image4, an image of star1 exists in spectrograph1, an image of star3 exists in spectrograph3, an image of star6 exists in image0, an image of star6 exists in image2, an image of star6 exists in image5, an image of star6 exists in spectrograph3, instrument0 is powered on, instrument1 is switched on, there is an image of groundstation0 in image4, there is an image of groundstation0 in image5, there is an image of groundstation0 in spectrograph1, there is an image of groundstation0 in spectrograph3, there is an image of groundstation2 in image0, there is an image of groundstation2 in image4, there is an image of groundstation2 in image5, there is an image of groundstation2 in spectrograph3, there is an image of groundstation4 in image4, there is an image of groundstation4 in image5, there is an image of groundstation4 in spectrograph1, there is an image of groundstation4 in spectrograph3, there is an image of phenomenon10 in image0, there is an image of phenomenon10 in image2, there is an image of phenomenon10 in image4, there is an image of phenomenon11 in image5, there is an image of phenomenon5 in image2, there is an image of phenomenon5 in spectrograph1, there is an image of phenomenon5 in spectrograph3, there is an image of phenomenon7 in image2, there is an image of phenomenon7 in image4, there is an image of phenomenon7 in image5, there is an image of phenomenon7 in spectrograph1, there is an image of phenomenon7 in spectrograph3, there is an image of phenomenon9 in image4, there is an image of phenomenon9 in image5, there is an image of planet8 in image2, there is an image of planet8 in spectrograph1, there is an image of planet8 in spectrograph3, there is an image of star1 in image5, there is an image of star3 in image0, there is an image of star3 in image2, there is an image of star3 in image4, there is an image of star3 in image5, there is an image of star3 in spectrograph1, there is an image of star6 in image4, and there is an image of star6 in spectrograph1?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated. Instrument1 supports image0, and it is also compatible with image2. Image2 is supported by instrument0, while instrument1 supports both image4 and image5. Instrument1 is installed on satellite1. Satellite0 and satellite1 both have available power. Satellite0 has instrument0 on board and is directed towards phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently pointed at star3."}
{"question_id": "657dc5bf-30ef-4e73-a230-028268461c06", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, from star3, satellite1 turns to star1, instrument1 is calibrated on satellite1 to star1, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5 and instrument1 which is on satellite1 takes an image of phenomenon5 in image4 to reach the current state. In this state, is it True or False that satellite1 has power?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: the instrument1 on satellite1 is activated, then satellite1 reorients from star3 to star1, followed by the calibration of instrument1 on satellite1 to star1. Next, satellite1 shifts its orientation from star1 to phenomenon10, and instrument1 on satellite1 captures an image of phenomenon10, which is recorded in both image5 and spectrograph3. Satellite1 then reorients from phenomenon10 to phenomenon11, and instrument1 on satellite1 takes an image of phenomenon11, which is recorded in spectrograph1. Finally, satellite1 reorients from phenomenon11 to phenomenon5, and instrument1 on satellite1 captures an image of phenomenon5 in image4, resulting in the current state. In this state, is it True or False that satellite1 has power?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently pointed towards star3."}
{"question_id": "1b1665a8-32f7-4b5f-a1f6-b17dc6feb295", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns from groundstation4 to star6, instrument3 that is on satellite1 is calibrated to star6, from star6, satellite1 turns to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for groundstation4, instrument1 is calibrated, for star6, instrument3 is calibrated, infrared3 is supported by instrument2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for star8, instrument2 is calibrated for star7, satellite0 has instrument1 on board, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is compatible with instrument3, spectrograph1 is supported by instrument1, spectrograph2 is compatible with instrument2, spectrograph2 is compatible with instrument3 and thermograph4 is supported by instrument0?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 reorients from star6 to planet14, an image of planet14 is captured by instrument3 on satellite1 using spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured by instrument3 on satellite1 using spectrograph1, and finally, satellite1 reorients from star10 to star12, and an image of star12 is captured by instrument3 on satellite1 using spectrograph1, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 is calibrated for groundstation9, instrument0 is calibrated for groundstation4, instrument1 is calibrated for groundstation4, instrument3 is calibrated for star6, instrument2 supports infrared3, instrument0 is onboard satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for star8, instrument2 is calibrated for star7, satellite0 has instrument1 onboard, satellite1 has instrument3 onboard, satellite1 has instrument2 onboard, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is compatible with instrument3, spectrograph1 is supported by instrument1, spectrograph2 is compatible with instrument2, spectrograph2 is compatible with instrument3, and instrument0 supports thermograph4?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. The calibration for star6 has been done with instrument3, star7 with instrument2, and star8 with instrument1. Instrument0 has also been calibrated for groundstation2. It is worth noting that instrument0 is currently on board satellite0 and supports spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3, spectrograph0, and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1, on the other hand, has instrument2 and instrument3 on board, has power, and is currently aimed at groundstation4. Spectrograph0 and spectrograph1 are compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "2e736c70-6798-4a4b-8e15-6b9efd370dd1", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: groundstation3 is where satellite0 is pointed, groundstation3 is where satellite1 is pointed, phenomenon16 is where satellite1 is pointed, planet11 is where satellite0 is pointed, planet13 is where satellite1 is pointed, satellite0 is aimed towards groundstation0, satellite0 is aimed towards groundstation2, satellite0 is aimed towards phenomenon10, satellite0 is aimed towards phenomenon16, satellite0 is aimed towards planet13, satellite0 is aimed towards star8, satellite0 is pointing to groundstation4, satellite0 is pointing to groundstation5, satellite0 is pointing to groundstation7, satellite0 is pointing to phenomenon17, satellite0 is pointing to planet12, satellite0 is pointing to planet14, satellite0 is pointing to star1, satellite0 is pointing to star15, satellite0 is pointing to star6, satellite0 is pointing to star9, satellite1 is aimed towards groundstation2, satellite1 is aimed towards groundstation4, satellite1 is aimed towards groundstation5, satellite1 is aimed towards planet12, satellite1 is aimed towards star8, satellite1 is pointing to groundstation0, satellite1 is pointing to groundstation7, satellite1 is pointing to phenomenon10, satellite1 is pointing to phenomenon17, satellite1 is pointing to planet11, satellite1 is pointing to planet14, satellite1 is pointing to star1, satellite1 is pointing to star6, satellite1 is pointing to star9 and star15 is where satellite1 is pointed?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, are all of the following properties that do not involve negations True or False: satellite0 is directed at groundstation3, satellite1 is directed at groundstation3, satellite1 is directed at phenomenon16, satellite0 is directed at planet11, satellite1 is directed at planet13, satellite0 is oriented towards groundstation0, satellite0 is oriented towards groundstation2, satellite0 is oriented towards phenomenon10, satellite0 is oriented towards phenomenon16, satellite0 is oriented towards planet13, satellite0 is oriented towards star8, satellite0 is pointing towards groundstation4, satellite0 is pointing towards groundstation5, satellite0 is pointing towards groundstation7, satellite0 is pointing towards phenomenon17, satellite0 is pointing towards planet12, satellite0 is pointing towards planet14, satellite0 is pointing towards star1, satellite0 is pointing towards star15, satellite0 is pointing towards star6, satellite0 is pointing towards star9, satellite1 is oriented towards groundstation2, satellite1 is oriented towards groundstation4, satellite1 is oriented towards groundstation5, satellite1 is oriented towards planet12, satellite1 is oriented towards star8, satellite1 is pointing towards groundstation0, satellite1 is pointing towards groundstation7, satellite1 is pointing towards phenomenon10, satellite1 is pointing towards phenomenon17, satellite1 is pointing towards planet11, satellite1 is pointing towards planet14, satellite1 is pointing towards star1, satellite1 is pointing towards star6, satellite1 is pointing towards star9, and star15 is the target of satellite1?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, with additional support from instrument1. Instrument0 is compatible with image3 and supports it. Infrared1 is compatible with instrument0, which in turn supports image3. Instrument1 is installed on satellite0 and supports infrared1. Instrument2 is calibrated for groundstation7, while instrument3 is calibrated for both groundstation5 and star6, and supports image2. Satellite0 has instrument0 and instrument2 on board, and has available power while pointing towards groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "deaf9326-304b-402d-9423-20ee0ab7f330", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns to phenomenon10 from star1, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5, image of phenomenon5 is taken with instrument1 on satellite1 in image4, image of phenomenon5 is taken with instrument1 on satellite1 in image5, satellite1 turns from phenomenon5 to phenomenon7, image of phenomenon7 is taken with instrument1 on satellite1 in image0, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, satellite1 turns to phenomenon9 from phenomenon7, image of phenomenon9 is taken with instrument1 on satellite1 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1, satellite1 turns to planet8 from phenomenon9 and satellite1's instrument1 takes an image of planet8 in image5 to reach the current state. In this state, is it True or False that image0 is not supported by instrument0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 reorients from star1 to phenomenon10, and instrument1 on satellite1 captures an image of phenomenon10 in both image5 and spectrograph3. Next, satellite1 reorients from phenomenon10 to phenomenon11, and an image of phenomenon11 is captured with instrument1 on satellite1 in spectrograph1. Satellite1 then reorients from phenomenon11 to phenomenon5, and images of phenomenon5 are captured with instrument1 on satellite1 in both image4 and image5. After that, satellite1 reorients from phenomenon5 to phenomenon7, and images of phenomenon7 are captured with instrument1 on satellite1 in both image0 and image4. Satellite1 then reorients from phenomenon7 to phenomenon9, and images of phenomenon9 are captured with instrument1 on satellite1 in both image5 and spectrograph1. Finally, satellite1 reorients from phenomenon9 to planet8, and an image of planet8 is captured with instrument1 on satellite1 in image5, resulting in the current state. In this state, is it True or False that image0 is not supported by instrument0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently directed towards star3."}
{"question_id": "5bd984e5-6dd5-4ae1-9db9-2699269f4dad", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, from star3, satellite1 turns to star1, instrument1 is calibrated on satellite1 to star1, satellite1 turns from star1 to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, instrument1 which is on satellite1 takes an image of phenomenon5 in image5, satellite1 turns from phenomenon5 to phenomenon7, image of phenomenon7 is taken with instrument1 on satellite1 in image0, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, from phenomenon7, satellite1 turns to phenomenon9, satellite1's instrument1 takes an image of phenomenon9 in image5, image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to planet8 from phenomenon9 and image of planet8 is taken with instrument1 on satellite1 in image5 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: satellite0 has power?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is supported by instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is supported by instrument1, image5 is supported by instrument1, instrument1 is on board satellite1, power is available for satellite0, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is aimed towards phenomenon5, spectrograph1 is compatible with instrument1, spectrograph3 is compatible with instrument1 and star3 is where satellite1 is pointed.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite1, instrument1 is activated, then satellite1 reorients from star3 to star1, followed by the calibration of instrument1 on satellite1 to star1. Next, satellite1 reorients from star1 to phenomenon10, and instrument1 on satellite1 captures an image of phenomenon10 in image5, as well as in spectrograph3. Satellite1 then reorients from phenomenon10 to phenomenon11, and instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1. This is followed by a reorientation from phenomenon11 to phenomenon5, where instrument1 on satellite1 captures images of phenomenon5 in both image4 and image5. Satellite1 then reorients from phenomenon5 to phenomenon7, and instrument1 on satellite1 captures images of phenomenon7 in image0 and image4. Next, satellite1 reorients from phenomenon7 to phenomenon9, and instrument1 on satellite1 captures images of phenomenon9 in image5 and spectrograph1. Finally, satellite1 reorients from phenomenon9 to planet8, and instrument1 on satellite1 captures an image of planet8 in image5, resulting in the current state. In this state, are the following properties that do not involve negations True or False: does satellite0 have power?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has been calibrated as well. Instrument1 supports image0, and it is also compatible with image2. Additionally, image2 is supported by instrument0. Furthermore, instrument1 supports both image4 and image5. Instrument1 is located on satellite1. Power supply is available for both satellite0 and satellite1. Satellite0 has instrument0 on board and is currently aimed at phenomenon5. Instrument1 is compatible with both spectrograph1 and spectrograph3. Satellite1 is currently directed towards star3."}
{"question_id": "1122300e-463f-4211-9e11-73a1b597bb0b", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on to reach the current state. In this state, is it True or False that image1 is supported by instrument3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated to achieve the current state. In this state, is it True or False that instrument3 supports image1?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board satellite0."}
{"question_id": "3d710a8c-7a26-4a32-9efe-85087755f3f9", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 that is on satellite1 is calibrated to star6, satellite1 turns to planet14 from star6, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from planet14 to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: calibration of instrument0 is incomplete, calibration of instrument3 is incomplete, image of groundstation2 does not exist in spectrograph1, image of groundstation3 does not exist in spectrograph0, image of groundstation3 does not exist in spectrograph1, image of groundstation3 does not exist in thermograph4, image of groundstation4 does not exist in thermograph4, image of groundstation5 does not exist in infrared3, image of groundstation5 does not exist in thermograph4, image of groundstation9 does not exist in spectrograph1, image of groundstation9 does not exist in spectrograph2, image of groundstation9 does not exist in thermograph4, image of phenomenon15 does not exist in infrared3, image of phenomenon15 does not exist in spectrograph1, image of phenomenon15 does not exist in spectrograph2, image of planet14 does not exist in spectrograph0, image of planet14 does not exist in spectrograph2, image of planet14 does not exist in thermograph4, image of star0 does not exist in spectrograph0, image of star0 does not exist in spectrograph1, image of star0 does not exist in thermograph4, image of star1 does not exist in infrared3, image of star1 does not exist in spectrograph0, image of star1 does not exist in spectrograph1, image of star1 does not exist in spectrograph2, image of star1 does not exist in thermograph4, image of star10 does not exist in spectrograph1, image of star10 does not exist in spectrograph2, image of star10 does not exist in thermograph4, image of star11 does not exist in infrared3, image of star12 does not exist in infrared3, image of star12 does not exist in spectrograph0, image of star12 does not exist in thermograph4, image of star13 does not exist in infrared3, image of star13 does not exist in spectrograph1, image of star13 does not exist in spectrograph2, image of star16 does not exist in spectrograph0, image of star16 does not exist in spectrograph2, image of star6 does not exist in spectrograph0, image of star6 does not exist in spectrograph2, image of star6 does not exist in thermograph4, image of star7 does not exist in infrared3, image of star7 does not exist in spectrograph2, image of star8 does not exist in infrared3, image of star8 does not exist in spectrograph0, image of star8 does not exist in thermograph4, instrument0 is not powered on, instrument1 is not calibrated, instrument1 is not turned on, instrument2 is not calibrated, instrument2 is not switched on, instrument3 is not turned on, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation3 in infrared3, there is no image of direction groundstation3 in spectrograph2, there is no image of direction groundstation4 in infrared3, there is no image of direction groundstation4 in spectrograph0, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph2, there is no image of direction groundstation5 in spectrograph0, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph0, there is no image of direction phenomenon15 in spectrograph0, there is no image of direction phenomenon15 in thermograph4, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph1, there is no image of direction star0 in infrared3, there is no image of direction star0 in spectrograph2, there is no image of direction star10 in infrared3, there is no image of direction star10 in spectrograph0, there is no image of direction star11 in spectrograph0, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in spectrograph2, there is no image of direction star11 in thermograph4, there is no image of direction star12 in spectrograph1, there is no image of direction star12 in spectrograph2, there is no image of direction star13 in spectrograph0, there is no image of direction star13 in thermograph4, there is no image of direction star16 in infrared3, there is no image of direction star16 in spectrograph1, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph1, there is no image of direction star7 in spectrograph0, there is no image of direction star7 in spectrograph1, there is no image of direction star7 in thermograph4, there is no image of direction star8 in spectrograph1 and there is no image of direction star8 in spectrograph2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated, on satellite0, instrument0 is activated, from groundstation4, satellite1 turns towards star6, instrument3 that is on satellite1 is adjusted to star6, satellite1 turns from star6 towards planet14, an image of planet14 is captured with instrument3 on satellite1 in spectrograph1, satellite1 turns from planet14 to star10, satellite1's instrument3 captures an image of star10 in spectrograph1, satellite1 turns to star12 from star10 and an image of star12 is captured with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: the calibration of instrument0 is not complete, the calibration of instrument3 is not complete, there is no image of groundstation2 in spectrograph1, there is no image of groundstation3 in spectrograph0, there is no image of groundstation3 in spectrograph1, there is no image of groundstation3 in thermograph4, there is no image of groundstation4 in thermograph4, there is no image of groundstation5 in infrared3, there is no image of groundstation5 in thermograph4, there is no image of groundstation9 in spectrograph1, there is no image of groundstation9 in spectrograph2, there is no image of groundstation9 in thermograph4, there is no image of phenomenon15 in infrared3, there is no image of phenomenon15 in spectrograph1, there is no image of phenomenon15 in spectrograph2, there is no image of planet14 in spectrograph0, there is no image of planet14 in spectrograph2, there is no image of planet14 in thermograph4, there is no image of star0 in spectrograph0, there is no image of star0 in spectrograph1, there is no image of star0 in thermograph4, there is no image of star1 in infrared3, there is no image of star1 in spectrograph0, there is no image of star1 in spectrograph1, there is no image of star1 in spectrograph2, there is no image of star1 in thermograph4, there is no image of star10 in spectrograph1, there is no image of star10 in spectrograph2, there is no image of star10 in thermograph4, there is no image of star11 in infrared3, there is no image of star12 in infrared3, there is no image of star12 in spectrograph0, there is no image of star12 in thermograph4, there is no image of star13 in infrared3, there is no image of star13 in spectrograph1, there is no image of star13 in spectrograph2, there is no image of star16 in spectrograph0, there is no image of star16 in spectrograph2, there is no image of star6 in spectrograph0, there is no image of star6 in spectrograph2, there is no image of star6 in thermograph4, there is no image of star7 in infrared3, there is no image of star7 in spectrograph2, there is no image of star8 in infrared3, there is no image of star8 in spectrograph0, there is no image of star8 in thermograph4, instrument0 is not powered on, instrument1 is not calibrated, instrument1 is not turned on, instrument2 is not calibrated, instrument2 is not switched on, instrument3 is not turned on, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation3 in infrared3, there is no image of direction groundstation3 in spectrograph2, there is no image of direction groundstation4 in infrared3, there is no image of direction groundstation4 in spectrograph0, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph2, there is no image of direction groundstation5 in spectrograph0, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph0, there is no image of direction phenomenon15 in spectrograph0, there is no image of direction phenomenon15 in thermograph4, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph1, there is no image of direction star0 in infrared3, there is no image of direction star0 in spectrograph2, there is no image of direction star10 in infrared3, there is no image of direction star10 in spectrograph0, there is no image of direction star11 in spectrograph0, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in spectrograph2, there is no image of direction star11 in thermograph4, there is no image of direction star12 in spectrograph1, there is no image of direction star12 in spectrograph2, there is no image of direction star13 in spectrograph0, there is no image of direction star13 in thermograph4, there is no image of direction star16 in infrared3, there is no image of direction star16 in spectrograph1, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph1, there is no image of direction star7 in spectrograph0, there is no image of direction star7 in spectrograph1, there is no image of direction star7 in thermograph4, there is no image of direction star8 in spectrograph1 and there is no image of direction star8 in spectrograph2?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "9d08f6b0-98e6-4546-8395-2faaf42d7713", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_12", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns to phenomenon16 from groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, satellite1's instrument3 takes an image of planet11 in image3, from planet11, satellite1 turns to planet13, instrument3 which is on satellite1 takes an image of planet13 in image0, from planet13, satellite1 turns to planet14, satellite1's instrument3 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, image of star15 is taken with instrument3 on satellite1 in image2, satellite0 turns from groundstation3 to star1, calibration of instrument0 which is on satellite0 to star1 is complete and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, is it True or False that satellite1 is not pointing to star15?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, satellite1 reorients from phenomenon17 to planet11, instrument3 on satellite1 captures an image of planet11 in image3, satellite1 reorients from planet11 to planet13, instrument3 on satellite1 captures an image of planet13 in image0, satellite1 reorients from planet13 to planet14, instrument3 on satellite1 captures an image of planet14 in image0, satellite1 reorients from planet14 to star15, an image of star15 is captured using instrument3 on satellite1 in image2, satellite0 reorients from groundstation3 to star1, instrument0 on satellite0 is calibrated to star1, and satellite0 reorients from star1 to phenomenon10 to reach the current state. In this state, is it True or False that satellite1 is not pointing to star15?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current direction towards groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "70028976-3cad-4f84-bb11-81718280dac1", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns to star10 from planet13, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 that is on satellite0 is turned off, on satellite0, instrument2 is switched on, satellite0 turns from star10 to star4, calibration of instrument2 which is on satellite0 to star4 is complete, satellite0 turns from star4 to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, is it True or False that for groundstation8, instrument0 is not calibrated?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 captures an image of planet13 and stores it in spectrograph2, satellite0 reorients from planet13 to star10, instrument1 on satellite0 captures an image of star10 and stores it in image6, another image of star10 is taken by instrument1 on satellite0 and stored in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, instrument2 on satellite0 captures an image of star16 and stores it in image0, and finally, instrument2 on satellite0 is deactivated to reach the current state. In this state, is it True or False that instrument0 is not calibrated for groundstation8?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board."}
{"question_id": "6a7e0c73-5246-4d9a-9cac-0b93785e501f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns to star6 from groundstation4, instrument3 that is on satellite1 is calibrated to star6, satellite1 turns from star6 to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, image of star10 is taken with instrument3 on satellite1 in spectrograph1, from star10, satellite1 turns to star12, instrument3 which is on satellite1 takes an image of star12 in spectrograph1, satellite1 turns to star0 from star12, satellite0 turns from star1 to groundstation2, calibration of instrument0 which is on satellite0 to groundstation2 is complete, satellite0 turns from groundstation2 to phenomenon15, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, from phenomenon15, satellite0 turns to star11, image of star11 is taken with instrument0 on satellite0 in thermograph4, from star11, satellite0 turns to star13 and instrument0 which is on satellite0 takes an image of star13 in spectrograph0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: image of groundstation2 does not exist in infrared3, image of groundstation2 does not exist in spectrograph1, image of groundstation3 does not exist in spectrograph0, image of groundstation3 does not exist in spectrograph1, image of groundstation3 does not exist in spectrograph2, image of groundstation3 does not exist in thermograph4, image of groundstation4 does not exist in infrared3, image of groundstation4 does not exist in spectrograph0, image of groundstation4 does not exist in spectrograph1, image of groundstation4 does not exist in spectrograph2, image of groundstation4 does not exist in thermograph4, image of groundstation5 does not exist in spectrograph0, image of groundstation5 does not exist in spectrograph1, image of groundstation5 does not exist in thermograph4, image of groundstation9 does not exist in infrared3, image of groundstation9 does not exist in spectrograph2, image of groundstation9 does not exist in thermograph4, image of phenomenon15 does not exist in spectrograph2, image of planet14 does not exist in spectrograph2, image of star0 does not exist in infrared3, image of star1 does not exist in infrared3, image of star1 does not exist in spectrograph1, image of star10 does not exist in infrared3, image of star11 does not exist in infrared3, image of star11 does not exist in spectrograph0, image of star12 does not exist in spectrograph0, image of star12 does not exist in spectrograph2, image of star13 does not exist in infrared3, image of star16 does not exist in spectrograph1, image of star16 does not exist in spectrograph2, image of star7 does not exist in spectrograph0, image of star7 does not exist in thermograph4, instrument1 is not calibrated, instrument1 is not powered on, instrument2 is not calibrated, instrument2 is not powered on, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation3 in infrared3, there is no image of direction groundstation5 in infrared3, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation9 in spectrograph0, there is no image of direction groundstation9 in spectrograph1, there is no image of direction phenomenon15 in infrared3, there is no image of direction phenomenon15 in spectrograph1, there is no image of direction phenomenon15 in thermograph4, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph0, there is no image of direction planet14 in thermograph4, there is no image of direction star0 in spectrograph0, there is no image of direction star0 in spectrograph1, there is no image of direction star0 in spectrograph2, there is no image of direction star0 in thermograph4, there is no image of direction star1 in spectrograph0, there is no image of direction star1 in spectrograph2, there is no image of direction star1 in thermograph4, there is no image of direction star10 in spectrograph0, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in thermograph4, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in spectrograph2, there is no image of direction star12 in infrared3, there is no image of direction star12 in thermograph4, there is no image of direction star13 in spectrograph1, there is no image of direction star13 in spectrograph2, there is no image of direction star13 in thermograph4, there is no image of direction star16 in infrared3, there is no image of direction star16 in spectrograph0, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph0, there is no image of direction star6 in spectrograph1, there is no image of direction star6 in spectrograph2, there is no image of direction star6 in thermograph4, there is no image of direction star7 in infrared3, there is no image of direction star7 in spectrograph1, there is no image of direction star7 in spectrograph2, there is no image of direction star8 in infrared3, there is no image of direction star8 in spectrograph0, there is no image of direction star8 in spectrograph1, there is no image of direction star8 in spectrograph2 and there is no image of direction star8 in thermograph4?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the instrument3 on satellite1 is activated, the instrument0 on satellite0 is turned on, satellite1 changes its orientation from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then changes its orientation from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 then changes its orientation from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, from star10, satellite1 changes its orientation to star12, an image of star12 is captured using instrument3 on satellite1 in spectrograph1, satellite1 then changes its orientation from star12 to star0, satellite0 changes its orientation from star1 to groundstation2, the calibration of instrument0 on satellite0 to groundstation2 is completed, satellite0 then changes its orientation from groundstation2 to phenomenon15, an image of phenomenon15 is captured using instrument0 on satellite0 in spectrograph0, from phenomenon15, satellite0 changes its orientation to star11, an image of star11 is captured using instrument0 on satellite0 in thermograph4, from star11, satellite0 changes its orientation to star13 and an image of star13 is captured using instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: there is no image of groundstation2 in infrared3, there is no image of groundstation2 in spectrograph1, there is no image of groundstation3 in spectrograph0, there is no image of groundstation3 in spectrograph1, there is no image of groundstation3 in spectrograph2, there is no image of groundstation3 in thermograph4, there is no image of groundstation4 in infrared3, there is no image of groundstation4 in spectrograph0, there is no image of groundstation4 in spectrograph1, there is no image of groundstation4 in spectrograph2, there is no image of groundstation4 in thermograph4, there is no image of groundstation5 in spectrograph0, there is no image of groundstation5 in spectrograph1, there is no image of groundstation5 in thermograph4, there is no image of groundstation9 in infrared3, there is no image of groundstation9 in spectrograph2, there is no image of groundstation9 in thermograph4, there is no image of phenomenon15 in spectrograph2, there is no image of planet14 in spectrograph2, there is no image of star0 in infrared3, there is no image of star1 in infrared3, there is no image of star1 in spectrograph1, there is no image of star10 in infrared3, there is no image of star11 in infrared3, there is no image of star11 in spectrograph0, there is no image of star12 in spectrograph0, there is no image of star12 in spectrograph2, there is no image of star13 in infrared3, there is no image of star16 in spectrograph1, there is no image of star16 in spectrograph2, there is no image of star7 in spectrograph0, there is no image of star7 in thermograph4, instrument1 is not calibrated and not powered on, instrument2 is not calibrated and not powered on, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation3 in infrared3, there is no image of direction groundstation5 in infrared3, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation9 in spectrograph0, there is no image of direction groundstation9 in spectrograph1, there is no image of direction phenomenon15 in infrared3, there is no image of direction phenomenon15 in spectrograph1, there is no image of direction phenomenon15 in thermograph4, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph0, there is no image of direction planet14 in thermograph4, there is no image of direction star0 in spectrograph0, there is no image of direction star0 in spectrograph1, there is no image of direction star0 in spectrograph2, there is no image of direction star0 in thermograph4, there is no image of direction star1 in spectrograph0, there is no image of direction star1 in spectrograph2, there is no image of direction star1 in thermograph4, there is no image of direction star10 in spectrograph0, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in thermograph4, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in spectrograph2, there is no image of direction star12 in infrared3, there is no image of direction star12 in thermograph4, there is no image of direction star13 in spectrograph1, there is no image of direction star13 in spectrograph2, there is no image of direction star13 in thermograph4, there is no image of direction star16 in infrared3, there is no image of direction star16 in spectrograph0, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph0, there is no image of direction star6 in spectrograph1, there is no image of direction star6 in spectrograph2, there is no image of direction star6 in thermograph4, there is no image of direction star7 in infrared3, there is no image of direction star7 in spectrograph1, there is no image of direction star7 in spectrograph2, there is no image of direction star8 in infrared3, there is no image of direction star8 in spectrograph0, there is no image of direction star8 in spectrograph1, there is no image of direction star8 in spectrograph2 and there is no image of direction star8 in thermograph4?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "8831bff5-7f0c-479f-aea5-aac43b39ee75", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, from planet11, satellite0 turns to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, is it True or False that image of planet13 does not exist in spectrograph2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5 and image6, subsequently, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 and stores it in image5, and another image of planet13 is taken by instrument1 on satellite0 and stored in spectrograph2, finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, is it True or False that an image of planet13 does not exist in spectrograph2?", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board."}
{"question_id": "0efe7002-6c2d-4712-be0f-93d3919e702c", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: groundstation5 is not where satellite0 is pointed, phenomenon16 is not where satellite1 is pointed, planet11 is not where satellite0 is pointed, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards phenomenon10, satellite0 is not aimed towards phenomenon16, satellite0 is not aimed towards planet13, satellite0 is not aimed towards planet14, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation7, satellite0 is not pointing to phenomenon17, satellite0 is not pointing to planet12, satellite0 is not pointing to star1, satellite0 is not pointing to star15, satellite0 is not pointing to star8, satellite0 is not pointing to star9, satellite1 is not aimed towards groundstation0, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards groundstation7, satellite1 is not aimed towards planet11, satellite1 is not aimed towards star1, satellite1 is not aimed towards star15, satellite1 is not pointing to groundstation4, satellite1 is not pointing to phenomenon17, satellite1 is not pointing to planet12, satellite1 is not pointing to planet13, satellite1 is not pointing to planet14, satellite1 is not pointing to star6, satellite1 is not pointing to star8, star6 is not where satellite0 is pointed and star9 is not where satellite1 is pointed?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, are all of the following properties that involve negations True or False: satellite0 is not oriented towards groundstation5, satellite1 is not oriented towards phenomenon16, satellite0 is not oriented towards planet11, satellite0 is not directed at groundstation4, satellite0 is not directed at phenomenon10, satellite0 is not directed at phenomenon16, satellite0 is not directed at planet13, satellite0 is not directed at planet14, satellite0 is not pointing towards groundstation0, satellite0 is not pointing towards groundstation2, satellite0 is not pointing towards groundstation7, satellite0 is not pointing towards phenomenon17, satellite0 is not pointing towards planet12, satellite0 is not pointing towards star1, satellite0 is not pointing towards star15, satellite0 is not pointing towards star8, satellite0 is not pointing towards star9, satellite1 is not directed at groundstation0, satellite1 is not directed at groundstation2, satellite1 is not directed at groundstation3, satellite1 is not directed at groundstation5, satellite1 is not directed at groundstation7, satellite1 is not directed at planet11, satellite1 is not directed at star1, satellite1 is not directed at star15, satellite1 is not pointing towards groundstation4, satellite1 is not pointing towards phenomenon17, satellite1 is not pointing towards planet12, satellite1 is not pointing towards planet13, satellite1 is not pointing towards planet14, satellite1 is not pointing towards star6, satellite1 is not pointing towards star8, and satellite0 is not oriented towards star6, and satellite1 is not oriented towards star9?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, has available power, and is currently pointing towards groundstation3. Satellite1 carries instrument3 on board, has power available, and is pointing towards phenomenon10."}
{"question_id": "e2d1d753-bb22-4155-a565-e5d69ef3048a", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, satellite1 turns to star6 from groundstation4, instrument3 that is on satellite1 is calibrated to star6, satellite1 turns to planet14 from star6, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, from planet14, satellite1 turns to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12, satellite1's instrument3 takes an image of star12 in spectrograph1, satellite1 turns from star12 to star0, satellite0 turns to groundstation2 from star1, instrument0 is calibrated on satellite0 to groundstation2, satellite0 turns from groundstation2 to phenomenon15, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, satellite0 turns to star11 from phenomenon15, instrument0 which is on satellite0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and image of star13 is taken with instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: star0 is where satellite1 is pointed and star13 is where satellite0 is pointed?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 reorients from planet14 to star10, instrument3 on satellite1 captures an image of star10 in spectrograph1, satellite1 reorients from star10 to star12, an image of star12 is captured using instrument3 on satellite1 in spectrograph1, satellite1 then reorients from star12 to star0, satellite0 reorients from star1 to groundstation2, instrument0 on satellite0 is calibrated to groundstation2, satellite0 reorients from groundstation2 to phenomenon15, an image of phenomenon15 is captured using instrument0 on satellite0 in spectrograph0, satellite0 reorients from phenomenon15 to star11, instrument0 on satellite0 captures an image of star11 in thermograph4, and finally, satellite0 reorients from star11 to star13, capturing an image of star13 using instrument0 on satellite0 in spectrograph0, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: satellite1 is currently pointed at star0 and satellite0 is currently pointed at star13?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
