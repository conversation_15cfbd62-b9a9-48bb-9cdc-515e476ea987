{"question_id": "25c0048f-46c6-4a51-ba9c-dd843f255c3b", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob walks to location3 from location2, bob walks from location3 to location4, bob walks to location5 from location4, spanner4 is picked up by bob from location5, from location5 to location6, bob walks, spanner1 is picked up by bob from location6, bob walks from location6 to location7 and from location7, bob picks up spanner5 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "440", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and subsequently location5. At location5, Bob picks up spanner4. He then proceeds to location6, where he picks up spanner1, and continues to location7. At location7, Bob picks up spanner5, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, and location4 is connected to location5. On the other hand, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and located at location8, spanner3, which is usable and also at location8, spanner4, which is at location5, and spanner5, which is located at location7 and is usable."}
{"question_id": "cbcd33b0-36fe-4ec2-b31c-73edfc52c982", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob walks from shed to location1, from location1 to location2, bob walks, bob walks to location3 from location2, from location3 to location4, bob walks, from location4 to location5, bob walks, bob picks up spanner4 from location5, bob walks from location5 to location6, from location6, bob picks up spanner1, bob walks to location7 from location6 and nut5 is tightened by bob using spanner3 at location9 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "9", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: Bob moves from the shed to location1, then to location2, then walks, then proceeds from location2 to location3, from location3 to location4, then walks, from location4 to location5, then walks, picks up spanner4 at location5, moves from location5 to location6, picks up spanner1 at location6, walks from location6 to location7, and finally, at location9, Bob tightens nut5 using spanner3 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and at location8, spanner3, which is usable and at location8, spanner4, which is at location5, and spanner5, which is usable and located at location7."}
{"question_id": "025811c1-f5a7-4086-9bd3-b129ce20d102", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks from location5 to location6, from location6, bob picks up spanner5 and from location6, bob picks up spanner2 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "439", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3. He then proceeds to walk from location2 to location3, from location3 to location4, from location4 to location5, and from location5 to location6. At location6, Bob picks up spanner5 and spanner2, resulting in the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "cb6547b3-e117-402a-b4a2-7d1226151e37", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, from location2, bob picks up spanner3, bob walks to location3 from location2, spanner5 is picked up by bob from location3, bob picks up spanner1 from location3, bob walks from location3 to location4, bob walks to location5 from location4, spanner2 is picked up by bob from location5 and from location5 to location6, bob walks to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "2", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner3. From location2, he proceeds to location3, where he picks up both spanner5 and spanner1. Bob then walks to location4 and subsequently to location5, where he collects spanner2. Finally, he moves from location5 to location6, reaching the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, the following items are found: nut1, which is unsecured, nut2, also unsecured, nut3, which is currently unsecured, nut4, which is loose, and nut5, also loose. The following spanners are available: spanner1, which is usable and located at location3, spanner2, which is functional and situated at location5, spanner3, which is usable and at location2, spanner4, which can be used and is at location6, and spanner5, which is functional and located at location3."}
{"question_id": "aa9cde8a-42eb-41ac-b3f4-1f21e4093e6c", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, from location2, bob picks up spanner3, bob walks from location2 to location3, bob picks up spanner5 from location3, from location3, bob picks up spanner1, bob walks from location3 to location4, bob walks to location5 from location4, bob picks up spanner2 from location5, from location5 to location6, bob walks, spanner4 is picked up by bob from location6, bob walks to location7 from location6, bob walks to location8 from location7, bob walks from location8 to location9, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "27", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2. At location2, Bob picks up spanner3, then proceeds to location3, where he collects spanner5 and spanner1. From location3, Bob walks to location4, then to location5, where he picks up spanner2. He then moves from location5 to location6, collects spanner4, and continues to location7, then to location8, and finally to location9. From location9, Bob walks to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "6342b6f3-76e6-4f40-9ead-c5c5d674d03b", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "251", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, what is the total count of valid state properties (including both affirmative and negated properties)? Provide the answer as an integer, or None if there are no valid properties.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and at location6, and spanner5 is at location3 and functional."}
{"question_id": "202287ce-4b5c-4764-aaab-98f88a81cc9a", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, spanner4 is picked up by bob from location2, bob picks up spanner3 from location2, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks from location4 to location5, bob walks from location5 to location6, from location6, bob picks up spanner5, from location6, bob picks up spanner2, from location6 to location7, bob walks, bob walks from location7 to location8, from location8, bob picks up spanner1, bob walks from location8 to location9, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "224", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He proceeds to location3, then location4, and subsequently walks to location5 and location6. At location6, Bob picks up spanner5 and spanner2, then walks to location7 and location8, where he collects spanner1. He then moves to location9 and finally reaches the gate. Upon arrival, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and also uses spanner2 to tighten nut4. In this resulting state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently situated at the gate and is loose, nut2 is at the gate and is loose, nut3 is located at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working condition and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "85f565df-d2f6-4a9c-9b90-8619cfe39846", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "31", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, what is the total count of valid properties that do not include negations? Express the answer as an integer, or write None if there are no such properties.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and is loose, nut2 is also located at the gate and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is usable and is at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is usable, and spanner5 is also at location1 and is usable."}
{"question_id": "07b4c34c-e514-457b-8578-cb01400d4f46", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "437", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is located and is loose, nut2 is also at the gate and is loose, nut3 is situated at the gate but is not secured, nut4 is at the gate and is loose, and nut5 is located at the gate but is not secured. Furthermore, the shed is connected to location1. Spanner1 is situated at location4 and is in working condition, spanner2 is located at location7 and can be used, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is in working condition, and spanner5 is also at location1 and is usable."}
{"question_id": "419e8535-f46e-4963-929d-b49db9c6df78", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob picks up spanner4 from location2, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob walks from location3 to location4, bob walks from location4 to location5, bob walks from location5 to location6, from location6, bob picks up spanner5, bob picks up spanner2 from location6, bob walks from location6 to location7, bob walks from location7 to location8, spanner1 is picked up by bob from location8, from location8 to location9, bob walks, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, bob tightens nut2 with spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "439", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he picks up spanner4 and spanner3. He then proceeds to location3, followed by location4, location5, and location6, where he collects spanner5 and spanner2. Next, Bob walks to location7, then location8, where he picks up spanner1, and finally moves to location9 before heading to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently located at the gate and is loose, nut2 is at the gate and is loose, nut3 is located at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is located at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "5ca68234-7e35-4047-a385-de66d234047e", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "31", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what is the total count of valid properties that do not involve negations? Provide the answer as an integer, or None if there are no such properties.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and located at location8, spanner3, which is usable and also at location8, spanner4, which is at location5, and spanner5, which is located at location7 and is usable."}
{"question_id": "6d933b18-dd72-4998-adc3-857993faf35f", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "251", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, what is the total count of valid state properties (including both affirmative and negated properties)? Provide the answer as an integer, or None if there are no valid properties.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently located at the gate and is loose, nut2 is at the gate and is loose, nut3 is located at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is located at the gate and is loose, spanner1 is in working condition and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "87d1ff2a-fa69-41c7-b1b0-7143694f4661", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner4 is picked up by bob from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, bob walks to location4 from location3, bob walks from location4 to location5, bob walks to location6 from location5, bob picks up spanner5 from location6, bob picks up spanner2 from location6, bob walks to location7 from location6, from location7 to location8, bob walks, from location8, bob picks up spanner1, bob walks from location8 to location9, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "440", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He proceeds to location3, then location4, and subsequently to location5. From there, he heads to location6, where he picks up spanner5 and spanner2. Bob then walks to location7, followed by location8, where he collects spanner1. He then moves to location9 and finally reaches the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently situated at the gate and is loose, nut2 is at the gate and is loose, nut3 is situated at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is in working order and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "a24018d5-0649-46d2-8acb-bde25eb549e5", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "439", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from the shed to reach the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "468f5fa3-818a-4ad8-846c-7f1debf37dde", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks to location6 from location5, from location6, bob picks up spanner5, from location6, bob picks up spanner2, bob walks from location6 to location7, bob walks from location7 to location8, from location8, bob picks up spanner1, bob walks from location8 to location9, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "27", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he collects spanner4 and spanner3. He proceeds from location2 to location3, then to location4, and from location4 to location5. Next, Bob walks from location5 to location6, where he picks up spanner5 and spanner2. He then moves from location6 to location7, and from location7 to location8, where he collects spanner1. From location8, Bob walks to location9 and then to the gate. At the gate, he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 and location2 are connected, location4 is connected to location5, location5 is connected to location6, location6 and location7 are connected, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "174635cb-a18f-4f80-80ea-877b5fefda97", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, spanner5 is picked up by bob from location1, from location1, bob picks up spanner4, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, bob picks up spanner1 from location4, from location4 to location5, bob walks, bob walks to location6 from location5 and from location6, bob picks up spanner3 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "439", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then collects spanner5 from location1, followed by picking up spanner4 from the same location. Next, Bob walks from location1 to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1, proceeds to location5, then to location6, and finally collects spanner3, resulting in the current state. In this state, what is the total number of actions that cannot be executed? Provide the answer as an integer, or write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and is loose, nut2 is also located there and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is usable and is at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is usable, and spanner5 is also at location1 and is usable."}
{"question_id": "0c03a630-e345-4666-9a76-dda8263d6caf", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks from location3 to location4, bob walks to location5 from location4, from location5, bob picks up spanner4, bob walks from location5 to location6, bob picks up spanner1 from location6, bob walks from location6 to location7 and bob picks up spanner5 from location7 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "220", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then to location2, then to location3, then to location4, and then to location5. From location5, Bob picks up spanner4, then walks to location6, picks up spanner1, walks to location7, and finally picks up spanner5 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. The shed is connected to location1. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. As for the spanners, spanner1 is available and is currently at location6. Spanner2 is functional and located at location8, while spanner3 is usable and also at location8. Spanner4 is available and at location5, and spanner5 is usable and located at location7."}
{"question_id": "7c63edef-22a0-4386-99c3-d30e17b7eaed", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "220", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, what is the total count of valid state properties that include negations? Express the answer as an integer, or write None if there are no such properties.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is also at the gate and not secured, nut3 is currently at the gate and not secured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and at location6, and spanner5 is at location3 and functional."}
{"question_id": "69d15917-1ef5-4043-8f7b-43cabffe49a9", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "439", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from the shed to reach the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, and location4 is connected to location5. On the other hand, location6 is linked to location7, location7 is connected to location8, and location8 is linked to location9. Additionally, location9 is connected to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and located at location8, spanner3, which is usable and also at location8, spanner4, which is at location5, and spanner5, which is located at location7 and is usable."}
{"question_id": "27c2b2d7-33ac-4331-a3df-b4eeb9ff56f0", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, spanner4 is picked up by bob from location2, from location2, bob picks up spanner3, bob walks to location3 from location2, bob walks from location3 to location4, bob walks from location4 to location5, bob walks from location5 to location6, bob picks up spanner5 from location6 and spanner2 is picked up by bob from location6 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "440", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3. He then proceeds to walk from location2 to location3, from location3 to location4, from location4 to location5, and from location5 to location6. At location6, Bob picks up spanner5 and spanner2, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "1ee5836d-8db0-403b-9ad0-1202ebba39f7", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob picks up spanner3 from location2, bob walks from location2 to location3, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, bob walks from location3 to location4, bob walks to location5 from location4, from location5, bob picks up spanner2 and bob walks from location5 to location6 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "251", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the sequence of actions is as follows: Bob moves from the shed to location1, then to location2, where he collects spanner3. He proceeds to location3, picks up spanner5 and spanner1, and then heads to location4. From there, he goes to location5, collects spanner2, and finally walks to location6, resulting in the current state. In this state, what is the total number of valid properties (including both affirmative and negated properties)? Please provide the answer as an integer, or 'None' if there are no valid properties.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "c28272ef-41b9-4ec3-aead-4e5849b84367", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, spanner5 is picked up by bob from location1, from location1, bob picks up spanner4, from location1 to location2, bob walks, bob walks from location2 to location3, from location3 to location4, bob walks, bob picks up spanner1 from location4, bob walks to location5 from location4, bob walks to location6 from location5 and spanner3 is picked up by bob from location6 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "440", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then picks up spanner5 and spanner4 from location1. He then proceeds to walk from location1 to location2, from location2 to location3, and from location3 to location4. At location4, Bob picks up spanner1 and walks to location5, then to location6, where he picks up spanner3, ultimately reaching the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and is loose, nut2 is also located at the gate and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is usable and is at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is usable, and spanner5 is also at location1 and is usable."}
