{"question_id": "09ce4c17-83af-46a2-bd7c-8077e8188ca3", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, block b9 is put down, block b2 is unstacked from top of block b6, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, the hand puts down the block b6, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1 and on top of block b4, block b8 is stacked to reach the current state. In this state, if from top of block b5, block b2 is unstacked, is it True or False that block b2 is not on block b5 and the hand is holding the block b2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from block b8, then placed down, block b2 is removed from the top of block b6, then stacked on top of block b5, block b6 is removed from the top of block b3, and then put down by the hand, block b3 is removed from the top of block b4, then stacked on top of block b9, and finally block b8 is removed from block b1 and stacked on top of block b4 to reach the current state. In this state, if block b2 is removed from the top of block b5, is it True or False that block b2 is no longer on block b5 and the hand is holding block b2?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "799ad12e-f39a-4198-b083-4ba4941f4117", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, if block b3 is put down on the table, is it True or False that block b3 is located on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b5 to reach the current state. In this state, if block b3 is placed on the table, is it True or False that block b3 is on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "46f665e7-ea0a-4bf4-85f9-96370ca50c99", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down on the table, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, the hand puts down the block b6, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, if block b2 is unstacked from block b5, is it True or False that block b2 is not clear, block b5 is clear and hand is holding some block?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8 and placed on the table, block b2 is removed from block b6 and placed on top of block b5, block b6 is removed from the top of block b3 and put down, block b3 is removed from the top of block b4 and stacked on top of block b9, and block b8 is removed from the top of block b1 and stacked on top of block b4 to achieve the current state. In this state, if block b2 is removed from block b5, is it True or False that block b2 is not clear, block b5 is clear, and the hand is holding a block?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is placed on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is also on the table, block b6 is positioned above block b3, block b7 is resting on the table, block b8 is on top of block b1, block b9 has no blocks on it, block b9 is positioned above block b8, and the hand is empty."}
{"question_id": "37e2c640-1752-4fbc-9b06-a7ae20902ffb", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down on the table, block b5 is unstacked from block b4, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, block b4 is put down, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked and block b7 is stacked on top of block b4 to reach the current state. In this state, if block b6 is picked up, is it True or False that block b5 is not clear and hand is not holding anything?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7 and placed on the table, then block b5 is removed from block b4 and placed on top of block b2, next block b4 is removed from block b1 and put down, after that block b5 is removed from the top of block b2 and placed on top of block b1, and finally block b7 is removed from the top of block b6 and placed on top of block b4 to achieve the current state. In this state, if block b6 is picked up, is it True or False that block b5 is not clear and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "6eda959a-13df-4f8d-90ff-ae6e435df08d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down, from top of block b6, block b1 is unstacked, block b1 is put down on the table, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, the hand puts down the block b6, block b7 is picked up by the hand and on top of block b2, block b7 is stacked to reach the current state. In this state, if from top of block b8, block b3 is unstacked, is it True or False that block b2 is not clear?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from the top of block b1 and placed down, then block b1 is removed from the top of block b6 and placed on the table. Next, block b2 is removed from the top of block b5, and then stacked on top of block b4. Block b6 is then removed from the top of block b7, placed down, and block b7 is picked up and stacked on top of block b2 to reach the current state. In this state, if block b3 is removed from the top of block b8, is it True or False that block b2 is not clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "d9a7bc9b-f829-4fe0-8a71-878dbdd0f66b", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down, block b1 is unstacked from block b6, the hand puts down the block b1, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, from top of block b7, block b6 is unstacked, the hand puts down the block b6, block b7 is picked up by the hand, on top of block b2, block b7 is stacked, from top of block b8, block b3 is unstacked, on top of block b7, block b3 is stacked, block b1 is picked up from the table, block b1 is stacked on top of block b3, block b5 is picked up from the table, on top of block b1, block b5 is stacked, block b8 is picked up, on top of block b5, block b8 is stacked and block b6 is picked up to reach the current state. In this state, if on top of block b8, block b6 is stacked, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is not being held, block b1 is not clear, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b4, block b1 is not on block b7, block b1 is not on top of block b5, block b1 is not on top of block b6, block b1 is not placed on top of block b8, block b1 is on top of block b3, block b2 is not being held, block b2 is not clear, block b2 is not on block b5, block b2 is not on block b8, block b2 is not on the table, block b2 is not on top of block b7, block b2 is not placed on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b6, block b2 is on block b4, block b3 is not being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b2, block b3 is not on block b8, block b3 is not on top of block b4, block b3 is not placed on top of block b1, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b3 is on block b7, block b4 is located on the table, block b4 is not clear, block b4 is not on block b5, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b2, block b4 is not placed on top of block b3, block b4 is not placed on top of block b6, block b4 is not placed on top of block b8, block b5 is not clear, block b5 is not located on the table, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on block b6, block b5 is not on block b8, block b5 is not on top of block b4, block b5 is not placed on top of block b7, block b5 is on top of block b1, block b6 is clear, block b6 is not on block b2, block b6 is not on block b5, block b6 is not on the table, block b6 is not on top of block b3, block b6 is not placed on top of block b1, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b6 is on block b8, block b7 is not being held by the hand, block b7 is not clear, block b7 is not located on the table, block b7 is not on top of block b3, block b7 is not on top of block b6, block b7 is not placed on top of block b1, block b7 is not placed on top of block b4, block b7 is not placed on top of block b5, block b7 is not placed on top of block b8, block b7 is on block b2, block b8 is not clear, block b8 is not on block b4, block b8 is not on block b6, block b8 is not on the table, block b8 is not on top of block b1, block b8 is not on top of block b3, block b8 is not on top of block b7, block b8 is not placed on top of block b2, block b8 is on top of block b5, hand is empty, the hand is not holding the block b4, the hand is not holding the block b5, the hand is not holding the block b6 and the hand is not holding the block b8", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1 and placed down, then block b1 is removed from block b6 and put down by the hand. Next, block b2 is unstacked from the top of block b5, and then stacked on top of block b4. Block b6 is then unstacked from the top of block b7 and put down by the hand. The hand then picks up block b7 and stacks it on top of block b2. Block b3 is unstacked from the top of block b8 and stacked on top of block b7. Block b1 is picked up from the table and stacked on top of block b3. Block b5 is then picked up from the table and stacked on top of block b1. Block b8 is picked up and stacked on top of block b5, and finally, block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b8, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "ed3cc06f-92bc-42db-8ad0-d4cee55f7983", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down, from top of block b6, block b2 is unstacked, the hand puts down the block b2, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked, block b7 is put down on the table, from top of block b4, block b1 is unstacked, on top of block b2, block b1 is stacked, block b4 is picked up by the hand, block b4 is stacked on top of block b5, block b1 is unstacked from block b2, block b1 is stacked on top of block b4, block b2 is picked up by the hand, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, if on top of block b2, block b7 is stacked, is it True or False that block b7 is placed on top of block b2 and the hand is not holding the block b7?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed on the table, then block b2 is unstacked from the top of block b6 and put down by the hand. Next, block b6 is picked up from the table and stacked on top of block b3. Block b5 is then unstacked from the top of block b7 and stacked on top of block b6. Block b7 is unstacked from the top of block b1 and placed on the table. Block b1 is then unstacked from the top of block b4 and stacked on top of block b2. Block b4 is picked up by the hand and stacked on top of block b5. Block b1 is then unstacked from block b2 and stacked on top of block b4. Block b2 is picked up by the hand and stacked on top of block b1. Finally, block b7 is picked up by the hand to reach the current state. In this state, if block b7 is stacked on top of block b2, is it True or False that block b7 is placed on top of block b2 and the hand is not holding block b7?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "b9b28544-66d0-4e0a-a0f4-b265997f19a0", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1 to reach the current state. In this state, if block b4 is put down, is it True or False that block b7 is not placed on top of block b1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is removed from the top of block b1 to reach the current state. In this state, if block b4 is placed down, is it True or False that block b7 is not on top of block b1?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "91880190-aa56-4259-80b3-639a79b1c714", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is put down, is it True or False that block b2 is on the table?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is put down, is it True or False that block b2 is on the table?\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is put down, is it True or False that block b2 is on the table?\n\nSince the original text is already clear and concise, the paraphrased text remains the same.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is positioned on block b8, block b4 has no blocks on it, block b4 is placed on block b1, block b5 is directly on the table, block b6 is stacked on block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is currently empty."}
{"question_id": "0ef2f2c7-7b0d-49bc-8130-14e48ac6e994", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, if block b3 is put down on the table, is it True or False that block b1 is clear and block b7 is not clear?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b7 to reach the current state. In this state, if block b3 is placed on the table, is it True or False that block b1 is clear and block b7 is not clear?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is resting on top of block b6, and the hand is empty."}
{"question_id": "7c049050-ee40-4bd4-959c-d2b429005bc7", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, from top of block b4, block b5 is unstacked, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, block b4 is put down on the table, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked, block b7 is stacked on top of block b4, block b6 is picked up from the table, on top of block b5, block b6 is stacked, block b2 is picked up, on top of block b6, block b2 is stacked, block b3 is picked up from the table, block b3 is stacked on top of block b2, block b7 is unstacked from block b4, block b7 is stacked on top of block b3 and block b4 is picked up from the table to reach the current state. In this state, if block b4 is stacked on top of block b7, is it True or False that block b1 is not clear?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, then placed on the table, block b5 is unstacked from block b4, and then stacked on top of block b2, block b4 is unstacked from block b1 and placed on the table, block b5 is unstacked from block b2 and then stacked on top of block b1, block b7 is unstacked from block b6 and stacked on top of block b4, block b6 is picked up from the table and stacked on top of block b5, block b2 is picked up and stacked on top of block b6, block b3 is picked up from the table and stacked on top of block b2, block b7 is unstacked from block b4 and stacked on top of block b3, and block b4 is picked up from the table to reach the current state. In this state, if block b4 is stacked on top of block b7, is it True or False that block b1 is not clear?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is positioned on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is positioned on the table, block b7 is placed on top of block b6 and the hand is empty."}
{"question_id": "9424a7b3-670d-4d3f-a0fc-1bf89624deb1", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, from top of block b6, block b2 is unstacked, block b2 is put down, block b6 is picked up by the hand, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, block b7 is unstacked from top of block b1 and the hand puts down the block b7 to reach the current state. In this state, if block b1 is unstacked from block b4, is it True or False that block b1 is not clear, block b4 is clear and hand is holding some block?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from block b5, then placed down, block b2 is removed from the top of block b6 and put down, block b6 is picked up by the hand, then stacked on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, and finally block b7 is removed from the top of block b1 and placed down by the hand to reach the current state. In this state, if block b1 is removed from block b4, is it True or False that block b1 is not clear, block b4 is clear and the hand is holding a block?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "f8a43998-a15a-486f-8793-d83cada95d03", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, the hand puts down the block b9, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, block b6 is put down on the table, from top of block b4, block b3 is unstacked, block b3 is stacked on top of block b9, block b8 is unstacked from block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, if block b2 is unstacked from top of block b5, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "block b1 is not being held by the hand, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b5, block b1 is not on block b8, block b1 is not on block b9, block b1 is not placed on top of block b3, block b1 is not placed on top of block b4, block b1 is not placed on top of block b6, block b2 is not clear, block b2 is not on block b4, block b2 is not on block b5, block b2 is not on block b8, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b7, block b2 is not placed on top of block b3, block b2 is not placed on top of block b6, block b2 is not placed on top of block b9, block b3 is not being held, block b3 is not on block b5, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not on top of block b7, block b3 is not on top of block b8, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b1, block b4 is not on block b8, block b4 is not on top of block b2, block b4 is not on top of block b3, block b4 is not on top of block b5, block b4 is not on top of block b6, block b4 is not placed on top of block b7, block b4 is not placed on top of block b9, block b5 is not on block b1, block b5 is not on block b6, block b5 is not on block b7, block b5 is not on block b9, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b8, block b5 is not placed on top of block b2, block b6 is not being held, block b6 is not on block b1, block b6 is not on block b3, block b6 is not on block b5, block b6 is not on block b8, block b6 is not on top of block b2, block b6 is not on top of block b7, block b6 is not placed on top of block b4, block b6 is not placed on top of block b9, block b7 is not being held, block b7 is not clear, block b7 is not on block b1, block b7 is not on block b4, block b7 is not on block b9, block b7 is not on top of block b3, block b7 is not placed on top of block b2, block b7 is not placed on top of block b5, block b7 is not placed on top of block b6, block b7 is not placed on top of block b8, block b8 is not being held, block b8 is not located on the table, block b8 is not on block b3, block b8 is not on top of block b1, block b8 is not on top of block b5, block b8 is not on top of block b7, block b8 is not placed on top of block b2, block b8 is not placed on top of block b6, block b8 is not placed on top of block b9, block b9 is not being held, block b9 is not clear, block b9 is not on block b3, block b9 is not on block b6, block b9 is not on top of block b2, block b9 is not on top of block b5, block b9 is not on top of block b7, block b9 is not placed on top of block b1, block b9 is not placed on top of block b4, block b9 is not placed on top of block b8, hand is holding some block and the hand is not holding the block b5", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from block b8, the hand releases block b9, block b2 is removed from the top of block b6 and placed on top of block b5, block b6 is removed from the top of block b3 and placed on the table, block b3 is removed from the top of block b4 and stacked on top of block b9, and block b8 is removed from block b1 and stacked on top of block b4 to achieve the current state. In this state, if block b2 is removed from the top of block b5, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "bee2d951-baeb-4482-abd6-d6b86b24db8c", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down on the table, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, the hand puts down the block b4, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, if block b6 is picked up from the table, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is located on the table, block b4 is on the table, block b5 is clear, block b5 is on block b1, block b6 is being held, block b7 is clear and block b7 is placed on top of block b4", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b7, then placed on the table, block b5 is removed from block b4 and placed on top of block b2, block b4 is removed from the top of block b1 and put down, block b5 is then removed from block b2 and stacked on top of block b1, and finally, block b7 is removed from the top of block b6 and stacked on top of block b4 to achieve the current state. In this state, if block b6 is lifted from the table, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is resting on top of block b6, and the hand is empty."}
{"question_id": "0d9d552a-7555-4b3b-bf34-d38ffa823dc4", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from top of block b6, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, block b6 is put down, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked and on top of block b4, block b8 is stacked to reach the current state. In this state, if block b2 is unstacked from top of block b5, is it True or False that block b3 is clear and block b6 is clear?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8, then placed down, block b2 is removed from the top of block b6, then stacked on top of block b5, block b6 is removed from the top of block b3, then placed down, block b3 is removed from the top of block b4 and stacked on top of block b9, and block b8 is removed from the top of block b1 and stacked on top of block b4 to reach the current state. In this state, if block b2 is removed from the top of block b5, is it True or False that block b3 is clear and block b6 is clear?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "66ca71d4-e373-4f18-b769-7f59d6f96b14", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked and on top of block b4, block b7 is stacked to reach the current state. In this state, if block b6 is picked up by the hand, is it True or False that block b6 is on the table?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7 and placed down, block b5 is removed from the top of block b4 and placed on top of block b2, block b4 is removed from block b1 and placed on the table, block b5 is then removed from the top of block b2 and placed on top of block b1, and block b7 is removed from the top of block b6 and placed on top of block b4, resulting in the current state. In this state, if block b6 is picked up by the hand, is it True or False that block b6 is on the table?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is resting on top of block b6, and the hand is empty."}
{"question_id": "b2b0245d-382a-4f63-8a1c-e26b14ec649c", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, if block b9 is put down on the table, is it True or False that block b9 is not being held?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, if block b9 is placed on the table, is it True or False that block b9 is not being held?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "0df53b9d-c592-4f16-b673-9e8dbfd2148d", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6 and block b7 is stacked on top of block b4 to reach the current state. In this state, if block b6 is picked up by the hand, is it True or False that block b3 is not placed on top of block b6?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following steps are taken: block b3 is removed from block b7, then placed on the table, block b5 is removed from block b4 and placed on top of block b2, block b4 is removed from block b1 and placed on the table, block b5 is then removed from block b2 and placed on top of block b1, and finally, block b7 is removed from block b6 and placed on top of block b4, resulting in the current state. In this state, if block b6 is picked up by the hand, is it True or False that block b3 is not on top of block b6?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "8f207e15-d7db-4e1a-bdc3-91abe35a041a", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down, block b1 is unstacked from top of block b6, block b1 is put down on the table, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, from top of block b7, block b6 is unstacked, the hand puts down the block b6, block b7 is picked up by the hand, block b7 is stacked on top of block b2, from top of block b8, block b3 is unstacked, block b3 is stacked on top of block b7, block b1 is picked up from the table, block b1 is stacked on top of block b3, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up from the table to reach the current state. In this state, if on top of block b8, block b6 is stacked, is it True or False that block b6 is clear?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1, then placed on the table, block b1 is then removed from block b6 and placed on the table, block b2 is removed from block b5, then stacked on top of block b4, block b6 is removed from block b7 and placed on the table, block b7 is picked up and stacked on top of block b2, block b3 is removed from block b8 and stacked on top of block b7, block b1 is picked up from the table and stacked on top of block b3, block b5 is picked up and stacked on top of block b1, block b8 is picked up from the table and stacked on top of block b5, and finally, block b6 is picked up from the table to reach the current state. In this state, if block b6 is stacked on top of block b8, is it True or False that block b6 is clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is positioned on the table, block b6 is placed on block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is currently empty."}
{"question_id": "9c277863-7b6b-4fed-9b8e-234e0b2c583f", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5 to reach the current state. In this state, if block b3 is put down, is it True or False that block b5 is placed on top of block b4?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is removed from the top of block b5 to reach the current state. In this state, if block b3 is placed down, is it True or False that block b5 is positioned on top of block b4?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "cc5bb240-2e74-431a-b96e-7d28bef46711", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, if block b9 is put down, is it True or False that block b9 is on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, if block b9 is placed on the table, is it True or False that block b9 is on the table?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8, and the hand is empty."}
{"question_id": "b644bc29-1a99-495a-8e6b-838291904f47", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7 to reach the current state. In this state, if block b3 is put down, is it True or False that block b3 is clear and hand is not holding anything?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7 to reach the current state. In this state, if block b3 is put down, is it True or False that block b3 is clear and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "82df0f11-f197-47ce-aa96-6bcbd1b91feb", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, the hand puts down the block b4, block b1 is unstacked from top of block b6, block b1 is put down, block b2 is unstacked from top of block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up from the table and block b7 is stacked on top of block b2 to reach the current state. In this state, if block b3 is unstacked from top of block b8, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is clear, block b1 is not being held by the hand, block b1 is not on block b2, block b1 is not on block b3, block b1 is not on block b6, block b1 is not on block b7, block b1 is not on top of block b4, block b1 is not on top of block b5, block b1 is not placed on top of block b8, block b1 is on the table, block b2 is not clear, block b2 is not on block b5, block b2 is not on block b6, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b8, block b2 is not placed on top of block b3, block b2 is not placed on top of block b7, block b2 is on block b4, block b3 is being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on block b4, block b3 is not on block b8, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b3 is not placed on top of block b7, block b4 is not being held, block b4 is not clear, block b4 is not on block b3, block b4 is not on top of block b2, block b4 is not on top of block b5, block b4 is not on top of block b6, block b4 is not on top of block b7, block b4 is not on top of block b8, block b4 is not placed on top of block b1, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b5 is not on block b1, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on block b4, block b5 is not on top of block b8, block b5 is not placed on top of block b6, block b5 is not placed on top of block b7, block b6 is clear, block b6 is located on the table, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on block b4, block b6 is not on block b5, block b6 is not placed on top of block b3, block b6 is not placed on top of block b7, block b6 is not placed on top of block b8, block b7 is clear, block b7 is not being held, block b7 is not located on the table, block b7 is not on top of block b4, block b7 is not on top of block b5, block b7 is not on top of block b6, block b7 is not on top of block b8, block b7 is not placed on top of block b1, block b7 is not placed on top of block b3, block b7 is on top of block b2, block b8 is clear, block b8 is not being held, block b8 is not on block b2, block b8 is not on block b4, block b8 is not on top of block b1, block b8 is not on top of block b7, block b8 is not placed on top of block b3, block b8 is not placed on top of block b5, block b8 is not placed on top of block b6, block b8 is on the table, hand is holding some block, the hand is not holding the block b2, the hand is not holding the block b5 and the hand is not holding the block b6", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from the top of block b1, block b4 is placed down, block b1 is removed from the top of block b6, block b1 is put down, block b2 is removed from the top of block b5, block b2 is stacked on top of block b4, block b6 is removed from block b7, block b6 is placed down, block b7 is picked up from the table, and block b7 is stacked on top of block b2 to achieve the current state. In this state, if block b3 is removed from the top of block b8, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "9d1da0d3-b28f-413b-91ce-7c30cdfd9616", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5 to reach the current state. In this state, if block b3 is put down on the table, is it True or False that block b7 is on the table?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is removed from the top of block b5 to reach the current state. In this state, if block b3 is placed on the table, is it True or False that block b7 is on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "5af5d082-435e-4dfe-bd5c-4b7401329681", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down on the table, from top of block b6, block b1 is unstacked, the hand puts down the block b1, block b2 is unstacked from block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up from the table and block b7 is stacked on top of block b2 to reach the current state. In this state, if block b3 is unstacked from top of block b8, is it True or False that block b3 is not placed on top of block b8 and the hand is holding the block b3?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1, then placed on the table, block b1 is then unstacked from block b6 and put down, block b2 is unstacked from block b5 and stacked on top of block b4, block b6 is unstacked from block b7 and placed on the table, and finally, block b7 is picked up and stacked on top of block b2, resulting in the current state. In this state, if block b3 is unstacked from the top of block b8, is it True or False that block b3 is no longer on top of block b8 and is being held by the hand?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is on top of block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "77674c18-d94f-4647-86c1-731920408d6d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, the hand puts down the block b4, block b1 is unstacked from top of block b6, block b1 is put down on the table, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up, block b7 is stacked on top of block b2, from top of block b8, block b3 is unstacked, on top of block b7, block b3 is stacked, block b1 is picked up, on top of block b3, block b1 is stacked, block b5 is picked up by the hand, on top of block b1, block b5 is stacked, block b8 is picked up, on top of block b5, block b8 is stacked and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b8, is it True or False that block b6 is not being held by the hand and block b6 is placed on top of block b8?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1, then block b4 is placed on the table by the hand, block b1 is unstacked from the top of block b6 and placed on the table, block b2 is unstacked from the top of block b5 and stacked on top of block b4, block b6 is unstacked from block b7 and placed on the table, block b7 is picked up and stacked on top of block b2, block b3 is unstacked from the top of block b8 and stacked on top of block b7, block b1 is picked up and stacked on top of block b3, block b5 is picked up by the hand and stacked on top of block b1, block b8 is picked up and stacked on top of block b5, and finally block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b8, is it True or False that block b6 is not being held by the hand and block b6 is placed on top of block b8?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is positioned on the table, block b6 is placed on block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is currently empty."}
{"question_id": "569635cf-e42f-4938-96f6-20acae553d2b", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down, block b1 is unstacked from block b6, block b1 is put down on the table, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up and block b7 is stacked on top of block b2 to reach the current state. In this state, if block b3 is unstacked from top of block b8, is it True or False that block b3 is not clear, block b8 is clear and hand is not empty?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1 and placed on the table, then block b1 is removed from block b6 and placed on the table. Next, block b2 is removed from the top of block b5 and stacked on top of block b4. Block b6 is then removed from block b7 and placed on the table, and block b7 is picked up and stacked on top of block b2, resulting in the current state. In this state, if block b3 is removed from the top of block b8, is it True or False that block b3 is not clear, block b8 is clear, and the hand is not empty?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is positioned on the table, block b6 is placed on block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is currently empty."}
{"question_id": "988696b6-725f-4c74-9eba-cd8b47cba9c6", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked to reach the current state. In this state, if the hand puts down the block b9, is it True or False that the hand is holding the block b8?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is removed from the top of block b8 to reach the current state. In this state, if the hand releases block b9, is it True or False that the hand is holding block b8?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "2ab3e45b-bb65-4a24-b4d6-e03809bf5123", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, the hand puts down the block b6, from top of block b4, block b3 is unstacked, block b3 is stacked on top of block b9, block b8 is unstacked from top of block b1, block b8 is stacked on top of block b4, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b8, block b3 is unstacked from block b9, block b3 is stacked on top of block b2, block b1 is unstacked from block b7, block b1 is stacked on top of block b3, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up to reach the current state. In this state, if on top of block b7, block b6 is stacked, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "block b1 is not being held by the hand, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b5, block b1 is not on block b6, block b1 is not placed on top of block b4, block b1 is not placed on top of block b7, block b1 is not placed on top of block b8, block b1 is not placed on top of block b9, block b2 is not being held, block b2 is not clear, block b2 is not located on the table, block b2 is not on block b5, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not placed on top of block b7, block b2 is not placed on top of block b9, block b3 is not being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b5, block b3 is not on block b6, block b3 is not on block b7, block b3 is not on block b8, block b3 is not on top of block b1, block b3 is not on top of block b4, block b3 is not on top of block b9, block b4 is not clear, block b4 is not on block b1, block b4 is not on top of block b5, block b4 is not on top of block b8, block b4 is not on top of block b9, block b4 is not placed on top of block b2, block b4 is not placed on top of block b3, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b5 is not being held by the hand, block b5 is not on block b1, block b5 is not on block b2, block b5 is not on block b4, block b5 is not on block b6, block b5 is not on top of block b3, block b5 is not on top of block b8, block b5 is not placed on top of block b7, block b5 is not placed on top of block b9, block b6 is not being held by the hand, block b6 is not located on the table, block b6 is not on block b4, block b6 is not on block b8, block b6 is not on top of block b3, block b6 is not on top of block b5, block b6 is not on top of block b9, block b6 is not placed on top of block b1, block b6 is not placed on top of block b2, block b7 is not clear, block b7 is not on block b3, block b7 is not on block b4, block b7 is not on block b6, block b7 is not on the table, block b7 is not on top of block b1, block b7 is not on top of block b8, block b7 is not placed on top of block b2, block b7 is not placed on top of block b5, block b8 is not being held by the hand, block b8 is not clear, block b8 is not located on the table, block b8 is not on block b1, block b8 is not on block b5, block b8 is not on top of block b2, block b8 is not on top of block b9, block b8 is not placed on top of block b3, block b8 is not placed on top of block b6, block b8 is not placed on top of block b7, block b9 is not clear, block b9 is not on block b2, block b9 is not on block b4, block b9 is not on block b5, block b9 is not on block b6, block b9 is not on block b7, block b9 is not on block b8, block b9 is not placed on top of block b1, block b9 is not placed on top of block b3, the hand is not holding the block b4, the hand is not holding the block b7 and the hand is not holding the block b9", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8 and placed down, block b2 is removed from the top of block b6 and placed on top of block b5, block b6 is then removed from the top of block b3 and put down, block b3 is removed from the top of block b4 and stacked on top of block b9, block b8 is removed from the top of block b1 and stacked on top of block b4, block b2 is then removed from the top of block b5 and stacked on top of block b8, block b3 is removed from block b9 and stacked on top of block b2, block b1 is removed from block b7 and stacked on top of block b3, block b7 is picked up and stacked on top of block b9, and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b7, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "28f0e1fa-4c77-4ed6-b19d-40bc0330ed5a", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, the hand puts down the block b4, block b1 is unstacked from block b6, block b1 is put down, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, the hand puts down the block b6, block b7 is picked up from the table and block b7 is stacked on top of block b2 to reach the current state. In this state, if from top of block b8, block b3 is unstacked, is it True or False that block b3 is not placed on top of block b8 and block b3 is on block b6?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed down, then block b1 is unstacked from block b6 and put down, block b2 is unstacked from block b5, placed on top of block b4, block b6 is removed from the top of block b7 and put down, and block b7 is picked up from the table and stacked on top of block b2, resulting in the current state. In this state, if block b3 is unstacked from the top of block b8, is it True or False that block b3 is not placed on top of block b8 and instead is placed on block b6?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "95eb6088-6e34-4f7e-bbbb-14149b000c7a", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, the hand puts down the block b6, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1, on top of block b4, block b8 is stacked, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, on top of block b2, block b3 is stacked, block b1 is unstacked from block b7, on top of block b3, block b1 is stacked, block b7 is picked up, on top of block b9, block b7 is stacked and block b6 is picked up to reach the current state. In this state, if on top of block b7, block b6 is stacked, is it True or False that block b6 is clear, block b7 is not clear and hand is empty?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8, then block b9 is placed down by the hand, block b2 is removed from block b6 and placed on top of block b5, block b6 is then removed from block b3 and placed down by the hand, block b3 is removed from the top of block b4 and placed on top of block b9, block b8 is removed from block b1 and placed on top of block b4, block b2 is then removed from the top of block b5 and placed on top of block b8, block b3 is removed from the top of block b9 and placed on top of block b2, block b1 is removed from block b7 and placed on top of block b3, block b7 is picked up and placed on top of block b9, and finally block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b7, is it True or False that block b6 is clear, block b7 is not clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "4eb9b026-da37-4379-a5cf-745b00f55b52", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, if block b9 is put down, is it True or False that block b9 is clear and hand is not holding anything?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, if block b9 is put down, is it True or False that block b9 is clear and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is above block b7, block b2 has no blocks on it, block b2 is placed on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 has no blocks on it, block b5 is on the table, block b6 is above block b3, block b7 is on the table, block b8 is on block b1, block b9 has no blocks on it, block b9 is on top of block b8, and the hand is empty."}
{"question_id": "69da1060-5b8b-4f88-aefb-fa890c69d2e6", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down on the table, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, block b6 is put down on the table, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked and on top of block b4, block b8 is stacked to reach the current state. In this state, if from top of block b5, block b2 is unstacked, is it True or False that block b9 is not on top of block b6?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 and placed on the table, then block b2 is removed from the top of block b6 and placed on top of block b5. Next, block b6 is removed from the top of block b3 and placed on the table, block b3 is then removed from the top of block b4 and placed on top of block b9. Finally, block b8 is removed from the top of block b1 and placed on top of block b4, resulting in the current state. In this state, if block b2 is removed from the top of block b5, is it True or False that block b9 is not on top of block b6?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8, and the hand is empty."}
{"question_id": "b633e7d2-0ed0-4c4d-9dda-7d8e1d467a6e", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, the hand puts down the block b3, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, the hand puts down the block b4, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, from top of block b6, block b7 is unstacked, block b7 is stacked on top of block b4, block b6 is picked up by the hand, block b6 is stacked on top of block b5, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up, block b3 is stacked on top of block b2, block b7 is unstacked from block b4, on top of block b3, block b7 is stacked and block b4 is picked up to reach the current state. In this state, if on top of block b7, block b4 is stacked, is it True or False that block b2 is placed on top of block b6 and block b3 is not on block b4?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed on the table, block b5 is then removed from the top of block b4 and placed on top of block b2, block b4 is removed from block b1 and placed on the table, block b5 is then removed from the top of block b2 and placed on top of block b1, block b7 is removed from the top of block b6 and placed on top of block b4, block b6 is picked up and placed on top of block b5, block b2 is picked up and placed on top of block b6, block b3 is picked up and placed on top of block b2, block b7 is removed from block b4 and placed on top of block b3, and finally, block b4 is picked up to reach the current state. In this state, if block b4 is placed on top of block b7, is it True or False that block b2 is on top of block b6 and block b3 is not on block b4?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is resting on top of block b6, and the hand is empty."}
{"question_id": "96d81885-86c3-473d-901e-f74e89dbd602", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up by the hand, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked and block b7 is put down on the table to reach the current state. In this state, if block b1 is unstacked from top of block b4, is it True or False that block b2 is not placed on top of block b1 and block b6 is not on top of block b1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5 and placed down, then block b2 is removed from the top of block b6 and placed down, block b6 is picked up and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, and finally, block b7 is removed from the top of block b1 and placed on the table, resulting in the current state. In this state, if block b1 is removed from the top of block b4, is it True or False that block b2 is not on top of block b1 and block b6 is not on top of block b1?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "397397fe-6eec-4dc4-a1f4-e20f62036ef6", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, the hand puts down the block b3, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, block b4 is put down on the table, block b5 is unstacked from block b2, on top of block b1, block b5 is stacked, from top of block b6, block b7 is unstacked, on top of block b4, block b7 is stacked, block b6 is picked up from the table, on top of block b5, block b6 is stacked, block b2 is picked up, on top of block b6, block b2 is stacked, block b3 is picked up, block b3 is stacked on top of block b2, block b7 is unstacked from top of block b4, on top of block b3, block b7 is stacked and block b4 is picked up to reach the current state. In this state, if on top of block b7, block b4 is stacked, is it True or False that block b4 is clear, block b7 is not clear and hand is not holding anything?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b7, then placed on the table by the hand, block b5 is unstacked from block b4, and then stacked on top of block b2. Next, block b4 is unstacked from block b1 and placed on the table. Block b5 is then unstacked from block b2 and stacked on top of block b1. Block b7 is unstacked from block b6 and stacked on top of block b4. Block b6 is picked up from the table and stacked on top of block b5. Block b2 is picked up and stacked on top of block b6. Block b3 is then picked up and stacked on top of block b2. Block b7 is unstacked from block b4 and stacked on top of block b3. Finally, block b4 is picked up to reach the current state. In this state, if block b4 is stacked on top of block b7, is it True or False that block b4 is clear, block b7 is not clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "e49413c0-06e5-405d-83a5-298c503c9ac1", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1 to reach the current state. In this state, if block b4 is put down on the table, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is clear, block b1 is not on the table, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b4, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is not on top of block b8, block b1 is on block b6, block b2 is clear, block b2 is not being held by the hand, block b2 is not located on the table, block b2 is not on block b1, block b2 is not on block b8, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is not placed on top of block b3, block b2 is placed on top of block b5, block b3 is clear, block b3 is not being held, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on block b6, block b3 is not on block b7, block b3 is not on top of block b5, block b3 is not placed on top of block b4, block b3 is on block b8, block b4 is clear, block b4 is located on the table, block b4 is not on block b1, block b4 is not on block b5, block b4 is not on block b6, block b4 is not on block b8, block b4 is not on top of block b3, block b4 is not placed on top of block b2, block b4 is not placed on top of block b7, block b5 is located on the table, block b5 is not being held by the hand, block b5 is not clear, block b5 is not on top of block b1, block b5 is not on top of block b2, block b5 is not on top of block b7, block b5 is not placed on top of block b3, block b5 is not placed on top of block b4, block b5 is not placed on top of block b6, block b5 is not placed on top of block b8, block b6 is not being held, block b6 is not clear, block b6 is not located on the table, block b6 is not on top of block b2, block b6 is not placed on top of block b1, block b6 is not placed on top of block b3, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b6 is not placed on top of block b8, block b6 is on top of block b7, block b7 is located on the table, block b7 is not being held, block b7 is not clear, block b7 is not on block b5, block b7 is not on block b6, block b7 is not on top of block b8, block b7 is not placed on top of block b1, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, block b7 is not placed on top of block b4, block b8 is located on the table, block b8 is not being held by the hand, block b8 is not clear, block b8 is not on block b1, block b8 is not on block b2, block b8 is not on block b3, block b8 is not on block b4, block b8 is not on block b5, block b8 is not on block b6, block b8 is not placed on top of block b7, hand is not holding anything, the hand is not holding the block b1 and the hand is not holding the block b4", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are taken: block b4 is removed from the top of block b1 to achieve the current state. In this state, if block b4 is placed on the table, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "2f9aacff-fae8-4e04-ad77-d08d6db4d04f", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, block b4 is put down, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up by the hand, on top of block b5, block b6 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, on top of block b3, block b7 is stacked and block b4 is picked up to reach the current state. In this state, if on top of block b7, block b4 is stacked, is it True or False that block b4 is not being held by the hand and block b4 is on top of block b7?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, then block b3 is placed down by the hand, block b5 is removed from the top of block b4, block b5 is placed on top of block b2, block b4 is removed from the top of block b1 and put down, block b5 is then removed from the top of block b2 and placed on top of block b1, block b7 is removed from the top of block b6 and placed on top of block b4, block b6 is picked up by the hand and placed on top of block b5, block b2 is picked up by the hand and placed on top of block b6, block b3 is picked up by the hand and placed on top of block b2, block b7 is removed from the top of block b4 and placed on top of block b3, resulting in the current state. In this state, if block b4 is placed on top of block b7, is it True or False that block b4 is not being held by the hand and block b4 is on top of block b7?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is positioned on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is situated on top of block b6 and the hand is empty."}
{"question_id": "800a5ede-28f1-4de8-8319-8c4ebc40df6e", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, the hand puts down the block b9, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, the hand puts down the block b6, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1, on top of block b4, block b8 is stacked, from top of block b5, block b2 is unstacked, on top of block b8, block b2 is stacked, from top of block b9, block b3 is unstacked, on top of block b2, block b3 is stacked, from top of block b7, block b1 is unstacked, on top of block b3, block b1 is stacked, block b7 is picked up from the table, on top of block b9, block b7 is stacked and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b7, is it True or False that block b9 is being held by the hand?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 and placed by the hand, block b2 is removed from the top of block b6 and placed on top of block b5, block b6 is removed from block b3 and placed by the hand, block b3 is removed from the top of block b4 and placed on top of block b9, block b8 is removed from the top of block b1 and placed on top of block b4, block b2 is removed from the top of block b5 and placed on top of block b8, block b3 is removed from the top of block b9 and placed on top of block b2, block b1 is removed from the top of block b7 and placed on top of block b3, block b7 is picked up from the table and placed on top of block b9, and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b7, is it True or False that block b9 is being held by the hand?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "41639843-19e0-496b-a6a4-b44915587358", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, the hand puts down the block b9, block b2 is unstacked from block b6, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, block b6 is put down on the table, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, on top of block b8, block b2 is stacked, from top of block b9, block b3 is unstacked, block b3 is stacked on top of block b2, from top of block b7, block b1 is unstacked, block b1 is stacked on top of block b3, block b7 is picked up from the table, block b7 is stacked on top of block b9 and block b6 is picked up from the table to reach the current state. In this state, if block b6 is stacked on top of block b7, is it True or False that block b6 is not being held by the hand and block b6 is on top of block b7?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 and placed on the table, then block b2 is removed from block b6 and stacked on top of block b5. Next, block b6 is removed from the top of block b3 and placed on the table, and block b3 is removed from block b4. Block b3 is then stacked on top of block b9. Block b8 is removed from the top of block b1 and stacked on top of block b4. Block b2 is then removed from the top of block b5 and stacked on top of block b8. Block b3 is removed from the top of block b9 and stacked on top of block b2. Block b1 is removed from the top of block b7 and stacked on top of block b3. Block b7 is picked up from the table and stacked on top of block b9, and block b6 is picked up from the table to reach the current state. In this state, if block b6 is stacked on top of block b7, is it True or False that block b6 is not being held by the hand and block b6 is on top of block b7?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "03aed1e9-7ce7-4378-8915-2ef5eb769454", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, if the hand puts down the block b3, is it True or False that block b3 is clear and hand is not holding anything?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b5 to reach the current state. In this state, if the hand releases block b3, is it True or False that block b3 is clear and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "48370764-8a63-4450-b36f-edb95a486c45", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, the hand puts down the block b3, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, block b4 is unstacked from top of block b1, block b4 is put down, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, block b7 is unstacked from block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, if block b6 is picked up, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is located on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b4, block b1 is not on top of block b6, block b1 is not placed on top of block b5, block b1 is not placed on top of block b7, block b2 is clear, block b2 is located on the table, block b2 is not being held by the hand, block b2 is not on block b6, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not placed on top of block b1, block b2 is not placed on top of block b5, block b2 is not placed on top of block b7, block b3 is clear, block b3 is located on the table, block b3 is not being held, block b3 is not on block b7, block b3 is not on top of block b1, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b5, block b3 is not placed on top of block b6, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b5, block b4 is not on top of block b3, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b6, block b4 is on the table, block b5 is clear, block b5 is not on the table, block b5 is not on top of block b3, block b5 is not on top of block b6, block b5 is not on top of block b7, block b5 is not placed on top of block b2, block b5 is not placed on top of block b4, block b5 is on block b1, block b6 is being held by the hand, block b6 is not clear, block b6 is not on block b2, block b6 is not on block b5, block b6 is not on block b7, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not placed on top of block b3, block b6 is not placed on top of block b4, block b7 is clear, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on block b5, block b7 is not on block b6, block b7 is not placed on top of block b2, block b7 is placed on top of block b4, hand is not empty, the hand is not holding the block b4, the hand is not holding the block b5 and the hand is not holding the block b7", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b7, then placed by the hand, block b5 is removed from the top of block b4, then stacked on top of block b2, block b4 is removed from the top of block b1 and placed, block b5 is removed from block b2 and stacked on top of block b1, and finally block b7 is removed from block b6 and stacked on top of block b4 to reach the current state. In this state, if block b6 is picked up, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is resting on top of block b6, and the hand is empty."}
{"question_id": "62fff928-b909-4a28-a61d-e6dfc56faf68", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5 to reach the current state. In this state, if the hand puts down the block b3, is it True or False that block b3 is not being held by the hand?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is removed from the top of block b5 to reach the current state. In this state, if the hand releases block b3, is it True or False that the hand is no longer holding block b3?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "e20c6ee1-151a-4176-8ed8-f31d19c2f5aa", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down on the table, block b2 is unstacked from top of block b6, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1 and block b7 is put down on the table to reach the current state. In this state, if block b1 is unstacked from top of block b4, is it True or False that block b1 is being held and block b1 is not on top of block b4?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5, then placed on the table, block b2 is removed from the top of block b6 and also placed on the table, block b6 is picked up from the table and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, and finally block b7 is removed from block b1 and placed on the table, resulting in the current state. In this state, if block b1 is removed from the top of block b4, is it True or False that block b1 is being held and block b1 is no longer on top of block b4?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "087cc6ea-10d9-46a5-abf2-212713418f9f", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is put down on the table, is it True or False that block b6 is not clear?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is placed on the table, is it True or False that block b6 is not clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "9af5b95c-0740-43e1-8b53-15ff985b221a", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, the hand puts down the block b9, block b2 is unstacked from block b6, block b2 is stacked on top of block b5, block b6 is unstacked from block b3, block b6 is put down on the table, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1, on top of block b4, block b8 is stacked, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, block b3 is unstacked from block b9, block b3 is stacked on top of block b2, from top of block b7, block b1 is unstacked, block b1 is stacked on top of block b3, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up by the hand to reach the current state. In this state, if block b6 is stacked on top of block b7, is it True or False that block b3 is clear, block b5 is not clear and hand is not empty?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from block b8, the hand places block b9 down, block b2 is removed from block b6, block b2 is placed on top of block b5, block b6 is removed from block b3, block b6 is put down on the table, block b3 is removed from the top of block b4, block b3 is placed on top of block b9, block b8 is removed from the top of block b1, block b8 is placed on top of block b4, block b2 is removed from the top of block b5, block b2 is placed on top of block b8, block b3 is removed from block b9, block b3 is placed on top of block b2, block b1 is removed from the top of block b7, block b1 is placed on top of block b3, block b7 is picked up, block b7 is placed on top of block b9, and block b6 is picked up by the hand to reach the current state. In this state, if block b6 is placed on top of block b7, is it True or False that block b3 is clear, block b5 is not clear, and the hand is not empty?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8, and the hand is empty."}
{"question_id": "8460e71d-85b0-4764-abb7-41e13bbcc7c7", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down on the table, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, block b4 is put down on the table, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, from top of block b6, block b7 is unstacked and on top of block b4, block b7 is stacked to reach the current state. In this state, if block b6 is picked up from the table, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "block b1 is not being held by the hand, block b1 is not clear, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b5, block b1 is not placed on top of block b4, block b1 is not placed on top of block b6, block b1 is not placed on top of block b7, block b2 is not being held by the hand, block b2 is not on block b3, block b2 is not on block b4, block b2 is not on block b5, block b2 is not on top of block b6, block b2 is not placed on top of block b1, block b2 is not placed on top of block b7, block b3 is not being held by the hand, block b3 is not on block b1, block b3 is not on top of block b2, block b3 is not on top of block b7, block b3 is not placed on top of block b4, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b3, block b4 is not on top of block b1, block b4 is not on top of block b2, block b4 is not on top of block b5, block b4 is not on top of block b7, block b4 is not placed on top of block b6, block b5 is not being held by the hand, block b5 is not on block b2, block b5 is not on block b7, block b5 is not on the table, block b5 is not on top of block b3, block b5 is not on top of block b6, block b5 is not placed on top of block b4, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b3, block b6 is not on block b4, block b6 is not on the table, block b6 is not on top of block b2, block b6 is not placed on top of block b5, block b6 is not placed on top of block b7, block b7 is not on block b1, block b7 is not on the table, block b7 is not on top of block b3, block b7 is not on top of block b6, block b7 is not placed on top of block b2, block b7 is not placed on top of block b5, hand is not empty and the hand is not holding the block b7", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7 and placed on the table, block b5 is removed from the top of block b4 and stacked on top of block b2, block b4 is removed from the top of block b1 and placed on the table, block b5 is then removed from the top of block b2 and stacked on top of block b1, and finally, block b7 is removed from the top of block b6 and stacked on top of block b4 to reach the current state. In this state, if block b6 is picked up from the table, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "a234a1e3-f1a3-4a7c-851e-89c642933e31", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is put down, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "block b1 is clear, block b1 is on block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is located on the table, block b5 is on the table, block b6 is placed on top of block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from block b1 to achieve the current state. In this state, if block b4 is placed down, what are all the valid properties of the state that do not include negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "f4f6d54d-fa87-4f8e-ab1d-2eb6a2526c66", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down, from top of block b6, block b2 is unstacked, block b2 is stacked on top of block b5, from top of block b3, block b6 is unstacked, block b6 is put down, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, on top of block b8, block b2 is stacked, block b3 is unstacked from block b9, block b3 is stacked on top of block b2, block b1 is unstacked from top of block b7, block b1 is stacked on top of block b3, block b7 is picked up by the hand, block b7 is stacked on top of block b9 and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b7, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is clear, block b1 is not being held, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b9, block b1 is not on top of block b4, block b1 is not on top of block b5, block b1 is not on top of block b8, block b1 is not placed on top of block b6, block b1 is not placed on top of block b7, block b1 is on block b3, block b2 is not being held by the hand, block b2 is not clear, block b2 is not located on the table, block b2 is not on block b1, block b2 is not on block b3, block b2 is not on block b4, block b2 is not on block b6, block b2 is not on block b7, block b2 is not on top of block b5, block b2 is not placed on top of block b9, block b2 is placed on top of block b8, block b3 is not being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b4, block b3 is not on top of block b5, block b3 is not on top of block b7, block b3 is not on top of block b8, block b3 is not on top of block b9, block b3 is not placed on top of block b6, block b3 is placed on top of block b2, block b4 is not being held, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b8, block b4 is not on top of block b1, block b4 is not on top of block b6, block b4 is not placed on top of block b3, block b4 is not placed on top of block b5, block b4 is not placed on top of block b7, block b4 is not placed on top of block b9, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b5 is not being held by the hand, block b5 is not on block b2, block b5 is not on top of block b1, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b6, block b5 is not on top of block b7, block b5 is not placed on top of block b8, block b5 is not placed on top of block b9, block b6 is clear, block b6 is not being held by the hand, block b6 is not on block b2, block b6 is not on block b9, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b3, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is not placed on top of block b8, block b6 is placed on top of block b7, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b6, block b7 is not placed on top of block b3, block b7 is not placed on top of block b8, block b7 is placed on top of block b9, block b8 is not being held by the hand, block b8 is not clear, block b8 is not located on the table, block b8 is not on block b1, block b8 is not on top of block b2, block b8 is not on top of block b3, block b8 is not on top of block b6, block b8 is not on top of block b7, block b8 is not placed on top of block b5, block b8 is not placed on top of block b9, block b8 is placed on top of block b4, block b9 is not being held by the hand, block b9 is not clear, block b9 is not on block b1, block b9 is not on block b2, block b9 is not on block b7, block b9 is not on block b8, block b9 is not on top of block b4, block b9 is not placed on top of block b3, block b9 is not placed on top of block b5, block b9 is not placed on top of block b6, block b9 is on the table, hand is not holding anything and the hand is not holding the block b7", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from the top of block b8 and placed down, then block b2 is removed from the top of block b6 and stacked on top of block b5. Next, block b6 is removed from the top of block b3 and placed down, and block b3 is removed from block b4. Block b3 is then stacked on top of block b9. Block b8 is removed from the top of block b1 and stacked on top of block b4. Block b2 is then removed from the top of block b5 and stacked on top of block b8. Block b3 is removed from block b9 and stacked on top of block b2. Block b1 is removed from the top of block b7 and stacked on top of block b3. Finally, block b7 is picked up and stacked on top of block b9, and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b7, what are all the valid properties of the state (both with and without negations)? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "cfc05223-d59b-4474-90ca-a13a5cf59435", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, the hand puts down the block b3, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, from top of block b1, block b4 is unstacked, block b4 is put down on the table, block b5 is unstacked from block b2, on top of block b1, block b5 is stacked, from top of block b6, block b7 is unstacked and on top of block b4, block b7 is stacked to reach the current state. In this state, if block b6 is picked up by the hand, is it True or False that block b6 is not on the table?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7, then placed on the table, block b5 is removed from block b4, and stacked on top of block b2, block b4 is removed from the top of block b1 and placed on the table, block b5 is then removed from block b2 and stacked on top of block b1, and finally, block b7 is removed from the top of block b6 and stacked on top of block b4, resulting in the current state. In this state, if the hand picks up block b6, is it True or False that block b6 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "52b7a682-6983-4025-961d-83ebfa2851cc", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, if block b3 is put down, is it True or False that block b3 is clear?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b5 to reach the current state. In this state, if block b3 is placed down, is it True or False that block b3 is clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "af1b2ce2-a9f0-4839-9ec4-d6fa643efaeb", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5 to reach the current state. In this state, if block b3 is put down, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is not clear, block b1 is not on the table, block b1 is not on top of block b5, block b1 is not placed on top of block b2, block b1 is not placed on top of block b3, block b1 is not placed on top of block b6, block b1 is not placed on top of block b7, block b1 is on top of block b4, block b2 is clear, block b2 is not being held by the hand, block b2 is not on block b1, block b2 is not on block b3, block b2 is not on block b7, block b2 is not on the table, block b2 is not on top of block b4, block b2 is not placed on top of block b5, block b2 is placed on top of block b6, block b3 is clear, block b3 is located on the table, block b3 is not being held, block b3 is not on block b5, block b3 is not on block b6, block b3 is not on block b7, block b3 is not on top of block b4, block b3 is not placed on top of block b1, block b3 is not placed on top of block b2, block b4 is located on the table, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b1, block b4 is not on block b2, block b4 is not on block b5, block b4 is not on top of block b3, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b5 is clear, block b5 is not being held by the hand, block b5 is not on block b6, block b5 is not on the table, block b5 is not on top of block b1, block b5 is not on top of block b2, block b5 is not placed on top of block b3, block b5 is not placed on top of block b4, block b5 is on block b7, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on block b3, block b6 is not on block b4, block b6 is not on block b7, block b6 is not on top of block b1, block b6 is not placed on top of block b2, block b6 is not placed on top of block b5, block b6 is on the table, block b7 is not being held, block b7 is not clear, block b7 is not on block b2, block b7 is not on block b3, block b7 is not on block b4, block b7 is not on block b6, block b7 is not on the table, block b7 is not placed on top of block b5, block b7 is on top of block b1, hand is not holding anything and the hand is not holding the block b1", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is unstacked from block b5 to achieve the current state. In this state, if block b3 is placed down, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "15c031e0-2f41-4187-bfe5-5e75ec6d8a38", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, if block b4 is put down, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "block b1 is not located on the table, block b1 is not on block b5, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b4, block b1 is not on top of block b8, block b1 is not placed on top of block b7, block b2 is not located on the table, block b2 is not on block b8, block b2 is not placed on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b4, block b2 is not placed on top of block b6, block b2 is not placed on top of block b7, block b3 is not being held by the hand, block b3 is not on block b1, block b3 is not on the table, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b5, block b3 is not placed on top of block b6, block b3 is not placed on top of block b7, block b4 is not on block b2, block b4 is not on top of block b1, block b4 is not on top of block b6, block b4 is not placed on top of block b3, block b4 is not placed on top of block b5, block b4 is not placed on top of block b7, block b4 is not placed on top of block b8, block b5 is not being held, block b5 is not clear, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on block b4, block b5 is not on block b6, block b5 is not on top of block b7, block b5 is not placed on top of block b1, block b5 is not placed on top of block b8, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b1, block b6 is not on block b3, block b6 is not on block b4, block b6 is not placed on top of block b2, block b6 is not placed on top of block b5, block b6 is not placed on top of block b8, block b7 is not being held, block b7 is not clear, block b7 is not on block b6, block b7 is not on block b8, block b7 is not on top of block b1, block b7 is not on top of block b3, block b7 is not on top of block b5, block b7 is not placed on top of block b2, block b7 is not placed on top of block b4, block b8 is not being held by the hand, block b8 is not clear, block b8 is not on block b2, block b8 is not on block b6, block b8 is not on top of block b7, block b8 is not placed on top of block b1, block b8 is not placed on top of block b3, block b8 is not placed on top of block b4, block b8 is not placed on top of block b5, the hand is not holding the block b1, the hand is not holding the block b2, the hand is not holding the block b4 and the hand is not holding the block b6", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is unstacked from the top of block b1 to reach the current state. In this state, if block b4 is placed down, what are all the valid properties of the state that include negations? Write None if there are no such properties.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is positioned on the table, block b6 is placed on block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is currently empty."}
{"question_id": "e7210ac6-e248-46f3-9607-49b1105f3e0b", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, from top of block b6, block b2 is unstacked, the hand puts down the block b2, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked and block b7 is put down on the table to reach the current state. In this state, if block b1 is unstacked from block b4, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is being held, block b1 is not clear, block b1 is not on block b2, block b1 is not on the table, block b1 is not on top of block b3, block b1 is not on top of block b4, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is not placed on top of block b6, block b2 is clear, block b2 is located on the table, block b2 is not being held by the hand, block b2 is not on block b1, block b2 is not on block b5, block b2 is not on block b6, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not placed on top of block b7, block b3 is not being held, block b3 is not clear, block b3 is not on block b2, block b3 is not on block b5, block b3 is not on block b7, block b3 is not on top of block b6, block b3 is not placed on top of block b1, block b3 is not placed on top of block b4, block b3 is on the table, block b4 is clear, block b4 is located on the table, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on top of block b1, block b4 is not placed on top of block b2, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b5 is clear, block b5 is not on block b2, block b5 is not on block b4, block b5 is not on block b7, block b5 is not on the table, block b5 is not on top of block b3, block b5 is not placed on top of block b1, block b5 is on block b6, block b6 is not being held by the hand, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on block b4, block b6 is not on top of block b5, block b6 is not placed on top of block b7, block b6 is placed on top of block b3, block b7 is clear, block b7 is not being held, block b7 is not on block b5, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not placed on top of block b1, block b7 is not placed on top of block b4, block b7 is not placed on top of block b6, block b7 is on the table, hand is not empty, the hand is not holding the block b4 and the hand is not holding the block b5", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from block b5, block b3 is placed on the table, block b2 is removed from the top of block b6, block b2 is put down, block b6 is picked up from the table, block b6 is placed on top of block b3, block b5 is removed from block b7, block b5 is stacked on top of block b6, block b7 is removed from the top of block b1, and block b7 is placed on the table to reach the current state. In this state, if block b1 is removed from block b4, what would be all the valid properties of the state (including both affirmative and negated properties)? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "80b71924-c1d3-42e3-acfb-1547ed905c8d", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, the hand puts down the block b4, block b5 is unstacked from top of block b2, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up from the table, block b6 is stacked on top of block b5, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, block b7 is unstacked from block b4, on top of block b3, block b7 is stacked and block b4 is picked up from the table to reach the current state. In this state, if block b4 is stacked on top of block b7, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "block b1 is located on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b4, block b1 is not on top of block b3, block b1 is not on top of block b7, block b1 is not placed on top of block b5, block b1 is not placed on top of block b6, block b2 is not being held by the hand, block b2 is not clear, block b2 is not on block b4, block b2 is not on the table, block b2 is not on top of block b5, block b2 is not placed on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b7, block b2 is on block b6, block b3 is not clear, block b3 is not on block b5, block b3 is not on block b7, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not on top of block b4, block b3 is not placed on top of block b6, block b3 is on top of block b2, block b4 is clear, block b4 is not being held, block b4 is not located on the table, block b4 is not on block b1, block b4 is not on top of block b2, block b4 is not on top of block b6, block b4 is not placed on top of block b3, block b4 is not placed on top of block b5, block b4 is on top of block b7, block b5 is not being held by the hand, block b5 is not clear, block b5 is not located on the table, block b5 is not on top of block b3, block b5 is not placed on top of block b2, block b5 is not placed on top of block b4, block b5 is not placed on top of block b6, block b5 is not placed on top of block b7, block b5 is on block b1, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on block b4, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not placed on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b7, block b6 is on block b5, block b7 is not being held by the hand, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on top of block b4, block b7 is not on top of block b5, block b7 is not placed on top of block b6, block b7 is on block b3, hand is not holding anything and the hand is not holding the block b3", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed down, then block b5 is unstacked from block b4 and stacked on top of block b2. Next, block b4 is unstacked from the top of block b1 and put down, followed by unstacking block b5 from block b2 and restacking it on top of block b1. Block b7 is then unstacked from the top of block b6 and stacked on top of block b4. Block b6 is picked up from the table and stacked on top of block b5, and block b2 is picked up and stacked on top of block b6. Block b3 is then picked up and stacked on top of block b2. Block b7 is unstacked from block b4 and restacked on top of block b3, and finally, block b4 is picked up from the table to reach the current state. In this state, if block b4 is stacked on top of block b7, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state None.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is resting on top of block b6, and the hand is empty."}
{"question_id": "dd5f723b-f99d-43c4-b242-44c332d590f1", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, if block b9 is put down, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "block b1 is placed on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is placed on top of block b3, block b7 is located on the table, block b8 is clear, block b8 is on block b1, block b9 is clear, block b9 is on the table and hand is empty", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is on block b6, block b3 is on block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, if block b9 is placed down, what are all the valid properties of the state that do not include negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on top of block b3, block b7 is situated on the table, block b8 is placed on block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8 and the hand is empty."}
{"question_id": "42ff7d3e-0d5a-407c-b083-e57045bc6d29", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down on the table, block b1 is unstacked from block b6, block b1 is put down, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up, block b7 is stacked on top of block b2, block b3 is unstacked from block b8, block b3 is stacked on top of block b7, block b1 is picked up, block b1 is stacked on top of block b3, block b5 is picked up by the hand, on top of block b1, block b5 is stacked, block b8 is picked up from the table, block b8 is stacked on top of block b5 and block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b8, is it True or False that block b5 is not on top of block b7 and block b7 is not on block b3?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is unstacked from block b6 and put down. Next, block b2 is unstacked from the top of block b5 and stacked on top of block b4. Block b6 is then unstacked from block b7 and placed on the table, after which block b7 is picked up and stacked on top of block b2. Block b3 is unstacked from block b8 and stacked on top of block b7. Block b1 is then picked up and stacked on top of block b3. Block b5 is picked up by the hand and stacked on top of block b1. Block b8 is picked up from the table and stacked on top of block b5. Finally, block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b8, is it True or False that block b5 is not on top of block b7 and block b7 is not on top of block b3?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "dbee6154-9883-4ae5-b1e8-89dce835c22f", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down, block b2 is unstacked from top of block b6, block b2 is put down on the table, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked, block b7 is put down, block b1 is unstacked from block b4, on top of block b2, block b1 is stacked, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, block b1 is unstacked from block b2, on top of block b4, block b1 is stacked, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up to reach the current state. In this state, if on top of block b2, block b7 is stacked, is it True or False that block b2 is not clear, block b7 is clear and hand is not holding anything?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5 and placed on the table, then block b2 is removed from the top of block b6 and placed on the table. Next, block b6 is picked up from the table and stacked on top of block b3. Block b5 is then removed from the top of block b7 and stacked on top of block b6. Block b7 is removed from the top of block b1 and placed on the table. Block b1 is then removed from block b4 and stacked on top of block b2. Block b4 is picked up by the hand and stacked on top of block b5. Block b1 is then removed from block b2 and stacked on top of block b4. Block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up to reach the current state. In this state, if block b7 is stacked on top of block b2, is it True or False that block b2 is not clear, block b7 is clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "e9624eae-acfa-4c6c-885b-6bce4dea541b", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, the hand puts down the block b3, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1 and the hand puts down the block b7 to reach the current state. In this state, if block b1 is unstacked from top of block b4, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "block b1 is not clear, block b1 is not located on the table, block b1 is not on block b5, block b1 is not on block b7, block b1 is not on top of block b4, block b1 is not on top of block b6, block b1 is not placed on top of block b2, block b1 is not placed on top of block b3, block b2 is not being held by the hand, block b2 is not on block b1, block b2 is not on block b4, block b2 is not on block b7, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not placed on top of block b6, block b3 is not being held, block b3 is not clear, block b3 is not on block b1, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not placed on top of block b5, block b3 is not placed on top of block b7, block b4 is not on block b1, block b4 is not on block b2, block b4 is not on block b7, block b4 is not on top of block b3, block b4 is not placed on top of block b5, block b4 is not placed on top of block b6, block b5 is not being held by the hand, block b5 is not on block b3, block b5 is not on the table, block b5 is not on top of block b2, block b5 is not on top of block b7, block b5 is not placed on top of block b1, block b5 is not placed on top of block b4, block b6 is not being held, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b4, block b6 is not on block b7, block b6 is not on top of block b2, block b6 is not on top of block b5, block b6 is not placed on top of block b1, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on block b6, block b7 is not on top of block b1, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, hand is not empty, the hand is not holding the block b4 and the hand is not holding the block b7", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5, block b3 is placed down by the hand, block b2 is removed from the top of block b6, block b2 is placed down by the hand, block b6 is picked up, block b6 is stacked on top of block b3, block b5 is removed from the top of block b7, block b5 is stacked on top of block b6, block b7 is removed from block b1, and the hand puts down block b7 to achieve the current state. In this state, if block b1 is removed from the top of block b4, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "77133acc-0d64-4f19-a4cf-246da99d5c7d", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down on the table, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1 and the hand puts down the block b7 to reach the current state. In this state, if from top of block b4, block b1 is unstacked, is it True or False that block b5 is not clear?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5 and placed on the table, then block b2 is removed from the top of block b6 and put down, after which block b6 is picked up and placed on top of block b3. Next, block b5 is removed from the top of block b7 and stacked on top of block b6, and block b7 is removed from the top of block b1 and put down by the hand, resulting in the current state. In this state, if block b1 is removed from the top of block b4, is it True or False that block b5 is not clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "1c77e80d-3530-46d2-a8cd-6aa7e0383f12", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down on the table, block b1 is unstacked from block b6, the hand puts down the block b1, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, block b6 is put down, block b7 is picked up, on top of block b2, block b7 is stacked, from top of block b8, block b3 is unstacked, block b3 is stacked on top of block b7, block b1 is picked up, on top of block b3, block b1 is stacked, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b8 is picked up by the hand, on top of block b5, block b8 is stacked and block b6 is picked up by the hand to reach the current state. In this state, if block b6 is stacked on top of block b8, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "block b1 is on top of block b3, block b2 is on top of block b4, block b3 is on top of block b7, block b4 is on the table, block b5 is placed on top of block b1, block b6 is clear, block b6 is placed on top of block b8, block b7 is on top of block b2, block b8 is placed on top of block b5 and hand is not holding anything", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1 and placed on the table, block b1 is then removed from block b6 and set down, block b2 is removed from the top of block b5 and stacked on top of block b4, block b6 is removed from the top of block b7 and set down, block b7 is picked up and stacked on top of block b2, block b3 is removed from the top of block b8 and stacked on top of block b7, block b1 is picked up and stacked on top of block b3, block b5 is picked up and stacked on top of block b1, block b8 is picked up and stacked on top of block b5, and finally block b6 is picked up to reach the current state. In this state, if block b6 is stacked on top of block b8, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is positioned on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is currently empty."}
{"question_id": "9c9621df-621e-44ae-b2ce-98b66195d874", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, from top of block b1, block b4 is unstacked, block b4 is put down on the table, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, from top of block b6, block b7 is unstacked and block b7 is stacked on top of block b4 to reach the current state. In this state, if block b6 is picked up by the hand, is it True or False that the hand is holding the block b6?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, then block b3 is placed on the table by the hand, block b5 is removed from the top of block b4, block b5 is then placed on top of block b2, block b4 is removed from the top of block b1, block b4 is put down on the table, block b5 is removed from the top of block b2, block b5 is then placed on top of block b1, block b7 is removed from the top of block b6, and block b7 is placed on top of block b4 to reach the current state. In this state, if the hand picks up block b6, is it True or False that the hand is holding block b6?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is stacked on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "21f84b25-d84f-4213-bc50-f742ffe125b8", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, if block b3 is put down on the table, is it True or False that block b5 is placed on top of block b1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b7 to reach the current state. In this state, if block b3 is placed on the table, is it True or False that block b5 is positioned on top of block b1?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on top of block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "b3701666-4bde-49aa-9524-e9ce2e269734", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, block b2 is unstacked from top of block b6, block b2 is put down on the table, block b6 is picked up by the hand, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked, block b7 is put down on the table, from top of block b4, block b1 is unstacked, block b1 is stacked on top of block b2, block b4 is picked up, block b4 is stacked on top of block b5, block b1 is unstacked from block b2, block b1 is stacked on top of block b4, block b2 is picked up from the table, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, if block b7 is stacked on top of block b2, is it True or False that block b3 is not clear and block b5 is clear?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from block b5 and placed on the table, block b2 is removed from block b6 and placed on the table, block b6 is picked up and placed on top of block b3, block b5 is removed from block b7 and placed on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from block b4 and placed on top of block b2, block b4 is picked up and placed on top of block b5, block b1 is removed from block b2 and placed on top of block b4, block b2 is picked up from the table and placed on top of block b1, and block b7 is picked up to reach the current state. In this state, if block b7 is stacked on top of block b2, is it True or False that block b3 is not clear and block b5 is clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "7964b4c5-9a95-4173-935a-6d08361df408", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, the hand puts down the block b3, from top of block b6, block b2 is unstacked, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1, block b7 is put down on the table, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up from the table, block b4 is stacked on top of block b5, block b1 is unstacked from top of block b2, block b1 is stacked on top of block b4, block b2 is picked up, block b2 is stacked on top of block b1 and block b7 is picked up from the table to reach the current state. In this state, if block b7 is stacked on top of block b2, is it True or False that block b4 is on block b6 and block b6 is on top of block b4?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5, the hand releases block b3, block b2 is unstacked from the top of block b6, block b2 is placed down, block b6 is picked up, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, block b7 is unstacked from the top of block b1 and placed on the table, block b1 is unstacked from the top of block b4, block b1 is stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is unstacked from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and block b7 is picked up from the table to reach the current state. In this state, if block b7 is stacked on top of block b2, is it True or False that block b4 is on block b6 and block b6 is on top of block b4?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on top of it, block b2 is stacked on block b6, block b3 has no blocks on top of it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "ee0d359e-5d10-4789-962f-eafca06ca56e", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, if block b4 is put down on the table, is it True or False that block b4 is on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is unstacked from the top of block b1 to reach the current state. In this state, if block b4 is placed on the table, is it True or False that block b4 is on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "fab68e3b-6a60-450c-ba08-65130e12cd1f", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, the hand puts down the block b4, block b5 is unstacked from block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, if block b6 is picked up from the table, is it True or False that block b6 is not clear and hand is holding some block?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is located on the table, block b7 is placed on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7 and placed down, then block b5 is removed from block b4 and stacked on top of block b2. Next, block b4 is removed from block b1 and put down, followed by removing block b5 from block b2 and stacking it on top of block b1. Finally, block b7 is removed from block b6 and stacked on top of block b4, resulting in the current state. In this state, if block b6 is picked up from the table, is it True or False that block b6 is not clear and the hand is holding a block?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is situated on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is positioned on the table, block b7 is on top of block b6 and the hand is empty."}
{"question_id": "5412a37f-2aad-4615-aee9-af863060c2d1", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is put down on the table, is it True or False that block b4 is not being held?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, if block b4 is placed on the table, is it True or False that block b4 is not being held?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "3695ccb4-85d6-43a1-9516-d9d3b82cbc10", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down on the table, from top of block b6, block b1 is unstacked, block b1 is put down, block b2 is unstacked from top of block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from top of block b7, block b6 is put down on the table, block b7 is picked up, on top of block b2, block b7 is stacked, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up from the table, on top of block b3, block b1 is stacked, block b5 is picked up from the table, block b5 is stacked on top of block b1, block b8 is picked up, on top of block b5, block b8 is stacked and block b6 is picked up from the table to reach the current state. In this state, if on top of block b8, block b6 is stacked, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "block b1 is not being held, block b1 is not clear, block b1 is not located on the table, block b1 is not on block b6, block b1 is not on block b7, block b1 is not on block b8, block b1 is not on top of block b2, block b1 is not on top of block b4, block b1 is not on top of block b5, block b2 is not being held, block b2 is not clear, block b2 is not on block b3, block b2 is not on the table, block b2 is not on top of block b5, block b2 is not placed on top of block b1, block b2 is not placed on top of block b6, block b2 is not placed on top of block b7, block b2 is not placed on top of block b8, block b3 is not being held by the hand, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on block b5, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not on top of block b8, block b4 is not clear, block b4 is not on block b5, block b4 is not on block b7, block b4 is not on top of block b1, block b4 is not on top of block b3, block b4 is not placed on top of block b2, block b4 is not placed on top of block b6, block b4 is not placed on top of block b8, block b5 is not clear, block b5 is not located on the table, block b5 is not on block b3, block b5 is not on block b4, block b5 is not on block b7, block b5 is not on top of block b6, block b5 is not placed on top of block b2, block b5 is not placed on top of block b8, block b6 is not being held, block b6 is not on block b1, block b6 is not on block b3, block b6 is not on block b4, block b6 is not on the table, block b6 is not on top of block b7, block b6 is not placed on top of block b2, block b6 is not placed on top of block b5, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b4, block b7 is not on block b6, block b7 is not on top of block b3, block b7 is not on top of block b5, block b7 is not on top of block b8, block b7 is not placed on top of block b1, block b8 is not clear, block b8 is not located on the table, block b8 is not on block b7, block b8 is not on top of block b1, block b8 is not on top of block b2, block b8 is not on top of block b4, block b8 is not on top of block b6, block b8 is not placed on top of block b3, the hand is not holding the block b4, the hand is not holding the block b5, the hand is not holding the block b7 and the hand is not holding the block b8", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b6, block b2 is clear, block b2 is on block b5, block b3 is clear, block b3 is on block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is on the table and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from block b1 and placed on the table, block b1 is then removed from the top of block b6 and put down, block b2 is removed from the top of block b5 and stacked on top of block b4, block b6 is removed from the top of block b7 and placed on the table, block b7 is picked up and stacked on top of block b2, block b3 is removed from block b8 and stacked on top of block b7, block b1 is picked up from the table and stacked on top of block b3, block b5 is picked up from the table and stacked on top of block b1, block b8 is picked up and stacked on top of block b5, and finally block b6 is picked up from the table to achieve the current state. In this state, if block b6 is stacked on top of block b8, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is directly on the table, block b6 is placed on block b7, block b7 is directly on the table, block b8 is directly on the table, and the hand is empty."}
{"question_id": "312bb33f-71e9-45fd-8978-03b057e90bbb", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, block b7 is put down on the table, block b1 is unstacked from block b4, on top of block b2, block b1 is stacked, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, block b1 is unstacked from top of block b2, block b1 is stacked on top of block b4, block b2 is picked up by the hand, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, if on top of block b2, block b7 is stacked, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "block b1 is on block b4, block b2 is placed on top of block b1, block b3 is on the table, block b4 is placed on top of block b5, block b5 is on block b6, block b6 is on top of block b3, block b7 is clear, block b7 is on top of block b2 and hand is empty", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed on the table, then block b2 is removed from the top of block b6 and placed on the table. Next, block b6 is picked up from the table and stacked on top of block b3. Block b5 is then removed from block b7 and stacked on top of block b6. Block b7 is removed from block b1 and placed on the table. Block b1 is then removed from block b4 and stacked on top of block b2. Block b4 is picked up and stacked on top of block b5. Block b1 is then removed from block b2 and stacked on top of block b4. Block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up to reach the current state. In this state, if block b7 is stacked on top of block b2, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is positioned on the table, block b5 is stacked on block b7, block b6 is positioned on the table, block b7 is stacked on block b1 and the hand is empty."}
