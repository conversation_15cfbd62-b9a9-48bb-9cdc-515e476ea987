{"id": 5945395448725160032, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c45 on board. The cars are at locations as follows: c29, c19, c36, c21, c8, c44, c13, c11, c38, c2, c20, c37, c26, c30, c40, c14, c12, c9, c43, c0, and c22 are at l0; c1, c39, c28, c6, c23, c17, c18, c27, c48, c33, c24, c46, c47, c35, c7, c15, c16, c42, c34, c4, c5, c3, c41, c31, c32, c10, c25, and c49 are at l1.", "question": "Which of the following actions can eventually be applied? A. board the car c20 at location l0. B. travel by sea from location c43 to location c4. C. debark the car c2 to location c8 from the ferry. D. board the car c26 at location c23.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car c20 at location l0", "travel by sea from location c43 to location c4", "debark the car c2 to location c8 from the ferry", "board the car c26 at location c23"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 8865770558839849461, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4, with the car c47 on board. The cars are at locations as follows: c28, c14, c9, c29, c38, c41, c8, c0, c27, c30, c46, and c5 are at l4; c23, c37, c13, c20, c7, c31, c34, c12, c6, and c25 are at l3; c4, c18, c36, c24, c42, c45, c16, c17, c48, c10, and c1 are at l0; c11, c19, c33, c2, c15, c44, c32, and c49 are at l1; c22, c35, c21, c3, c43, c39, c40, and c26 are at l2.", "question": "Which of the following actions can eventually be applied? A. sail from location c47 to location c8. B. sail from location c12 to location c49. C. embark the car c33 at location l1 on to the ferry. D. embark the car l3 at location c7 on to the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location c47 to location c8", "sail from location c12 to location c49", "embark the car c33 at location l1 on to the ferry", "embark the car l3 at location c7 on to the ferry"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7580961157121846567, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c9, c7, and c2 are at l0; c8, c1, c4, and c5 are at l1; c6, c3, and c0 are at l2.", "question": "Which of the following actions can eventually be applied? A. travel by sea from location c4 to location l0. B. board car c1 at location c9. C. unload the car l2 from the ferry to location c5. D. unload the car c3 from the ferry to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location c4 to location l0", "board car c1 at location c9", "unload the car l2 from the ferry to location c5", "unload the car c3 from the ferry to location l0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -7762910174775461862, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c0 on board. The cars are at locations as follows: c2 is at l1; c1 is at l0.", "question": "Which of the following actions can eventually be applied? A. sail from location c0 to location c2. B. board car c0 at location c2. C. board car c1 at location l0. D. board car l0 at location c1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location c0 to location c2", "board car c0 at location c2", "board car c1 at location l0", "board car l0 at location c1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 698839260684053327, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l2, with the car c40 on board. The cars are at locations as follows: c26, c28, c14, c20, c29, c35, c38, c0, c47, c36, c2, and c45 are at l4; c23, c39, c42, c37, c15, c22, c3, c13, c49, c7, c4, c43, c31, and c5 are at l3; c21, c19, c48, c33, c24, c46, c34, c44, and c32 are at l1; c27, c18, c6, c11, c41, c25, c9, c17, c10, and c1 are at l0; c8, c16, c30, and c12 are at l2.", "question": "Which of the following actions can eventually be applied? A. load the car c29 at location c0 on to the ferry. B. debark car c27 to location c25 from the ferry. C. debark car c40 to location l3 from the ferry. D. debark car c22 to location c12 from the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the car c29 at location c0 on to the ferry", "debark car c27 to location c25 from the ferry", "debark car c40 to location l3 from the ferry", "debark car c22 to location c12 from the ferry"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 5967824783105565768, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4, with the car c0 on board. The cars are at locations as follows: c1 is at l3; c2 is at l4.", "question": "Which of the following actions can eventually be applied? A. unload the car l3 from the ferry to location c0. B. unload the car l4 from the ferry to location l1. C. embark the car l3 at location c2 on to the ferry. D. travel by sea from location l2 to location l4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car l3 from the ferry to location c0", "unload the car l4 from the ferry to location l1", "embark the car l3 at location c2 on to the ferry", "travel by sea from location l2 to location l4"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -2655862187112764159, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c9, c7, and c6 are at l0; c8, c1, c2, c4, c5, and c0 are at l1; c3 is at l2.", "question": "Which of the following actions can eventually be applied? A. board car c7 at location c1. B. debark the car c3 to location c0 from the ferry. C. debark the car c1 to location l0 from the ferry. D. board car c5 at location c0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board car c7 at location c1", "debark the car c3 to location c0 from the ferry", "debark the car c1 to location l0 from the ferry", "board car c5 at location c0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7057624437091831260, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c38 on board. The cars are at locations as follows: c29, c36, c6, c21, c8, c44, c13, c11, c2, c33, c35, c20, c37, c25, c26, c30, c14, c12, c9, c0, and c22 are at l0; c1, c39, c28, c45, c23, c17, c18, c19, c27, c48, c24, c46, c47, c7, c15, c16, c40, c42, c34, c4, c5, c3, c41, c43, c31, c32, c10, and c49 are at l1.", "question": "Which of the following actions can eventually be applied? A. unload the car c20 from the ferry to location c1. B. board the car l1 at the location c32. C. unload the car c21 from the ferry to location c39. D. unload the car c0 from the ferry to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car c20 from the ferry to location c1", "board the car l1 at the location c32", "unload the car c21 from the ferry to location c39", "unload the car c0 from the ferry to location l1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2985568666475827396, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c17 on board. The cars are at locations as follows: c29, c19, c18, c6, c8, c44, c13, c11, c2, c3, c33, c35, c20, c25, c30, c23, c14, c12, c9, c0, and c22 are at l0; c21, c1, c39, c28, c45, c37, c27, c48, c26, c24, c38, c46, c47, c7, c15, c16, c40, c42, c34, c4, c5, c41, c36, c43, c31, c32, c10, and c49 are at l1.", "question": "Which of the following actions can eventually be applied? A. debark the car c28 to location c45 from the ferry. B. board car c34 at location c15. C. board car c22 at location l0. D. travel by sea from location c39 to location c28.", "choices": {"label": ["A", "B", "C", "D"], "text": ["debark the car c28 to location c45 from the ferry", "board car c34 at location c15", "board car c22 at location l0", "travel by sea from location c39 to location c28"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -1742370745479040009, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c1, c0, and c2 are at l3.", "question": "Which of the following actions can eventually be applied? A. travel by sea from location c0 to location l2. B. travel by sea from location l1 to location l2. C. travel by sea from location c0 to location c1. D. unload the car l1 from the ferry to location l2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location c0 to location l2", "travel by sea from location l1 to location l2", "travel by sea from location c0 to location c1", "unload the car l1 from the ferry to location l2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 6622905800496884581, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c3 on board. The cars are at locations as follows: c9, c2, c6, c8, c0, and c1 are at l0; c7, c4, and c5 are at l1.", "question": "Which of the following actions can eventually be applied? A. sail from location c2 to location l1. B. unload the car c7 from the ferry to location l0. C. unload the car c3 from the ferry to location c7. D. unload the car c8 from the ferry to location c3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location c2 to location l1", "unload the car c7 from the ferry to location l0", "unload the car c3 from the ferry to location c7", "unload the car c8 from the ferry to location c3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 7997404780415709120, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c9 and c2 are at l1; c4, c5, c7, c3, c6, c0, c8, and c1 are at l0.", "question": "Which of the following actions can eventually be applied? A. board the car l0 at the location l1. B. board the car c9 at the location l1. C. travel by sea from location c8 to location c5. D. travel by sea from location c8 to location c0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car l0 at the location l1", "board the car c9 at the location l1", "travel by sea from location c8 to location c5", "travel by sea from location c8 to location c0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -6232981174094679728, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0, with the car c3 on board. The cars are at locations as follows: c9, c4, c8, c6, c0, and c1 are at l0; c2, c7, and c5 are at l1.", "question": "Which of the following actions can eventually be applied? A. board car l0 at location c4. B. debark car c4 to location l1 from the ferry. C. board car c3 at location c6. D. debark car c9 to location c6 from the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board car l0 at location c4", "debark car c4 to location l1 from the ferry", "board car c3 at location c6", "debark car c9 to location c6 from the ferry"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -5080950710486112977, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c9 on board. The cars are at locations as follows: c4, c7, c3, c8, c6, and c0 are at l0; c1, c2, and c5 are at l1.", "question": "Which of the following actions can eventually be applied? A. debark car c3 to location c7 from the ferry. B. sail from location c2 to location c7. C. sail from location l0 to location l1. D. debark car l0 to location l1 from the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["debark car c3 to location c7 from the ferry", "sail from location c2 to location c7", "sail from location l0 to location l1", "debark car l0 to location l1 from the ferry"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 2498859198207962934, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0, with the car c17 on board. The cars are at locations as follows: c1, c18, c11, c19, c12, c7, c16, c4, c5, and c14 are at l1; c15, c6, c8, c13, c2, c3, c9, c10, and c0 are at l0.", "question": "Which of the following actions can eventually be applied? A. unload the car c13 from the ferry to location l1. B. load the car l1 at location l0 on to the ferry. C. load the car c19 at location c9 on to the ferry. D. load the car c1 at location c8 on to the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car c13 from the ferry to location l1", "load the car l1 at location l0 on to the ferry", "load the car c19 at location c9 on to the ferry", "load the car c1 at location c8 on to the ferry"]}, "query": "Which action is reachable from this state?", "answer": "A"}
