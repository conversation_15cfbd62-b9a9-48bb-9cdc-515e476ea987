{"id": 7800485699555802964, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_6, block_2, and block_8. The following block(s) are stacked on top of another block: block_7 is on block_10, block_1 is on block_8, block_9 is on block_1, block_4 is on block_6, block_5 is on block_9, and block_3 is on block_7. The goal is to reach a state where the following facts hold: The block block_7 is on top of block block_10, The block block_2 is currently situated above the block block_5, The block block_8 is currently situated under the block block_1, The block block_3 is currently situated under the block block_6, The block block_4 is on top of block block_2, The block block_9 is currently situated under the block block_5, The block block_3 is on top of block block_7, and The block block_9 is on top of block block_1. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_5 block_9)", "(unstack block_4 block_6)", "(unstack block_3 block_7)"], "opt": "6", "yes": ["(pick-up block_2)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_2) (clear block_3) (clear block_4) (clear block_5) (handempty) (on block_1 block_8) (on block_3 block_7) (on block_4 block_6) (on block_5 block_9) (on block_7 block_10) (on block_9 block_1) (ontable block_10) (ontable block_2) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -1242606717750499188, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5, block_4, and block_2. The following block(s) are stacked on top of another block: block_1 is on block_2 and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_5 is currently situated above the block block_4, The block block_4 is on top of block block_1, and The block block_3 is currently situated above the block block_5. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_1 block_2)"], "opt": "8", "yes": ["(unstack block_3 block_5)", "(pick-up block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (clear block_4) (handempty) (on block_1 block_2) (on block_3 block_5) (ontable block_2) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -3735709314604815337, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5, block_4, and block_1. The following block(s) is stacked on top of another block: block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_5 is on top of block block_4, The block block_4 is on top of block block_1, and The block block_3 is currently situated above the block block_5. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(stack block_2 block_4)", "(stack block_2 block_5)", "(stack block_2 block_3)"], "opt": "9", "yes": ["(put-down block_2)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (clear block_5) (holding block_2) (on block_3 block_1) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -5761602442132941239, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4, block_2, and block_1. The following block(s) are stacked on top of another block: block_3 is on block_1 and block_5 is on block_3. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_5, The block block_4 is on top of block block_1, and The block block_5 is currently situated under the block block_3. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(pick-up block_2)", "(pick-up block_4)"], "opt": "10", "yes": ["(unstack block_5 block_3)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (clear block_5) (handempty) (on block_3 block_1) (on block_5 block_3) (ontable block_1) (ontable block_2) (ontable block_4))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 5470664424051677801, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_10, block_6, block_4, and block_8. The following block(s) are stacked on top of another block: block_7 is on block_10, block_1 is on block_8, block_9 is on block_1, block_5 is on block_9, and block_3 is on block_7. The goal is to reach a state where the following facts hold: The block block_7 is currently situated above the block block_10, The block block_2 is currently situated above the block block_5, The block block_1 is on top of block block_8, The block block_6 is currently situated above the block block_3, The block block_4 is on top of block block_2, The block block_5 is on top of block block_9, The block block_7 is currently situated under the block block_3, and The block block_9 is on top of block block_1. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(stack block_2 block_4)"], "no": ["(stack block_2 block_3)", "(stack block_2 block_6)", "(put-down block_2)"], "opt": "5", "yes": ["(stack block_2 block_5)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_3) (clear block_4) (clear block_5) (clear block_6) (holding block_2) (on block_1 block_8) (on block_3 block_7) (on block_5 block_9) (on block_7 block_10) (on block_9 block_1) (ontable block_10) (ontable block_4) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 1426698303064938929, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_2, block_1, and block_8. The following block(s) are stacked on top of another block: block_7 is on block_10, block_5 is on block_2, block_9 is on block_5, block_6 is on block_4, block_4 is on block_9, and block_3 is on block_7. The goal is to reach a state where the following facts hold: The block block_7 is currently situated above the block block_10, The block block_2 is currently situated above the block block_5, The block block_1 is currently situated above the block block_8, The block block_3 is currently situated under the block block_6, The block block_2 is currently situated under the block block_4, The block block_5 is currently situated above the block block_9, The block block_3 is on top of block block_7, and The block block_1 is currently situated under the block block_9. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_3 block_7)", "(pick-up block_8)"], "opt": "14", "yes": ["(pick-up block_1)", "(unstack block_6 block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_3) (clear block_6) (clear block_8) (handempty) (on block_3 block_7) (on block_4 block_9) (on block_5 block_2) (on block_6 block_4) (on block_7 block_10) (on block_9 block_5) (ontable block_1) (ontable block_10) (ontable block_2) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 7070984405243512409, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_3, block_2, block_1, and block_8. The following block(s) are stacked on top of another block: block_7 is on block_10, block_5 is on block_2, block_9 is on block_5, block_4 is on block_9, and block_6 is on block_1. The goal is to reach a state where the following facts hold: The block block_10 is currently situated under the block block_7, The block block_2 is currently situated above the block block_5, The block block_8 is currently situated under the block block_1, The block block_6 is on top of block block_3, The block block_4 is currently situated above the block block_2, The block block_5 is on top of block block_9, The block block_3 is on top of block block_7, and The block block_1 is currently situated under the block block_9. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_6 block_1)", "(unstack block_7 block_10)", "(pick-up block_8)"], "opt": "16", "yes": ["(unstack block_4 block_9)", "(pick-up block_3)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_3) (clear block_4) (clear block_6) (clear block_7) (clear block_8) (handempty) (on block_4 block_9) (on block_5 block_2) (on block_6 block_1) (on block_7 block_10) (on block_9 block_5) (ontable block_1) (ontable block_10) (ontable block_2) (ontable block_3) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 7589671926228217235, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3, block_5, and block_4. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_1 is on block_3. The goal is to reach a state where the following facts hold: The block block_2 is currently situated above the block block_5, The block block_4 is on top of block block_2, The block block_1 is on top of block block_3, and The block block_1 is currently situated under the block block_5. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_1 block_3)", "(unstack block_2 block_4)"], "opt": "6", "yes": ["(pick-up block_5)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_2) (clear block_5) (handempty) (on block_1 block_3) (on block_2 block_4) (ontable block_3) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 5350177900431928851, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_10. The following block(s) are on the table: block_2, block_1, and block_8. The following block(s) are stacked on top of another block: block_5 is on block_2, block_7 is on block_3, block_3 is on block_4, block_9 is on block_5, block_4 is on block_9, and block_6 is on block_1. The goal is to reach a state where the following facts hold: The block block_7 is on top of block block_10, The block block_2 is currently situated above the block block_5, The block block_1 is currently situated above the block block_8, The block block_6 is on top of block block_3, The block block_4 is currently situated above the block block_2, The block block_9 is currently situated under the block block_5, The block block_3 is on top of block block_7, and The block block_9 is currently situated above the block block_1. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(stack block_10 block_8)", "(stack block_10 block_6)", "(stack block_10 block_7)"], "opt": "19", "yes": ["(put-down block_10)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_6) (clear block_7) (clear block_8) (holding block_10) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_6 block_1) (on block_7 block_3) (on block_9 block_5) (ontable block_1) (ontable block_2) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -8696473841778235250, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5, block_4, and block_1. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_5 is currently situated above the block block_4, The block block_1 is currently situated under the block block_4, and The block block_5 is currently situated under the block block_3. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(pick-up block_1)"], "opt": "10", "yes": ["(unstack block_3 block_5)", "(unstack block_2 block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_2) (clear block_3) (handempty) (on block_2 block_4) (on block_3 block_5) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 8562182200835891088, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_3. The following block(s) are on the table: block_4 and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2 and block_1 is on block_5. The goal is to reach a state where the following facts hold: The block block_5 is currently situated under the block block_4, The block block_4 is currently situated under the block block_2, and The block block_1 is on top of block block_3. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(stack block_3 block_4)", "(stack block_3 block_1)"], "opt": "9", "yes": ["(put-down block_3)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_4) (holding block_3) (on block_1 block_5) (on block_5 block_2) (ontable block_2) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 8419942434014547044, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_2 is on block_5, block_3 is on block_2, and block_4 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is currently situated above the block block_5, The block block_2 is currently situated above the block block_4, and The block block_1 is on top of block block_3. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_4 block_1)"], "opt": "10", "yes": ["(unstack block_3 block_2)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (handempty) (on block_2 block_5) (on block_3 block_2) (on block_4 block_1) (ontable block_1) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 1186239431563741926, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_3 is on block_4, block_2 is on block_1, and block_4 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_2 is currently situated above the block block_4, and The block block_1 is on top of block block_3. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_2 block_1)"], "opt": "6", "yes": ["(unstack block_3 block_4)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (handempty) (on block_2 block_1) (on block_3 block_4) (on block_4 block_5) (ontable block_1) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -5979104998177606311, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_5. The following block(s) are on the table: block_3 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_3 and block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_5 is currently situated under the block block_4, The block block_2 is on top of block block_4, and The block block_1 is on top of block block_3. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(stack block_5 block_4)", "(stack block_5 block_2)"], "opt": "7", "yes": ["(put-down block_5)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (holding block_5) (on block_2 block_1) (on block_4 block_3) (ontable block_1) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -3170479490826623369, "group": "goal_closer_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_5 is on block_2, block_4 is on block_1, and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_2 is on top of block block_4, and The block block_3 is currently situated under the block block_1. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(unstack block_4 block_1)"], "opt": "10", "yes": ["(unstack block_3 block_5)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (handempty) (on block_3 block_5) (on block_4 block_1) (on block_5 block_2) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
