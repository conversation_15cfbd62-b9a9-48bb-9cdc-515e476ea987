{"id": -3823581107978385211, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c5 on board. The cars are at locations as follows: c2 and c1 are at l1; c0, c3, c8, and c6 are at l2; c9, c4, and c7 are at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c0 is at location l2, Car c1 is at location l1, Car c9 is at location l0, Car c5 is at location l2, Car c4 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c6 is at location l2, and Car c7 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c7 is on board the ferry. B. Car c5 is at location l2. C. Car c7 is at location l2. D. Car c0 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c7 is on board the ferry", "Car c5 is at location l2", "Car c7 is at location l2", "Car c0 is at location l1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 323288633219465416, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2 is at l4; c0 is at l2; c1 is at l3. The goal is to reach a state where the following facts hold: Car c1 is at location l3, Car c0 is at location l3, and Car c2 is at location l3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c0 is at location l1. B. Car c2 is at location l0. C. Car c1 is at location l4. D. The ferry is at l4 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c0 is at location l1", "Car c2 is at location l0", "Car c1 is at location l4", "The ferry is at l4 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 4578576821411003212, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c8 on board. The cars are at locations as follows: c2, c1, and c4 are at l1; c0, c5, and c3 are at l2; c6, c9, and c7 are at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c0 is at location l2, Car c1 is at location l1, Car c9 is at location l0, Car c5 is at location l2, Car c4 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c6 is at location l2, and Car c7 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c6 is on the ferry. B. Car c3 is at location l0. C. Car c1 is at location l0. D. Car c3 is on the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c6 is on the ferry", "Car c3 is at location l0", "Car c1 is at location l0", "Car c3 is on the ferry"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -6367402055180483032, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c25 on board. The cars are at locations as follows: c46, c15, c32, c7, c1, c19, c34, c39, c38, c26, c42, c4, c49, c43, c31, c41, c10, c27, c16, c40, c45, c28, c48, c37, c47, c5, c36, and c24 are at l1; c11, c0, c13, c18, c30, c6, c8, c2, c44, c17, c9, c22, c33, c23, c29, c21, c12, c35, c20, c3, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c7 is at location l1, Car c11 is at location l0, Car c15 is at location l1, Car c0 is at location l0, Car c32 is at location l1, Car c1 is at location l1, Car c13 is at location l0, Car c34 is at location l1, Car c18 is at location l1, Car c30 is at location l0, Car c33 is at location l1, Car c39 is at location l1, Car c40 is at location l0, Car c42 is at location l1, Car c4 is at location l1, Car c8 is at location l0, Car c49 is at location l1, Car c2 is at location l0, Car c44 is at location l0, Car c38 is at location l0, Car c37 is at location l0, Car c46 is at location l0, Car c31 is at location l1, Car c23 is at location l1, Car c9 is at location l0, Car c41 is at location l1, Car c45 is at location l0, Car c17 is at location l1, Car c22 is at location l0, Car c10 is at location l1, Car c27 is at location l1, Car c16 is at location l1, Car c3 is at location l1, Car c28 is at location l1, Car c29 is at location l0, Car c19 is at location l0, Car c26 is at location l0, Car c6 is at location l1, Car c12 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c36 is at location l0, Car c43 is at location l0, Car c47 is at location l1, Car c5 is at location l1, Car c20 is at location l0, Car c35 is at location l1, Car c24 is at location l1, Car c25 is at location l0, and Car c14 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c31 is on board the ferry. B. Ferry has car c35 on board. C. Car c32 is on board the ferry. D. Car c30 is on the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c31 is on board the ferry", "Ferry has car c35 on board", "Car c32 is on board the ferry", "Car c30 is on the ferry"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -2125005911776345063, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c43 on board. The cars are at locations as follows: c46, c15, c32, c7, c1, c34, c13, c18, c33, c39, c42, c4, c49, c31, c23, c41, c17, c10, c27, c16, c45, c3, c28, c48, c47, c5, c35, and c24 are at l1; c11, c0, c30, c40, c6, c8, c2, c44, c38, c37, c9, c22, c29, c19, c26, c21, c12, c36, c20, c25, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c7 is at location l1, Car c11 is at location l0, Car c15 is at location l1, Car c0 is at location l0, Car c32 is at location l1, Car c1 is at location l1, Car c13 is at location l0, Car c34 is at location l1, Car c18 is at location l1, Car c30 is at location l0, Car c33 is at location l1, Car c39 is at location l1, Car c40 is at location l0, Car c42 is at location l1, Car c4 is at location l1, Car c8 is at location l0, Car c49 is at location l1, Car c2 is at location l0, Car c44 is at location l0, Car c38 is at location l0, Car c37 is at location l0, Car c46 is at location l0, Car c31 is at location l1, Car c23 is at location l1, Car c9 is at location l0, Car c41 is at location l1, Car c45 is at location l0, Car c17 is at location l1, Car c22 is at location l0, Car c10 is at location l1, Car c27 is at location l1, Car c16 is at location l1, Car c3 is at location l1, Car c28 is at location l1, Car c29 is at location l0, Car c19 is at location l0, Car c26 is at location l0, Car c6 is at location l1, Car c12 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c36 is at location l0, Car c43 is at location l0, Car c47 is at location l1, Car c5 is at location l1, Car c20 is at location l0, Car c35 is at location l1, Car c24 is at location l1, Car c25 is at location l0, and Car c14 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c11 is on board the ferry. B. Car c9 is on board the ferry. C. Car c13 is on the ferry. D. Car c27 is at location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c11 is on board the ferry", "Car c9 is on board the ferry", "Car c13 is on the ferry", "Car c27 is at location l0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -236533319900816804, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2 is at l4; c0 is at l0; c1 is at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l3, Car c0 is at location l3, and Car c2 is at location l3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c2 is at location l0. B. Car c1 is on the ferry. C. The ferry is at l2 location. D. Car c0 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c2 is at location l0", "Car c1 is on the ferry", "The ferry is at l2 location", "Car c0 is at location l1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 2722040789690472586, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c13 on board. The cars are at locations as follows: c46, c7, c22, c1, c32, c19, c34, c21, c39, c38, c26, c42, c4, c49, c43, c31, c41, c27, c40, c45, c28, c48, c25, c37, c47, c5, c36, and c24 are at l1; c11, c15, c0, c18, c30, c6, c8, c2, c10, c44, c16, c17, c9, c33, c23, c29, c12, c35, c20, c3, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c7 is at location l1, Car c11 is at location l0, Car c15 is at location l1, Car c0 is at location l0, Car c32 is at location l1, Car c1 is at location l1, Car c13 is at location l0, Car c34 is at location l1, Car c18 is at location l1, Car c30 is at location l0, Car c33 is at location l1, Car c39 is at location l1, Car c40 is at location l0, Car c42 is at location l1, Car c4 is at location l1, Car c8 is at location l0, Car c49 is at location l1, Car c2 is at location l0, Car c44 is at location l0, Car c38 is at location l0, Car c37 is at location l0, Car c46 is at location l0, Car c31 is at location l1, Car c23 is at location l1, Car c9 is at location l0, Car c41 is at location l1, Car c45 is at location l0, Car c17 is at location l1, Car c22 is at location l0, Car c10 is at location l1, Car c27 is at location l1, Car c16 is at location l1, Car c3 is at location l1, Car c28 is at location l1, Car c29 is at location l0, Car c19 is at location l0, Car c26 is at location l0, Car c6 is at location l1, Car c12 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c36 is at location l0, Car c43 is at location l0, Car c47 is at location l1, Car c5 is at location l1, Car c20 is at location l0, Car c35 is at location l1, Car c24 is at location l1, Car c25 is at location l0, and Car c14 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c14 is at location l1. B. Ferry has car c24 on board. C. Car c11 is at location l1. D. Car c16 is on the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c14 is at location l1", "Ferry has car c24 on board", "Car c11 is at location l1", "Car c16 is on the ferry"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 4305963889261346880, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c5 on board. The cars are at locations as follows: c2, c1, and c8 are at l1; c0, c3, and c6 are at l2; c4, c9, and c7 are at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c0 is at location l2, Car c1 is at location l1, Car c9 is at location l0, Car c5 is at location l2, Car c4 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c6 is at location l2, and Car c7 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. There are no cars on the ferry. B. Car c5 is at location l0. C. Car c1 is at location l0. D. Car c2 is on board the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["There are no cars on the ferry", "Car c5 is at location l0", "Car c1 is at location l0", "Car c2 is on board the ferry"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -2070937803580411386, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c22 on board. The cars are at locations as follows: c46, c15, c32, c7, c1, c34, c39, c38, c42, c4, c49, c43, c31, c41, c17, c10, c27, c16, c40, c45, c28, c48, c25, c37, c47, c5, c36, and c24 are at l1; c11, c0, c13, c18, c30, c6, c8, c2, c44, c9, c33, c23, c29, c19, c26, c21, c12, c35, c20, c3, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c7 is at location l1, Car c11 is at location l0, Car c15 is at location l1, Car c0 is at location l0, Car c32 is at location l1, Car c1 is at location l1, Car c13 is at location l0, Car c34 is at location l1, Car c18 is at location l1, Car c30 is at location l0, Car c33 is at location l1, Car c39 is at location l1, Car c40 is at location l0, Car c42 is at location l1, Car c4 is at location l1, Car c8 is at location l0, Car c49 is at location l1, Car c2 is at location l0, Car c44 is at location l0, Car c38 is at location l0, Car c37 is at location l0, Car c46 is at location l0, Car c31 is at location l1, Car c23 is at location l1, Car c9 is at location l0, Car c41 is at location l1, Car c45 is at location l0, Car c17 is at location l1, Car c22 is at location l0, Car c10 is at location l1, Car c27 is at location l1, Car c16 is at location l1, Car c3 is at location l1, Car c28 is at location l1, Car c29 is at location l0, Car c19 is at location l0, Car c26 is at location l0, Car c6 is at location l1, Car c12 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c36 is at location l0, Car c43 is at location l0, Car c47 is at location l1, Car c5 is at location l1, Car c20 is at location l0, Car c35 is at location l1, Car c24 is at location l1, Car c25 is at location l0, and Car c14 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c17 is at location l0. B. Ferry has car c46 on board. C. Car c2 is at location l1. D. Car c11 is on board the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c17 is at location l0", "Ferry has car c46 on board", "Car c2 is at location l1", "Car c11 is on board the ferry"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 8908272307640410180, "group": "landmarks_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2, c7, c1, c8, and c5 are at l1; c0 and c3 are at l2; c4, c6, and c9 are at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c0 is at location l2, Car c1 is at location l1, Car c9 is at location l0, Car c5 is at location l2, Car c4 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c6 is at location l2, and Car c7 is at location l0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Car c4 is at location l1. B. Car c2 is at location l0. C. Car c5 is on the ferry. D. Car c1 is at location l2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c4 is at location l1", "Car c2 is at location l0", "Car c5 is on the ferry", "Car c1 is at location l2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 6835978133384204769, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l3-0, l3-2, and l3-1 are in c3; l4-1, l4-2, and l4-0 are in c4; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t4 is at l4-0, t3, a0, and p0 are at l3-0, p1 is at l3-1, t1 is at l1-0, t0 is at l0-2, t2 is at l2-1, p3 is in t3, p2 is in t2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p3 is at l3-2, p2 is at l2-2, and p1 is at l3-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l0-0. B. t0 is at l0-0. C. t2 is at l2-2. D. p3 is at l4-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l0-0", "t0 is at l0-0", "t2 is at l2-2", "p3 is at l4-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 4921639997979964023, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l3-0, l3-2, and l3-1 are in c3; l4-1, l4-2, and l4-0 are in c4; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t4 is at l4-0, t3 is at l3-0, p3 is at l4-1, p0 and a0 are at l2-0, p2 and t2 are at l2-1, t0 is at l0-2, t1 is at l1-2, p1 is in t3. The goal is to reach a state where the following facts hold: p0 is at l3-0, p3 is at l3-2, p2 is at l2-2, and p1 is at l3-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p1 is at l4-2. B. a0 is at l4-0. C. p3 is at l1-2. D. t3 is at l3-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is at l4-2", "a0 is at l4-0", "p3 is at l1-2", "t3 is at l3-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 1658489319248825054, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, p3, t1, and p0 are at l1-2, p2 and a0 are at l0-0, t2 is at l2-1, t0 is at l0-1, p1 is at l1-0, p4 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, and p1 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p3 is at l1-0. B. p2 is at l2-2. C. t1 is at l1-0. D. p4 is in t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is at l1-0", "p2 is at l2-2", "t1 is at l1-0", "p4 is in t0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 5690629149065232131, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0. Currently, p3 and p1 are at l0-2, t0 is at l0-1, t1 is at l1-2, a0 is at l1-0, p0 is in t0, p2 is in a0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p1 is at l0-1, p0 is at l0-1, and p2 is at l0-1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is at l0-0. B. p3 is in t0. C. p1 is at l1-0. D. p1 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is at l0-0", "p3 is in t0", "p1 is at l1-0", "p1 is at l0-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -1264303715999081777, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, p3 and p1 are at l2-0, t0, p2, and a0 are at l0-0, t2 is at l2-1, t1 is at l1-1, p4 is at l1-0, p0 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, and p1 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is at l2-1. B. t0 is at l0-2. C. p4 is at l1-1. D. p2 is in a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is at l2-1", "t0 is at l0-2", "p4 is at l1-1", "p2 is in a0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -4380647070619639126, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0. Currently, p2 and a0 are at l1-0, t0 is at l0-2, t1 is at l1-1, p3, p0, and p1 are in t0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p1 is at l0-1, p0 is at l0-1, and p2 is at l0-1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p3 is in a0. B. p0 is at l0-2. C. t0 is at l0-1. D. p3 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is in a0", "p0 is at l0-2", "t0 is at l0-1", "p3 is at l0-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 1212016093787866341, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l0-4, l0-9, l0-3, l0-6, l0-1, l0-2, l0-8, l0-7, l0-5, and l0-0 are in c0; l1-0, l1-9, l1-5, l1-3, l1-2, l1-6, l1-8, l1-7, l1-1, and l1-4 are in c1; l3-0, l3-5, l3-3, l3-9, l3-1, l3-8, l3-2, l3-7, l3-4, and l3-6 are in c3; l4-1, l4-4, l4-2, l4-7, l4-8, l4-9, l4-0, l4-6, l4-3, and l4-5 are in c4; l2-9, l2-3, l2-6, l2-2, l2-0, l2-8, l2-1, l2-7, l2-5, and l2-4 are in c2. Currently, t4 is at l4-0, t3 and p2 are at l3-0, p3 is at l2-9, t2 is at l2-0, t0 is at l0-2, a0 is at l0-0, t1 is at l1-8, p0 is at l2-4, p1 is in t1. The goal is to reach a state where the following facts hold: p2 is at l4-8, p1 is at l2-7, p3 is at l1-1, and p0 is at l1-5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is in t1. B. p3 is at l1-3. C. p2 is at l1-8. D. p3 is at l1-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is in t1", "p3 is at l1-3", "p2 is at l1-8", "p3 is at l1-2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 1455248976243101813, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t0 is at l0-0, p2 is at l0-1, t2 is at l2-1, t1 and p0 are at l1-2, a0 and p1 are at l1-0, p3 and p4 are in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, and p1 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l2-0. B. p0 is at l1-1. C. a0 is at l0-0. D. p0 is at l2-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l2-0", "p0 is at l1-1", "a0 is at l0-0", "p0 is at l2-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 8009344041751059892, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l3-0, l3-2, and l3-1 are in c3; l4-1, l4-2, and l4-0 are in c4; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t3 is at l3-0, p3 is at l4-1, p1 is at l3-1, t0 is at l0-0, a0 is at l2-0, t4 is at l4-2, t1 is at l1-2, t2 is at l2-2, p0 is in a0, p2 is in t2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p3 is at l3-2, p2 is at l2-2, and p1 is at l3-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l4-1. B. p3 is at l1-2. C. p0 is at l1-0. D. t4 is at l4-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l4-1", "p3 is at l1-2", "p0 is at l1-0", "t4 is at l4-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6138221031130765187, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l0-4, l0-9, l0-3, l0-6, l0-1, l0-2, l0-8, l0-7, l0-5, and l0-0 are in c0; l1-0, l1-9, l1-5, l1-3, l1-2, l1-6, l1-8, l1-7, l1-1, and l1-4 are in c1; l3-0, l3-5, l3-3, l3-9, l3-1, l3-8, l3-2, l3-7, l3-4, and l3-6 are in c3; l4-1, l4-4, l4-2, l4-7, l4-8, l4-9, l4-0, l4-6, l4-3, and l4-5 are in c4; l2-9, l2-3, l2-6, l2-2, l2-0, l2-8, l2-1, l2-7, l2-5, and l2-4 are in c2. Currently, t3 is at l3-0, t2 is at l2-7, t1 is at l1-0, a0 is at l2-0, t0 is at l0-2, t4 is at l4-2, p2 is at l4-8, p1 is in t2, p0 and p3 are in a0. The goal is to reach a state where the following facts hold: p2 is at l4-8, p1 is at l2-7, p3 is at l1-1, and p0 is at l1-5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p1 is at l2-9. B. t1 is at l1-5. C. p0 is in t4. D. p3 is at l4-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is at l2-9", "t1 is at l1-5", "p0 is in t4", "p3 is at l4-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7118131213557277978, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_3. The goal is to reach a state where the following facts hold: The block block_3 is currently situated above the block block_1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robotic arm is holding block_3. B. The block block_3 is on top of block block_3. C. The block block_1 is on top of block block_1. D. The block block_1 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_3", "The block block_3 is on top of block block_3", "The block block_1 is on top of block block_1", "The block block_1 is currently being held by the robotic arm"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -6685431312027226529, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_1 and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_5 is currently situated under the block block_3, and The block block_5 is on top of block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. No blocks are placed on top of block_5. B. The block block_1 is on top of block block_2. C. The block block_2 is currently situated above the block block_4. D. The robotic arm is holding block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["No blocks are placed on top of block_5", "The block block_1 is on top of block block_2", "The block block_2 is currently situated above the block block_4", "The robotic arm is holding block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 4678240301829034066, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_1, block_2 is on block_4, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_5 is currently situated under the block block_3, and The block block_4 is currently situated under the block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Block block_4 is clear. B. The block block_5 is currently situated above the block block_2. C. The block block_2 is currently situated under the block block_2. D. The block block_4 is on top of block block_4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_4 is clear", "The block block_5 is currently situated above the block block_2", "The block block_2 is currently situated under the block block_2", "The block block_4 is on top of block block_4"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 7598263982846238383, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_5, and block_3. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_1 is on block_4. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_3 is currently situated above the block block_5, and The block block_4 is currently situated under the block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robotic arm is holding block_3. B. The block block_4 is on top of block block_3. C. The robotic arm is holding block_2. D. The block block_2 is currently situated under the block block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_3", "The block block_4 is on top of block block_3", "The robotic arm is holding block_2", "The block block_2 is currently situated under the block block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 3315024090946547688, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_4 and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2 and block_3 is on block_4. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_1, The block block_5 is currently situated under the block block_3, and The block block_5 is currently situated above the block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Block block_5 is located on the table. B. The block block_1 is currently situated above the block block_3. C. The block block_3 is on top of block block_3. D. No blocks are placed on top of block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_5 is located on the table", "The block block_1 is currently situated above the block block_3", "The block block_3 is on top of block block_3", "No blocks are placed on top of block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -6768951488006466563, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_2, block_8, and block_10. The following block(s) are stacked on top of another block: block_5 is on block_2, block_3 is on block_7, block_6 is on block_3, block_9 is on block_5, block_7 is on block_10, and block_4 is on block_9. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_5, The block block_4 is currently situated above the block block_2, The block block_1 is currently situated under the block block_9, The block block_7 is currently situated under the block block_3, The block block_5 is currently situated above the block block_9, The block block_8 is currently situated under the block block_1, The block block_6 is on top of block block_3, and The block block_10 is currently situated under the block block_7.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. No blocks are placed on top of block_1. B. The block block_7 is on top of block block_7. C. Block block_3 is placed on the table. D. The block block_9 is currently situated under the block block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["No blocks are placed on top of block_1", "The block block_7 is on top of block block_7", "Block block_3 is placed on the table", "The block block_9 is currently situated under the block block_3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -4986561784387295667, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_3, block_2 is on block_1, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_4 is on top of block block_2, The block block_5 is on top of block block_1, and The block block_5 is currently situated under the block block_2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated under the block block_1. B. The block block_2 is currently situated above the block block_2. C. The block block_1 is currently being held by the robotic arm. D. The block block_4 is currently situated above the block block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_1", "The block block_2 is currently situated above the block block_2", "The block block_1 is currently being held by the robotic arm", "The block block_4 is currently situated above the block block_5"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 633469100244057525, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_3. The following block(s) are stacked on top of another block: block_1 is on block_2 and block_2 is on block_3. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated under the block block_2. B. The block block_2 is currently situated under the block block_3. C. The block block_3 is currently being held by the robotic arm. D. The block block_3 is currently situated under the block block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_2", "The block block_2 is currently situated under the block block_3", "The block block_3 is currently being held by the robotic arm", "The block block_3 is currently situated under the block block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -6965764745176315240, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_2, block_5, and block_1. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_1, The block block_5 is currently situated under the block block_3, and The block block_5 is currently situated above the block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_5 is currently situated under the block block_1. B. The block block_2 is on top of block block_5. C. The robotic arm is not holding anything. D. The block block_3 is currently situated under the block block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_5 is currently situated under the block block_1", "The block block_2 is on top of block block_5", "The robotic arm is not holding anything", "The block block_3 is currently situated under the block block_5"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 2289501855374731759, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_4, block_2, and block_3. The following block(s) is stacked on top of another block: block_1 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is on top of block block_1, and The block block_5 is currently situated under the block block_2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated above the block block_5. B. The block block_4 is currently situated above the block block_4. C. The block block_2 is currently situated above the block block_1. D. The block block_4 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated above the block block_5", "The block block_4 is currently situated above the block block_4", "The block block_2 is currently situated above the block block_1", "The block block_4 is currently being held by the robotic arm"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2941311238080170375, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-0f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-1 is at position f4-2f. Key key0-2 is at position f3-0f. Key key0-0 is at position f1-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-1 is at f3-2f location. B. Key key0-1 is at f4-3f location. C. Robot is holding key0-0. D. Key key0-0 is at f3-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f3-2f location", "Key key0-1 is at f4-3f location", "Robot is holding key0-0", "Key key0-0 is at f3-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 655464149194719030, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f3-1f and is holding key0-2. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-3 is at f1-1f location. B. Key key0-1 is at f2-1f location. C. Robot is holding key0-1. D. Key key0-3 is at f3-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-3 is at f1-1f location", "Key key0-1 is at f2-1f location", "Robot is holding key0-1", "Key key0-3 is at f3-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -1724825638758658297, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-2f and is holding key0-2. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-2 is at f3-3f location, and Key key0-0 is at f3-3f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-0f location. B. Robot is at f3-0f location. C. Robot is at f1-3f location. D. Key key0-0 is at f4-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-0f location", "Robot is at f3-0f location", "Robot is at f1-3f location", "Key key0-0 is at f4-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -133803207604662038, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-2 is at position f1-4f. Key key0-0 is at position f2-2f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is holding key0-1. B. Key key0-0 is at f1-0f location. C. Robot is at f4-1f location. D. Key key0-2 is at f0-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is holding key0-1", "Key key0-0 is at f1-0f location", "Robot is at f4-1f location", "Key key0-2 is at f0-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -8090760317253429613, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f4-3f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-1f location. B. Robot is at f3-2f location. C. Key key0-1 is at f3-4f location. D. Key key0-1 is at f0-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-1f location", "Robot is at f3-2f location", "Key key0-1 is at f3-4f location", "Key key0-1 is at f0-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7142182234195325005, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-3f and is holding key0-1. All the positions are open except the following: f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-0 is at position f0-1f. Key key0-2 is at position f4-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f4-3f location. B. Robot is at f1-4f location. C. Key key0-3 is at f2-3f location. D. Robot is at f2-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-3f location", "Robot is at f1-4f location", "Key key0-3 is at f2-3f location", "Robot is at f2-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 2392814088437140753, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-0 is at position f3-3f. Key key0-1 is at position f1-2f. Key key0-2 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-2 is at f3-3f location, and Key key0-0 is at f3-3f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f2-3f location. B. Robot is at f3-0f location. C. Key key0-1 is at f3-4f location. D. Robot is at f3-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f2-3f location", "Robot is at f3-0f location", "Key key0-1 is at f3-4f location", "Robot is at f3-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 7771150773807176329, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-1f and is holding key0-3. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f. Key key0-2 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-2f location. B. Robot is at f2-1f location. C. Key key0-0 is at f4-0f location. D. Robot is at f4-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-2f location", "Robot is at f2-1f location", "Key key0-0 is at f4-0f location", "Robot is at f4-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 9025789639356130383, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-2 is at position f1-4f. Key key0-0 is at position f2-2f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f3-2f location. B. Key key0-0 is at f4-2f location. C. Key key0-1 is at f4-0f location. D. Key key0-2 is at f2-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f3-2f location", "Key key0-0 is at f4-2f location", "Key key0-1 is at f4-0f location", "Key key0-2 is at f2-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -94078601785636705, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f4-3f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-2f location. B. Robot is holding key0-2. C. Key key0-1 is at f0-2f location. D. Key key0-1 is at f3-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-2f location", "Robot is holding key0-2", "Key key0-1 is at f0-2f location", "Key key0-1 is at f3-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -2717395259725572867, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_4 and holding color white and robot robot1 is at tile_7 and holding color white; tile_10, tile_5, tile_2, tile_1, tile_6, tile_12, tile_11, tile_3, tile_9, and tile_8 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot2 is at tile_9 location. B. tile_7 is clear. C. Tile tile_10 is painted in black color. D. Robot robot2 is at tile_5 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_9 location", "tile_7 is clear", "Tile tile_10 is painted in black color", "Robot robot2 is at tile_5 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -8749527647283597157, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_5 and holding color white; tile_2, tile_6, tile_3, and tile_9 are clear; tile_7 is painted black, tile_8 is painted white, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is at tile_2 location. B. tile_5 is clear. C. Robot robot2 is at tile_2 location. D. Tile tile_2 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_2 location", "tile_5 is clear", "Robot robot2 is at tile_2 location", "Tile tile_2 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 6627747300641254593, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_6 and holding color black and robot robot1 is at tile_4 and holding color white; tile_2, tile_1, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_2 is painted in white color. B. Robot robot2 is at tile_2 location. C. tile_6 is clear. D. Tile tile_4 is painted in black color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_2 is painted in white color", "Robot robot2 is at tile_2 location", "tile_6 is clear", "Tile tile_4 is painted in black color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -677794193056967264, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_4 is to the right of tile_3, tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_17 is to the right of tile_16, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_13 is to the right of tile_12, and tile_8 is to the right of tile_7. Further, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_1 is down from tile_6, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_15 is down from tile_20, and tile_13 is down from tile_18 Currently, robot robot1 is at tile_4 and holding color white and robot robot2 is at tile_7 and holding color white; tile_12, tile_14, tile_5, tile_1, tile_9, tile_2, tile_19, and tile_3 are clear; tile_20 is painted white, tile_15 is painted black, tile_6 is painted white, tile_17 is painted black, tile_10 is painted white, tile_13 is painted black, tile_11 is painted black, tile_16 is painted white, tile_8 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_7 is painted in black color, Tile tile_10 is painted in white color, Tile tile_13 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, and Tile tile_18 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_20 is painted in black color. B. Tile tile_5 is painted in white color. C. tile_7 is clear. D. Tile tile_17 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_20 is painted in black color", "Tile tile_5 is painted in white color", "tile_7 is clear", "Tile tile_17 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -6795096273383498461, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_5 and holding color black and robot robot1 is at tile_1 and holding color white; tile_2, tile_4, tile_3, and tile_8 are clear; tile_7 is painted black, tile_6 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot2 is at tile_6 location. B. Robot robot1 is at tile_3 location. C. Robot robot2 is at tile_4 location. D. tile_5 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_6 location", "Robot robot1 is at tile_3 location", "Robot robot2 is at tile_4 location", "tile_5 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6266842166761872358, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_3 is to the right of tile_2, tile_23 is to the right of tile_22, tile_22 is to the right of tile_21, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_18 is to the right of tile_17, tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_17 is to the right of tile_16, tile_21 is to the right of tile_20, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_24 is to the right of tile_23, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_5 is to the right of tile_4, and tile_15 is to the right of tile_14. Further, tile_9 is down from tile_15, tile_10 is down from tile_16, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_13 is down from tile_19, tile_5 is down from tile_11, tile_2 is down from tile_8, tile_7 is down from tile_13, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_4 is down from tile_10, tile_12 is down from tile_18, tile_8 is down from tile_14, tile_1 is down from tile_7, tile_3 is down from tile_9, tile_11 is down from tile_17, tile_14 is down from tile_20, and tile_16 is down from tile_22 Currently, robot robot2 is at tile_5 and holding color white and robot robot1 is at tile_11 and holding color white; tile_17, tile_10, tile_6, tile_1, tile_15, tile_4, tile_9, tile_2, and tile_3 are clear; tile_23 is painted white, tile_19 is painted white, tile_24 is painted black, tile_8 is painted black, tile_7 is painted white, tile_20 is painted black, tile_12 is painted black, tile_14 is painted white, tile_13 is painted black, tile_16 is painted white, tile_22 is painted black, tile_21 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_14 is painted in white color, Tile tile_8 is painted in black color, Tile tile_7 is painted in white color, Tile tile_20 is painted in black color, Tile tile_23 is painted in white color, Tile tile_11 is painted in white color, Tile tile_12 is painted in black color, Tile tile_19 is painted in white color, Tile tile_21 is painted in white color, Tile tile_13 is painted in black color, Tile tile_10 is painted in black color, Tile tile_16 is painted in white color, Tile tile_22 is painted in black color, Tile tile_9 is painted in white color, Tile tile_18 is painted in white color, and Tile tile_24 is painted in black color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. tile_11 is clear. B. Tile tile_2 is painted in black color. C. Robot robot2 is at tile_11 location. D. Robot robot1 is at tile_13 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["tile_11 is clear", "Tile tile_2 is painted in black color", "Robot robot2 is at tile_11 location", "Robot robot1 is at tile_13 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -7641327919687212661, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_9 and holding color white and robot robot1 is at tile_4 and holding color black; tile_7, tile_5, tile_2, tile_1, tile_6, tile_12, tile_11, tile_3, and tile_8 are clear; tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_2 is painted in white color. B. Robot robot2 is at tile_12 location. C. Robot robot1 is at tile_11 location. D. tile_9 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_2 is painted in white color", "Robot robot2 is at tile_12 location", "Robot robot1 is at tile_11 location", "tile_9 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 4059588696135519664, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_4 is to the right of tile_3, tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_17 is to the right of tile_16, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_13 is to the right of tile_12, and tile_8 is to the right of tile_7. Further, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_1 is down from tile_6, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_15 is down from tile_20, and tile_13 is down from tile_18 Currently, robot robot1 is at tile_10 and holding color black and robot robot2 is at tile_2 and holding color white; tile_17, tile_6, tile_12, tile_14, tile_5, tile_1, tile_4, tile_9, tile_7, tile_19, tile_11, and tile_3 are clear; tile_20 is painted white, tile_15 is painted black, tile_13 is painted black, tile_16 is painted white, tile_8 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_7 is painted in black color, Tile tile_10 is painted in white color, Tile tile_13 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, and Tile tile_18 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot2 is at tile_10 location. B. Tile tile_11 is painted in white color. C. tile_10 is clear. D. Tile tile_16 is painted in black color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_10 location", "Tile tile_11 is painted in white color", "tile_10 is clear", "Tile tile_16 is painted in black color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -6079728576040432942, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color black; tile_5, tile_1, tile_6, tile_3, tile_9, and tile_8 are clear; tile_7 is painted black, tile_10 is painted white, tile_11 is painted black, and tile_12 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is at tile_3 location. B. tile_10 is clear. C. Robot robot1 is at tile_10 location. D. tile_4 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_3 location", "tile_10 is clear", "Robot robot1 is at tile_10 location", "tile_4 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 8603784826906026050, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_2 and holding color black; tile_6, tile_4, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_1 is painted in white color. B. Robot robot1 is at tile_4 location. C. Tile tile_2 is painted in black color. D. Tile tile_4 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_1 is painted in white color", "Robot robot1 is at tile_4 location", "Tile tile_2 is painted in black color", "Tile tile_4 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -1866930494136301385, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball7, and right gripper is carrying the ball ball5. Additionally, ball1, ball2, and ball4 are at room1, ball3 is at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball7 is at room1 location, Ball ball3 is at room1 location, Ball ball4 is in room room1, Ball ball1 is at room3 location, Ball ball6 is in room room2, and Ball ball5 is in room room2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is carrying the ball ball1 in the right gripper. B. Robot robot1 is carrying the ball ball3 in the left gripper. C. Robot robot1 is at room1 location. D. Robot robot1 is carrying the ball ball2 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball1 in the right gripper", "Robot robot1 is carrying the ball ball3 in the left gripper", "Robot robot1 is at room1 location", "Robot robot1 is carrying the ball ball2 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -8393105248375561552, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball3. Additionally, ball1 is at room2, ball4 is at room1. The goal is to reach a state where the following facts hold: Ball ball2 is in room room2, Ball ball1 is at room2 location, Ball ball4 is in room room2, and Ball ball3 is at room2 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is carrying the ball ball4 in the left gripper. B. Robot robot1 is at room1 location. C. Robot robot1 is carrying the ball ball1 in the left gripper. D. Ball ball3 is in room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball4 in the left gripper", "Robot robot1 is at room1 location", "Robot robot1 is carrying the ball ball1 in the left gripper", "Ball ball3 is in room room1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -7608132875073545005, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball5. Additionally, ball2, ball7, ball3, and ball4 are at room1, ball1 is at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball7 is in room room1, Ball ball3 is at room1 location, Ball ball4 is in room room1, Ball ball1 is at room3 location, Ball ball6 is at room2 location, and Ball ball5 is at room2 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is at room3 location. B. Robot robot1 is carrying the ball ball7 in the right gripper. C. Ball ball1 is in room room2. D. Ball ball5 is in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is at room3 location", "Robot robot1 is carrying the ball ball7 in the right gripper", "Ball ball1 is in room room2", "Ball ball5 is in room room2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -251484871744676983, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball3. Additionally, ball1 and ball4 are at room1, ball5 and ball7 are at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is in room room1, Ball ball7 is in room room1, Ball ball3 is in room room1, Ball ball4 is in room room1, Ball ball1 is at room3 location, Ball ball6 is at room2 location, and Ball ball5 is in room room2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball2 is in room room2. B. Robot robot1 is in room room2. C. Robot robot1 is carrying the ball ball2 in the right gripper. D. Robot robot1 is carrying the ball ball4 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball2 is in room room2", "Robot robot1 is in room room2", "Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball ball4 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 368914849215493310, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball4. Additionally, ball1 is at room2, ball2 is at room1, ball3 is at room5. The goal is to reach a state where the following facts hold: Ball ball1 is in room room4, Ball ball4 is at room5 location, Ball ball3 is in room room4, and Ball ball2 is in room room3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is at room2 location. B. Ball ball3 is at room1 location. C. Ball ball4 is at room3 location. D. Robot robot1 is in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is at room2 location", "Ball ball3 is at room1 location", "Ball ball4 is at room3 location", "Robot robot1 is in room room2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5104728610006852199, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball4 and ball3 are at room5, ball2 is at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room4 location, Ball ball4 is in room room5, Ball ball3 is in room room4, and Ball ball2 is at room3 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is at room3 location. B. Robot robot1 is at room4 location. C. Robot robot1 is at room2 location. D. Ball ball2 is at room5 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is at room3 location", "Robot robot1 is at room4 location", "Robot robot1 is at room2 location", "Ball ball2 is at room5 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -5149912426663232676, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball6, and right gripper is carrying the ball ball10. Additionally, ball11, ball9, ball2, ball5, ball4, ball14, and ball15 are at room1, ball1, ball3, ball13, and ball12 are at room2, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball10 is in room room3, Ball ball14 is in room room3, Ball ball1 is at room2 location, Ball ball3 is at room2 location, Ball ball2 is in room room1, Ball ball9 is at room3 location, Ball ball5 is in room room1, Ball ball4 is in room room1, Ball ball8 is in room room1, Ball ball13 is at room2 location, Ball ball11 is at room3 location, Ball ball7 is in room room3, Ball ball12 is in room room2, Ball ball6 is at room1 location, and Ball ball15 is in room room1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is in room room3. B. Robot robot1 is carrying the ball ball15 in the left gripper. C. Ball ball3 is in room room1. D. Robot robot1 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is in room room3", "Robot robot1 is carrying the ball ball15 in the left gripper", "Ball ball3 is in room room1", "Robot robot1 is at room3 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8909197110121940967, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball2, ball4, and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room2 location, Ball ball1 is in room room2, Ball ball4 is at room2 location, and Ball ball3 is at room2 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball1 is at room2 location. B. Robot robot1 is carrying the ball ball4 in the left gripper. C. Robot robot1 is carrying the ball ball2 in the right gripper. D. Robot robot1 is carrying the ball ball1 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball1 is at room2 location", "Robot robot1 is carrying the ball ball4 in the left gripper", "Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball ball1 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -1741791280652155792, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room10, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball3. Additionally, ball1 is at room4, ball2 is at room6. The goal is to reach a state where the following facts hold: Ball ball4 is in room room9, Ball ball1 is at room8 location, Ball ball3 is in room room8, and Ball ball2 is at room6 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball3 is in room room7. B. Robot robot1 is at room9 location. C. Ball ball2 is at room1 location. D. Ball ball1 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball3 is in room room7", "Robot robot1 is at room9 location", "Ball ball2 is at room1 location", "Ball ball1 is at room3 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -5782485328808204446, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room6 and both grippers are free. Additionally, ball1 is at room4, ball3 and ball2 are at room6, ball4 is at room9. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball1 is in room room8, Ball ball3 is at room8 location, and Ball ball2 is in room room6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball2 is at room4 location. B. Robot robot1 is carrying the ball ball3 in the right gripper. C. Robot robot1 is at room4 location. D. Ball ball2 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball2 is at room4 location", "Robot robot1 is carrying the ball ball3 in the right gripper", "Robot robot1 is at room4 location", "Ball ball2 is at room3 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 4821718203476065346, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint1; Soil data were communicated from the following waypoints: waypoint2, waypoint1. Image objective0 was communicated in mode low_res. Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode colour. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective3 in mode high_res. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective6 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Image objective3 was communicated in mode high_res, Image objective4 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint2;, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Image objective5 was communicated in mode high_res. B. Rover rover1 has image objective1 in mode colour. C. Rover rover1 has rock analyzed in waypoint waypoint2. D. Image objective3 was communicated in mode colour.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Image objective5 was communicated in mode high_res", "Rover rover1 has image objective1 in mode colour", "Rover rover1 has rock analyzed in waypoint waypoint2", "Image objective3 was communicated in mode colour"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -4419243224709509874, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has image objective0 in mode low_res. B. Rover rover1 is at waypoint0. C. Rover rover0 has image objective0 in mode colour. D. Rover rover0 has its camera camera0 calibrated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has image objective0 in mode low_res", "Rover rover1 is at waypoint0", "Rover rover0 has image objective0 in mode colour", "Rover rover0 has its camera camera0 calibrated"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 3500558693611047165, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store1 and store0 are full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 is at waypoint1. B. Rocks can be sampled at the following location(s): waypoint0. C. Store(s) store1 is empty. D. Rocks can be sampled at the following location(s): waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint1", "Rocks can be sampled at the following location(s): waypoint0", "Store(s) store1 is empty", "Rocks can be sampled at the following location(s): waypoint1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 6142717132055529809, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode low_res, and Soil data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has image objective1 in mode low_res. B. Rover rover1 is at waypoint1. C. Image objective0 was communicated in mode low_res. D. Rover rover0 has image objective0 in mode high_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has image objective1 in mode low_res", "Rover rover1 is at waypoint1", "Image objective0 was communicated in mode low_res", "Rover rover0 has image objective0 in mode high_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 7367733930913896581, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode colour.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has image objective2 in mode high_res. B. Rover rover1 has image objective1 in mode high_res. C. Image objective1 was communicated in mode colour. D. Soil can be sampled at the following location(s): waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has image objective2 in mode high_res", "Rover rover1 has image objective1 in mode high_res", "Image objective1 was communicated in mode colour", "Soil can be sampled at the following location(s): waypoint0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -3610328923271677066, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover1 has its camera camera0 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode low_res, and Soil data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Image objective1 was communicated in mode high_res. B. Rover rover1 is at waypoint0. C. Rock data was communicated from waypoint waypoint1; . D. Rover rover0 has image objective0 in mode high_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Image objective1 was communicated in mode high_res", "Rover rover1 is at waypoint0", "Rock data was communicated from waypoint waypoint1; ", "Rover rover0 has image objective0 in mode high_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 6470264818412767253, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has image objective0 in mode colour. B. Rover rover0 has its camera camera0 calibrated. C. Rover rover1 is at waypoint2. D. Rover rover1 has image objective0 in mode low_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has image objective0 in mode colour", "Rover rover0 has its camera camera0 calibrated", "Rover rover1 is at waypoint2", "Rover rover1 has image objective0 in mode low_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -7946745159529043314, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint2; Image objective3 was communicated in mode high_res. Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective3 in mode high_res. Rover rover1 has image objective0 in mode colour. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective6 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Image objective3 was communicated in mode high_res, Image objective4 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint2;, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Image objective6 was communicated in mode high_res. B. Image objective5 was communicated in mode high_res. C. Rover rover0 has image objective0 in mode low_res. D. Rover rover0 has image objective3 in mode low_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Image objective6 was communicated in mode high_res", "Image objective5 was communicated in mode high_res", "Rover rover0 has image objective0 in mode low_res", "Rover rover0 has image objective3 in mode low_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 8599727145682852999, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store1 and store0 are full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has its camera camera0 calibrated. B. Rover rover1 is at waypoint0. C. Image objective1 was communicated in mode colour. D. Rover rover0 is at waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has its camera camera0 calibrated", "Rover rover1 is at waypoint0", "Image objective1 was communicated in mode colour", "Rover rover0 is at waypoint1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1670320605776143986, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports high_res and low_res and colour. Camera camera2 supports colour and low_res. Camera camera1 supports colour. Rover rover0 can traverse from waypoint2 to waypoint5, waypoint3 to waypoint0, waypoint6 to waypoint5, waypoint5 to waypoint0, waypoint0 to waypoint5, waypoint5 to waypoint2, waypoint1 to waypoint5, waypoint5 to waypoint1, waypoint5 to waypoint4, waypoint0 to waypoint3, waypoint4 to waypoint5, waypoint5 to waypoint6. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint3 to waypoint0, waypoint5 to waypoint0, waypoint2 to waypoint0, waypoint1 to waypoint6, waypoint4 to waypoint2, waypoint0 to waypoint6, waypoint6 to waypoint0, waypoint0 to waypoint5, waypoint0 to waypoint3, waypoint2 to waypoint4, waypoint6 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint4, waypoint3, waypoint5, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint2: waypoint3, waypoint5, waypoint1, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint6: waypoint1, waypoint0, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint0: waypoint6, waypoint3, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint3: waypoint6, waypoint1, waypoint0, waypoint4, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint5: waypoint1, waypoint0, waypoint4, waypoint3, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint4: waypoint3, waypoint5, waypoint2, waypoint6, and waypoint1. Objective objective1 is visible from waypoint4, waypoint2, waypoint5, and waypoint0. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint5. Rocks can be sampled at the following location(s): waypoint3, waypoint6, waypoint2, and waypoint4. Soil can be sampled at the following location(s): waypoint3, waypoint6, waypoint1, and waypoint4. Rovers rover1 and rover0 are available. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint4;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint5;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has its camera camera1 calibrated. B. Rover rover0 has rock analyzed in waypoint waypoint2. C. Rover rover0 is at waypoint0. D. Rover rover1 has its camera camera0 calibrated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has its camera camera1 calibrated", "Rover rover0 has rock analyzed in waypoint waypoint2", "Rover rover0 is at waypoint0", "Rover rover1 has its camera camera0 calibrated"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1910086906025976655, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x3-y0, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x2-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x1-y1. B. Place loc-x3-y1 has been visited. C. the robot is in place loc-x1-y3. D. the robot is in place loc-x1-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1", "Place loc-x3-y1 has been visited", "the robot is in place loc-x1-y3", "the robot is in place loc-x1-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -4445256229344889313, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x3-y4, loc-x3-y3, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x1-y4, loc-x2-y0, loc-x2-y4, loc-x3-y2, and loc-x1-y1. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y4 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Place loc-x0-y0 has been visited. B. the robot is in place loc-x2-y0. C. the robot is in place loc-x1-y4. D. the robot is in place loc-x1-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x0-y0 has been visited", "the robot is in place loc-x2-y0", "the robot is in place loc-x1-y4", "the robot is in place loc-x1-y3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -827256317947915185, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x0-y1, loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x3-y3, loc-x0-y2, loc-x2-y0, loc-x0-y0, loc-x1-y1, loc-x1-y2, and loc-x3-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Place loc-x1-y3 has been visited. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x2-y1. D. the robot is in place loc-x3-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x1-y3 has been visited", "the robot is in place loc-x1-y2", "the robot is in place loc-x2-y1", "the robot is in place loc-x3-y0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 8594127284290650318, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x0-y1, loc-x2-y1, loc-x2-y2, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y0. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x2-y2. D. the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y0", "the robot is in place loc-x1-y2", "the robot is in place loc-x2-y2", "the robot is in place loc-x0-y1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -8672099747881699697, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x1-y2, loc-x1-y0, loc-x2-y3, loc-x0-y3, loc-x0-y2, loc-x3-y3, loc-x0-y0, loc-x3-y2, and loc-x1-y1. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x1-y1. B. the robot is in place loc-x0-y2. C. the robot is in place loc-x2-y2. D. Place loc-x3-y0 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1", "the robot is in place loc-x0-y2", "the robot is in place loc-x2-y2", "Place loc-x3-y0 has been visited"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 7934525284400456754, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x3-y0, loc-x2-y3, loc-x0-y3, loc-x2-y0, loc-x3-y2, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x1-y1. B. the robot is in place loc-x0-y3. C. the robot is in place loc-x3-y0. D. the robot is in place loc-x2-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1", "the robot is in place loc-x0-y3", "the robot is in place loc-x3-y0", "the robot is in place loc-x2-y0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 3038884635460812906, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x0-y1, loc-x1-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y0. B. the robot is in place loc-x0-y1. C. Place loc-x3-y3 has been visited. D. the robot is in place loc-x1-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y0", "the robot is in place loc-x0-y1", "Place loc-x3-y3 has been visited", "the robot is in place loc-x1-y0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 3263163352828609249, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y3, loc-x2-y2, loc-x0-y3, loc-x0-y2, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y4 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x1-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x0-y4 has been visited, and Place loc-x1-y1 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x0-y3. B. the robot is in place loc-x1-y3. C. Place loc-x2-y0 has been visited. D. the robot is in place loc-x0-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y3", "the robot is in place loc-x1-y3", "Place loc-x2-y0 has been visited", "the robot is in place loc-x0-y2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 6577204245055594759, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x3-y0, loc-x2-y3, loc-x0-y3, loc-x2-y0, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y1 has been visited, and Place loc-x1-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x2-y0. B. the robot is in place loc-x2-y2. C. the robot is in place loc-x2-y1. D. the robot is in place loc-x0-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0", "the robot is in place loc-x2-y2", "the robot is in place loc-x2-y1", "the robot is in place loc-x0-y3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 6526866722911959407, "group": "landmarks_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y1, loc-x1-y3, loc-x2-y1, loc-x2-y2, loc-x1-y0, loc-x0-y3, loc-x0-y2, loc-x0-y0, loc-x1-y1, and loc-x1-y2. The goal is to reach a state where the following facts hold: Place loc-x0-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y0 has been visited, Place loc-x0-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y2 has been visited, and Place loc-x3-y2 has been visited.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. the robot is in place loc-x2-y1. B. the robot is in place loc-x1-y1. C. the robot is in place loc-x0-y3. D. Place loc-x2-y3 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y1", "the robot is in place loc-x1-y1", "the robot is in place loc-x0-y3", "Place loc-x2-y3 has been visited"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -2538260229138553878, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, pallet0, pallet2, crate0, pallet1, pallet5, and pallet4 are clear; hoist0, hoist2, hoist4, hoist5, and hoist3 are available; pallet4 is at distributor0, pallet1 is at depot1, truck0 is at depot3, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, hoist1 is at depot1, truck1 is at distributor1, hoist2 is at depot2, hoist3 is at depot3, crate0 is at depot3, and hoist4 is at distributor0; crate0 is on pallet3; crate1 is in truck0; hoist1 is lifting crate2. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot2. B. hoist1 is available. C. hoist2 is lifting crate2. D. hoist0 is lifting crate1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot2", "hoist1 is available", "hoist2 is lifting crate2", "hoist0 is lifting crate1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4064811377540810573, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 12 pallets, 10 depots, 12 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet9, pallet2, crate0, pallet7, pallet1, pallet11, pallet5, pallet10, pallet6, pallet4, and pallet3 are clear; hoist10, hoist0, hoist7, hoist8, hoist11, hoist2, hoist4, hoist5, hoist6, hoist1, and hoist3 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet7 is at depot7, pallet1 is at depot1, hoist0 is at depot0, crate0 is at depot8, pallet0 is at depot0, pallet8 is at depot8, hoist11 is at distributor1, pallet4 is at depot4, hoist8 is at depot8, pallet11 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist7 is at depot7, pallet5 is at depot5, hoist1 is at depot1, truck0 is at depot9, hoist5 is at depot5, hoist6 is at depot6, truck1 is at depot4, pallet9 is at depot9, hoist2 is at depot2, pallet6 is at depot6, hoist3 is at depot3, hoist4 is at depot4, and hoist10 is at distributor0; crate0 is on pallet8; hoist9 is lifting crate1. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. truck1 is at depot2. B. truck1 is at distributor0. C. crate0 is on pallet1. D. hoist8 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at depot2", "truck1 is at distributor0", "crate0 is on pallet1", "hoist8 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6318226318585049424, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 pallets, 3 depots, 5 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet1, crate1, pallet4, and pallet3 are clear; hoist0, hoist2, hoist4, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet4 is at distributor1, pallet2 is at depot2, crate1 is at depot2, truck0 is at depot0, hoist1 is at depot1, pallet3 is at distributor0, hoist4 is at distributor1, hoist2 is at depot2, truck1 is at depot0, and hoist3 is at distributor0; crate1 is on pallet2; hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is in truck1. B. truck1 is at depot1. C. crate0 is on pallet1. D. hoist3 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is in truck1", "truck1 is at depot1", "crate0 is on pallet1", "hoist3 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 2689151212590719852, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 pallets, 3 depots, 5 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet1, pallet4, and pallet3 are clear; hoist0, hoist4, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet4 is at distributor1, pallet2 is at depot2, hoist1 is at depot1, truck1 is at depot2, pallet3 is at distributor0, hoist4 is at distributor1, hoist2 is at depot2, hoist3 is at distributor0, and truck0 is at depot2; hoist2 is lifting crate1 and hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot0. B. truck0 is at depot0. C. hoist1 is lifting crate1. D. crate0 is on pallet1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot0", "truck0 is at depot0", "hoist1 is lifting crate1", "crate0 is on pallet1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 717344203269010308, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, crate2, pallet0, pallet2, pallet1, pallet5, and pallet3 are clear; hoist0, hoist2, hoist5, and hoist3 are available; pallet4 is at distributor0, pallet1 is at depot1, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, truck1 is at distributor0, truck0 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, hoist1 is at depot1, hoist2 is at depot2, crate2 is at distributor0, hoist3 is at depot3, and hoist4 is at distributor0; crate2 is on pallet4; hoist4 is lifting crate1 and hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is on crate2. B. crate1 is on pallet4. C. truck1 is at depot2. D. crate0 is on pallet3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is on crate2", "crate1 is on pallet4", "truck1 is at depot2", "crate0 is on pallet3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 2926004180338008153, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 12 pallets, 10 depots, 12 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet9, pallet2, crate0, pallet7, pallet1, pallet11, pallet5, pallet10, pallet6, pallet4, and pallet3 are clear; hoist9, hoist10, hoist0, hoist7, hoist8, hoist11, hoist2, hoist4, hoist5, hoist6, hoist1, and hoist3 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet7 is at depot7, pallet1 is at depot1, hoist0 is at depot0, crate0 is at depot8, pallet0 is at depot0, pallet8 is at depot8, hoist11 is at distributor1, pallet4 is at depot4, truck0 is at depot8, hoist8 is at depot8, pallet11 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist7 is at depot7, pallet5 is at depot5, hoist1 is at depot1, hoist5 is at depot5, hoist6 is at depot6, truck1 is at depot4, pallet9 is at depot9, hoist2 is at depot2, pallet6 is at depot6, hoist3 is at depot3, hoist4 is at depot4, and hoist10 is at distributor0; crate0 is on pallet8; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot7. B. crate1 is at distributor0. C. hoist11 is lifting crate1. D. hoist11 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot7", "crate1 is at distributor0", "hoist11 is lifting crate1", "hoist11 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1729045778126097445, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 9 pallets, 7 depots, 9 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet8, pallet7, pallet1, pallet5, pallet6, crate1, and pallet4 are clear; hoist0, hoist7, hoist8, hoist2, hoist4, hoist5, hoist6, hoist1, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet4 is at depot4, pallet7 is at distributor0, pallet8 is at distributor1, hoist8 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, crate1 is at depot3, pallet5 is at depot5, hoist1 is at depot1, truck0 is at distributor0, hoist5 is at depot5, hoist6 is at depot6, hoist2 is at depot2, pallet6 is at depot6, hoist3 is at depot3, hoist7 is at distributor0, truck1 is at depot0, and hoist4 is at depot4; crate1 is on pallet3; crate0 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot1. B. crate0 is on pallet2. C. crate0 is on pallet5. D. hoist8 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot1", "crate0 is on pallet2", "crate0 is on pallet5", "hoist8 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1046894280134860432, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, crate2, pallet0, pallet2, crate0, crate1, and pallet4 are clear; hoist0, hoist2, hoist4, hoist5, hoist1, and hoist3 are available; crate1 is at distributor1, pallet4 is at distributor0, pallet1 is at depot1, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, truck0 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, hoist1 is at depot1, truck1 is at depot2, hoist2 is at depot2, hoist3 is at depot3, crate2 is at depot1, crate0 is at depot3, and hoist4 is at distributor0; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate2 is in truck1. B. truck1 is at depot0. C. truck1 is at depot3. D. hoist1 is lifting crate2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate2 is in truck1", "truck1 is at depot0", "truck1 is at depot3", "hoist1 is lifting crate2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2189971100438029835, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 pallets, 2 depots, 4 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet2, crate0, crate1, and pallet3 are clear; hoist0, hoist2, hoist1, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet3 is at distributor1, pallet2 is at distributor0, crate0 is at depot1, hoist2 is at distributor0, truck0 is at distributor0, hoist1 is at depot1, crate1 is at depot0, truck1 is at depot1, and hoist3 is at distributor1; crate0 is on pallet1 and crate1 is on pallet0. The goal is to reach a state where the following facts hold: crate0 is on crate1 and crate1 is on pallet0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. truck0 is at depot0. B. truck0 is at depot1. C. crate0 is on crate1. D. crate1 is on crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck0 is at depot0", "truck0 is at depot1", "crate0 is on crate1", "crate1 is on crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -1730737824790609949, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, crate2, pallet0, pallet2, crate0, pallet5, and pallet3 are clear; hoist0, hoist2, hoist4, hoist5, hoist1, and hoist3 are available; pallet4 is at distributor0, pallet1 is at depot1, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, truck1 is at depot2, hoist2 is at depot2, crate2 is at distributor0, hoist3 is at depot3, crate0 is at depot1, and hoist4 is at distributor0; crate0 is on pallet1 and crate2 is on pallet4; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate2 is on crate0. B. crate1 is on crate2. C. crate1 is on pallet5. D. crate0 is on pallet0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate2 is on crate0", "crate1 is on crate2", "crate1 is on pallet5", "crate0 is on pallet0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 8560346137411232700, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-2f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f0-2f and f2-3f. The gold is at f1-3f location. The laser is at f1-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f2-3f location. B. The robot is at position f1-1f. C. The laser is at f2-0f location. D. The robot is at position f1-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f2-3f location", "The robot is at position f1-1f", "The laser is at f2-0f location", "The robot is at position f1-3f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 8254514148218295006, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have soft rock: f0-3f, f2-2f, f2-1f, f1-3f, f1-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot's arm is empty. B. The robot is at position f1-3f. C. The laser is at f2-2f location. D. The laser is at f2-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot's arm is empty", "The robot is at position f1-3f", "The laser is at f2-2f location", "The laser is at f2-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -2286980433751099581, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-2f and f2-1f. The gold is at f0-2f location. The laser is at f1-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f1-2f location. B. The robot is at position f0-2f. C. Soft rock at f0-2f. D. Location(s) f1-2f is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f1-2f location", "The robot is at position f0-2f", "Soft rock at f0-2f", "Location(s) f1-2f is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7141105574059734251, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a laser. The following locations have soft rock: f0-3f, f2-2f, f2-1f, f1-3f, f1-2f, and f2-3f. The gold is at f0-3f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Soft rock at f0-1f. B. The robot is at position f1-1f. C. Location(s) f0-3f is clear. D. The laser is at f2-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soft rock at f0-1f", "The robot is at position f1-1f", "Location(s) f0-3f is clear", "The laser is at f2-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -5277026456352773288, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-2f, f0-1f, f1-1f, and f0-2f. The gold is at f0-2f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f2-1f. B. Location(s) f2-2f is clear. C. The robot is at position f0-2f. D. The laser is at f1-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-1f", "Location(s) f2-2f is clear", "The robot is at position f0-2f", "The laser is at f1-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -1142956497938763211, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f2-1f, f2-4f, f0-4f, f2-2f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f1-4f. B. The robot is at position f1-0f. C. Location(s) f0-4f is clear. D. Location(s) f2-1f is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f1-4f", "The robot is at position f1-0f", "Location(s) f0-4f is clear", "Location(s) f2-1f is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -2771737250744636651, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f2-1f, f1-2f, f2-4f, f0-4f, f2-2f, and f2-3f. The gold is at f0-4f location. The laser is at f0-2f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f2-4f location. B. Location(s) f0-4f is clear. C. The laser is at f0-3f location. D. The laser is at f2-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f2-4f location", "Location(s) f0-4f is clear", "The laser is at f0-3f location", "The laser is at f2-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4634419972385712283, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f3-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-1f, f3-3f, f1-3f, and f1-2f. The following locations have soft rock: f2-1f, f3-2f, f0-3f, f2-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f1-3f. B. Location(s) f2-2f is clear. C. Location(s) f0-3f is clear. D. The robot is at position f2-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f1-3f", "Location(s) f2-2f is clear", "Location(s) f0-3f is clear", "The robot is at position f2-3f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 68184643332045963, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f2-2f, f2-1f, f2-4f, f2-3f, and f0-4f. The gold is at f0-4f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f2-3f. B. The robot is at position f2-0f. C. Location(s) f1-4f is clear. D. The robot is at position f0-4f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-3f", "The robot is at position f2-0f", "Location(s) f1-4f is clear", "The robot is at position f0-4f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8650716301187257740, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-1f, f3-3f, f1-3f, and f1-2f. The following locations have soft rock: f2-1f, f0-2f, f3-2f, f0-3f, f2-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f0-3f location. B. Location(s) f0-3f is clear. C. Location(s) f1-3f is clear. D. The robot is at position f2-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f0-3f location", "Location(s) f0-3f is clear", "Location(s) f1-3f is clear", "The robot is at position f2-1f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 2250439443254950490, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, groundstation2, phenomenon5, star3, star6, star1, groundstation0. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite5 has following instruments onboard: instrument12, instrument14, instrument13. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite6 has following instruments onboard: instrument16, instrument17, instrument15. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite4 has following instruments onboard: instrument10, instrument11, instrument9. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite2 is pointing to groundstation2. Power is available on the following satellite(s): satellite4, satellite3, satellite5, satellite6, satellite0. Following instruments are powered on: instrument4, instrument6. The goal is to reach a state where the following facts hold: A image0 mode image of target phenomenon5 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite4 is pointing to star1, A image1 mode image of target star6 is available, and Satellite satellite5 is pointing to groundstation0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. A image0 mode image of target phenomenon5 is available. B. Satellite satellite1 is pointing to star3. C. Satellite satellite2 is pointing to groundstation4. D. A image0 mode image of target groundstation0 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["A image0 mode image of target phenomenon5 is available", "Satellite satellite1 is pointing to star3", "Satellite satellite2 is pointing to groundstation4", "A image0 mode image of target groundstation0 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 6105003849156979941, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, star4, planet5, star2, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite6 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument7 supports image of mode image1 and its calibration target is star2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation3. Satellite satellite2 is pointing to planet6. Satellite satellite3 is pointing to groundstation1. Satellite satellite6 is pointing to planet6. Satellite satellite8 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite5, satellite1, satellite0, satellite4, satellite3, satellite8, satellite7, satellite2. Following instruments are powered on: instrument10. Following instruments are calibrated: instrument10. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet6 is available. The goal is to reach a state where the following facts hold: Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, A infrared2 mode image of target planet6 is available, and Satellite satellite6 is pointing to star4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite7 is pointing to star4. B. A image1 mode image of target groundstation0 is available. C. Satellite satellite6 is pointing to star4. D. A thermograph0 mode image of target star4 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite7 is pointing to star4", "A image1 mode image of target groundstation0 is available", "Satellite satellite6 is pointing to star4", "A thermograph0 mode image of target star4 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -3816485843654493285, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, groundstation2, phenomenon5, star3, star6, star1, groundstation0. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite5 has following instruments onboard: instrument12, instrument14, instrument13. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite6 has following instruments onboard: instrument16, instrument17, instrument15. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite4 has following instruments onboard: instrument10, instrument11, instrument9. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4.  Currently, Satellite satellite3 is pointing to groundstation4. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star6. Satellite satellite6 is pointing to star3. Satellite satellite5 is pointing to groundstation0. Power is available on the following satellite(s): satellite4, satellite3, satellite0, satellite1, satellite6. Following instruments are powered on: instrument14, instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. A image1 mode image of target star6 is available. The goal is to reach a state where the following facts hold: A image0 mode image of target phenomenon5 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite4 is pointing to star1, A image1 mode image of target star6 is available, and Satellite satellite5 is pointing to groundstation0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Following instruments are powered on: instrument1. B. Following instruments are calibrated: instrument2. C. Following instruments are calibrated: instrument13. D. Satellite satellite4 is pointing to star1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Following instruments are powered on: instrument1", "Following instruments are calibrated: instrument2", "Following instruments are calibrated: instrument13", "Satellite satellite4 is pointing to star1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1129428429997905057, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): planet7, groundstation1, star10, groundstation5, groundstation4, star9, planet8, star0, star2, groundstation6, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph2 and its calibration target is groundstation5. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to planet7. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite1 is pointing to star9. Satellite satellite3 is pointing to star9. Power is available on the following satellite(s): satellite4, satellite3, satellite5, satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet7 is available. The goal is to reach a state where the following facts hold: Satellite satellite5 is pointing to star2, A infrared1 mode image of target star9 is available, Satellite satellite4 is pointing to star10, Satellite satellite1 is pointing to star9, A infrared1 mode image of target planet8 is available, A thermograph2 mode image of target planet7 is available, and A infrared1 mode image of target star10 is available.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite2 is pointing to star0. B. Satellite satellite1 is pointing to star0. C. A infrared1 mode image of target planet8 is available. D. Satellite satellite5 is pointing to planet7.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite2 is pointing to star0", "Satellite satellite1 is pointing to star0", "A infrared1 mode image of target planet8 is available", "Satellite satellite5 is pointing to planet7"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -7538443036864725431, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): planet7, groundstation1, star10, groundstation5, groundstation4, star9, planet8, star0, star2, groundstation6, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph2 and its calibration target is groundstation5. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite4 is pointing to planet8. Satellite satellite1 is pointing to star10. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to planet8. Power is available on the following satellite(s): satellite4, satellite3, satellite5, satellite2, satellite0. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. The goal is to reach a state where the following facts hold: Satellite satellite5 is pointing to star2, A infrared1 mode image of target star9 is available, Satellite satellite4 is pointing to star10, Satellite satellite1 is pointing to star9, A infrared1 mode image of target planet8 is available, A thermograph2 mode image of target planet7 is available, and A infrared1 mode image of target star10 is available.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite2 is pointing to star2. B. Satellite satellite2 is pointing to groundstation5. C. A infrared1 mode image of target groundstation5 is available. D. Satellite satellite1 is pointing to star9.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite2 is pointing to star2", "Satellite satellite2 is pointing to groundstation5", "A infrared1 mode image of target groundstation5 is available", "Satellite satellite1 is pointing to star9"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2269589358458987078, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, star4, planet5, star2, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite6 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument7 supports image of mode image1 and its calibration target is star2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation3. Satellite satellite2 is pointing to planet6. Satellite satellite3 is pointing to groundstation1. Satellite satellite8 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite7 is pointing to star2. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite6, satellite5, satellite1, satellite0, satellite4, satellite3, satellite8, satellite7, satellite2. The goal is to reach a state where the following facts hold: Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, A infrared2 mode image of target planet6 is available, and Satellite satellite6 is pointing to star4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite8 is pointing to planet6. B. Satellite satellite8 is pointing to star4. C. Following instruments are powered on: instrument13. D. Following instruments are calibrated: instrument3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite8 is pointing to planet6", "Satellite satellite8 is pointing to star4", "Following instruments are powered on: instrument13", "Following instruments are calibrated: instrument3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 6150788479937358022, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, star4, planet5, star2, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode image1 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to planet5. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet5 is available. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite0 is pointing to planet6. B. A infrared2 mode image of target star4 is available. C. A thermograph0 mode image of target groundstation3 is available. D. A thermograph0 mode image of target star4 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite0 is pointing to planet6", "A infrared2 mode image of target star4 is available", "A thermograph0 mode image of target groundstation3 is available", "A thermograph0 mode image of target star4 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 2426928770476244268, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation4, star0, phenomenon6, planet5, star2, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation3. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. The goal is to reach a state where the following facts hold: A thermograph2 mode image of target planet5 is available, A spectrograph0 mode image of target phenomenon6 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. A spectrograph0 mode image of target groundstation3 is available. B. Following instruments are calibrated: instrument0. C. Satellite satellite0 is pointing to star0. D. Satellite satellite1 is pointing to phenomenon6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["A spectrograph0 mode image of target groundstation3 is available", "Following instruments are calibrated: instrument0", "Satellite satellite0 is pointing to star0", "Satellite satellite1 is pointing to phenomenon6"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 5638491118465662235, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation4, star0, phenomenon6, planet5, star2, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite1 is pointing to star2. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument4, instrument0. Following instruments are calibrated: instrument4. The goal is to reach a state where the following facts hold: A thermograph2 mode image of target planet5 is available, A spectrograph0 mode image of target phenomenon6 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. A spectrograph0 mode image of target groundstation4 is available. B. A spectrograph0 mode image of target phenomenon6 is available. C. Satellite satellite0 is pointing to star0. D. A spectrograph0 mode image of target star0 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["A spectrograph0 mode image of target groundstation4 is available", "A spectrograph0 mode image of target phenomenon6 is available", "Satellite satellite0 is pointing to star0", "A spectrograph0 mode image of target star0 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -4450422086382299668, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation4, star0, phenomenon6, planet5, star2, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Satellite satellite1 is pointing to phenomenon6. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. The goal is to reach a state where the following facts hold: A thermograph2 mode image of target planet5 is available, A spectrograph0 mode image of target phenomenon6 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite1 is pointing to planet5. B. A thermograph2 mode image of target planet5 is available. C. Satellite satellite2 is pointing to groundstation1. D. A spectrograph0 mode image of target star0 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite1 is pointing to planet5", "A thermograph2 mode image of target planet5 is available", "Satellite satellite2 is pointing to groundstation1", "A spectrograph0 mode image of target star0 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 481197292643812895, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, kevin, bob, xena, alice, ted, and dave. There are 7 items/roles: valerian, quince, leek, mushroom, ulluco, parsnip, and yam. Currently, heidi is assigned valerian, ted is assigned mushroom, alice is assigned yam, bob is assigned leek, xena is assigned parsnip, kevin is assigned quince, and dave is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, ted is assigned leek, bob is assigned parsnip, alice is assigned valerian, xena is assigned quince, and dave is assigned ulluco.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. ted is assigned quince. B. dave is assigned quince. C. heidi is assigned parsnip. D. ted is assigned leek.", "choices": {"label": ["A", "B", "C", "D"], "text": ["ted is assigned quince", "dave is assigned quince", "heidi is assigned parsnip", "ted is assigned leek"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 3340046248284691489, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, vic, bob, xena, quentin, and liam. There are 6 items/roles: sander, nibbler, wrench, ratchet, knead, and pliers. Currently, xena is assigned pliers, bob is assigned ratchet, liam is assigned wrench, quentin is assigned sander, vic is assigned knead, and frank is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, liam is assigned sander, quentin is assigned knead, bob is assigned nibbler, xena is assigned wrench, and vic is assigned ratchet.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. bob is assigned nibbler. B. frank is assigned knead. C. xena is assigned ratchet. D. bob is assigned wrench.", "choices": {"label": ["A", "B", "C", "D"], "text": ["bob is assigned nibbler", "frank is assigned knead", "xena is assigned ratchet", "bob is assigned wrench"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 105499911598814093, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, xena is assigned whale, heidi is assigned necklace, zoe is assigned quadcopter, carol is assigned guitar, michelle is assigned slinky, alice is assigned iceskates, dave is assigned zebra, and vic is assigned frisbee. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. vic is assigned necklace. B. dave is assigned necklace. C. vic is assigned zebra. D. michelle is assigned whale.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned necklace", "dave is assigned necklace", "vic is assigned zebra", "michelle is assigned whale"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -1293079638875164679, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, vic, bob, xena, quentin, and liam. There are 6 items/roles: sander, nibbler, wrench, ratchet, knead, and pliers. Currently, bob is assigned sander, liam is assigned ratchet, quentin is assigned knead, vic is assigned pliers, xena is assigned wrench, and frank is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, liam is assigned sander, quentin is assigned knead, bob is assigned nibbler, xena is assigned wrench, and vic is assigned ratchet.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. bob is assigned wrench. B. quentin is assigned pliers. C. vic is assigned wrench. D. vic is assigned ratchet.", "choices": {"label": ["A", "B", "C", "D"], "text": ["bob is assigned wrench", "quentin is assigned pliers", "vic is assigned wrench", "vic is assigned ratchet"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5863871147475670648, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, dave is assigned iceskates, alice is assigned necklace, zoe is assigned frisbee, xena is assigned slinky, vic is assigned whale, heidi is assigned guitar, michelle is assigned zebra, and carol is assigned quadcopter. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. alice is assigned frisbee. B. alice is assigned zebra. C. vic is assigned guitar. D. michelle is assigned guitar.", "choices": {"label": ["A", "B", "C", "D"], "text": ["alice is assigned frisbee", "alice is assigned zebra", "vic is assigned guitar", "michelle is assigned guitar"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4940705949281648741, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, vic, bob, xena, quentin, and liam. There are 6 items/roles: sander, nibbler, wrench, ratchet, knead, and pliers. Currently, quentin is assigned pliers, frank is assigned wrench, bob is assigned sander, liam is assigned nibbler, xena is assigned ratchet, and vic is assigned knead. The goal is to reach a state where the following facts hold: frank is assigned pliers, liam is assigned sander, quentin is assigned knead, bob is assigned nibbler, xena is assigned wrench, and vic is assigned ratchet.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. quentin is assigned knead. B. frank is assigned knead. C. vic is assigned sander. D. vic is assigned nibbler.", "choices": {"label": ["A", "B", "C", "D"], "text": ["quentin is assigned knead", "frank is assigned knead", "vic is assigned sander", "vic is assigned nibbler"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 4253607171926211489, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, kevin, bob, xena, alice, ted, and dave. There are 7 items/roles: valerian, quince, leek, mushroom, ulluco, parsnip, and yam. Currently, dave is assigned parsnip, heidi is assigned quince, alice is assigned yam, bob is assigned mushroom, kevin is assigned leek, xena is assigned valerian, and ted is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, ted is assigned leek, bob is assigned parsnip, alice is assigned valerian, xena is assigned quince, and dave is assigned ulluco.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. xena is assigned yam. B. kevin is assigned yam. C. kevin is assigned ulluco. D. dave is assigned leek.", "choices": {"label": ["A", "B", "C", "D"], "text": ["xena is assigned yam", "kevin is assigned yam", "kevin is assigned ulluco", "dave is assigned leek"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 3006376119510676894, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, dave is assigned iceskates, carol is assigned frisbee, alice is assigned necklace, zoe is assigned whale, heidi is assigned guitar, michelle is assigned zebra, xena is assigned slinky, and vic is assigned quadcopter. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. vic is assigned slinky. B. alice is assigned zebra. C. heidi is assigned necklace. D. dave is assigned frisbee.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned slinky", "alice is assigned zebra", "heidi is assigned necklace", "dave is assigned frisbee"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -3140714423911560623, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, xena is assigned whale, heidi is assigned quadcopter, carol is assigned necklace, zoe is assigned guitar, alice is assigned iceskates, michelle is assigned zebra, vic is assigned frisbee, and dave is assigned slinky. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. xena is assigned slinky. B. dave is assigned necklace. C. xena is assigned zebra. D. vic is assigned slinky.", "choices": {"label": ["A", "B", "C", "D"], "text": ["xena is assigned slinky", "dave is assigned necklace", "xena is assigned zebra", "vic is assigned slinky"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -440863436631593540, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, xena is assigned whale, alice is assigned quadcopter, zoe is assigned frisbee, carol is assigned slinky, heidi is assigned iceskates, michelle is assigned necklace, vic is assigned guitar, and dave is assigned zebra. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. vic is assigned slinky. B. carol is assigned guitar. C. heidi is assigned slinky. D. carol is assigned frisbee.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned slinky", "carol is assigned guitar", "heidi is assigned slinky", "carol is assigned frisbee"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5220604179093862793, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder2 is at location17. sinkbasin2 is at location6. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. handtowelholder1 is at location18. cabinet2 is at location11. cabinet4 is at location15. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. garbagecan1 is at location2.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. toiletpaper1 and soapbar1 are at location7. towel1 and showerglass1 are at location5. soapbottle2 and spraybottle1 are at location4. lightswitch1 is at location13. cloth1 and candle1 are at location3. cloth2 is at location11. showerdoor1 is at location1. mirror1 is at location9. handtowel1 is at location18. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. sink1 is at location12. handtowel2 is at location17. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location17. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. toiletpaper2 is in cabinet3. spraybottle1 and soapbottle2 are in cabinet1. soapbar1 and toiletpaper1 are in toilet1. towel1 is on towelholder1. spraybottle2 and soapbottle1 are in cabinet4. cloth1 and candle1 are on countertop1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. cabinet4, cabinet2, cabinet3, and cabinet1 are closed. Nothing has been validated. agent1 is holding object soapbar2. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. toiletpaper1 is at location11. B. soapbar2 is at location7. C. agent1 is holding object toiletpaper2. D. It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["toiletpaper1 is at location11", "soapbar2 is at location7", "agent1 is holding object toiletpaper2", "It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2463252122767603041, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. vase1 and dishsponge2 are at location13. spatula3, glassbottle2, spatula2, potato2, and sink1 are at location6. soapbottle1 is at location4. apple1, lettuce2, cellphone2, peppershaker1, knife1, cellphone1, plate1, and lettuce1 are at location2. stoveknob3 and stoveknob2 are at location12. glassbottle1, butterknife1, spoon2, bread1, cellphone3, houseplant1, plate3, creditcard1, knife2, spatula1, and papertowelroll1 are at location3. saltshaker1 is at location15. cup1 is at location24. plate2, mug1, bowl1, tomato1, egg2, egg1, and potato1 are at location10. peppershaker2 is at location20. window2 is at location28. pan1 and spoon1 are at location11. bowl2 is at location29. stoveknob4 is at location7. apple2, potato3, and glassbottle3 are at location8. peppershaker3 is at location16. dishsponge1 and fork1 are at location27. window1 is at location30. chair2 is at location1. chair1 is at location23. soapbottle2 and statue1 are at location26. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location29. The objects are in/on receptacle as follows. statue1 and soapbottle2 are on shelf3. vase1 and dishsponge2 are in cabinet5. pot1 is on stoveburner3. spatula2, glassbottle2, spatula3, and potato2 are in sinkbasin1. dishsponge1 and fork1 are in drawer3. cup1 is in microwave1. lettuce2, plate1, knife1, cellphone1, cellphone2, lettuce1, apple1, and peppershaker1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. saltshaker1 is in drawer2. mug1, potato1, egg1, tomato1, plate2, egg2, and bowl1 are in fridge1. spoon2, spatula1, knife2, glassbottle1, creditcard1, plate3, bread1, houseplant1, papertowelroll1, cellphone3, and butterknife1 are on countertop3. peppershaker2 is in drawer1. bowl2 is on shelf2. soapbottle1 is in cabinet6. pan1 is on stoveburner4. pot1 is on stoveburner1. peppershaker3 is in cabinet3. vase2 is on shelf1. spoon1 and pan1 are on countertop2. pan1 is on stoveburner2. cellphone2 is on plate1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. potato1 is cool. B. It has been validated that an object of type spatulatype is in a receptacle of type drawertype. C. It has been validated that an object of type mugtype is hot and is in a receptacle of type shelftype. D. agent agent1 is at location location11.", "choices": {"label": ["A", "B", "C", "D"], "text": ["potato1 is cool", "It has been validated that an object of type spatulatype is in a receptacle of type drawertype", "It has been validated that an object of type mugtype is hot and is in a receptacle of type shelftype", "agent agent1 is at location location11"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 8072341704661862746, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. glassbottle1 is at location19. glassbottle3, butterknife1, soapbottle1, houseplant1, saltshaker2, lettuce3, knife1, spoon3, tomato1, and fork2 are at location3. lettuce1, cup2, cup1, potato2, apple1, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. spoon2, fork1, spatula2, and sink1 are at location6. cellphone3, bread1, mug1, creditcard2, statue1, lettuce2, spoon1, and creditcard1 are at location2. saltshaker1 is at location16. vase2 and papertowelroll1 are at location29. cellphone1, pan1, and peppershaker1 are at location11. saltshaker3 is at location26. peppershaker2 and cellphone2 are at location20. plate1 is at location4. window2 is at location28. bowl1 and glassbottle2 are at location14. stoveknob4 is at location7. apple2 and egg2 are at location8. window1 is at location30. chair2 is at location1. chair1 is at location23. dishsponge1 is at location15. vase1 is at location17. spatula1 is at location27. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location8. The objects are in/on receptacle as follows. soapbottle1, knife1, tomato1, fork2, lettuce3, saltshaker2, spoon3, houseplant1, butterknife1, and glassbottle3 are on countertop3. pot1 is on stoveburner3. spatula2, fork1, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. dishsponge1 is in drawer2. apple1, potato1, potato2, lettuce1, cup2, and cup1 are in fridge1. lettuce2, creditcard2, spoon1, creditcard1, cellphone3, statue1, bread1, and mug1 are on countertop1. vase2 and papertowelroll1 are on shelf2. saltshaker3 is on shelf3. plate1 is in cabinet6. glassbottle2 and bowl1 are in cabinet4. apple2 and egg2 are in garbagecan1. spatula1 is in drawer3. peppershaker2 and cellphone2 are in drawer1. vase1 is on shelf1. peppershaker1, cellphone1, and pan1 are on countertop2. pan1 is on stoveburner4. pot1 is on stoveburner1. glassbottle1 is in cabinet1. pan1 is on stoveburner2. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. egg1 is hot. Nothing has been validated. agent1 is holding object egg1. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. plate1 is on countertop1. B. It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype. C. saltshaker3 is at location15. D. It has been validated that an object of type pottype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["plate1 is on countertop1", "It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype", "saltshaker3 is at location15", "It has been validated that an object of type pottype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7474463588091933846, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder2 is at location17. sinkbasin2 is at location6. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. handtowelholder1 is at location18. cabinet2 is at location11. cabinet4 is at location15. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. garbagecan1 is at location2.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. toiletpaper1 and soapbar1 are at location7. towel1 and showerglass1 are at location5. soapbottle2 and spraybottle1 are at location4. lightswitch1 is at location13. cloth1, soapbar2, and candle1 are at location3. cloth2 is at location11. showerdoor1 is at location1. mirror1 is at location9. handtowel1 is at location18. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. sink1 is at location12. handtowel2 is at location17. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location10. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. toiletpaper2 is in cabinet3. spraybottle1 and soapbottle2 are in cabinet1. soapbar1 and toiletpaper1 are in toilet1. towel1 is on towelholder1. spraybottle2 and soapbottle1 are in cabinet4. cloth1, candle1, and soapbar2 are on countertop1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. cabinet4, cabinet2, cabinet3, and cabinet1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. spraybottle1 is at location2. B. cloth2 is at location15. C. It has been validated that two objects of type toiletpapertype are in a receptacle of type countertoptype. D. It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["spraybottle1 is at location2", "cloth2 is at location15", "It has been validated that two objects of type toiletpapertype are in a receptacle of type countertoptype", "It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 3673285689265613903, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. vase1 and dishsponge2 are at location13. spatula3, glassbottle2, spatula2, potato2, and sink1 are at location6. soapbottle1 is at location4. apple1, lettuce2, cellphone2, peppershaker1, knife1, cellphone1, plate1, and lettuce1 are at location2. stoveknob3 and stoveknob2 are at location12. glassbottle1, butterknife1, spoon2, bread1, cellphone3, houseplant1, plate3, creditcard1, knife2, spatula1, and papertowelroll1 are at location3. saltshaker1 is at location15. cup1 is at location24. plate2, mug1, bowl1, tomato1, egg2, egg1, and potato1 are at location10. peppershaker2 is at location20. window2 is at location28. pan1 and spoon1 are at location11. bowl2 is at location29. stoveknob4 is at location7. apple2, potato3, and glassbottle3 are at location8. peppershaker3 is at location16. dishsponge1 and fork1 are at location27. window1 is at location30. chair2 is at location1. chair1 is at location23. soapbottle2 and statue1 are at location26. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location3. The objects are in/on receptacle as follows. statue1 and soapbottle2 are on shelf3. vase1 and dishsponge2 are in cabinet5. pot1 is on stoveburner3. spatula2, glassbottle2, spatula3, and potato2 are in sinkbasin1. dishsponge1 and fork1 are in drawer3. cup1 is in microwave1. lettuce2, plate1, knife1, cellphone1, cellphone2, lettuce1, apple1, and peppershaker1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. saltshaker1 is in drawer2. mug1, potato1, egg1, tomato1, plate2, egg2, and bowl1 are in fridge1. spoon2, spatula1, knife2, glassbottle1, creditcard1, plate3, bread1, houseplant1, papertowelroll1, cellphone3, and butterknife1 are on countertop3. peppershaker2 is in drawer1. bowl2 is on shelf2. soapbottle1 is in cabinet6. pan1 is on stoveburner4. pot1 is on stoveburner1. peppershaker3 is in cabinet3. vase2 is on shelf1. spoon1 and pan1 are on countertop2. pan1 is on stoveburner2. cellphone2 is on plate1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. agent agent1 is at location location11. B. saltshaker1 is at location26. C. plate2 is at location2. D. butterknife1 is on countertop1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 is at location location11", "saltshaker1 is at location26", "plate2 is at location2", "butterknife1 is on countertop1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -9114955175993077227, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder2 is at location17. sinkbasin2 is at location6. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. handtowelholder1 is at location18. cabinet2 is at location11. cabinet4 is at location15. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. garbagecan1 is at location2.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. toiletpaper1 and soapbar1 are at location7. towel1 and showerglass1 are at location5. soapbottle2 and spraybottle1 are at location4. lightswitch1 is at location13. cloth1 and candle1 are at location3. cloth2 and soapbar2 are at location11. showerdoor1 is at location1. mirror1 is at location9. handtowel1 is at location18. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. sink1 is at location12. handtowel2 is at location17. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location11. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. toiletpaper2 is in cabinet3. spraybottle1 and soapbottle2 are in cabinet1. soapbar1 and toiletpaper1 are in toilet1. towel1 is on towelholder1. spraybottle2 and soapbottle1 are in cabinet4. cloth1 and candle1 are on countertop1. cloth2 and soapbar2 are in cabinet2. handtowel1 is on handtowelholder1. cabinet4, cabinet3, and cabinet1 are closed. soapbar2 is clean. cabinet2 is checked. cabinet2 is open. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. soapbottle2 is in cabinet3. B. toiletpaper1 is in garbagecan1. C. handtowel1 is at location6. D. It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["soapbottle2 is in cabinet3", "toiletpaper1 is in garbagecan1", "handtowel1 is at location6", "It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6347893514412102976, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. drawer1 is at location21. stoveburner4 and stoveburner2 are at location22. stoveburner3 and stoveburner1 are at location6. coffeemachine1 and countertop2 are at location12. countertop3 is at location4. drawer3 is at location28. cabinet3 is at location17. microwave1 is at location25. shelf1 is at location18. toaster1 is at location10. sinkbasin1 is at location7. shelf3 is at location27. cabinet5 is at location14. fridge1 is at location11. cabinet2 is at location23. countertop1 is at location3. cabinet4 is at location15. shelf2 is at location30. garbagecan1 is at location9. cabinet6 is at location5. drawer2 is at location16. cabinet1 is at location20.  Currently, the objects are at locations as follows. stoveknob3 and stoveknob2 are at location13. papertowelroll1, soapbottle2, and glassbottle1 are at location9. statue1, bread1, apple2, apple1, apple3, creditcard1, cellphone1, dishsponge3, and spatula2 are at location3. butterknife1, peppershaker2, fork2, butterknife2, houseplant1, cellphone3, spoon2, statue2, spatula3, soapbottle3, and knife1 are at location4. peppershaker1 is at location20. tomato2, potato2, egg1, cup1, cup2, potato1, tomato1, and lettuce1 are at location11. stoveknob1 is at location19. creditcard3 and saltshaker1 are at location27. cup3, potato3, egg2, sink1, tomato3, and fork1 are at location7. plate1 and plate2 are at location14. window1 is at location31. soapbottle1 and vase1 are at location5. lightswitch1 is at location26. cellphone2 is at location21. mug2, bowl1, glassbottle2, and creditcard2 are at location30. spatula1 is at location28. chair1 is at location24. window2 is at location29. chair2 is at location1. dishsponge1 and spoon1 are at location16. mug1 is at location25. vase2 is at location18. pot1 is at location22. dishsponge2 is at location2. pan1 is at location6. stoveknob4 is at location8. agent agent1 is at location location4. The objects are in/on receptacle as follows. plate2, dishsponge2, and plate1 are in cabinet5. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. knife1, fork2, spoon2, soapbottle3, butterknife2, statue2, houseplant1, cellphone3, peppershaker2, butterknife1, and spatula3 are on countertop3. pan1 is on stoveburner3. dishsponge1 and spoon1 are in drawer2. peppershaker1 is in cabinet1. spatula2, dishsponge3, cellphone1, creditcard1, apple2, statue1, bread1, apple1, and apple3 are on countertop1. mug1 is in microwave1. potato1, egg1, tomato1, potato2, tomato2, lettuce1, cup2, and cup1 are in fridge1. fork1, cup3, tomato3, egg2, and potato3 are in sinkbasin1. pot1 is on stoveburner2. spatula1 is in drawer3. pan1 is on stoveburner1. creditcard3 and saltshaker1 are on shelf3. soapbottle1 and vase1 are in cabinet6. pot1 is on stoveburner4. cellphone2 is in drawer1. dishsponge2 is on plate1. vase2 is on shelf1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype. B. creditcard2 is on countertop1. C. peppershaker2 is at location18. D. peppershaker1 is at location17.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype", "creditcard2 is on countertop1", "peppershaker2 is at location18", "peppershaker1 is at location17"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -3904675772082885588, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. drawer1 is at location21. stoveburner4 and stoveburner2 are at location22. stoveburner3 and stoveburner1 are at location6. coffeemachine1 and countertop2 are at location12. countertop3 is at location4. drawer3 is at location28. cabinet3 is at location17. microwave1 is at location25. shelf1 is at location18. toaster1 is at location10. sinkbasin1 is at location7. shelf3 is at location27. cabinet5 is at location14. fridge1 is at location11. cabinet2 is at location23. countertop1 is at location3. cabinet4 is at location15. shelf2 is at location30. garbagecan1 is at location9. cabinet6 is at location5. drawer2 is at location16. cabinet1 is at location20.  Currently, the objects are at locations as follows. stoveknob3 and stoveknob2 are at location13. papertowelroll1, soapbottle2, and glassbottle1 are at location9. statue1, bread1, apple2, apple1, apple3, creditcard1, cellphone1, dishsponge3, and spatula2 are at location3. butterknife1, peppershaker2, fork2, butterknife2, houseplant1, cellphone3, spoon2, statue2, spatula3, soapbottle3, and knife1 are at location4. peppershaker1 is at location20. tomato2, potato2, egg1, cup1, cup2, potato1, tomato1, and lettuce1 are at location11. stoveknob1 is at location19. saltshaker1 is at location15. creditcard3 is at location27. cup3, potato3, egg2, sink1, tomato3, and fork1 are at location7. plate1 and plate2 are at location14. window1 is at location31. soapbottle1 and vase1 are at location5. lightswitch1 is at location26. cellphone2 is at location21. mug2, bowl1, glassbottle2, and creditcard2 are at location30. spatula1 is at location28. chair1 is at location24. window2 is at location29. chair2 is at location1. dishsponge1 and spoon1 are at location16. mug1 is at location25. vase2 is at location18. pot1 is at location22. dishsponge2 is at location2. pan1 is at location6. stoveknob4 is at location8. agent agent1 is at location location9. The objects are in/on receptacle as follows. plate2, dishsponge2, and plate1 are in cabinet5. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. knife1, fork2, spoon2, soapbottle3, butterknife2, statue2, houseplant1, cellphone3, peppershaker2, butterknife1, and spatula3 are on countertop3. pan1 is on stoveburner3. dishsponge1 and spoon1 are in drawer2. peppershaker1 is in cabinet1. spatula2, dishsponge3, cellphone1, creditcard1, apple2, statue1, bread1, apple1, and apple3 are on countertop1. mug1 is in microwave1. potato1, egg1, tomato1, potato2, tomato2, lettuce1, cup2, and cup1 are in fridge1. fork1, cup3, tomato3, egg2, and potato3 are in sinkbasin1. pot1 is on stoveburner2. spatula1 is in drawer3. pan1 is on stoveburner1. creditcard3 is on shelf3. soapbottle1 and vase1 are in cabinet6. pot1 is on stoveburner4. cellphone2 is in drawer1. dishsponge2 is on plate1. vase2 is on shelf1. saltshaker1 is in cabinet4. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. It has been validated that two objects of type tomatotype are in a receptacle of type sinkbasintype. B. It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype. C. saltshaker1 is at location18. D. fork2 is in sinkbasin1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that two objects of type tomatotype are in a receptacle of type sinkbasintype", "It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype", "saltshaker1 is at location18", "fork2 is in sinkbasin1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": *******************, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. glassbottle1 is at location19. glassbottle3, butterknife1, soapbottle1, houseplant1, saltshaker2, lettuce3, knife1, spoon3, tomato1, and fork2 are at location3. lettuce1, cup2, cup1, potato2, apple1, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. spoon2, fork1, spatula2, and sink1 are at location6. cellphone3, bread1, mug1, creditcard2, statue1, lettuce2, spoon1, and creditcard1 are at location2. saltshaker1 is at location16. vase2 and papertowelroll1 are at location29. cellphone1, pan1, and peppershaker1 are at location11. saltshaker3 is at location26. peppershaker2 and cellphone2 are at location20. plate1 is at location4. window2 is at location28. bowl1 and glassbottle2 are at location14. stoveknob4 is at location7. apple2 and egg1 are at location8. window1 is at location30. chair2 is at location1. chair1 is at location23. dishsponge1 is at location15. vase1 is at location17. spatula1 is at location27. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location8. The objects are in/on receptacle as follows. soapbottle1, knife1, tomato1, fork2, lettuce3, saltshaker2, spoon3, houseplant1, butterknife1, and glassbottle3 are on countertop3. pot1 is on stoveburner3. spatula2, fork1, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. dishsponge1 is in drawer2. apple1, potato1, potato2, lettuce1, cup2, and cup1 are in fridge1. lettuce2, creditcard2, spoon1, creditcard1, cellphone3, statue1, bread1, and mug1 are on countertop1. vase2 and papertowelroll1 are on shelf2. saltshaker3 is on shelf3. plate1 is in cabinet6. glassbottle2 and bowl1 are in cabinet4. apple2 and egg1 are in garbagecan1. spatula1 is in drawer3. peppershaker2 and cellphone2 are in drawer1. vase1 is on shelf1. peppershaker1, cellphone1, and pan1 are on countertop2. pan1 is on stoveburner4. pot1 is on stoveburner1. glassbottle1 is in cabinet1. pan1 is on stoveburner2. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. egg2 is hot. Nothing has been validated. agent1 is holding object egg2. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. It has been validated that an object of type potatotype is in a receptacle of type garbagecantype. B. plate1 is at location22. C. It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype. D. agent1 is holding object spoon1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type potatotype is in a receptacle of type garbagecantype", "plate1 is at location22", "It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype", "agent1 is holding object spoon1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 7539660820340071809, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. vase1 and dishsponge2 are at location13. spatula3, glassbottle2, spatula2, potato2, and sink1 are at location6. soapbottle1 is at location4. apple1, lettuce2, cellphone2, peppershaker1, knife1, cellphone1, plate1, and lettuce1 are at location2. stoveknob3 and stoveknob2 are at location12. glassbottle1, butterknife1, spoon2, bread1, cellphone3, houseplant1, plate3, creditcard1, knife2, spatula1, and papertowelroll1 are at location3. saltshaker1 is at location15. cup1 is at location24. plate2, mug1, bowl1, tomato1, egg2, egg1, and potato1 are at location10. peppershaker2 is at location20. window2 is at location28. pan1 and spoon1 are at location11. bowl2 is at location29. stoveknob4 is at location7. apple2, glassbottle3, and potato3 are at location8. peppershaker3 is at location16. dishsponge1 and fork1 are at location27. window1 is at location30. chair2 is at location1. chair1 is at location23. soapbottle2 and statue1 are at location26. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location16. The objects are in/on receptacle as follows. statue1 and soapbottle2 are on shelf3. vase1 and dishsponge2 are in cabinet5. pot1 is on stoveburner3. spatula2, glassbottle2, spatula3, and potato2 are in sinkbasin1. dishsponge1 and fork1 are in drawer3. cup1 is in microwave1. lettuce2, plate1, knife1, cellphone1, cellphone2, lettuce1, apple1, and peppershaker1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. saltshaker1 is in drawer2. mug1, potato1, egg1, tomato1, plate2, egg2, and bowl1 are in fridge1. spoon2, spatula1, knife2, glassbottle1, creditcard1, plate3, bread1, houseplant1, papertowelroll1, cellphone3, and butterknife1 are on countertop3. peppershaker2 is in drawer1. bowl2 is on shelf2. soapbottle1 is in cabinet6. pan1 is on stoveburner4. pot1 is on stoveburner1. peppershaker3 is in cabinet3. vase2 is on shelf1. spoon1 and pan1 are on countertop2. pan1 is on stoveburner2. cellphone2 is on plate1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. spatula3 is at location11. B. agent agent1 is at location location11. C. It has been validated that an object of type dishspongetype is in a receptacle of type drawertype. D. It has been validated that an object of type tomatotype is hot and is in a receptacle of type countertoptype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["spatula3 is at location11", "agent agent1 is at location location11", "It has been validated that an object of type dishspongetype is in a receptacle of type drawertype", "It has been validated that an object of type tomatotype is hot and is in a receptacle of type countertoptype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
