{"id": 146672430259489784, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet1 is at location20. stoveburner2 and stoveburner4 are at location22. sinkbasin1 is at location7. coffeemachine1 and countertop2 are at location12. drawer2 is at location16. toaster1 is at location10. shelf1 is at location18. microwave1 is at location25. cabinet3 is at location17. cabinet4 is at location15. shelf2 is at location30. countertop3 is at location4. stoveburner3 and stoveburner1 are at location6. drawer1 is at location21. cabinet6 is at location5. cabinet2 is at location23. fridge1 is at location11. cabinet5 is at location14. shelf3 is at location27. countertop1 is at location3. drawer3 is at location28. garbagecan1 is at location9.  Currently, the objects are at locations as follows. papertowelroll1, soapbottle2, and glassbottle1 are at location9. pot1 is at location22. fork2, soapbottle3, statue2, butterknife1, spoon2, cellphone3, knife1, spatula3, butterknife2, peppershaker2, and houseplant1 are at location4. lettuce1, tomato1, tomato2, potato1, potato2, egg1, cup2, and cup1 are at location11. spatula1 is at location28. creditcard2, glassbottle2, bowl1, and mug2 are at location30. cellphone1, apple3, dishsponge3, apple2, bread1, statue1, apple1, creditcard1, and spatula2 are at location3. fork1, egg2, cup3, sink1, tomato3, and potato3 are at location7. cellphone2 is at location21. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. vase2 is at location18. creditcard3 and saltshaker1 are at location27. soapbottle1 and vase1 are at location5. stoveknob1 is at location19. window1 is at location31. chair1 is at location24. chair2 is at location1. peppershaker1 is at location20. stoveknob3 and stoveknob2 are at location13. pan1 is at location6. plate2 and plate1 are at location14. stoveknob4 is at location8. mug1 is at location25. dishsponge2 is at location2. agent agent1 is at location location32. The objects are in/on receptacle as follows. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. fork1, egg2, cup3, tomato3, and potato3 are in sinkbasin1. plate1, dishsponge2, and plate2 are in cabinet5. statue1, bread1, spatula2, dishsponge3, cellphone1, apple2, apple3, apple1, and creditcard1 are on countertop1. knife1, peppershaker2, butterknife2, soapbottle3, spatula3, cellphone3, statue2, houseplant1, spoon2, butterknife1, and fork2 are on countertop3. creditcard3 and saltshaker1 are on shelf3. vase2 is on shelf1. peppershaker1 is in cabinet1. spoon1 and dishsponge1 are in drawer2. soapbottle2, papertowelroll1, and glassbottle1 are in garbagecan1. cup1, egg1, cup2, potato2, lettuce1, tomato2, potato1, and tomato1 are in fridge1. dishsponge2 is on plate1. cellphone2 is in drawer1. pan1 is on stoveburner1. spatula1 is in drawer3. pan1 is on stoveburner3. soapbottle1 and vase1 are in cabinet6. mug1 is in microwave1. pot1 is on stoveburner2. pot1 is on stoveburner4. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 moves from the current position location32 to the next position location21 that has the receptacle drawer1, agent agent1 moves from the current position location21 to the next position location27 that has the receptacle shelf3, agent agent1 grasps object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 moves from the current position location27 to the next position location5 that has the receptacle cabinet6, agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5, check that object saltshaker1 of type saltshakertype is in a receptacle cabinet6 of type cabinettype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5?", "answer": "no"}
{"id": *******************, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. apple1, cellphone1, plate1, peppershaker1, cellphone2, lettuce1, knife1, and lettuce2 are at location2. spoon2, mug2, knife2, houseplant1, glassbottle1, bread1, papertowelroll1, plate3, spatula1, butterknife1, cellphone3, and creditcard1 are at location3. mug1, bowl1, egg1, plate2, tomato1, egg2, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. potato2, sink1, spatula3, spatula2, and glassbottle2 are at location6. fork1 and dishsponge1 are at location27. pot1 is at location5. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. apple2, glassbottle3, and potato3 are at location8. soapbottle2 and statue1 are at location26. lightswitch1 is at location25. saltshaker1 is at location15. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. bowl2 is at location29. vase2 is at location17. cup1 is at location24. peppershaker3 is at location16. soapbottle1 is at location4. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. cellphone2, lettuce1, lettuce2, cellphone1, peppershaker1, apple1, plate1, and knife1 are on countertop1. glassbottle2, spatula2, potato2, and spatula3 are in sinkbasin1. dishsponge2 and vase1 are in cabinet5. saltshaker1 is in drawer2. soapbottle2 and statue1 are on shelf3. egg2, mug1, egg1, plate2, bowl1, potato1, and tomato1 are in fridge1. pan1 is on stoveburner4. spatula1, papertowelroll1, plate3, mug2, knife2, creditcard1, glassbottle1, bread1, cellphone3, houseplant1, spoon2, and butterknife1 are on countertop3. pan1 and spoon1 are on countertop2. peppershaker2 is in drawer1. glassbottle3, potato3, and apple2 are in garbagecan1. pot1 is on stoveburner1. vase2 is on shelf1. fork1 and dishsponge1 are in drawer3. cup1 is in microwave1. bowl2 is on shelf2. soapbottle1 is in cabinet6. peppershaker3 is in cabinet3. pot1 is on stoveburner3. pan1 is on stoveburner2. cellphone2 is on plate1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle microwave1 from the current location location31 to the next location location24, agent agent1 navigates to the receptacle countertop3 from the current location location24 to the next location location3, agent agent1 collects object mug2 from receptacle countertop3 while at location location3, agent agent1 navigates to the receptacle microwave1 from the current location location3 to the next location location24, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 navigates to the receptacle coffeemachine1 from the current location location24 to the next location location11, agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11, ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 warms up object mug2 with a microwave microwave1 at location location24?", "answer": "yes"}
{"id": 1532946796535048058, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder1 is at location18. handtowelholder2 is at location17. garbagecan1 is at location2. cabinet4 is at location15. cabinet3 is at location8. toilet1 is at location7. countertop1 is at location3. towelholder1 is at location5. sinkbasin2 is at location6. sinkbasin1 is at location16. toiletpaperhanger1 is at location10. cabinet2 is at location11. cabinet1 is at location4.  Currently, the objects are at locations as follows. soapbar1 and toiletpaper1 are at location7. plunger1 and scrubbrush1 are at location10. handtowel2 is at location17. spraybottle2 and soapbottle1 are at location15. cloth1, candle1, and soapbar2 are at location3. sink2 is at location14. toiletpaper2 is at location8. sink1 is at location12. mirror1 is at location9. cloth2 is at location11. handtowel1 is at location18. showerdoor1 is at location1. spraybottle1 and soapbottle2 are at location4. showerglass1 and towel1 are at location5. lightswitch1 is at location13. spraybottle3 is at location2. agent agent1 is at location location19. The objects are in/on receptacle as follows. soapbottle1 and spraybottle2 are in cabinet4. soapbottle2 and spraybottle1 are in cabinet1. toiletpaper1 and soapbar1 are in toilet1. candle1, cloth1, and soapbar2 are on countertop1. cloth2 is in cabinet2. towel1 is on towelholder1. handtowel1 is on handtowelholder1. handtowel2 is on handtowelholder2. toiletpaper2 is in cabinet3. spraybottle3 is in garbagecan1. cabinet1, cabinet4, cabinet3, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 goes to receptacle countertop1 from the current location location19 to the next location location3, agent agent1 picks up object soapbar2 from receptacle countertop1 that is at location location3, agent agent1 goes to receptacle cabinet2 from the current location location3 to the next location location11, agent agent1 goes to receptacle sinkbasin2 from the current location location11 to the next location location6, agent agent1 goes to receptacle sinkbasin1 from the current location location6 to the next location location16, agent agent1 cleans an object soapbar2 in a sink sinkbasin1 that is in location location16, agent agent1 goes to receptacle cabinet2 from the current location location16 to the next location location11, agent agent1 goes to receptacle cabinet4 from the current location location11 to the next location location15, agent agent1 opens receptacle cabinet4 while at location location15, agent agent1 puts down an object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15, validate that object soapbar2 of type soapbartype is clean and in receptacle cabinet4 of type cabinettype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 goes to receptacle cabinet2 from the current location location3 to the next location location11?", "answer": "no"}
{"id": -3057328468786367428, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. apple1, cellphone1, plate1, peppershaker1, cellphone2, lettuce1, knife1, and lettuce2 are at location2. spoon2, mug2, knife2, houseplant1, glassbottle1, bread1, papertowelroll1, plate3, spatula1, butterknife1, cellphone3, and creditcard1 are at location3. mug1, bowl1, egg1, plate2, tomato1, egg2, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. potato2, sink1, spatula3, spatula2, and glassbottle2 are at location6. fork1 and dishsponge1 are at location27. pot1 is at location5. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. apple2, glassbottle3, and potato3 are at location8. soapbottle2 and statue1 are at location26. lightswitch1 is at location25. saltshaker1 is at location15. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. bowl2 is at location29. vase2 is at location17. cup1 is at location24. peppershaker3 is at location16. soapbottle1 is at location4. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. cellphone2, lettuce1, lettuce2, cellphone1, peppershaker1, apple1, plate1, and knife1 are on countertop1. glassbottle2, spatula2, potato2, and spatula3 are in sinkbasin1. dishsponge2 and vase1 are in cabinet5. saltshaker1 is in drawer2. soapbottle2 and statue1 are on shelf3. egg2, mug1, egg1, plate2, bowl1, potato1, and tomato1 are in fridge1. pan1 is on stoveburner4. spatula1, papertowelroll1, plate3, mug2, knife2, creditcard1, glassbottle1, bread1, cellphone3, houseplant1, spoon2, and butterknife1 are on countertop3. pan1 and spoon1 are on countertop2. peppershaker2 is in drawer1. glassbottle3, potato3, and apple2 are in garbagecan1. pot1 is on stoveburner1. vase2 is on shelf1. fork1 and dishsponge1 are in drawer3. cup1 is in microwave1. bowl2 is on shelf2. soapbottle1 is in cabinet6. peppershaker3 is in cabinet3. pot1 is on stoveburner3. pan1 is on stoveburner2. cellphone2 is on plate1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 moves from the current position location31 to the next position location11 that has the receptacle coffeemachine1, agent agent1 moves from the current position location11 to the next position location3 that has the receptacle countertop3, agent agent1 moves from the current position location3 to the next position location6 that has the receptacle sinkbasin1, agent agent1 moves from the current position location6 to the next position location3 that has the receptacle countertop3, agent agent1 collects object mug2 from receptacle countertop3 while at location location3, agent agent1 moves from the current position location3 to the next position location11 that has the receptacle coffeemachine1, agent agent1 moves from the current position location11 to the next position location24 that has the receptacle microwave1, agent agent1 heats up object mug2 with a microwave microwave1 at location location24, agent agent1 moves from the current position location24 to the next position location11 that has the receptacle coffeemachine1, agent agent1 places object mug2 with type mugtype in a receptacle coffeemachine1 with type coffeemachinetype at location location11, check that object mug2 of type mugtype is warmed up and in receptacle coffeemachine1 of type coffeemachinetype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 moves from the current position location3 to the next position location6 that has the receptacle sinkbasin1 and agent agent1 moves from the current position location6 to the next position location3 that has the receptacle countertop3?", "answer": "yes"}
{"id": -5736473591703056007, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. drawer3 is at location17. shelf4 is at location23. garbagecan1 is at location2. drawer5 and drawer4 are at location12. shelf3 is at location11. drawer2 is at location18. desk1 is at location3. bed1 is at location13. shelf5 is at location22. laundryhamper1 is at location8. desk2 is at location10. shelf2 is at location25. drawer1 is at location21. drawer6 is at location1. safe1 is at location6. shelf1 is at location20.  Currently, the objects are at locations as follows. pencil3, cd3, pen1, mug2, and cellphone2 are at location10. bowl3 is at location24. bowl1, cd1, mug1, alarmclock1, and pencil1 are at location3. alarmclock2 is at location11. pillow1, cellphone1, laptop1, laptop2, pillow2, and book1 are at location13. alarmclock3, desklamp1, and bowl2 are at location23. chair1 is at location21. window1 is at location5. keychain1 and keychain2 are at location6. chair2 is at location26. cellphone3 is at location12. lightswitch1 is at location14. pencil2 and creditcard1 are at location22. baseballbat1 is at location9. blinds1 is at location16. cd2 is at location2. mirror1 is at location19. window2 is at location4. blinds2 is at location15. basketball1 is at location7. laundryhamperlid1 is at location8. agent agent1 is at location location27. The objects are in/on receptacle as follows. cellphone1, laptop2, laptop1, pillow1, book1, and pillow2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. alarmclock3, pencil3, pen1, bowl2, mug2, cellphone2, desklamp1, and cd3 are on desk2. cd1, pencil1, bowl1, mug1, and alarmclock1 are on desk1. bowl3 is on shelf6. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. cellphone3 is in drawer5. drawer3, safe1, drawer1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle bed1 from the current location location27 to the next location location13, agent agent1 navigates to the receptacle shelf1 from the current location location13 to the next location location20, agent agent1 navigates to the receptacle bed1 from the current location location20 to the next location location13, agent agent1 collects object book1 from receptacle bed1 that is at location location13, agent agent1 navigates to the receptacle drawer6 from the current location location13 to the next location location1, agent agent1 navigates to the receptacle desk2 from the current location location1 to the next location location10, agent agent1 navigates to the receptacle shelf4 from the current location location10 to the next location location23, agent agent1 toggles a togglable object desklamp1 that is on the receptacle shelf4 at location location23, validate that togglable object desklamp1 of type desklamptype is toggled and in receptacle shelf4 at location location23 while agent agent1 is holding object book1 of type booktype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 navigates to the receptacle shelf1 from the current location location13 to the next location location20 and agent agent1 navigates to the receptacle bed1 from the current location location20 to the next location location13?", "answer": "yes"}
{"id": 6625409156980340616, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. spoon3, lettuce3, knife1, saltshaker2, soapbottle1, tomato1, houseplant1, glassbottle3, butterknife1, and fork2 are at location3. dishsponge1 is at location15. pan1, peppershaker1, and cellphone1 are at location11. creditcard1, creditcard2, statue1, bread1, spoon1, lettuce2, mug1, and cellphone3 are at location2. cellphone2 and peppershaker2 are at location20. stoveknob3 and stoveknob2 are at location12. papertowelroll1 and vase2 are at location29. saltshaker3 is at location26. apple1, lettuce1, potato2, potato1, cup2, and cup1 are at location10. plate1 is at location4. pot1 is at location5. sink1, fork1, spatula2, and spoon2 are at location6. apple2, egg1, and egg2 are at location8. spatula1 is at location27. lightswitch1 is at location25. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. glassbottle1 is at location19. saltshaker1 is at location16. glassbottle2 and bowl1 are at location14. vase1 is at location17. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. fork1, spatula2, and spoon2 are in sinkbasin1. soapbottle1, knife1, spoon3, saltshaker2, houseplant1, lettuce3, tomato1, butterknife1, fork2, and glassbottle3 are on countertop3. vase1 is on shelf1. statue1, bread1, lettuce2, creditcard2, cellphone3, spoon1, mug1, and creditcard1 are on countertop1. pan1 is on stoveburner4. cellphone1, pan1, and peppershaker1 are on countertop2. peppershaker2 and cellphone2 are in drawer1. vase2 and papertowelroll1 are on shelf2. pot1 is on stoveburner1. saltshaker1 is in cabinet3. egg1, egg2, and apple2 are in garbagecan1. plate1 is in cabinet6. cup1, cup2, potato2, apple1, lettuce1, and potato1 are in fridge1. glassbottle2 and bowl1 are in cabinet4. spatula1 is in drawer3. saltshaker3 is on shelf3. pot1 is on stoveburner3. pan1 is on stoveburner2. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle garbagecan1 from the current location location31 to the next location location8, agent agent1 collects object egg1 from receptacle garbagecan1 that is at location location8, agent agent1 navigates to the receptacle drawer1 from the current location location8 to the next location location20, agent agent1 navigates to the receptacle coffeemachine1 from the current location location20 to the next location location11, agent agent1 navigates to the receptacle garbagecan1 from the current location location11 to the next location location8, agent agent1 navigates to the receptacle microwave1 from the current location location8 to the next location location24, agent agent1 heats up object egg1 with a microwave microwave1 that is in location location24, agent agent1 navigates to the receptacle garbagecan1 from the current location location24 to the next location location8, agent agent1 places object egg1 with type eggtype in a receptacle garbagecan1 with type garbagecantype at location location8, check that object egg1 of type eggtype is hot and in receptacle garbagecan1 of type garbagecantype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 navigates to the receptacle microwave1 from the current location location8 to the next location location24?", "answer": "no"}
{"id": -6264628563946099607, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. drawer3 is at location17. shelf4 is at location23. garbagecan1 is at location2. drawer5 and drawer4 are at location12. shelf3 is at location11. drawer2 is at location18. desk1 is at location3. bed1 is at location13. shelf5 is at location22. laundryhamper1 is at location8. desk2 is at location10. shelf2 is at location25. drawer1 is at location21. drawer6 is at location1. safe1 is at location6. shelf1 is at location20.  Currently, the objects are at locations as follows. pencil3, cd3, pen1, mug2, and cellphone2 are at location10. bowl3 is at location24. bowl1, cd1, mug1, alarmclock1, and pencil1 are at location3. alarmclock2 is at location11. pillow1, cellphone1, laptop1, laptop2, pillow2, and book1 are at location13. alarmclock3, desklamp1, and bowl2 are at location23. chair1 is at location21. window1 is at location5. keychain1 and keychain2 are at location6. chair2 is at location26. cellphone3 is at location12. lightswitch1 is at location14. pencil2 and creditcard1 are at location22. baseballbat1 is at location9. blinds1 is at location16. cd2 is at location2. mirror1 is at location19. window2 is at location4. blinds2 is at location15. basketball1 is at location7. laundryhamperlid1 is at location8. agent agent1 is at location location27. The objects are in/on receptacle as follows. cellphone1, laptop2, laptop1, pillow1, book1, and pillow2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. alarmclock3, pencil3, pen1, bowl2, mug2, cellphone2, desklamp1, and cd3 are on desk2. cd1, pencil1, bowl1, mug1, and alarmclock1 are on desk1. bowl3 is on shelf6. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. cellphone3 is in drawer5. drawer3, safe1, drawer1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 navigates from the current position location27 to the next position location13 that has a receptacle bed1, agent agent1 grasps object book1 from receptacle bed1 while at location location13, agent agent1 navigates from the current position location13 to the next position location23 that has a receptacle shelf4, agent agent1 turns on object desklamp1 that is on the receptacle shelf4 at location location23, agent agent1 turns off object desklamp1 that is on the receptacle shelf4 at location location23, agent agent1 turns on object desklamp1 that is on the receptacle shelf4 at location location23, agent agent1 turns off object desklamp1 that is on the receptacle shelf4 at location location23, ensure that object desklamp1 of type desklamptype is toggled and in receptacle shelf4 at location location23 while agent agent1 is holding object book1 of type booktype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 grasps object book1 from receptacle bed1 while at location location13?", "answer": "no"}
{"id": 2038181437345155360, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet1 is at location20. stoveburner2 and stoveburner4 are at location22. sinkbasin1 is at location7. coffeemachine1 and countertop2 are at location12. drawer2 is at location16. toaster1 is at location10. shelf1 is at location18. microwave1 is at location25. cabinet3 is at location17. cabinet4 is at location15. shelf2 is at location30. countertop3 is at location4. stoveburner3 and stoveburner1 are at location6. drawer1 is at location21. cabinet6 is at location5. cabinet2 is at location23. fridge1 is at location11. cabinet5 is at location14. shelf3 is at location27. countertop1 is at location3. drawer3 is at location28. garbagecan1 is at location9.  Currently, the objects are at locations as follows. papertowelroll1, soapbottle2, and glassbottle1 are at location9. pot1 is at location22. fork2, soapbottle3, statue2, butterknife1, spoon2, cellphone3, knife1, spatula3, butterknife2, peppershaker2, and houseplant1 are at location4. lettuce1, tomato1, tomato2, potato1, potato2, egg1, cup2, and cup1 are at location11. spatula1 is at location28. creditcard2, glassbottle2, bowl1, and mug2 are at location30. cellphone1, apple3, dishsponge3, apple2, bread1, statue1, apple1, creditcard1, and spatula2 are at location3. fork1, egg2, cup3, sink1, tomato3, and potato3 are at location7. cellphone2 is at location21. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. vase2 is at location18. creditcard3 and saltshaker1 are at location27. soapbottle1 and vase1 are at location5. stoveknob1 is at location19. window1 is at location31. chair1 is at location24. chair2 is at location1. peppershaker1 is at location20. stoveknob3 and stoveknob2 are at location13. pan1 is at location6. plate2 and plate1 are at location14. stoveknob4 is at location8. mug1 is at location25. dishsponge2 is at location2. agent agent1 is at location location32. The objects are in/on receptacle as follows. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. fork1, egg2, cup3, tomato3, and potato3 are in sinkbasin1. plate1, dishsponge2, and plate2 are in cabinet5. statue1, bread1, spatula2, dishsponge3, cellphone1, apple2, apple3, apple1, and creditcard1 are on countertop1. knife1, peppershaker2, butterknife2, soapbottle3, spatula3, cellphone3, statue2, houseplant1, spoon2, butterknife1, and fork2 are on countertop3. creditcard3 and saltshaker1 are on shelf3. vase2 is on shelf1. peppershaker1 is in cabinet1. spoon1 and dishsponge1 are in drawer2. soapbottle2, papertowelroll1, and glassbottle1 are in garbagecan1. cup1, egg1, cup2, potato2, lettuce1, tomato2, potato1, and tomato1 are in fridge1. dishsponge2 is on plate1. cellphone2 is in drawer1. pan1 is on stoveburner1. spatula1 is in drawer3. pan1 is on stoveburner3. soapbottle1 and vase1 are in cabinet6. mug1 is in microwave1. pot1 is on stoveburner2. pot1 is on stoveburner4. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle fridge1 from the current location location32 to the next location location11, agent agent1 navigates to the receptacle drawer1 from the current location location11 to the next location location21, agent agent1 navigates to the receptacle shelf3 from the current location location21 to the next location location27, agent agent1 picks up object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 navigates to the receptacle cabinet4 from the current location location27 to the next location location15, agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet4 with type cabinettype at location location15, ensure that object saltshaker1 of type saltshakertype is in a receptacle cabinet4 of type cabinettype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet4 with type cabinettype at location location15?", "answer": "no"}
{"id": -*******************, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. drawer3 is at location17. shelf4 is at location23. garbagecan1 is at location2. drawer5 and drawer4 are at location12. shelf3 is at location11. drawer2 is at location18. desk1 is at location3. bed1 is at location13. shelf5 is at location22. laundryhamper1 is at location8. desk2 is at location10. shelf2 is at location25. drawer1 is at location21. drawer6 is at location1. safe1 is at location6. shelf1 is at location20.  Currently, the objects are at locations as follows. pencil3, cd3, pen1, mug2, and cellphone2 are at location10. bowl3 is at location24. bowl1, cd1, mug1, alarmclock1, and pencil1 are at location3. alarmclock2 is at location11. pillow1, cellphone1, laptop1, laptop2, pillow2, and book1 are at location13. alarmclock3, desklamp1, and bowl2 are at location23. chair1 is at location21. window1 is at location5. keychain1 and keychain2 are at location6. chair2 is at location26. cellphone3 is at location12. lightswitch1 is at location14. pencil2 and creditcard1 are at location22. baseballbat1 is at location9. blinds1 is at location16. cd2 is at location2. mirror1 is at location19. window2 is at location4. blinds2 is at location15. basketball1 is at location7. laundryhamperlid1 is at location8. agent agent1 is at location location27. The objects are in/on receptacle as follows. cellphone1, laptop2, laptop1, pillow1, book1, and pillow2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. alarmclock3, pencil3, pen1, bowl2, mug2, cellphone2, desklamp1, and cd3 are on desk2. cd1, pencil1, bowl1, mug1, and alarmclock1 are on desk1. bowl3 is on shelf6. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. cellphone3 is in drawer5. drawer3, safe1, drawer1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 goes to receptacle bed1 from the current location location27 to the next location location13, agent agent1 goes to receptacle shelf1 from the current location location13 to the next location location20, agent agent1 goes to receptacle bed1 from the current location location20 to the next location location13, agent agent1 collects object book1 from receptacle bed1 while at location location13, agent agent1 goes to receptacle drawer1 from the current location location13 to the next location location21, agent agent1 goes to receptacle desk2 from the current location location21 to the next location location10, agent agent1 turns on object desklamp1 that is on the receptacle desk2 at location location10, validate that togglable object desklamp1 of type desklamptype is toggled and in receptacle desk2 at location location10 while agent agent1 is holding object book1 of type booktype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 goes to receptacle shelf1 from the current location location13 to the next location location20 and agent agent1 goes to receptacle bed1 from the current location location20 to the next location location13?", "answer": "yes"}
{"id": 8897837736915869020, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. spoon3, lettuce3, knife1, saltshaker2, soapbottle1, tomato1, houseplant1, glassbottle3, butterknife1, and fork2 are at location3. dishsponge1 is at location15. pan1, peppershaker1, and cellphone1 are at location11. creditcard1, creditcard2, statue1, bread1, spoon1, lettuce2, mug1, and cellphone3 are at location2. cellphone2 and peppershaker2 are at location20. stoveknob3 and stoveknob2 are at location12. papertowelroll1 and vase2 are at location29. saltshaker3 is at location26. apple1, lettuce1, potato2, potato1, cup2, and cup1 are at location10. plate1 is at location4. pot1 is at location5. sink1, fork1, spatula2, and spoon2 are at location6. apple2, egg1, and egg2 are at location8. spatula1 is at location27. lightswitch1 is at location25. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. glassbottle1 is at location19. saltshaker1 is at location16. glassbottle2 and bowl1 are at location14. vase1 is at location17. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. fork1, spatula2, and spoon2 are in sinkbasin1. soapbottle1, knife1, spoon3, saltshaker2, houseplant1, lettuce3, tomato1, butterknife1, fork2, and glassbottle3 are on countertop3. vase1 is on shelf1. statue1, bread1, lettuce2, creditcard2, cellphone3, spoon1, mug1, and creditcard1 are on countertop1. pan1 is on stoveburner4. cellphone1, pan1, and peppershaker1 are on countertop2. peppershaker2 and cellphone2 are in drawer1. vase2 and papertowelroll1 are on shelf2. pot1 is on stoveburner1. saltshaker1 is in cabinet3. egg1, egg2, and apple2 are in garbagecan1. plate1 is in cabinet6. cup1, cup2, potato2, apple1, lettuce1, and potato1 are in fridge1. glassbottle2 and bowl1 are in cabinet4. spatula1 is in drawer3. saltshaker3 is on shelf3. pot1 is on stoveburner3. pan1 is on stoveburner2. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Given the plan: \"agent agent1 goes to receptacle garbagecan1 from the current location location31 to the next location location8, agent agent1 collects object egg1 from receptacle garbagecan1 that is at location location8, agent agent1 goes to receptacle coffeemachine1 from the current location location8 to the next location location11, agent agent1 goes to receptacle garbagecan1 from the current location location11 to the next location location8, agent agent1 goes to receptacle coffeemachine1 from the current location location8 to the next location location11, agent agent1 goes to receptacle garbagecan1 from the current location location11 to the next location location8, agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24, agent agent1 heats up object egg1 with a microwave microwave1 at location location24, agent agent1 goes to receptacle garbagecan1 from the current location location24 to the next location location8, agent agent1 places object egg1 of type eggtype in a receptacle garbagecan1 of type garbagecantype while at location location8, validate that object egg1 of type eggtype is hot and in receptacle garbagecan1 of type garbagecantype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 goes to receptacle garbagecan1 from the current location location11 to the next location location8 and agent agent1 goes to receptacle coffeemachine1 from the current location location8 to the next location location11?", "answer": "yes"}
