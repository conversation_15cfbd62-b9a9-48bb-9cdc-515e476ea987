{"question_id": "0704392e-7902-4341-9fb0-dc5b6355685b", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "365", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels of f5 and f4, to location l1, then cargo c0 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Next, vehicle v0 moves from location l1, which has fuel levels of f7 and f6, to location l0, and cargo c0 is unloaded from vehicle v0, which has available spaces s0 and s1, at location l0. This sequence of actions continues with vehicle v0 moving between locations l0 and l1, loading and unloading cargo c1, c2, c4, and c6, and updating the fuel levels accordingly. The sequence of actions is as follows: vehicle v0 moves from location l0 to l1, cargo c1 is loaded, then vehicle v0 moves back to l0, and cargo c1 is unloaded. This pattern repeats for cargo c2 and c4. Finally, vehicle v0 moves from location l0 to l1, cargo c6 is loaded, and vehicle v0 moves back to location l0, which has fuel levels of f3 and f2, to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at l1."}
{"question_id": "2bdbbc0b-7de1-4b5d-87c5-2ff4327a55be", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "448", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, utilizing spaces s0 and s1. Subsequently, at location l0, cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, using spaces s0 and s1. Next, at location l1, cargo c3 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f7 and f6, to location l0. Upon arrival, cargo c3 is unloaded from vehicle v0, utilizing spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0 at location l0, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected. Location l0 contains fuel f3, and location l1 is also connected to l0. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which contains space s1, is located at l0."}
{"question_id": "c272b5ac-c216-4395-a61f-77cee5a52964", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "6", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, then vehicle v0 moves from location l1, where the fuel levels are f8 and f7, to location l0, at which point cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0; subsequently, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0, and vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1, where cargo c0 is then unloaded from vehicle v0, which has spaces s0 and s1; next, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, and vehicle v0 moves from location l1, with fuel levels f7 and f6, to location l0, where cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1; at location l0, cargo c1 is then loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 moves from location l0, with fuel levels f2 and f1, to location l1, where cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1; subsequently, cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, and vehicle v0 moves from location l1, with fuel levels f6 and f5, to location l0, where cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1; at location l0, cargo c7 is then loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 moves from location l0, with fuel levels f1 and f0, to location l1, where cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which also contains space s1, is located at l0."}
{"question_id": "a50487bd-63b4-4dcc-8486-03ca48a71e4c", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "635", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 relocates from location l1, which has fuel levels of f3 and f2, to location l0, resulting in the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. It's also noted that l1 is connected to l0. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Furthermore, vehicle v1 contains space s2 and is present at location l1."}
{"question_id": "78ab9093-7949-4088-bca5-a1f878c29ccd", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "95", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, what is the total count of valid state properties that include negations? Express the answer as an integer, or write None if there are no such properties.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is located at l1."}
{"question_id": "495fc7b9-ada2-444c-be6f-f44c5357f1fa", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "368", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 travels from location l0, which has fuel levels f5 and f4, to location l1. Upon arrival at l1, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then returns to location l0, which now has fuel levels f7 and f6. At l0, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 moves back to location l1, which has fuel levels f4 and f3. At l1, cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels to location l0, which now has fuel levels f6 and f5. At l0, cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. Finally, vehicle v0 moves to location l1, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is located at l1."}
{"question_id": "d551188c-2666-43a3-b4bd-79f688f48175", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "354", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: vehicle v0 transitions from location l0 with fuel levels f5 and f4 to location l1, resulting in the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or None if there are no such actions.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at l1."}
{"question_id": "0d22cbe9-5d87-44ce-b1e6-0d7cc2ade6be", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "26", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded into vehicle v0, which has spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which still has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded into vehicle v0, which has spaces s1 and s0, and vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is available at l1, fuel f7 is available at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, spaces s0 and s1 are adjacent, vehicle v0 occupies space s1 and vehicle v0 is currently at location l1."}
{"question_id": "d5f4e038-a5b0-4ff1-ab23-049764925d9a", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "104", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1. Subsequently, at location l0, cargo c2 is also loaded into vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then proceeds to move from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, vacating spaces s0 and s1. Next, cargo c10 is loaded into vehicle v0 at location l1, occupying spaces s1 and s0. Following this, cargo c2 is unloaded from vehicle v0 at location l1, freeing up spaces s0 and s1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, to location l0. Upon arrival, cargo c10 is unloaded from vehicle v0 at location l0, releasing spaces s1 and s2. Finally, at location l0, cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, resulting in the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. It's also noted that l1 is connected to l0. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Furthermore, vehicle v1 contains space s2 and is present at location l1."}
{"question_id": "f7bff2fa-396f-4773-b2e4-9591aaf36c01", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "1", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: at location l1, cargo c1 is loaded onto vehicle v0 using spaces s1 and s0, while cargo c3 is unloaded from vehicle v0 using spaces s0 and s1 at the same location l1. At location l0, cargo c1 is then unloaded from vehicle v0 using spaces s0 and s1. Vehicle v0 moves from location l0, which has fuel levels f7 and f6, to location l1. At location l1, cargo c4 is loaded onto vehicle v0 using spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0 using spaces s0 and s1. Vehicle v0 moves from location l0, which has fuel levels f6 and f5, to location l1. At location l1, cargo c6 is loaded onto vehicle v0 using spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f4 and f3, to location l0. At location l0, cargo c6 is unloaded from vehicle v0 using spaces s0 and s1. Vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1. At location l1, cargo c7 is loaded onto vehicle v0 using spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c7 is unloaded from vehicle v0 using spaces s0 and s1. Vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c8 is loaded onto vehicle v0 using spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, to location l0. Finally, at location l0, cargo c8 is unloaded from vehicle v0 using spaces s0 and s1 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is available at l1, fuel f7 is available at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, spaces s0 and s1 are adjacent, vehicle v0 occupies space s1 and vehicle v0 is currently at location l1."}
{"question_id": "6bd6fdbe-b68e-4887-a3ad-118ce0d71f55", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "442", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, where the fuel levels are f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, where the fuel levels are f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f7 and f6, to location l0. Upon arrival at location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, where the fuel levels are f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Then, cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, where the fuel levels are f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1. Finally, cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which contains space s1, is located at l0."}
{"question_id": "2c4a6783-1625-47d9-9188-9b90f422182d", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "26", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, what is the total count of valid state properties that do not include negations? Express the answer as an integer, or write None if there are no such properties.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is available at l1, fuel f7 is available at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is connected to location l1, location l1 is connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 is equipped with space s1 and vehicle v0 is currently at location l1."}
{"question_id": "7a3bc518-a54f-40bd-ad01-b8d6f0b67baa", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "9", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has available spaces s0 and s1, and then vehicle v0 travels from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has available spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has available spaces s0 and s1, and then vehicle v0 travels from location l0, with fuel levels f6 and f5, back to location l1. This sequence of actions continues with cargo c6 being loaded onto vehicle v0 at location l1, and then unloaded at location l0, followed by cargo c7 being loaded at location l1 and unloaded at location l0, and finally cargo c8 being loaded at location l1 and unloaded at location l0. In the resulting state, what is the total number of executable actions? Provide the answer as an integer, or None if there are no executable actions.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is present in l1, fuel f7 is present in l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, spaces s0 and s1 are adjacent, vehicle v0 occupies space s1 and vehicle v0 is currently at location l1."}
{"question_id": "055b17f5-a2db-4305-8b98-f093242dabb3", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "105", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1. Finally, at location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is present in l1, fuel f7 is present in l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, spaces s0 and s1 are adjacent, vehicle v0 is equipped with space s1 and vehicle v0 is currently at location l1."}
{"question_id": "3012b194-e05b-487b-ad1e-0a2a6ec00f47", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "142", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, using spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, using spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, using spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, using spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, using spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, using spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which contains space s1, is located at l0."}
{"question_id": "b280d81d-db6b-4a86-8552-bbda9cf42760", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "26", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Considering the initial condition, the following actions are taken: vehicle v0 transitions from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, what is the total count of valid properties that do not involve negations? Provide the answer as an integer, or None if there are no such properties.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected to each other. Location l0 has a fuel level of f4, whereas location l1 has a fuel level of f3. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "6d18fba1-5724-41fb-b5e6-75e4c5033ed1", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "2", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, what is the total number of actions that can be executed? Provide the answer as an integer, or write None if there are no executable actions.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is positioned at l0, cargo c2 is located at l0, cargo c3 is situated at l0, cargo c4 is found at l1, cargo c5 is located at l1, cargo c6 is positioned at l1, cargo c7 is found at l1, cargo c8 is located at l1, cargo c9 is situated at l1, fuel f6 is available at l1, fuel f7 is available at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, spaces s0 and s1 are adjacent, vehicle v0 occupies space s1 and vehicle v0 is currently at location l1."}
