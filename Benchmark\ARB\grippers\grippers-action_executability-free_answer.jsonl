{"question_id": "e97f905d-1c5c-40db-aa6f-7440a8f9c83f", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball3 in room5 and robot1 moves to room2 from room5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from room1, robot1 moves to room3", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 in room4, then moves from room4 to room5, drops ball7 in room5 using lgripper1, proceeds to room1 from room5, picks up ball1 in room1 with lgripper1 of robot1, and simultaneously picks up ball3 in room1 with rgripper1 of robot1, moves to room5 from room1, then redirects to room3 from room1, drops ball3 in room5 using rgripper1 of robot1, and finally moves to room2 from room5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "433e4dcd-5e68-4260-b51b-66a453b75e66", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "ball3 is dropped in room4 with lgripper1 by robot1, ball2 is dropped in room2 with lgripper1 by robot1, ball2 is dropped in room5 with lgripper1 by robot1, in room4, robot1's lgripper1 drops ball5, from room3, robot1's rgripper1 picks up ball7, ball4 is dropped in room5 with lgripper1 by robot1, from room3, robot1's lgripper1 picks up ball4, rgripper1 of robot1 drops ball7 in room2, lgripper1 of robot1 picks up ball6 in room3, rgripper1 of robot1 picks up ball2 in room3, in room3, robot1's lgripper1 drops ball1, ball5 is dropped in room2 with lgripper1 by robot1, lgripper1 of robot1 picks up ball7 in room3, ball3 is picked from room5 with lgripper1 by robot1, from room2, robot1's lgripper1 picks up ball2, lgripper1 of robot1 drops ball6 in room4, ball5 is picked from room1 with lgripper1 by robot1, lgripper1 of robot1 drops ball5 in room1, in room4, robot1's rgripper1 drops ball5, ball7 is picked from room5 with rgripper1 by robot1, rgripper1 of robot1 picks up ball2 in room2, ball6 is dropped in room3 with rgripper1 by robot1, from room2, robot1's rgripper1 picks up ball6, ball6 is picked from room4 with lgripper1 by robot1, lgripper1 of robot1 picks up ball5 in room4, from room3, robot1's rgripper1 picks up ball3, lgripper1 of robot1 drops ball4 in room2, ball1 is dropped in room2 with rgripper1 by robot1, from room2, robot1's rgripper1 picks up ball1, in room5, robot1's rgripper1 drops ball2, robot1 moves to room4 from room3, robot1 moves to room4 from room2, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball1 in room2, from room5, robot1 moves to room1, rgripper1 of robot1 picks up ball5 in room2, from room4, robot1's rgripper1 picks up ball2, from room2, robot1's rgripper1 picks up ball7, from room4, robot1's rgripper1 picks up ball1, in room3, robot1's lgripper1 drops ball7, ball6 is picked from room3 with rgripper1 by robot1, robot1 moves to room1 from room3, ball5 is picked from room3 with lgripper1 by robot1, ball4 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room5, ball3 is picked from room2 with rgripper1 by robot1, in room4, robot1's lgripper1 drops ball4, ball4 is dropped in room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room4, ball5 is picked from room4 with rgripper1 by robot1, in room5, robot1's lgripper1 drops ball7, in room4, robot1's rgripper1 drops ball4, ball3 is dropped in room1 with lgripper1 by robot1, in room3, robot1's rgripper1 drops ball4, rgripper1 of robot1 drops ball7 in room1, in room5, robot1's lgripper1 drops ball3, from room5, robot1's lgripper1 picks up ball2, in room2, robot1's rgripper1 drops ball5, ball6 is picked from room5 with rgripper1 by robot1, from room4, robot1's lgripper1 picks up ball3, from room5, robot1's lgripper1 picks up ball1, ball7 is picked from room5 with lgripper1 by robot1, robot1 moves to room3 from room4, ball4 is dropped in room5 with rgripper1 by robot1, ball6 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 drops ball4 in room1, ball1 is picked from room3 with lgripper1 by robot1, ball4 is picked from room4 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room2, lgripper1 of robot1 picks up ball2 in room1, rgripper1 of robot1 drops ball6 in room2, ball7 is picked from room4 with lgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room4, rgripper1 of robot1 picks up ball4 in room4, ball7 is dropped in room1 with lgripper1 by robot1, ball4 is dropped in room2 with rgripper1 by robot1, in room4, robot1's lgripper1 drops ball7, rgripper1 of robot1 picks up ball7 in room1, rgripper1 of robot1 picks up ball7 in room4, in room3, robot1's rgripper1 drops ball2, in room5, robot1's lgripper1 drops ball5, rgripper1 of robot1 drops ball1 in room1, lgripper1 of robot1 drops ball6 in room5, in room3, robot1's rgripper1 drops ball5, ball1 is picked from room4 with lgripper1 by robot1, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball3 in room2, from room3, robot1 moves to room5, lgripper1 of robot1 picks up ball6 in room2, ball3 is dropped in room3 with lgripper1 by robot1, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1's lgripper1 picks up ball4, in room2, robot1's rgripper1 drops ball3, from room3, robot1's lgripper1 picks up ball2, robot1 moves from room2 to room1, rgripper1 of robot1 drops ball7 in room3, rgripper1 of robot1 picks up ball2 in room5, ball2 is dropped in room1 with rgripper1 by robot1, lgripper1 of robot1 picks up ball6 in room5, ball2 is picked from room1 with rgripper1 by robot1, in room2, robot1's rgripper1 drops ball2, in room2, robot1's lgripper1 drops ball6, robot1 moves from room5 to room2, robot1 moves to room5 from room2, ball1 is dropped in room3 with rgripper1 by robot1, ball1 is dropped in room5 with rgripper1 by robot1, from room2, robot1's lgripper1 picks up ball7, ball2 is picked from room4 with lgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, ball3 is picked from room5 with rgripper1 by robot1, ball5 is dropped in room5 with rgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, in room4, robot1's rgripper1 drops ball3, ball7 is dropped in room2 with lgripper1 by robot1, in room3, robot1's lgripper1 drops ball2, rgripper1 of robot1 picks up ball1 in room3, from room1, robot1's lgripper1 picks up ball4, from room5, robot1's rgripper1 picks up ball5, lgripper1 of robot1 picks up ball1 in room1, lgripper1 of robot1 drops ball6 in room1, ball5 is picked from room2 with lgripper1 by robot1, ball1 is dropped in room4 with rgripper1 by robot1, in room5, robot1's rgripper1 drops ball7, ball4 is picked from room3 with rgripper1 by robot1, lgripper1 of robot1 picks up ball5 in room5, ball2 is dropped in room4 with rgripper1 by robot1, ball2 is dropped in room4 with lgripper1 by robot1, lgripper1 of robot1 drops ball1 in room4, from room1, robot1's lgripper1 picks up ball3, robot1 moves from room3 to room2, lgripper1 of robot1 drops ball5 in room3, ball5 is dropped in room1 with rgripper1 by robot1, ball1 is dropped in room5 with lgripper1 by robot1, ball7 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball5, from room2, robot1's lgripper1 picks up ball4, ball4 is picked from room5 with rgripper1 by robot1, in room5, robot1's rgripper1 drops ball6, from room4, robot1 moves to room2, from room3, robot1's rgripper1 picks up ball5, lgripper1 of robot1 picks up ball3 in room3, ball3 is dropped in room3 with rgripper1 by robot1, ball1 is picked from room5 with rgripper1 by robot1, lgripper1 of robot1 picks up ball3 in room2, in room4, robot1's rgripper1 drops ball7, in room3, robot1's lgripper1 drops ball4, from room4, robot1 moves to room1, in room4, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball1 in room1, robot1 moves from room5 to room4, ball2 is dropped in room1 with lgripper1 by robot1, lgripper1 of robot1 drops ball6 in room3 and rgripper1 of robot1 drops ball3 in room1", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 moves from room1 to room5, drops ball3 in room5 using rgripper1, and then moves to room2. In room2, robot1's rgripper1 picks up ball4, and the robot moves back to room1. In room1, robot1 drops ball4 using rgripper1 and then picks up ball6 using the same gripper, resulting in the current state. In this state, list all actions that cannot be executed. If there are none, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is positioned in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "b60b8981-d0f7-4160-82cf-57cb9982f7aa", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room3, in room3, robot1's lgripper1 drops ball1, from room3, robot1 moves to room4, ball2 is dropped in room4 with rgripper1 by robot1, from room4, robot1 moves to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot1 moves to room3 from room2, robot1 moves to room1 from room2, ball5 is picked from room2 with rgripper1 by robot1, lgripper1 of robot1 drops ball4 in room2, from room2, robot1 moves to room5 and robot1 moves from room2 to room4", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1 moves from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2, and uses rgripper1 to pick up ball2. Next, robot1 moves to room3, where it drops ball1 using lgripper1, and then returns to room4, dropping ball2 with rgripper1. Finally, robot1 moves back to room2 and uses lgripper1 to pick up ball4, resulting in the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "136cf7a7-adda-44fc-97c8-f63426125db8", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, ball3 is picked from room2 with lgripper2 by robot1, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room3 to room2, ball7 is dropped in room2 with rgripper2 by robot2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves from room2 to room1, in room1, robot2's lgripper2 drops ball4, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "ball3 is picked from room2 with lgripper2 by robot1", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, then robot2 uses lgripper2 to pick up ball1 in room3, and robot2's rgripper2 picks up ball2 in room3. Meanwhile, robot1 uses lgripper2 to pick up ball3 in room2. Next, robot2 drops ball1 in room2 using lgripper2, and then robot2's rgripper2 drops ball2 in room2. After that, robot2 moves from room2 to room3, where lgripper2 of robot2 picks up ball4, and rgripper2 of robot2 picks up ball7. Then, robot2 moves from room3 to room2, and robot2 drops ball7 in room2 using rgripper2. Following this, robot2's rgripper2 picks up ball3 in room2. Next, robot2 moves from room2 to room1, where robot2 drops ball4 using lgripper2. Then, robot2's lgripper2 picks up ball5 in room1, and robot2's rgripper2 drops ball3 in room1. After that, robot2 picks up ball6 in room1 using rgripper2, and then moves from room1 to room2. Finally, robot2 drops ball5 in room2 using lgripper2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "77771c82-cd0c-4fd2-bf3b-99c405e61937", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3 and from room5, robot1 moves to room2 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "robot1 moves from room1 to room4, robot1 moves to room1 from room4, lgripper1 of robot1 drops ball3 in room2, ball3 is picked from room6 with rgripper1 by robot1, rgripper1 of robot1 drops ball6 in room1, from room5, robot1's lgripper1 picks up ball7, robot1 moves from room3 to room6, from room5, robot1's lgripper1 picks up ball1, from room5, robot1's lgripper1 picks up ball4, ball4 is dropped in room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room3, rgripper1 of robot1 drops ball5 in room1, in room1, robot1's lgripper1 drops ball2, robot1 moves to room5 from room6, robot1 moves to room6 from room4, ball1 is dropped in room1 with rgripper1 by robot1, lgripper1 of robot1 drops ball7 in room4, from room2, robot1's rgripper1 picks up ball3, ball1 is dropped in room1 with lgripper1 by robot1, lgripper1 of robot1 picks up ball5 in room5, ball2 is dropped in room3 with lgripper1 by robot1, lgripper1 of robot1 drops ball6 in room4, rgripper1 of robot1 picks up ball1 in room4, from room6, robot1's lgripper1 picks up ball6, ball5 is dropped in room6 with rgripper1 by robot1, in room1, robot1's rgripper1 drops ball2, in room1, robot1's lgripper1 drops ball6, ball7 is dropped in room6 with lgripper1 by robot1, ball7 is picked from room1 with rgripper1 by robot1, rgripper1 of robot1 drops ball5 in room2, lgripper1 of robot1 drops ball2 in room6, ball2 is picked from room1 with rgripper1 by robot1, from room6, robot1's lgripper1 picks up ball4, lgripper1 of robot1 drops ball1 in room3, from room1, robot1's lgripper1 picks up ball6, rgripper1 of robot1 drops ball5 in room5, lgripper1 of robot1 picks up ball2 in room3, robot1 moves from room1 to room2, from room2, robot1's rgripper1 picks up ball1, in room6, robot1's rgripper1 drops ball4, ball3 is dropped in room2 with rgripper1 by robot1, ball5 is dropped in room3 with rgripper1 by robot1, from room5, robot1 moves to room2, ball5 is dropped in room4 with lgripper1 by robot1, from room2, robot1's lgripper1 picks up ball5, rgripper1 of robot1 drops ball3 in room1, ball6 is dropped in room6 with rgripper1 by robot1, from room5, robot1's rgripper1 picks up ball2, from room4, robot1's rgripper1 picks up ball4, in room5, robot1's lgripper1 drops ball7, ball1 is picked from room6 with lgripper1 by robot1, in room2, robot1's lgripper1 drops ball5, rgripper1 of robot1 drops ball2 in room2, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4, in room4, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball6 in room6, rgripper1 of robot1 drops ball4 in room3, ball3 is dropped in room3 with lgripper1 by robot1, ball3 is picked from room6 with lgripper1 by robot1, from room4, robot1's lgripper1 picks up ball5, robot1 moves from room6 to room4, rgripper1 of robot1 picks up ball4 in room6, in room2, robot1's lgripper1 drops ball1, robot1 moves from room4 to room3, rgripper1 of robot1 picks up ball1 in room6, in room3, robot1's lgripper1 drops ball6, ball4 is picked from room4 with lgripper1 by robot1, lgripper1 of robot1 picks up ball5 in room3, rgripper1 of robot1 picks up ball7 in room2, ball4 is picked from room5 with rgripper1 by robot1, rgripper1 of robot1 drops ball7 in room1, lgripper1 of robot1 drops ball4 in room1, ball5 is dropped in room5 with lgripper1 by robot1, in room6, robot1's rgripper1 drops ball3, in room3, robot1's lgripper1 drops ball7, rgripper1 of robot1 picks up ball7 in room6, lgripper1 of robot1 picks up ball3 in room2, ball2 is picked from room1 with lgripper1 by robot1, ball5 is picked from room6 with rgripper1 by robot1, in room3, robot1's lgripper1 drops ball5, ball1 is dropped in room4 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball2, robot1 moves to room4 from room5, robot1 moves to room6 from room1, lgripper1 of robot1 picks up ball6 in room5, ball5 is picked from room1 with rgripper1 by robot1, robot1 moves to room1 from room3, lgripper1 of robot1 drops ball3 in room6, ball4 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 drops ball4 in room5, ball6 is dropped in room2 with rgripper1 by robot1, lgripper1 of robot1 picks up ball7 in room6, from room3, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball4 in room1, lgripper1 of robot1 picks up ball3 in room5, rgripper1 of robot1 picks up ball5 in room5, ball6 is picked from room4 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, ball5 is dropped in room6 with lgripper1 by robot1, rgripper1 of robot1 picks up ball1 in room3, ball3 is picked from room4 with lgripper1 by robot1, ball3 is dropped in room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball7 in room2, rgripper1 of robot1 picks up ball1 in room5, in room5, robot1's rgripper1 drops ball6, lgripper1 of robot1 picks up ball3 in room3, from room4, robot1's rgripper1 picks up ball2, rgripper1 of robot1 drops ball4 in room1, in room6, robot1's rgripper1 drops ball2, in room5, robot1's lgripper1 drops ball4, from room6, robot1's lgripper1 picks up ball5, lgripper1 of robot1 drops ball3 in room1, robot1 moves to room3 from room6, ball7 is picked from room1 with lgripper1 by robot1, from room5, robot1's lgripper1 picks up ball2, lgripper1 of robot1 drops ball1 in room5, lgripper1 of robot1 drops ball4 in room6, in room5, robot1's lgripper1 drops ball6, ball6 is picked from room2 with lgripper1 by robot1, lgripper1 of robot1 drops ball6 in room2, in room3, robot1's lgripper1 drops ball4, from room6, robot1's rgripper1 picks up ball2, robot1 moves from room6 to room2, ball2 is picked from room6 with lgripper1 by robot1, ball7 is dropped in room4 with rgripper1 by robot1, rgripper1 of robot1 drops ball2 in room3, ball1 is picked from room1 with rgripper1 by robot1, robot1 moves from room5 to room1, ball2 is picked from room3 with rgripper1 by robot1, lgripper1 of robot1 picks up ball7 in room2, from room5, robot1's rgripper1 picks up ball7, lgripper1 of robot1 drops ball2 in room2, lgripper1 of robot1 picks up ball2 in room4, from room1, robot1's lgripper1 picks up ball3, lgripper1 of robot1 drops ball7 in room1, from room3, robot1's lgripper1 picks up ball6, from room4, robot1's rgripper1 picks up ball6, ball2 is dropped in room5 with lgripper1 by robot1, ball7 is dropped in room6 with rgripper1 by robot1, robot1 moves from room1 to room5, ball7 is dropped in room2 with lgripper1 by robot1, lgripper1 of robot1 drops ball1 in room6, from room4, robot1's lgripper1 picks up ball1, rgripper1 of robot1 drops ball3 in room3, lgripper1 of robot1 picks up ball7 in room4, ball4 is dropped in room2 with rgripper1 by robot1, ball3 is dropped in room4 with rgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room2, from room2, robot1's rgripper1 picks up ball6, lgripper1 of robot1 drops ball6 in room6, ball6 is picked from room5 with rgripper1 by robot1, from room5, robot1 moves to room3, robot1 moves from room6 to room1, robot1 moves from room3 to room2, ball1 is dropped in room6 with rgripper1 by robot1, lgripper1 of robot1 picks up ball7 in room3, from room4, robot1 moves to room5, from room3, robot1's rgripper1 picks up ball5, robot1 moves to room4 from room3, robot1 moves from room1 to room3, in room4, robot1's lgripper1 drops ball2, rgripper1 of robot1 drops ball1 in room4, ball1 is picked from room2 with lgripper1 by robot1, ball6 is dropped in room3 with rgripper1 by robot1, in room5, robot1's rgripper1 drops ball3, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball7 in room4, ball7 is dropped in room5 with rgripper1 by robot1, from room4, robot1's rgripper1 picks up ball5, from room5, robot1's rgripper1 picks up ball3, in room4, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room5 from room3, ball7 is dropped in room3 with rgripper1 by robot1, ball3 is dropped in room5 with lgripper1 by robot1, ball1 is dropped in room5 with rgripper1 by robot1, ball5 is dropped in room1 with lgripper1 by robot1, ball5 is dropped in room4 with rgripper1 by robot1, rgripper1 of robot1 drops ball1 in room2, ball1 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball7 in room3, rgripper1 of robot1 picks up ball3 in room4, rgripper1 of robot1 picks up ball4 in room3, from room1, robot1's lgripper1 picks up ball5, from room3, robot1's lgripper1 picks up ball4, from room5, robot1 moves to room6, ball3 is picked from room3 with rgripper1 by robot1 and ball4 is dropped in room4 with lgripper1 by robot1", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, where it picks up ball1 with lgripper1 and ball3 with rgripper1. Robot1 then moves from room1 to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1 in room5. Finally, robot1 moves from room5 to room2 to reach the current state. In this state, list all actions that cannot be executed. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "f9fc4ac9-8bd6-4bfc-b1b0-00d91371e788", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "ball3 is picked from room1 with rgripper1 by robot1, lgripper1 of robot1 picks up ball3 in room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room3, ball6 is picked from room1 with lgripper1 by robot1, robot1 moves to room4 from room1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room5 from room1, rgripper1 of robot1 picks up ball1 in room1 and robot1 moves to room2 from room1", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: starting from room4, robot1 proceeds to room1 to attain the current state. In this state, enumerate all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is present at room2, ball3 is present in room1, ball4 is located at room2, ball5 is present at room2, ball6 is located in room1, robot1 is located in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is free."}
{"question_id": "2623def1-ced6-4087-bb6f-a2657a37c664", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot1 moves to room1 from room4, robot1 moves to room6 from room4, ball7 is dropped in room4 with lgripper1 by robot1, from room4, robot1 moves to room2, from room4, robot1 moves to room3 and robot1 moves from room4 to room5", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: robot1 uses lgripper1 to pick up ball7 from room4, resulting in the current state. Now, list all possible actions that can be executed in this state. If there are no actions, write None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "8a3ea2f4-368c-405a-ad5e-49e81fcd61d2", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "ball2 is dropped in room1 with rgripper1 by robot2, lgripper2 of robot2 drops ball2 in room1, ball6 is dropped in room3 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, rgripper1 of robot1 picks up ball3 in room1, robot2 moves from room1 to room2, ball2 is picked from room2 with rgripper2 by robot1, rgripper1 of robot2 picks up ball2 in room1, ball3 is picked from room3 with lgripper1 by robot2, ball4 is picked from room1 with rgripper1 by robot1, rgripper1 of robot1 drops ball1 in room1, ball6 is picked from room3 with lgripper1 by robot2, ball1 is picked from room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball2, in room3, robot2's lgripper1 drops ball5, ball5 is dropped in room3 with lgripper2 by robot1, lgripper2 of robot1 drops ball3 in room1, in room2, robot2's lgripper1 drops ball1, lgripper2 of robot2 picks up ball7 in room2, in room2, robot1's lgripper2 drops ball4, ball4 is dropped in room1 with lgripper1 by robot1, ball4 is dropped in room1 with rgripper1 by robot1, ball1 is picked from room3 with lgripper2 by robot1, lgripper1 of robot1 drops ball2 in room1, in room2, robot2's lgripper1 drops ball4, from room1, robot1's rgripper2 picks up ball2, lgripper2 of robot2 drops ball3 in room3, ball7 is picked from room1 with rgripper2 by robot2, lgripper1 of robot2 drops ball5 in room2, ball6 is picked from room3 with rgripper1 by robot1, lgripper2 of robot2 drops ball5 in room2, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot1 picks up ball7 in room1, ball2 is dropped in room2 with lgripper1 by robot1, rgripper2 of robot2 drops ball6 in room3, rgripper1 of robot2 picks up ball3 in room2, robot2 moves from room1 to room3, in room1, robot1's lgripper2 drops ball5, in room1, robot2's rgripper2 drops ball1, ball2 is dropped in room2 with rgripper1 by robot2, ball4 is dropped in room2 with rgripper2 by robot1, from room1, robot2's lgripper2 picks up ball6, in room3, robot2's lgripper2 drops ball4, robot1 moves from room1 to room3, ball6 is picked from room3 with rgripper1 by robot2, ball3 is dropped in room2 with rgripper1 by robot2, in room2, robot1's lgripper2 drops ball6, ball7 is dropped in room1 with rgripper2 by robot1, in room3, robot1's lgripper2 drops ball1, ball3 is dropped in room2 with rgripper1 by robot1, from room1, robot2's lgripper1 picks up ball7, from room1, robot2's rgripper2 picks up ball3, lgripper1 of robot1 drops ball6 in room3, ball5 is picked from room3 with lgripper1 by robot1, lgripper1 of robot2 picks up ball7 in room3, ball5 is picked from room2 with lgripper1 by robot1, ball4 is dropped in room3 with rgripper2 by robot1, lgripper2 of robot1 drops ball4 in room3, in room1, robot1's rgripper2 drops ball4, ball7 is picked from room2 with rgripper2 by robot1, rgripper1 of robot2 picks up ball4 in room3, ball5 is picked from room2 with lgripper2 by robot2, in room3, robot2's rgripper2 drops ball7, ball2 is dropped in room2 with lgripper2 by robot2, from room3, robot1's lgripper2 picks up ball6, rgripper1 of robot1 drops ball5 in room1, from room1, robot1's lgripper2 picks up ball6, in room3, robot1's lgripper1 drops ball1, rgripper2 of robot1 picks up ball4 in room3, robot1 moves from room3 to room2, from room1, robot1's rgripper1 picks up ball7, ball1 is picked from room2 with rgripper2 by robot1, ball7 is picked from room2 with lgripper1 by robot2, rgripper1 of robot1 picks up ball2 in room2, lgripper2 of robot2 drops ball3 in room2, ball5 is dropped in room1 with lgripper1 by robot1, ball1 is picked from room3 with rgripper2 by robot1, ball1 is dropped in room1 with rgripper1 by robot2, lgripper2 of robot2 drops ball1 in room3, ball2 is picked from room1 with lgripper1 by robot1, lgripper1 of robot1 picks up ball6 in room3, lgripper1 of robot1 drops ball7 in room2, in room2, robot2's lgripper1 drops ball2, ball2 is dropped in room2 with lgripper2 by robot1, ball6 is dropped in room3 with rgripper2 by robot1, ball7 is dropped in room1 with lgripper2 by robot1, ball6 is picked from room2 with rgripper1 by robot2, rgripper1 of robot1 drops ball5 in room2, ball1 is dropped in room2 with lgripper2 by robot1, lgripper1 of robot2 picks up ball5 in room3, rgripper1 of robot2 drops ball3 in room3, ball4 is dropped in room2 with rgripper1 by robot2, from room3, robot2's lgripper1 picks up ball4, rgripper1 of robot2 picks up ball4 in room2, ball5 is picked from room1 with lgripper1 by robot2, in room3, robot2's lgripper2 drops ball2, in room2, robot1's lgripper2 drops ball3, ball6 is dropped in room1 with lgripper1 by robot2, in room1, robot2's lgripper1 drops ball7, in room2, robot2's rgripper2 drops ball2, ball7 is picked from room1 with lgripper1 by robot1, from room3, robot1 moves to room1, rgripper1 of robot1 picks up ball4 in room3, from room2, robot1's lgripper2 picks up ball7, ball7 is dropped in room2 with rgripper2 by robot1, from room1, robot1's lgripper2 picks up ball2, rgripper1 of robot2 picks up ball7 in room3, from room3, robot1's rgripper1 picks up ball2, from room2, robot1's rgripper1 picks up ball6, from room3, robot1's rgripper2 picks up ball6, in room3, robot1's lgripper1 drops ball5, from room1, robot2's rgripper2 picks up ball5, ball4 is dropped in room3 with rgripper2 by robot2, lgripper1 of robot1 drops ball2 in room3, ball1 is dropped in room1 with lgripper2 by robot2, ball6 is picked from room1 with rgripper1 by robot2, from room2, robot1's lgripper2 picks up ball5, rgripper2 of robot1 picks up ball6 in room2, in room2, robot1's rgripper2 drops ball6, ball4 is picked from room3 with lgripper2 by robot1, lgripper2 of robot1 drops ball7 in room3, robot1 moves from room1 to room2, rgripper1 of robot2 picks up ball5 in room3, rgripper2 of robot2 drops ball3 in room1, ball5 is dropped in room3 with rgripper2 by robot1, ball1 is picked from room2 with lgripper2 by robot1, ball4 is picked from room1 with lgripper1 by robot2, rgripper2 of robot1 picks up ball3 in room1, rgripper1 of robot1 picks up ball6 in room1, rgripper1 of robot2 picks up ball2 in room3, from room1, robot2's lgripper1 picks up ball6, ball6 is dropped in room2 with rgripper2 by robot2, rgripper1 of robot2 picks up ball1 in room3, in room1, robot2's lgripper1 drops ball3, ball6 is dropped in room1 with lgripper1 by robot1, ball6 is dropped in room2 with rgripper1 by robot1, from room1, robot1's lgripper1 picks up ball5, lgripper1 of robot1 picks up ball2 in room2, lgripper1 of robot2 drops ball5 in room1, ball4 is picked from room2 with lgripper1 by robot1, in room1, robot2's lgripper2 drops ball5, lgripper1 of robot2 picks up ball3 in room1, from room1, robot1's lgripper1 picks up ball3, in room2, robot1's lgripper1 drops ball3, in room2, robot1's lgripper1 drops ball4, ball5 is dropped in room3 with rgripper2 by robot2, ball6 is dropped in room1 with lgripper2 by robot2, ball2 is picked from room1 with rgripper2 by robot2, lgripper1 of robot1 picks up ball3 in room3, from room1, robot2's lgripper2 picks up ball3, in room1, robot1's lgripper2 drops ball2, from room3, robot1's lgripper1 picks up ball1, ball7 is dropped in room1 with lgripper2 by robot2, lgripper1 of robot2 drops ball6 in room2, ball5 is dropped in room2 with rgripper2 by robot2, rgripper1 of robot1 picks up ball5 in room3, lgripper2 of robot2 picks up ball5 in room3, rgripper1 of robot2 drops ball4 in room1, from room2, robot2's lgripper1 picks up ball5, from room2, robot1's lgripper1 picks up ball6, ball5 is picked from room2 with rgripper2 by robot1, from room3, robot2's lgripper2 picks up ball3, rgripper1 of robot2 picks up ball2 in room2, ball4 is dropped in room2 with lgripper2 by robot2, ball1 is picked from room1 with lgripper2 by robot1, ball4 is picked from room2 with rgripper1 by robot1, ball2 is dropped in room2 with rgripper1 by robot1, from room1, robot1's lgripper2 picks up ball3, from room2, robot1's lgripper2 picks up ball2, ball7 is picked from room1 with rgripper2 by robot1, from room2, robot2's lgripper2 picks up ball1, rgripper1 of robot2 picks up ball3 in room3, from room2, robot1's rgripper1 picks up ball1, ball4 is picked from room2 with lgripper1 by robot2, lgripper2 of robot1 picks up ball3 in room3, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot2 drops ball2 in room3, ball1 is dropped in room2 with rgripper2 by robot1, in room2, robot2's lgripper2 drops ball6, in room3, robot1's lgripper2 drops ball2, ball6 is dropped in room2 with lgripper1 by robot1, in room1, robot2's rgripper2 drops ball6, ball4 is picked from room1 with rgripper2 by robot2, lgripper2 of robot2 picks up ball4 in room1, in room3, robot2's rgripper1 drops ball6, rgripper1 of robot1 picks up ball3 in room3, lgripper2 of robot2 picks up ball6 in room3, ball4 is dropped in room1 with rgripper2 by robot2, ball1 is picked from room1 with rgripper2 by robot2, rgripper1 of robot1 picks up ball7 in room2, in room1, robot1's lgripper1 drops ball7, lgripper2 of robot1 drops ball6 in room3, in room3, robot2's rgripper2 drops ball3, ball1 is dropped in room1 with lgripper1 by robot2, ball5 is picked from room1 with lgripper2 by robot1, in room2, robot1's lgripper1 drops ball1, from room1, robot2's lgripper1 picks up ball2, in room1, robot2's rgripper1 drops ball5, ball7 is picked from room3 with lgripper2 by robot1, ball2 is dropped in room3 with lgripper1 by robot2, ball4 is picked from room2 with rgripper2 by robot1, lgripper1 of robot2 picks up ball1 in room2, lgripper2 of robot1 drops ball4 in room1, ball3 is picked from room3 with rgripper2 by robot1, in room2, robot2's rgripper1 drops ball1, rgripper2 of robot1 drops ball6 in room1, from room3, robot1's lgripper1 picks up ball7, ball1 is dropped in room1 with lgripper2 by robot1, in room1, robot2's rgripper1 drops ball6, robot2 moves to room3 from room2, lgripper2 of robot2 drops ball1 in room2, lgripper1 of robot2 drops ball4 in room1, in room2, robot1's lgripper2 drops ball7, from room3, robot1's rgripper1 picks up ball7, ball4 is dropped in room3 with rgripper1 by robot2, rgripper2 of robot2 drops ball7 in room1, ball2 is picked from room3 with rgripper2 by robot1, ball6 is picked from room2 with rgripper2 by robot2, from room1, robot1's rgripper2 picks up ball6, from room1, robot2's rgripper1 picks up ball3, from room2, robot2's lgripper1 picks up ball3, rgripper2 of robot2 picks up ball4 in room2, from room2, robot1's lgripper2 picks up ball6, ball7 is dropped in room3 with lgripper2 by robot2, lgripper1 of robot1 picks up ball4 in room1, rgripper1 of robot1 drops ball2 in room3, in room3, robot1's rgripper1 drops ball6, ball5 is picked from room3 with lgripper2 by robot1, in room2, robot2's rgripper2 drops ball4, ball6 is dropped in room1 with lgripper2 by robot1, lgripper2 of robot2 drops ball5 in room3, ball2 is picked from room3 with lgripper1 by robot1, ball1 is picked from room1 with rgripper2 by robot1, rgripper1 of robot2 picks up ball4 in room1, ball2 is picked from room2 with lgripper2 by robot2, rgripper1 of robot2 drops ball6 in room2, ball1 is picked from room1 with lgripper1 by robot2, from room2, robot1's rgripper2 picks up ball3, ball3 is picked from room3 with rgripper2 by robot2, from room2, robot2's rgripper2 picks up ball5, from room2, robot1's lgripper2 picks up ball4, ball3 is picked from room2 with rgripper2 by robot2, in room3, robot2's lgripper1 drops ball4, lgripper1 of robot1 drops ball3 in room1, from room1, robot2's rgripper1 picks up ball5, ball3 is dropped in room2 with lgripper1 by robot2, from room2, robot1's lgripper1 picks up ball7, rgripper1 of robot1 drops ball7 in room1, in room2, robot1's rgripper1 drops ball4, rgripper2 of robot2 drops ball1 in room3, ball5 is dropped in room2 with rgripper2 by robot1, in room2, robot2's lgripper2 drops ball7, rgripper2 of robot2 drops ball1 in room2, rgripper1 of robot1 drops ball2 in room1, ball1 is picked from room2 with rgripper1 by robot2, lgripper1 of robot1 picks up ball6 in room1, ball3 is dropped in room1 with lgripper2 by robot2, in room2, robot1's lgripper1 drops ball5, ball1 is dropped in room3 with rgripper1 by robot1, from room1, robot2's rgripper1 picks up ball1, ball5 is picked from room2 with rgripper1 by robot2, rgripper2 of robot1 drops ball5 in room1, ball3 is dropped in room2 with rgripper2 by robot1, in room2, robot2's lgripper1 drops ball7, from room2, robot2's rgripper2 picks up ball1, ball5 is picked from room1 with rgripper1 by robot1, in room1, robot2's rgripper1 drops ball3, lgripper1 of robot2 picks up ball6 in room2, from room1, robot1's rgripper2 picks up ball4, rgripper2 of robot2 drops ball7 in room2, ball7 is picked from room2 with rgripper2 by robot2, from room3, robot1's rgripper1 picks up ball1, ball4 is picked from room1 with lgripper2 by robot1, lgripper2 of robot2 picks up ball2 in room1, in room2, robot1's rgripper2 drops ball2, in room1, robot2's rgripper1 drops ball7, rgripper2 of robot2 drops ball2 in room1, ball5 is dropped in room1 with rgripper2 by robot2, in room3, robot1's rgripper1 drops ball3, in room3, robot1's lgripper1 drops ball4, from room3, robot2's lgripper1 picks up ball1, in room3, robot2's rgripper1 drops ball7, rgripper2 of robot1 picks up ball5 in room1, ball1 is picked from room1 with lgripper2 by robot2, lgripper1 of robot2 picks up ball2 in room2, ball5 is dropped in room3 with rgripper1 by robot2, lgripper2 of robot2 picks up ball4 in room2, ball3 is picked from room2 with lgripper2 by robot1, rgripper2 of robot2 drops ball2 in room3, rgripper2 of robot1 drops ball3 in room3, lgripper1 of robot2 drops ball1 in room3, in room1, robot1's rgripper1 drops ball6, ball2 is picked from room2 with rgripper2 by robot2, rgripper2 of robot1 drops ball1 in room1, in room2, robot2's rgripper1 drops ball5, ball5 is picked from room3 with rgripper2 by robot1, in room1, robot1's rgripper2 drops ball3, ball7 is dropped in room2 with rgripper1 by robot1, lgripper1 of robot2 drops ball6 in room3, from room3, robot1's lgripper2 picks up ball2, rgripper2 of robot2 drops ball3 in room2, in room3, robot1's rgripper2 drops ball2, from room2, robot1's rgripper1 picks up ball5, ball2 is dropped in room1 with rgripper2 by robot1, ball2 is dropped in room1 with lgripper1 by robot2, in room3, robot1's rgripper2 drops ball7, ball3 is dropped in room1 with rgripper1 by robot1, in room2, robot1's rgripper1 drops ball1, in room3, robot1's rgripper1 drops ball7, ball1 is dropped in room1 with lgripper1 by robot1, from room3, robot2's rgripper2 picks up ball5, in room2, robot2's rgripper1 drops ball7, from room1, robot2's rgripper2 picks up ball6, from room2, robot2's lgripper2 picks up ball3, lgripper1 of robot1 drops ball3 in room3, from room2, robot1's lgripper1 picks up ball1, in room2, robot1's lgripper2 drops ball5, in room3, robot1's lgripper1 drops ball7, rgripper1 of robot2 picks up ball7 in room2, lgripper2 of robot1 drops ball3 in room3, lgripper1 of robot2 picks up ball2 in room3, lgripper1 of robot1 picks up ball4 in room3, ball1 is dropped in room3 with rgripper1 by robot2, from room3, robot2's rgripper2 picks up ball6, robot2 moves from room2 to room1, from room1, robot2's rgripper1 picks up ball7, rgripper2 of robot1 picks up ball7 in room3, ball6 is picked from room2 with lgripper2 by robot2, ball5 is dropped in room3 with rgripper1 by robot1, ball3 is dropped in room3 with lgripper1 by robot2, ball7 is picked from room1 with lgripper2 by robot2, rgripper2 of robot1 drops ball1 in room3, lgripper1 of robot2 drops ball7 in room3 and in room3, robot1's rgripper1 drops ball4", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: robot2 transitions from room2 to room3 to achieve the current state. In this state, identify all actions that cannot be executed. If there are no such actions, indicate None.", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is present in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "ba658296-a2d9-4cb4-a626-281d4d78784e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2 and robot2 moves from room3 to room2 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "ball2 is dropped in room1 with rgripper1 by robot2, lgripper2 of robot2 drops ball2 in room1, ball6 is dropped in room3 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, from room1, robot1's rgripper1 picks up ball3, from room1, robot2 moves to room2, rgripper2 of robot2 picks up ball1 in room3, from room2, robot1's rgripper2 picks up ball2, from room1, robot2's rgripper1 picks up ball2, from room3, robot2's lgripper1 picks up ball3, from room1, robot1's rgripper1 picks up ball4, rgripper1 of robot1 drops ball1 in room1, ball6 is picked from room3 with lgripper1 by robot2, ball1 is picked from room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball2, ball5 is dropped in room3 with lgripper1 by robot2, in room3, robot1's lgripper2 drops ball5, in room1, robot1's lgripper2 drops ball3, ball1 is dropped in room2 with lgripper1 by robot2, lgripper2 of robot2 picks up ball7 in room2, in room2, robot1's lgripper2 drops ball4, lgripper1 of robot1 drops ball4 in room1, ball4 is dropped in room1 with rgripper1 by robot1, ball1 is picked from room3 with lgripper2 by robot1, in room1, robot1's lgripper1 drops ball2, lgripper1 of robot2 drops ball4 in room2, from room1, robot1's rgripper2 picks up ball2, in room3, robot2's lgripper2 drops ball3, from room1, robot2's rgripper2 picks up ball7, lgripper1 of robot2 drops ball5 in room2, rgripper1 of robot1 picks up ball6 in room3, in room2, robot2's lgripper2 drops ball5, lgripper2 of robot2 drops ball4 in room1, ball7 is picked from room1 with lgripper2 by robot1, lgripper1 of robot1 drops ball2 in room2, in room3, robot2's rgripper2 drops ball6, rgripper1 of robot2 picks up ball3 in room2, from room1, robot2 moves to room3, lgripper2 of robot1 drops ball5 in room1, ball1 is dropped in room1 with rgripper2 by robot2, ball2 is dropped in room2 with rgripper1 by robot2, ball4 is dropped in room2 with rgripper2 by robot1, from room1, robot2's lgripper2 picks up ball6, in room3, robot2's lgripper2 drops ball4, robot1 moves to room3 from room1, ball6 is picked from room3 with rgripper1 by robot2, rgripper1 of robot2 drops ball3 in room2, ball6 is dropped in room2 with lgripper2 by robot1, rgripper2 of robot1 drops ball7 in room1, in room3, robot1's lgripper2 drops ball1, rgripper1 of robot1 drops ball3 in room2, ball7 is picked from room1 with lgripper1 by robot2, rgripper2 of robot2 picks up ball3 in room1, in room3, robot1's lgripper1 drops ball6, lgripper1 of robot1 picks up ball5 in room3, lgripper1 of robot2 picks up ball7 in room3, from room2, robot1's lgripper1 picks up ball5, ball4 is dropped in room3 with rgripper2 by robot1, in room3, robot1's lgripper2 drops ball4, rgripper2 of robot1 drops ball4 in room1, ball7 is picked from room2 with rgripper2 by robot1, from room3, robot2's rgripper1 picks up ball4, lgripper2 of robot2 picks up ball5 in room2, rgripper2 of robot2 drops ball7 in room3, in room2, robot2's lgripper2 drops ball2, lgripper2 of robot1 picks up ball6 in room3, ball5 is dropped in room1 with rgripper1 by robot1, from room1, robot1's lgripper2 picks up ball6, in room3, robot1's lgripper1 drops ball1, from room3, robot1's rgripper2 picks up ball4, from room3, robot1 moves to room2, ball7 is picked from room1 with rgripper1 by robot1, rgripper2 of robot1 picks up ball1 in room2, ball7 is picked from room2 with lgripper1 by robot2, ball3 is dropped in room2 with lgripper2 by robot2, ball5 is dropped in room1 with lgripper1 by robot1, from room3, robot1's rgripper2 picks up ball1, rgripper1 of robot2 drops ball1 in room1, lgripper2 of robot2 drops ball1 in room3, from room1, robot1's lgripper1 picks up ball2, from room3, robot1's lgripper1 picks up ball6, ball7 is dropped in room2 with lgripper1 by robot1, lgripper1 of robot2 drops ball2 in room2, ball2 is dropped in room2 with lgripper2 by robot1, rgripper2 of robot1 drops ball6 in room3, rgripper2 of robot2 picks up ball4 in room3, lgripper2 of robot1 drops ball7 in room1, from room2, robot2's rgripper1 picks up ball6, in room2, robot1's rgripper1 drops ball5, lgripper2 of robot2 picks up ball7 in room3, lgripper2 of robot1 drops ball1 in room2, ball5 is picked from room3 with lgripper1 by robot2, rgripper1 of robot2 drops ball3 in room3, rgripper1 of robot2 drops ball4 in room2, from room3, robot2's lgripper1 picks up ball4, rgripper1 of robot2 picks up ball4 in room2, lgripper1 of robot2 picks up ball5 in room1, ball2 is dropped in room3 with lgripper2 by robot2, lgripper2 of robot1 drops ball3 in room2, in room1, robot2's lgripper1 drops ball6, in room1, robot2's lgripper1 drops ball7, rgripper2 of robot2 drops ball2 in room2, lgripper1 of robot1 picks up ball7 in room1, from room3, robot1 moves to room1, rgripper1 of robot1 picks up ball4 in room3, lgripper2 of robot1 picks up ball7 in room2, ball7 is dropped in room2 with rgripper2 by robot1, ball2 is picked from room1 with lgripper2 by robot1, from room3, robot2's rgripper1 picks up ball7, from room3, robot1's rgripper1 picks up ball2, rgripper1 of robot1 picks up ball6 in room2, ball6 is picked from room3 with rgripper2 by robot1, in room3, robot1's lgripper1 drops ball5, from room1, robot2's rgripper2 picks up ball5, in room3, robot2's rgripper2 drops ball4, in room3, robot1's lgripper1 drops ball2, in room1, robot2's lgripper2 drops ball1, ball6 is picked from room1 with rgripper1 by robot2, lgripper2 of robot1 picks up ball5 in room2, from room2, robot1's rgripper2 picks up ball6, in room2, robot1's rgripper2 drops ball6, lgripper2 of robot1 picks up ball4 in room3, in room3, robot1's lgripper2 drops ball7, robot1 moves to room2 from room1, ball5 is picked from room3 with rgripper1 by robot2, ball3 is dropped in room1 with rgripper2 by robot2, in room3, robot1's rgripper2 drops ball5, from room2, robot1's lgripper2 picks up ball1, ball4 is picked from room1 with lgripper1 by robot2, from room1, robot1's rgripper2 picks up ball3, rgripper1 of robot1 picks up ball6 in room1, ball2 is picked from room3 with rgripper1 by robot2, lgripper1 of robot2 picks up ball6 in room1, in room2, robot2's rgripper2 drops ball6, from room3, robot2's rgripper1 picks up ball1, lgripper1 of robot2 drops ball3 in room1, in room1, robot1's lgripper1 drops ball6, rgripper1 of robot1 drops ball6 in room2, from room1, robot1's lgripper1 picks up ball5, ball5 is dropped in room1 with lgripper1 by robot2, lgripper1 of robot1 picks up ball4 in room2, lgripper2 of robot2 drops ball5 in room1, ball3 is picked from room1 with lgripper1 by robot2, lgripper1 of robot1 picks up ball3 in room1, ball3 is dropped in room2 with lgripper1 by robot1, lgripper1 of robot1 drops ball4 in room2, rgripper2 of robot2 drops ball5 in room3, ball6 is dropped in room1 with lgripper2 by robot2, ball2 is picked from room1 with rgripper2 by robot2, from room3, robot1's lgripper1 picks up ball3, ball3 is picked from room1 with lgripper2 by robot2, ball2 is dropped in room1 with lgripper2 by robot1, ball1 is picked from room3 with lgripper1 by robot1, lgripper2 of robot2 drops ball7 in room1, ball6 is dropped in room2 with lgripper1 by robot2, ball5 is dropped in room2 with rgripper2 by robot2, from room3, robot1's rgripper1 picks up ball5, lgripper2 of robot2 picks up ball5 in room3, in room1, robot2's rgripper1 drops ball4, lgripper1 of robot2 picks up ball5 in room2, lgripper1 of robot1 picks up ball6 in room2, from room2, robot1's rgripper2 picks up ball5, ball3 is picked from room3 with lgripper2 by robot2, ball4 is picked from room3 with lgripper2 by robot2, from room2, robot2's rgripper1 picks up ball2, lgripper2 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball4 in room2, in room2, robot1's rgripper1 drops ball2, from room1, robot1's lgripper2 picks up ball3, lgripper2 of robot1 picks up ball2 in room2, from room1, robot1's rgripper2 picks up ball7, lgripper2 of robot2 picks up ball1 in room2, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper1 picks up ball3, lgripper1 of robot2 picks up ball4 in room2, ball3 is picked from room3 with lgripper2 by robot1, from room1, robot1's lgripper1 picks up ball1, ball2 is dropped in room3 with rgripper1 by robot2, ball1 is dropped in room2 with rgripper2 by robot1, lgripper2 of robot2 drops ball6 in room2, in room3, robot1's lgripper2 drops ball2, ball6 is dropped in room2 with lgripper1 by robot1, ball6 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball4, from room1, robot2's lgripper2 picks up ball4, in room3, robot2's rgripper1 drops ball6, from room3, robot1's rgripper1 picks up ball3, ball6 is picked from room3 with lgripper2 by robot2, in room1, robot2's rgripper2 drops ball4, ball1 is picked from room1 with rgripper2 by robot2, rgripper1 of robot1 picks up ball7 in room2, from room3, robot2's rgripper2 picks up ball7, ball2 is picked from room3 with lgripper2 by robot2, in room1, robot1's lgripper1 drops ball7, lgripper2 of robot1 drops ball6 in room3, ball3 is dropped in room3 with rgripper2 by robot2, in room1, robot2's lgripper1 drops ball1, ball5 is picked from room1 with lgripper2 by robot1, in room2, robot1's lgripper1 drops ball1, ball2 is picked from room1 with lgripper1 by robot2, rgripper1 of robot2 drops ball5 in room1, ball7 is picked from room3 with lgripper2 by robot1, ball2 is dropped in room3 with lgripper1 by robot2, from room2, robot1's rgripper2 picks up ball4, lgripper1 of robot2 picks up ball1 in room2, in room1, robot1's lgripper2 drops ball4, rgripper2 of robot1 picks up ball3 in room3, in room2, robot2's rgripper1 drops ball1, rgripper2 of robot1 drops ball6 in room1, ball7 is picked from room3 with lgripper1 by robot1, in room1, robot1's lgripper2 drops ball1, ball6 is dropped in room1 with rgripper1 by robot2, lgripper2 of robot2 drops ball1 in room2, ball4 is dropped in room1 with lgripper1 by robot2, lgripper2 of robot1 drops ball7 in room2, rgripper1 of robot1 picks up ball7 in room3, in room3, robot2's rgripper1 drops ball4, ball7 is dropped in room1 with rgripper2 by robot2, from room3, robot1's rgripper2 picks up ball2, rgripper2 of robot2 picks up ball6 in room2, ball6 is picked from room1 with rgripper2 by robot1, rgripper1 of robot2 picks up ball3 in room1, ball3 is picked from room2 with lgripper1 by robot2, from room2, robot2's rgripper2 picks up ball4, from room2, robot1's lgripper2 picks up ball6, lgripper2 of robot2 drops ball7 in room3, ball4 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 drops ball2 in room3, ball6 is dropped in room3 with rgripper1 by robot1, lgripper2 of robot1 picks up ball5 in room3, rgripper2 of robot2 drops ball4 in room2, ball6 is dropped in room1 with lgripper2 by robot1, lgripper2 of robot2 drops ball5 in room3, from room3, robot1's lgripper1 picks up ball2, rgripper2 of robot1 picks up ball1 in room1, rgripper1 of robot2 picks up ball4 in room1, from room2, robot2's lgripper2 picks up ball2, in room2, robot2's rgripper1 drops ball6, ball1 is picked from room1 with lgripper1 by robot2, from room2, robot1's rgripper2 picks up ball3, rgripper2 of robot2 picks up ball3 in room3, from room2, robot2's rgripper2 picks up ball5, ball4 is picked from room2 with lgripper2 by robot1, rgripper2 of robot2 picks up ball3 in room2, lgripper1 of robot2 drops ball4 in room3, lgripper1 of robot1 drops ball3 in room1, ball5 is picked from room1 with rgripper1 by robot2, lgripper1 of robot2 drops ball3 in room2, from room2, robot1's lgripper1 picks up ball7, rgripper1 of robot1 drops ball7 in room1, in room2, robot1's rgripper1 drops ball4, ball1 is dropped in room3 with rgripper2 by robot2, rgripper2 of robot1 drops ball5 in room2, ball7 is dropped in room2 with lgripper2 by robot2, ball1 is dropped in room2 with rgripper2 by robot2, in room1, robot1's rgripper1 drops ball2, rgripper1 of robot2 picks up ball1 in room2, lgripper1 of robot1 picks up ball6 in room1, in room1, robot2's lgripper2 drops ball3, in room2, robot1's lgripper1 drops ball5, ball1 is dropped in room3 with rgripper1 by robot1, ball1 is picked from room1 with rgripper1 by robot2, from room2, robot2's rgripper1 picks up ball5, in room1, robot1's rgripper2 drops ball5, in room2, robot1's rgripper2 drops ball3, in room2, robot2's lgripper1 drops ball7, ball1 is picked from room2 with rgripper2 by robot2, ball5 is picked from room1 with rgripper1 by robot1, ball3 is dropped in room1 with rgripper1 by robot2, lgripper1 of robot2 picks up ball6 in room2, from room3, robot2 moves to room2, rgripper2 of robot1 picks up ball4 in room1, from room2, robot2's rgripper2 picks up ball7, ball1 is picked from room3 with rgripper1 by robot1, ball4 is picked from room1 with lgripper2 by robot1, ball2 is picked from room1 with lgripper2 by robot2, in room2, robot1's rgripper2 drops ball2, rgripper1 of robot2 drops ball7 in room1, ball2 is dropped in room1 with rgripper2 by robot2, rgripper2 of robot2 drops ball5 in room1, rgripper1 of robot1 drops ball3 in room3, lgripper1 of robot1 drops ball4 in room3, from room3, robot2's lgripper1 picks up ball1, ball7 is dropped in room3 with rgripper1 by robot2, from room1, robot1's rgripper2 picks up ball5, lgripper2 of robot2 picks up ball1 in room1, lgripper1 of robot2 picks up ball2 in room2, rgripper1 of robot2 drops ball5 in room3, lgripper2 of robot2 picks up ball4 in room2, from room2, robot1's lgripper2 picks up ball3, in room3, robot2's rgripper2 drops ball2, ball3 is dropped in room3 with rgripper2 by robot1, ball1 is dropped in room3 with lgripper1 by robot2, ball6 is dropped in room1 with rgripper1 by robot1, ball2 is picked from room2 with rgripper2 by robot2, rgripper2 of robot1 drops ball1 in room1, rgripper1 of robot2 drops ball5 in room2, from room3, robot1's rgripper2 picks up ball5, rgripper2 of robot1 drops ball3 in room1, in room2, robot1's rgripper1 drops ball7, ball6 is dropped in room3 with lgripper1 by robot2, from room3, robot1's lgripper2 picks up ball2, in room2, robot2's rgripper2 drops ball3, rgripper2 of robot1 drops ball2 in room3, ball5 is picked from room2 with rgripper1 by robot1, in room1, robot1's rgripper2 drops ball2, lgripper1 of robot2 drops ball2 in room1, in room3, robot1's rgripper2 drops ball7, in room1, robot1's rgripper1 drops ball3, ball1 is dropped in room2 with rgripper1 by robot1, rgripper1 of robot1 drops ball7 in room3, lgripper1 of robot1 drops ball1 in room1, ball5 is picked from room3 with rgripper2 by robot2, rgripper1 of robot2 drops ball7 in room2, from room1, robot2's rgripper2 picks up ball6, lgripper2 of robot2 picks up ball3 in room2, in room3, robot1's lgripper1 drops ball3, in room2, robot1's lgripper2 drops ball5, rgripper2 of robot2 picks up ball2 in room3, in room3, robot1's lgripper1 drops ball7, rgripper1 of robot2 picks up ball7 in room2, ball3 is dropped in room3 with lgripper2 by robot1, lgripper1 of robot2 picks up ball2 in room3, from room3, robot1's lgripper1 picks up ball4, robot2 moves to room1 from room3, rgripper1 of robot2 drops ball1 in room3, ball6 is picked from room3 with rgripper2 by robot2, from room1, robot2's rgripper1 picks up ball7, rgripper2 of robot1 picks up ball7 in room3, lgripper2 of robot2 picks up ball6 in room2, in room3, robot1's rgripper1 drops ball5, ball3 is dropped in room3 with lgripper1 by robot2, from room1, robot2's lgripper2 picks up ball7, rgripper2 of robot1 drops ball1 in room3, ball7 is dropped in room3 with lgripper1 by robot2 and ball4 is dropped in room3 with rgripper1 by robot1", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot2 moves from room2 to room3, robot2's left gripper (lgripper2) picks up ball1 in room3, robot2's right gripper (rgripper2) picks up ball2 in room3, robot2 moves from room3 to room2, robot2's lgripper2 drops ball1 in room2, robot2's rgripper2 drops ball2 in room2, robot2 moves from room2 to room3, robot2's lgripper2 picks up ball4 in room3, robot2's rgripper2 picks up ball7 in room3, and robot2 moves from room3 to room2 to reach the current state. In this state, list all actions that cannot be executed. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, robot1 has both lgripper1 and rgripper1 available, robot2 is in room2, and robot2 has both lgripper2 and rgripper2 available."}
{"question_id": "ffcb2883-18d6-48f2-b8bd-413880ffd6d3", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: in room5, robot1's lgripper1 drops ball3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "ball3 is dropped in room5 with lgripper1 by robot1", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: in room5, robot1's lgripper1 releases ball3 to achieve the current state. What is the first action in the sequence that cannot be executed? If there are none, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "81c3c527-01f0-4aaa-a2a9-6c35214b59a7", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves to room5 from room4, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room3, robot1 moves to room3 from room6, ball5 is picked from room3 with rgripper1 by robot1 and robot1 moves to room6 from room3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from room6, robot1 moves to room3", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from room4, robot1 uses its left gripper (lgripper1) to pick up ball7, then moves to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves to room1, picks up ball1 with lgripper1, and then uses its right gripper (rgripper1) to pick up ball3. Robot1 then moves to room5, drops ball1 using lgripper1, and drops ball3 using rgripper1. After that, robot1 moves to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, then moves to room1, drops ball4 using rgripper1, and picks up ball6 using rgripper1. Finally, robot1 moves to room3, then to room6, picks up ball5 with rgripper1, and moves to room6 again to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "b5974cf2-ec74-4222-b9c8-7ed619b590ac", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, ball2 is picked from room1 with rgripper1 by robot1, from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2 and robot2 moves from room3 to room2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from room1, robot1's rgripper1 picks up ball2", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot2 relocates from room2 to room3, then robot2's left gripper (lgripper2) grasps ball1 in room3, and its right gripper (rgripper2) picks up ball2 in room3. Next, robot2 moves back to room2, drops ball1 in room2 using lgripper2, and robot1's right gripper (rgripper1) picks up ball2 in room1. Subsequently, robot2 moves from room2 to room3 again, where lgripper2 of robot2 picks up ball4, and rgripper2 of robot2 picks up ball7, before robot2 returns to room2, resulting in the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "a9d613e0-54a1-448f-818e-283cb51c6d94", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from room1, robot1 moves to room4, from room1, robot1 moves to room2, rgripper1 of robot1 drops ball6 in room1, from room1, robot1 moves to room5, robot1 moves to room3 from room1 and in room1, robot1's lgripper1 drops ball1", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 moves from room1 to room5 and drops ball3 in room5 using rgripper1. From room5, robot1 proceeds to room2, where it picks up ball4 with rgripper1. Then, robot1 moves back to room1 from room2, drops ball4 in room1 using rgripper1, and finally picks up ball6 in room1 with rgripper1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is positioned in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "9cbff81c-aef1-41db-89c4-f88add6b5c1e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room1, ball2 is picked from room2 with lgripper1 by robot1, in room2, robot2's lgripper2 drops ball4, rgripper1 of robot1 picks up ball1 in room2, robot1 moves to room3 from room2, from room2, robot2 moves to room3, ball3 is picked from room2 with rgripper1 by robot1, rgripper2 of robot2 drops ball7 in room2, lgripper1 of robot1 picks up ball1 in room2, robot2 moves to room1 from room2 and lgripper1 of robot1 picks up ball3 in room2", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then it uses lgripper2 to pick up ball1 and rgripper2 to pick up ball2, both in room3. Next, robot2 returns to room2 from room3, and in room2, it drops ball1 with lgripper2 and ball2 with rgripper2. After that, robot2 moves back to room3 from room2, picks up ball4 with lgripper2 and ball7 with rgripper2, and finally moves back to room2 to reach the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "2b56939d-0edb-4517-ab91-4b667c80147b", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball4 is picked from room1 with lgripper1 by robot2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "ball4 is picked from room1 with lgripper1 by robot2", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 uses lgripper1 to pick ball4 from room1 to achieve the current state. What is the first action in the sequence that cannot be executed? If there are no such actions, write None.", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, robot1 has both lgripper1 and rgripper1 available, robot2 is in room2, and robot2 has both lgripper2 and rgripper2 available."}
{"question_id": "13d4c7ba-d07d-460b-a88b-c8352c0b0e46", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, from room2, robot1's rgripper1 picks up ball2, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, ball2 is dropped in room4 with rgripper1 by robot1, from room4, robot1 moves to room2, ball4 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room2, from room2, robot1 moves to room5, ball4 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball3 in room1, ball5 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot1 moves to room2 from room5, robot1 moves to room3 from room5, in room5, robot1's rgripper1 drops ball6, lgripper1 of robot1 picks up ball3 in room5, ball4 is picked from room5 with lgripper1 by robot1, robot1 moves to room4 from room5 and from room5, robot1 moves to room1", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then picks up ball1 in room1 using lgripper1, proceeds to room2, and uses rgripper1 to pick up ball2. Next, robot1 moves to room3, drops ball1 in room3 with lgripper1, and then returns to room4, where it drops ball2 with rgripper1. From room4, robot1 moves to room2, picks up ball4 with lgripper1, and also picks up ball5 with rgripper1 in room2. Then, robot1 moves to room5, drops ball4 with lgripper1, and returns to room1, where it picks up ball3 with lgripper1 and drops ball5 with rgripper1. Additionally, robot1 picks up ball6 in room1 with rgripper1, moves to room5, and finally drops ball3 in room5 using lgripper1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is in room1, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "6754b444-12e3-45c5-9143-665d6dad7707", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1 moves to room2, rgripper1 of robot1 drops ball5 in room5, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball4 in room2, rgripper1 of robot1 picks up ball5 in room2, robot1 moves to room5 from room2, in room5, robot1's lgripper1 drops ball4, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball3, in room1, robot1's rgripper1 drops ball5, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room5 from room1 and in room5, robot1's lgripper1 drops ball3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "rgripper1 of robot1 drops ball5 in room5", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1, then moves to room2, but instead of dropping ball1, it incorrectly drops ball5 in room5, then moves to room3, where it correctly drops ball1 using lgripper1, and continues to room4, where it drops ball2 using rgripper1, then moves to room2, picks up ball4 with lgripper1 and ball5 with rgripper1, moves to room5, drops ball4 with lgripper1, moves to room1, picks up ball3 with lgripper1, drops ball5 with rgripper1, picks up ball6 with rgripper1, moves to room5, and finally drops ball3 with lgripper1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "3b872a19-63ab-41e2-9682-25376b523ec2", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, ball7 is picked from room3 with rgripper1 by robot1, from room3, robot1 moves to room4, lgripper1 of robot1 drops ball1 in room4, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, from room2, robot1's lgripper1 picks up ball2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "ball2 is dropped in room2 with lgripper1 by robot1, from room2, robot1 moves to room4, in room2, robot1's rgripper1 drops ball5, robot1 moves to room3 from room2, robot1 moves to room1 from room2 and from room2, robot1 moves to room5", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then uses its lgripper1 to pick up ball1 in room1, and uses its rgripper1 to pick up ball3 in room1. Next, robot1 moves from room1 to room5, drops ball3 in room5 using its rgripper1, and then moves to room2. In room2, robot1 uses its rgripper1 to pick up ball4, moves to room1, and drops ball4 in room1 using its rgripper1. Robot1 then uses its rgripper1 to pick up ball6 in room1, moves to room3, drops ball6 in room3, and picks up ball7 in room3 using its rgripper1. After moving to room4, robot1 drops ball1 and ball7 using its lgripper1 and rgripper1, respectively. Then, robot1 moves to room2 and picks up ball2 with its lgripper1 and ball5 with its rgripper1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
