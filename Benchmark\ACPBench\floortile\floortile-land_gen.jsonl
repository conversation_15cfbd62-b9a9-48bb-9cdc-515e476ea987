{"id": 2708267893979694827, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_14 is to the right of tile_13, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, and tile_20 is to the right of tile_19. Further, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_1 is down from tile_6, tile_6 is down from tile_11, tile_4 is down from tile_9, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_2 is down from tile_7, tile_15 is down from tile_20, and tile_9 is down from tile_14 Currently, robot robot2 is at tile_12 and holding color black and robot robot1 is at tile_7 and holding color white; tile_5, tile_17, tile_2, tile_1, tile_11, tile_6, tile_3, and tile_4 are clear; tile_13 is painted black, tile_14 is painted white, tile_16 is painted white, tile_19 is painted black, tile_9 is painted black, tile_8 is painted white, tile_20 is painted white, tile_15 is painted black, tile_18 is painted white, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_13 is painted in black color, Tile tile_14 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_6 is painted in white color, Tile tile_20 is painted in white color, Tile tile_11 is painted in black color, Tile tile_18 is painted in white color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(painted tile_16 black)", "(robot-at robot2 tile_2)", "(painted tile_11 white)", "(clear tile_19)", "(painted tile_19 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(painted tile_5 black)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(painted tile_1 white)", "(clear tile_16)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(robot-at robot1 tile_18)", "(robot-at robot1 tile_16)", "(clear tile_15)", "(painted tile_3 black)", "(robot-at robot2 tile_19)", "(painted tile_12 black)", "(robot-at robot2 tile_14)", "(robot-at robot2 tile_9)", "(robot-at robot2 tile_18)", "(robot-at robot2 tile_16)", "(robot-at robot2 tile_15)", "(robot-at robot1 tile_19)", "(robot-at robot1 tile_13)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(robot-at robot1 tile_10)", "(painted tile_2 black)", "(painted tile_17 white)", "(clear tile_13)", "(clear tile_9)", "(painted tile_10 black)", "(robot-at robot1 tile_20)", "(painted tile_20 black)", "(painted tile_14 black)", "(robot-at robot2 tile_8)", "(robot-at robot1 tile_9)", "(painted tile_4 black)", "(painted tile_15 white)", "(robot-at robot2 tile_13)", "(painted tile_6 black)", "(clear tile_20)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_17)", "(robot-at robot1 tile_15)", "(clear tile_14)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_14)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_12)", "(painted tile_4 white)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_20)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_3)", "(clear tile_18)", "(robot-at robot2 tile_5)", "(painted tile_18 black)", "(painted tile_5 white)", "(robot-at robot1 tile_6)", "(robot-at robot2 tile_17)", "(painted tile_7 white)", "(painted tile_9 white)", "(painted tile_13 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_10)"], "yes": ["(clear tile_12)", "(clear tile_7)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_17) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_8 white) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_7) (robot-at robot2 tile_12) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 8815486642783195504, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_14 is to the right of tile_13, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, and tile_20 is to the right of tile_19. Further, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_1 is down from tile_6, tile_6 is down from tile_11, tile_4 is down from tile_9, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_2 is down from tile_7, tile_15 is down from tile_20, and tile_9 is down from tile_14 Currently, robot robot2 is at tile_2 and holding color black, robot robot1 is at tile_1 and holding color white, and robot robot3 is at tile_3 and holding color black; tile_8, tile_5, tile_11, tile_6, tile_4, and tile_13 are clear; tile_17 is painted black, tile_14 is painted white, tile_16 is painted white, tile_19 is painted black, tile_9 is painted black, tile_7 is painted black, tile_20 is painted white, tile_15 is painted black, tile_12 is painted white, tile_18 is painted white, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_13 is painted in black color, Tile tile_14 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_6 is painted in white color, Tile tile_20 is painted in white color, Tile tile_11 is painted in black color, Tile tile_18 is painted in white color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot3 tile_10)", "(painted tile_11 white)", "(painted tile_19 white)", "(robot-at robot2 tile_4)", "(robot-at robot3 tile_4)", "(painted tile_3 black)", "(robot-at robot2 tile_19)", "(clear tile_12)", "(robot-at robot3 tile_15)", "(robot-at robot2 tile_14)", "(robot-at robot1 tile_10)", "(clear tile_9)", "(painted tile_10 black)", "(painted tile_6 black)", "(robot-at robot1 tile_14)", "(painted tile_4 white)", "(robot-at robot2 tile_11)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(robot-at robot1 tile_2)", "(clear tile_17)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot3 tile_2)", "(robot-at robot1 tile_18)", "(clear tile_15)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(robot-at robot2 tile_18)", "(robot-at robot2 tile_15)", "(robot-at robot3 tile_20)", "(robot-at robot1 tile_20)", "(painted tile_20 black)", "(robot-at robot2 tile_8)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_17)", "(clear tile_14)", "(robot-at robot3 tile_16)", "(robot-at robot1 tile_12)", "(robot-at robot2 tile_20)", "(robot-at robot3 tile_17)", "(painted tile_7 white)", "(painted tile_9 white)", "(clear tile_2)", "(robot-at robot3 tile_11)", "(robot-at robot3 tile_18)", "(robot-has robot2 white)", "(robot-has robot3 white)", "(robot-at robot3 tile_6)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(robot-at robot3 tile_13)", "(robot-at robot3 tile_8)", "(robot-at robot1 tile_19)", "(painted tile_2 black)", "(painted tile_17 white)", "(painted tile_14 black)", "(robot-at robot1 tile_9)", "(clear tile_20)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot2 tile_10)", "(clear tile_18)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-at robot2 tile_12)", "(painted tile_13 white)", "(clear tile_10)", "(painted tile_16 black)", "(clear tile_19)", "(robot-at robot3 tile_14)", "(painted tile_5 black)", "(clear tile_7)", "(robot-at robot3 tile_5)", "(robot-at robot3 tile_12)", "(robot-at robot3 tile_19)", "(robot-at robot3 tile_1)", "(clear tile_16)", "(robot-has robot1 black)", "(robot-at robot2 tile_16)", "(robot-at robot1 tile_13)", "(painted tile_3 white)", "(painted tile_4 black)", "(robot-at robot3 tile_9)", "(painted tile_15 white)", "(robot-at robot2 tile_13)", "(robot-at robot1 tile_15)", "(robot-at robot1 tile_8)", "(painted tile_18 black)", "(robot-at robot1 tile_6)", "(robot-at robot2 tile_17)", "(robot-at robot1 tile_16)", "(robot-at robot3 tile_7)"], "yes": ["(clear tile_1)", "(clear tile_3)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_11) (clear tile_13) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_8) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_12 white) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_7 black) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_2) (robot-at robot3 tile_3) (robot-has robot1 white) (robot-has robot2 black) (robot-has robot3 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 6124093827168307835, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_14 is to the right of tile_13, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, and tile_20 is to the right of tile_19. Further, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_1 is down from tile_6, tile_6 is down from tile_11, tile_4 is down from tile_9, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_2 is down from tile_7, tile_15 is down from tile_20, and tile_9 is down from tile_14 Currently, robot robot2 is at tile_3 and holding color black and robot robot1 is at tile_2 and holding color black; tile_8, tile_5, tile_7, tile_1, tile_4, tile_9, and tile_14 are clear; tile_17 is painted black, tile_13 is painted black, tile_16 is painted white, tile_19 is painted black, tile_6 is painted white, tile_11 is painted black, tile_20 is painted white, tile_15 is painted black, tile_12 is painted white, tile_18 is painted white, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_13 is painted in black color, Tile tile_14 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_6 is painted in white color, Tile tile_20 is painted in white color, Tile tile_11 is painted in black color, Tile tile_18 is painted in white color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(painted tile_11 white)", "(painted tile_19 white)", "(robot-at robot2 tile_4)", "(painted tile_3 black)", "(robot-at robot2 tile_19)", "(clear tile_12)", "(clear tile_11)", "(robot-at robot2 tile_14)", "(clear tile_6)", "(robot-at robot1 tile_10)", "(clear tile_13)", "(painted tile_10 black)", "(painted tile_6 black)", "(robot-at robot1 tile_14)", "(painted tile_4 white)", "(robot-at robot2 tile_11)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_17)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_18)", "(clear tile_15)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(robot-at robot2 tile_18)", "(robot-at robot2 tile_15)", "(robot-at robot1 tile_20)", "(painted tile_20 black)", "(robot-at robot2 tile_8)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_17)", "(robot-at robot1 tile_12)", "(robot-at robot2 tile_20)", "(robot-has robot1 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(clear tile_2)", "(robot-has robot2 white)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(robot-at robot1 tile_19)", "(painted tile_2 black)", "(painted tile_17 white)", "(painted tile_14 black)", "(robot-at robot1 tile_9)", "(clear tile_20)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot2 tile_10)", "(clear tile_18)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-at robot2 tile_12)", "(painted tile_13 white)", "(clear tile_10)", "(painted tile_16 black)", "(clear tile_19)", "(painted tile_5 black)", "(clear tile_16)", "(robot-at robot2 tile_16)", "(robot-at robot1 tile_13)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_4 black)", "(painted tile_15 white)", "(robot-at robot2 tile_13)", "(robot-at robot1 tile_15)", "(robot-at robot1 tile_8)", "(painted tile_18 black)", "(robot-at robot1 tile_6)", "(robot-at robot2 tile_17)", "(robot-at robot1 tile_16)"], "yes": ["(clear tile_3)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_14) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_3) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 2108494175986443098, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 Currently, robot robot1 is at tile_2 and holding color black and robot robot2 is at tile_8 and holding color white; tile_4, tile_1, tile_5, tile_7, tile_6, and tile_3 are clear; tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(robot-at robot2 tile_4)", "(robot-has robot2 black)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(robot-has robot1 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_8)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_8) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 2094626873423249533, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_21 is to the right of tile_20, tile_15 is to the right of tile_14, tile_23 is to the right of tile_22, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_22 is to the right of tile_21, tile_14 is to the right of tile_13, tile_9 is to the right of tile_8, tile_24 is to the right of tile_23, and tile_16 is to the right of tile_15. Further, tile_16 is down from tile_22, tile_2 is down from tile_8, tile_6 is down from tile_12, tile_14 is down from tile_20, tile_7 is down from tile_13, tile_13 is down from tile_19, tile_18 is down from tile_24, tile_1 is down from tile_7, tile_9 is down from tile_15, tile_8 is down from tile_14, tile_4 is down from tile_10, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_12 is down from tile_18, tile_11 is down from tile_17, tile_10 is down from tile_16, tile_3 is down from tile_9, and tile_15 is down from tile_21 Currently, robot robot2 is at tile_2 and holding color white and robot robot1 is at tile_4 and holding color white; tile_5, tile_21, tile_17, tile_10, tile_15, tile_1, tile_11, tile_6, tile_3, and tile_9 are clear; tile_19 is painted white, tile_13 is painted black, tile_23 is painted white, tile_14 is painted white, tile_16 is painted white, tile_12 is painted black, tile_20 is painted black, tile_8 is painted black, tile_24 is painted black, tile_22 is painted black, tile_7 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_21 is painted in white color, Tile tile_17 is painted in black color, Tile tile_23 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_12 is painted in black color, Tile tile_24 is painted in black color, Tile tile_13 is painted in black color, Tile tile_14 is painted in white color, Tile tile_16 is painted in white color, Tile tile_10 is painted in black color, Tile tile_22 is painted in black color, Tile tile_20 is painted in black color, Tile tile_7 is painted in white color, Tile tile_9 is painted in white color, Tile tile_18 is painted in white color, and Tile tile_8 is painted in black color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(painted tile_22 white)", "(robot-at robot2 tile_4)", "(robot-at robot2 tile_21)", "(robot-at robot1 tile_23)", "(painted tile_3 black)", "(robot-at robot2 tile_19)", "(clear tile_12)", "(painted tile_6 white)", "(robot-at robot2 tile_14)", "(clear tile_24)", "(painted tile_11 black)", "(robot-at robot1 tile_10)", "(clear tile_13)", "(robot-at robot1 tile_24)", "(painted tile_6 black)", "(robot-at robot1 tile_14)", "(painted tile_4 white)", "(robot-at robot2 tile_11)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_22)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(painted tile_23 black)", "(robot-at robot1 tile_22)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(painted tile_1 white)", "(robot-at robot2 tile_23)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_18)", "(robot-at robot2 tile_9)", "(robot-at robot2 tile_18)", "(painted tile_24 white)", "(robot-at robot2 tile_15)", "(robot-at robot1 tile_20)", "(robot-at robot2 tile_8)", "(painted tile_20 white)", "(painted tile_2 white)", "(robot-at robot1 tile_17)", "(clear tile_14)", "(robot-at robot1 tile_12)", "(painted tile_12 white)", "(robot-at robot2 tile_20)", "(clear tile_2)", "(robot-at robot1 tile_21)", "(robot-at robot2 tile_6)", "(painted tile_9 black)", "(painted tile_19 black)", "(robot-at robot1 tile_19)", "(painted tile_2 black)", "(painted tile_17 white)", "(painted tile_14 black)", "(clear tile_22)", "(robot-at robot1 tile_9)", "(clear tile_20)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot2 tile_10)", "(clear tile_18)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-at robot2 tile_12)", "(painted tile_13 white)", "(robot-at robot2 tile_24)", "(painted tile_16 black)", "(clear tile_19)", "(robot-has robot2 black)", "(painted tile_5 black)", "(clear tile_7)", "(clear tile_16)", "(robot-has robot1 black)", "(painted tile_7 black)", "(robot-at robot2 tile_16)", "(robot-at robot1 tile_13)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_8 white)", "(clear tile_23)", "(painted tile_21 black)", "(painted tile_4 black)", "(painted tile_15 white)", "(robot-at robot2 tile_13)", "(robot-at robot1 tile_15)", "(robot-at robot1 tile_8)", "(painted tile_18 black)", "(robot-at robot1 tile_6)", "(robot-at robot2 tile_17)", "(robot-at robot1 tile_16)", "(painted tile_10 white)"], "yes": ["(clear tile_4)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_15) (clear tile_17) (clear tile_21) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_9) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_16 white) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black) (painted tile_7 white) (painted tile_8 black) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": -2230494624135429208, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_2 is down from tile_5, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_7 and holding color black and robot robot1 is at tile_9 and holding color white; tile_10, tile_4, tile_1, tile_12, tile_11, tile_5, tile_8, tile_6, tile_3, and tile_2 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(painted tile_11 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(robot-at robot1 tile_10)", "(painted tile_4 black)", "(painted tile_10 black)", "(robot-at robot2 tile_8)", "(painted tile_6 black)", "(robot-at robot2 tile_12)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_12)", "(robot-at robot1 tile_8)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_7)", "(clear tile_9)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_7) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -2735412907898546442, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_2 is down from tile_5, and tile_9 is down from tile_12 Currently, robot robot1 is at tile_5 and holding color black and robot robot2 is at tile_3 and holding color white; tile_1, tile_6, and tile_2 are clear; tile_7 is painted black, tile_4 is painted white, tile_12 is painted white, tile_8 is painted white, tile_9 is painted black, tile_11 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(painted tile_11 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 black)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_7)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(clear tile_12)", "(clear tile_11)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(clear tile_3)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(robot-at robot1 tile_10)", "(clear tile_4)", "(clear tile_9)", "(painted tile_4 black)", "(painted tile_10 black)", "(robot-at robot2 tile_8)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(robot-at robot2 tile_12)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_12)", "(robot-at robot1 tile_8)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(robot-has robot1 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_10)"], "yes": ["(clear tile_5)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_5) (robot-at robot2 tile_3) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -9220753346051711618, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_21 is to the right of tile_20, tile_15 is to the right of tile_14, tile_23 is to the right of tile_22, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_22 is to the right of tile_21, tile_14 is to the right of tile_13, tile_9 is to the right of tile_8, tile_24 is to the right of tile_23, and tile_16 is to the right of tile_15. Further, tile_16 is down from tile_22, tile_2 is down from tile_8, tile_6 is down from tile_12, tile_14 is down from tile_20, tile_7 is down from tile_13, tile_13 is down from tile_19, tile_18 is down from tile_24, tile_1 is down from tile_7, tile_9 is down from tile_15, tile_8 is down from tile_14, tile_4 is down from tile_10, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_12 is down from tile_18, tile_11 is down from tile_17, tile_10 is down from tile_16, tile_3 is down from tile_9, and tile_15 is down from tile_21 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_6 and holding color white; tile_5, tile_7, tile_10, tile_16, tile_1, tile_3, tile_4, tile_13, and tile_9 are clear; tile_17 is painted black, tile_11 is painted white, tile_19 is painted white, tile_23 is painted white, tile_14 is painted white, tile_12 is painted black, tile_20 is painted black, tile_8 is painted black, tile_15 is painted black, tile_21 is painted white, tile_24 is painted black, tile_22 is painted black, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_21 is painted in white color, Tile tile_17 is painted in black color, Tile tile_23 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_12 is painted in black color, Tile tile_24 is painted in black color, Tile tile_13 is painted in black color, Tile tile_14 is painted in white color, Tile tile_16 is painted in white color, Tile tile_10 is painted in black color, Tile tile_22 is painted in black color, Tile tile_20 is painted in black color, Tile tile_7 is painted in white color, Tile tile_9 is painted in white color, Tile tile_18 is painted in white color, and Tile tile_8 is painted in black color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(painted tile_22 white)", "(robot-at robot2 tile_4)", "(clear tile_21)", "(robot-at robot2 tile_21)", "(robot-at robot1 tile_23)", "(painted tile_3 black)", "(robot-at robot2 tile_19)", "(clear tile_12)", "(clear tile_11)", "(painted tile_6 white)", "(robot-at robot2 tile_14)", "(clear tile_6)", "(painted tile_11 black)", "(clear tile_24)", "(robot-at robot1 tile_10)", "(robot-at robot1 tile_24)", "(painted tile_6 black)", "(robot-at robot1 tile_14)", "(painted tile_4 white)", "(robot-at robot2 tile_11)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_22)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(painted tile_23 black)", "(robot-at robot1 tile_22)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_17)", "(painted tile_1 white)", "(robot-at robot2 tile_23)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_18)", "(clear tile_15)", "(robot-at robot2 tile_9)", "(robot-at robot2 tile_18)", "(painted tile_24 white)", "(robot-at robot2 tile_15)", "(robot-at robot1 tile_20)", "(robot-at robot2 tile_8)", "(painted tile_20 white)", "(painted tile_2 white)", "(robot-at robot1 tile_17)", "(clear tile_14)", "(robot-at robot1 tile_12)", "(painted tile_12 white)", "(robot-at robot2 tile_20)", "(robot-at robot1 tile_21)", "(robot-has robot2 white)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(painted tile_9 black)", "(painted tile_19 black)", "(robot-at robot1 tile_19)", "(painted tile_2 black)", "(painted tile_17 white)", "(painted tile_14 black)", "(clear tile_22)", "(robot-at robot1 tile_9)", "(clear tile_20)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot2 tile_10)", "(clear tile_18)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-at robot2 tile_12)", "(painted tile_13 white)", "(robot-at robot2 tile_24)", "(painted tile_16 black)", "(clear tile_19)", "(painted tile_5 black)", "(robot-has robot1 black)", "(painted tile_7 black)", "(robot-at robot2 tile_16)", "(robot-at robot1 tile_13)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_8 white)", "(clear tile_23)", "(painted tile_21 black)", "(painted tile_4 black)", "(painted tile_15 white)", "(robot-at robot2 tile_13)", "(robot-at robot1 tile_15)", "(robot-at robot1 tile_8)", "(painted tile_18 black)", "(robot-at robot2 tile_17)", "(robot-at robot1 tile_16)", "(painted tile_10 white)"], "yes": ["(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_13) (clear tile_16) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_9) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_11 white) (painted tile_12 black) (painted tile_14 white) (painted tile_15 black) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black) (painted tile_8 black) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": 3786294354926367353, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_13 is to the right of tile_12, tile_12 is to the right of tile_11, tile_14 is to the right of tile_13, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, and tile_20 is to the right of tile_19. Further, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_1 is down from tile_6, tile_6 is down from tile_11, tile_4 is down from tile_9, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_2 is down from tile_7, tile_15 is down from tile_20, and tile_9 is down from tile_14 Currently, robot robot2 is at tile_1 and holding color black, robot robot1 is at tile_2 and holding color white, and robot robot3 is at tile_12 and holding color black; tile_8, tile_5, tile_7, tile_11, tile_6, tile_3, tile_4, tile_13, tile_9, and tile_14 are clear; tile_17 is painted black, tile_16 is painted white, tile_19 is painted black, tile_20 is painted white, tile_15 is painted black, tile_18 is painted white, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_13 is painted in black color, Tile tile_14 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_6 is painted in white color, Tile tile_20 is painted in white color, Tile tile_11 is painted in black color, Tile tile_18 is painted in white color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot3 tile_10)", "(robot-at robot2 tile_2)", "(painted tile_11 white)", "(painted tile_19 white)", "(robot-at robot2 tile_4)", "(robot-at robot3 tile_4)", "(clear tile_1)", "(painted tile_3 black)", "(robot-at robot2 tile_19)", "(robot-at robot3 tile_15)", "(robot-at robot2 tile_14)", "(robot-at robot1 tile_10)", "(painted tile_10 black)", "(painted tile_6 black)", "(robot-at robot1 tile_14)", "(painted tile_4 white)", "(robot-at robot2 tile_11)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot3 tile_3)", "(painted tile_1 black)", "(clear tile_17)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot3 tile_2)", "(robot-at robot1 tile_18)", "(clear tile_15)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(robot-at robot2 tile_18)", "(robot-at robot2 tile_15)", "(robot-at robot3 tile_20)", "(robot-at robot1 tile_20)", "(painted tile_20 black)", "(robot-at robot2 tile_8)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_17)", "(robot-at robot3 tile_16)", "(robot-at robot1 tile_12)", "(robot-at robot2 tile_20)", "(robot-at robot3 tile_17)", "(painted tile_7 white)", "(painted tile_9 white)", "(clear tile_2)", "(robot-at robot3 tile_11)", "(robot-at robot3 tile_18)", "(robot-has robot2 white)", "(robot-has robot3 white)", "(robot-at robot3 tile_6)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(robot-at robot3 tile_13)", "(robot-at robot3 tile_8)", "(robot-at robot1 tile_19)", "(painted tile_2 black)", "(painted tile_17 white)", "(painted tile_14 black)", "(robot-at robot1 tile_9)", "(clear tile_20)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot2 tile_10)", "(clear tile_18)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-at robot2 tile_12)", "(painted tile_13 white)", "(clear tile_10)", "(painted tile_16 black)", "(clear tile_19)", "(robot-at robot3 tile_14)", "(painted tile_5 black)", "(robot-at robot3 tile_5)", "(robot-at robot3 tile_19)", "(robot-at robot3 tile_1)", "(clear tile_16)", "(robot-has robot1 black)", "(robot-at robot2 tile_16)", "(robot-at robot1 tile_13)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_4 black)", "(robot-at robot3 tile_9)", "(painted tile_15 white)", "(robot-at robot2 tile_13)", "(robot-at robot1 tile_15)", "(robot-at robot1 tile_8)", "(painted tile_18 black)", "(robot-at robot1 tile_6)", "(robot-at robot2 tile_17)", "(robot-at robot1 tile_16)", "(robot-at robot3 tile_7)"], "yes": ["(clear tile_12)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_11) (clear tile_13) (clear tile_14) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_1) (robot-at robot3 tile_12) (robot-has robot1 white) (robot-has robot2 black) (robot-has robot3 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -8846649477211358363, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_1, tile_9, tile_6, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, and tile_5 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_5)", "(clear tile_7)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(painted tile_4 black)", "(robot-at robot1 tile_9)", "(robot-at robot2 tile_8)", "(painted tile_6 black)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_4)", "(robot-at robot2 tile_6)", "(robot-at robot2 tile_3)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_6) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_5 black) (painted tile_7 black) (painted tile_8 white) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 9160386177509913200, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 12 tiles. \nThe tiles locations are: tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_2 is down from tile_5, and tile_9 is down from tile_12 \nCurrently, robot robot1 is at tile_1 and holding color white and robot robot2 is at tile_8 and holding color black; tile_4, tile_9, tile_5, tile_7, tile_6, tile_3, and tile_2 are clear; tile_12 is painted white, tile_11 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(painted tile_11 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(clear tile_1)", "(painted tile_3 black)", "(clear tile_12)", "(clear tile_11)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(painted tile_2 black)", "(robot-at robot1 tile_10)", "(painted tile_4 black)", "(painted tile_10 black)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(robot-at robot2 tile_12)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_12)", "(robot-at robot1 tile_8)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_10)"], "yes": ["(clear tile_8)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_8) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 8388948775127950653, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color white and robot robot1 is at tile_1 and holding color white; tile_4, tile_6, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, tile_9 is painted black, and tile_5 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_4)", "(robot-has robot2 black)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_5)", "(clear tile_7)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(clear tile_1)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot2 tile_8)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_3) (clear tile_4) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_5 black) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 5534083814025355815, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_1, tile_5, tile_6, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_7)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot1 tile_9)", "(robot-at robot2 tile_8)", "(painted tile_6 black)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_4)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_5) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 8441436456817991537, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 12 tiles. \nThe tiles locations are: tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_2 is down from tile_5, and tile_9 is down from tile_12 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_6 and holding color black; tile_4, tile_1, tile_8, tile_5, and tile_3 are clear; tile_7 is painted black, tile_12 is painted white, tile_11 is painted black, tile_9 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(painted tile_11 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_7)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(clear tile_12)", "(clear tile_11)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(robot-at robot1 tile_10)", "(clear tile_9)", "(painted tile_4 black)", "(painted tile_10 black)", "(robot-at robot2 tile_8)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(robot-at robot2 tile_12)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_12)", "(robot-at robot1 tile_8)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-has robot1 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_10)"], "yes": ["(clear tile_6)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_7 black) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_2) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 1556332777485676351, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot1 is at tile_1 and holding color white and robot robot2 is at tile_5 and holding color black; tile_4, tile_7, tile_6, tile_3, and tile_2 are clear; tile_8 is painted white and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(clear tile_1)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot1 tile_9)", "(robot-at robot2 tile_8)", "(painted tile_6 black)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_5)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_6) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_5) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
