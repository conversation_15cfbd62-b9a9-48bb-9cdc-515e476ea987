{"question_id": "649506f6-87e0-49bb-9e2b-afe5b342e035", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, if at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, is it True or False that cargo c6 is not located in vehicle v0, cargo c6 is situated at location l0, vehicle v0 contains space s1 and vehicle v0 does not have space s0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 travels from location l0, which has fuel levels f5 and f4, to location l1, then cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Next, vehicle v0 moves from location l1, which has fuel levels f7 and f6, back to location l0, and cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, at location l0. Vehicle v0 then travels from location l0, which has fuel levels f4 and f3, to location l1, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0, at location l1. This is followed by vehicle v0 moving from location l1, which has fuel levels f6 and f5, to location l0, and cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1, at location l0. Vehicle v0 then travels from location l0, which has fuel levels f3 and f2, to location l1, and cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Next, vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, and cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1, at location l0. Vehicle v0 then travels from location l0, which has fuel levels f2 and f1, to location l1, and cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0, at location l1. This is followed by vehicle v0 moving from location l1, which has fuel levels f4 and f3, to location l0, and cargo c4 is unloaded from vehicle v0, releasing spaces s0 and s1, at location l0. Vehicle v0 then travels from location l0, which has fuel levels f1 and f0, to location l1, and cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Finally, vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, reaching the current state. In this state, if at location l0, cargo c6 is unloaded from vehicle v0, utilizing spaces s0 and s1, is it True or False that cargo c6 is no longer in vehicle v0, cargo c6 is now situated at location l0, vehicle v0 still contains space s1, and vehicle v0 no longer has space s0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is located at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f2 is adjacent to f3, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, f0 is adjacent to f1, and f6 is adjacent to f7. Location l0 is supplied with fuel f5, and l0 is connected to l1, which is supplied with fuel f7. Spaces s0 and s1 are adjacent, and locations l0 and l1 are interconnected. Vehicle v0 is equipped with space s1 and is currently at l0, while vehicle v1, which contains space s1, is situated at l1."}
{"question_id": "0e6f7b41-240c-4429-89be-de5311dd54a2", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, if cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, is it True or False that cargo c0 is located in vehicle v0, cargo c0 is not at location l1cargo c0 is not present at location l1, vehicle v0 contains space s0 and vehicle v0 does not have space s1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, if cargo c0 is loaded in vehicle v0 with available spaces s1 and s0 at location l1, is it True or False that cargo c0 is in vehicle v0, cargo c0 is not located at location l1, cargo c0 is absent from location l1, vehicle v0 has space s0, and vehicle v0 does not have space s1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is situated at l1, c8 is at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f2 is adjacent to f3, f3 is adjacent to f4, f4 is adjacent to f5, and f5 is adjacent to f6. Additionally, f0 is adjacent to f1, and f6 is adjacent to f7. Location l0 is equipped with fuel f5, and l1 is connected to l0. Location l1 is equipped with fuel f7. Space s0 is adjacent to space s1. There is a connection between locations l0 and l1. Vehicle v0 is equipped with space s1 and is currently at location l0. Vehicle v1 contains space s1 and is situated at location l1."}
{"question_id": "40f14468-d8df-4827-bf57-68a6e4d54e8a", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, is it True or False that location l1 does not have a fuel-level of f6, location l1 has fuel f5, vehicle v0 is at location l0 and vehicle v0 is not present at location l1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, is it True or False that location l1 no longer has a fuel-level of f6, location l1 is associated with fuel f5, vehicle v0 is now at location l0, and vehicle v0 is no longer present at location l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, and space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "cce9cfcc-a920-4168-a469-c35be5f06b5c", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, if vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, is it True or False that cargo c1 is not situated at location l0, cargo c2 is not situated at location l1, cargo c3 is not located in vehicle v0 and cargo c4 is at location l1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, which has fuel levels f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c0 is loaded onto vehicle v0 using spaces s1 and s0, after which vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, at which point cargo c0 is unloaded from vehicle v0 using spaces s0 and s1, cargo c3 is loaded onto vehicle v0 using spaces s1 and s0, vehicle v0 then travels from location l1, which has fuel levels f7 and f6, to location l0, where cargo c3 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c1 is loaded onto vehicle v0 using spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1, is it True or False that cargo c1 is no longer at location l0, cargo c2 is not at location l1, cargo c3 is not in vehicle v0, and cargo c4 is at location l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is adjacent to f1, f2 is adjacent to f3, f5 is adjacent to f6, and f7 is adjacent to f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "bf93c977-5a14-4989-a990-ea36b7d2789a", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, is it True or False that fuel f7 exists in location l1, fuel f8 does not exist in location l1, vehicle v0 is not situated at location l1 and vehicle v0 is situated at location l0?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0, is it True or False that fuel f7 is present at location l1, fuel f8 is not present at location l1, vehicle v0 is no longer at location l1, and vehicle v0 is now at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "635a6448-f885-4a24-9c5b-c3b800b86ecd", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, if at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, is it True or False that cargo c0 is located in vehicle v0, cargo c0 is not at location l0cargo c0 is not present at location l0, vehicle v0 contains space s1 and vehicle v0 does not have space s2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 with fuel levels f3 and f2 to location l0. In this resulting state, if vehicle v0 is at location l0, and cargo c0 is loaded into vehicle v0 which has spaces s2 and s1, is it True or False that cargo c0 is in vehicle v0, cargo c0 is not at location l0, cargo c0 is not present at location l0, vehicle v0 has space s1, and vehicle v0 does not have space s2?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Furthermore, cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Cargo c8 and c9 are also located at l1. Fuel f3 is available at l1. The fuel levels are arranged such that f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. A connection exists between locations l0 and l1. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "4a815ec7-48eb-4c9e-bba3-bdf247c56210", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if cargo c9 is unloaded from vehicle v0 with space s0 and space s1 at location l0, is it True or False that cargo c9 is situated at location l0, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s0 and vehicle v0 has space s1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c0 is loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Subsequently, cargo c10 is loaded onto vehicle v0, occupying spaces s1 and s0, and cargo c2 is unloaded from vehicle v0, vacating spaces s0 and s1. Vehicle v0 then proceeds from location l1, which has fuel levels f2 and f1, to location l0. Upon arrival at location l0, cargo c10 is unloaded from vehicle v0, releasing spaces s1 and s2. Next, cargo c3 is loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c4 is also loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c3 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c4 is unloaded from vehicle v0, vacating spaces s1 and s2. Cargo c5 is then loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c9 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then relocates from location l1, which has fuel levels f1 and f0, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, releasing spaces s0 and s1. Finally, cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, if cargo c9 is unloaded from vehicle v0, occupying spaces s0 and s1, at location l0, is it True or False that cargo c9 is situated at location l0, vehicle v0 no longer contains cargo c9, vehicle v0 does not have space s0, and vehicle v0 has space s1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Furthermore, cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Cargo c8 and c9 are also located at l1. Fuel f3 is available at l1. The fuel levels are arranged such that f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. A connection exists between locations l0 and l1. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "22fa9aa5-144e-4fe0-974e-6e6aba011fcf", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, is it True or False that location l1 does not have a fuel-level of f5, location l1 has fuel f4, vehicle v0 is not present at location l1 and vehicle v0 is present at location l0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. Cargo c1 is then loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1. Cargo c7 is then loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, is it True or False that location l1 does not have a fuel level of f5, location l1 has fuel f4, vehicle v0 is no longer present at location l1, and vehicle v0 is present at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1, as well as between l1 and l0. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "d98af91f-d7bc-44e4-9995-2ee7173b3b5a", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0 to reach the current state. In this state, if cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, is it True or False that cargo c6 is at location l0, cargo c6 is not located in vehicle v0, vehicle v0 contains space s1 and vehicle v0 does not contain space s0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, and then vehicle v0 travels from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1, and then vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which still has spaces s0 and s1, and then vehicle v0 returns to location l1 from location l0, which has fuel levels f6 and f5. Next, cargo c6 is loaded onto vehicle v0 at location l1, occupying spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, if cargo c6 is unloaded from vehicle v0 at location l0, utilizing spaces s0 and s1, is it True or False that cargo c6 is at location l0, cargo c6 is no longer in vehicle v0, vehicle v0 still contains space s1, and vehicle v0 no longer contains space s0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, and l1 is connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "432f9ab4-e4ee-486d-afdf-37d9e82c55ce", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, if vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, is it True or False that cargo c1 is not situated at location l1, fuel f7 exists in location l0 and location l0 does not have fuel f5?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, where the fuel levels are f6 and f5, to location l0, at which point cargo c1 is unloaded from vehicle v0, utilizing spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1, at location l1, cargo c4 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0, at which point cargo c4 is unloaded from vehicle v0, utilizing spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1, at location l1, cargo c6 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, at location l0, cargo c6 is unloaded from vehicle v0, utilizing spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f5 and f4, back to location l1, at location l1, cargo c7 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f3 and f2, to location l0, at location l0, cargo c7 is unloaded from vehicle v0, utilizing spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f4 and f3, back to location l1, at location l1, cargo c8 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f2 and f1, to location l0, at which point cargo c8 is unloaded from vehicle v0, utilizing spaces s0 and s1, resulting in the current state. In this state, if vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1, is it True or False that cargo c1 is not at location l1, fuel f7 is present at location l0, and location l0 does not have fuel f5?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, and space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "8bd89750-5b3e-42e5-a285-afdd0d7403cd", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4 to reach the current state. In this state, if cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, is it True or False that fuel f2 exists in location l1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, if cargo c0 is loaded in vehicle v0 with available spaces s1 and s0 at location l1, is it True or False that fuel f2 is present at location l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is supplied with fuel f5, locations l0 and l1 are connected, location l1 is supplied with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is currently at location l1."}
{"question_id": "c56fca66-b178-4a3e-8f94-f315516742ec", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, if vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, is it True or False that fuel f1 exists in location l0, location l0 does not have fuel f2, vehicle v0 is not present at location l0 and vehicle v0 is situated at location l1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, then vehicle v0 moves from location l1, where the fuel levels are f8 and f7, to location l0, at which point cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. Subsequently, at location l0, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. Next, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, at location l1, and cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, which has fuel levels f7 and f6, to location l0, and cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. Finally, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0, resulting in the current state. In this state, if vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1, is it True or False that fuel f1 exists in location l0, location l0 does not have fuel f2, vehicle v0 is no longer at location l0, and vehicle v0 is now situated at location l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is adjacent to f1, f2 is adjacent to f3, f5 is adjacent to f6, and f7 is adjacent to f8. Location l1 is equipped with fuel f8. Space s0 is connected to space s1. A connection exists between locations l0 and l1, and vice versa. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "c2f1282a-4c46-4b0c-9993-9b3d3cf8c803", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, is it True or False that location l0 does not have fuel f6?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1. At location l1, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves back to location l0 from location l1, which now has fuel levels f7 and f6. Upon arrival at location l0, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then relocates from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. Subsequently, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, and cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0 at location l1, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, is it True or False that location l0 does not have fuel f6?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is supplied with fuel f5, locations l0 and l1 are connected, location l1 is supplied with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is currently at location l1."}
{"question_id": "225d169f-e341-408c-89bb-9ea19bf7731b", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, if cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, is it True or False that vehicle v0 contains space s0?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location l1, cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1, where cargo c4 is loaded into vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. After that, vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1, where cargo c6 is loaded into vehicle v0, which has spaces s1 and s0, and vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, if cargo c6 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0, is it True or False that vehicle v0 contains space s0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are also neighbors, f2 and f3 are adjacent, and f3 and f4 are connected. Moreover, f5 and f6 are neighboring fuel levels. Locations l0 and l1 are interconnected, and l1 is also connected to l0. Space s0 is adjacent to space s1, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "7e009afb-1e3a-4210-8e1a-a82bf7e698d7", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, if cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, is it True or False that cargo c4 is at location l0, vehicle v0 does not contain space s0 and vehicle v1 contains cargo c7?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, then cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Next, vehicle v0 moves back to location l0, which has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, at location l0. Vehicle v0 then proceeds to location l1, which has fuel levels f4 and f3, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0, at location l1. This is followed by vehicle v0 moving to location l0, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1, at location l0. The sequence continues with vehicle v0 moving to location l1, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Then, vehicle v0 relocates to location l0, which has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1, at location l0. Vehicle v0 then moves to location l1, which has fuel levels f2 and f1, and cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0, at location l1. Next, vehicle v0 proceeds to location l0, which has fuel levels f4 and f3, and cargo c4 is unloaded from vehicle v0, releasing spaces s0 and s1, at location l0. The sequence continues with vehicle v0 moving to location l1, which has fuel levels f1 and f0, and cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l1. Finally, vehicle v0 relocates to location l0, which has fuel levels f3 and f2. In this final state, if cargo c6 is unloaded from vehicle v0, utilizing spaces s0 and s1, at location l0, is it True or False that cargo c4 is at location l0, vehicle v0 does not contain space s0, and vehicle v1 contains cargo c7?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is supplied with fuel f5, locations l0 and l1 are connected, location l1 is supplied with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is currently at location l1."}
{"question_id": "60e485ea-084d-4e19-b5d0-1f0a19cd5395", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, if cargo c9 is unloaded from vehicle v0 with space s0 and space s1 at location l0, is it True or False that cargo c3 is not situated at location l0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 is loaded onto vehicle v0, occupying spaces s1 and s0. Additionally, cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then moves back to location l0, which has fuel levels f2 and f1. At location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 and c4 are loaded onto vehicle v0, occupying spaces s2 and s1, and spaces s1 and s0, respectively. Vehicle v0 then moves to location l1, which has fuel levels f3 and f2. At location l1, cargo c3 and c4 are unloaded from vehicle v0, freeing up spaces s0 and s1, and s1 and s2, respectively. Cargo c5 and c9 are then loaded onto vehicle v0, occupying spaces s2 and s1, and spaces s1 and s0, respectively. Vehicle v0 then moves back to location l0, which has fuel levels f1 and f0. At location l0, cargo c5 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0. In this state, if cargo c9 is unloaded from vehicle v0, occupying spaces s0 and s1, at location l0, is it True or False that cargo c3 is not situated at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. Cargos c2, c3, and c4 are all situated at l0. Cargo c5 is present at l1, whereas cargos c6 and c7 are at l0. Cargos c8 and c9 are located at l1. Fuel f3 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. Locations l0 and l1 have a connection. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "88e34516-0964-46f3-86af-cbe58c0d1b8d", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, is it True or False that cargo c8 is in vehicle v0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0, is it True or False that cargo c8 is in vehicle v0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, and c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are positioned at l0. Fuel f3 is available at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is adjacent to f1, f2 is adjacent to f3, f5 is adjacent to f6, and f7 is adjacent to f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "fc1786f1-adb1-4cb9-8f75-da28a8a572fa", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1 to reach the current state. In this state, if cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, is it True or False that cargo c4 is not at location l0cargo c4 is not present at location l0, vehicle v0 contains cargo c4, vehicle v0 does not have space s1 and vehicle v0 has space s0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded into vehicle v0, utilizing spaces s2 and s1, and cargo c2 is also loaded into vehicle v0, occupying spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. At location l1, cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0, and cargo c2 is unloaded from vehicle v0, releasing spaces s0 and s1. Then, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0. Upon arrival, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, resulting in the current state. In this state, if cargo c4 is loaded into vehicle v0, utilizing spaces s1 and s0 at location l0, is it True or False that cargo c4 is not at location l0, given that cargo c4 is not present at location l0, vehicle v0 contains cargo c4, vehicle v0 does not have space s1, and vehicle v0 has space s0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. Cargos c2, c3, and c4 are all situated at l0. Cargo c5 is present at l1, whereas cargos c6 and c7 are at l0. Cargos c8 and c9 are located at l1. Fuel f3 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. A connection exists between locations l0 and l1. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "c8d31fca-184c-4310-8346-1fc7c395464f", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, if vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, is it True or False that location l1 does not have a fuel-level of f5, location l1 has a fuel-level of f4, vehicle v0 is not at location l1 and vehicle v0 is situated at location l0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f5, location l1 and location l0 are connected, location l1 has fuel f7, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, back to location l0, where cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, freeing up spaces s0 and s1. Finally, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, and cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, is it True or False that location l1 does not have a fuel level of f5, location l1 has a fuel level of f4, vehicle v0 is no longer at location l1, and vehicle v0 is now situated at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is positioned at l1, c3 is found at l0, c4 is positioned at l1, c5 is located at l0, c6 is at l1, c7 is positioned at l1, c8 is at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f6 and f7 are adjacent, location l0 is equipped with fuel f5, locations l0 and l1 are connected, location l1 is equipped with fuel f7, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is situated at location l1."}
{"question_id": "8ce9d72d-dedb-410f-832c-d6867efff210", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, if at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, is it True or False that vehicle v1 contains cargo c1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is at location l1, cargo c10 is present at location l1, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is at location l1, fuel f3 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, spaces s1 and s2 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, if vehicle v0 is at location l0 and cargo c0 is loaded into it, occupying spaces s2 and s1, is it True or False that vehicle v1 is carrying cargo c1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. In contrast, cargo c2, c3, and c4 are all situated at l0. Furthermore, cargo c5 is present at l1, whereas cargo c6 and c7 are at l0. Cargo c8 and c9 are also located at l1. Fuel f3 is available at l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f1 and f2 are neighbors, f2 and f3 are neighbors, and f4 and f5 are neighbors. Location l0 has a fuel level of f4. Locations l0 and l1 are connected. Spaces s0 and s1 are adjacent, and s1 is also adjacent to s2. A connection exists between locations l0 and l1. Vehicle v0, which contains space s2, is situated at l1. Similarly, vehicle v1, also containing space s2, is situated at l1."}
{"question_id": "a2b9d42a-e15b-4211-aca3-db2bc0737bd2", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, is it True or False that cargo c6 is located in vehicle v1, location l0 has a fuel-level of f3 and location l1 does not have fuel f0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is present at location l1, cargo c3 is at location l1, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l1 has fuel f8, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 travels from location l1, with fuel levels f7 and f6, to location l0. Upon arrival at location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l0, with fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, if vehicle v0 moves from location l1, with fuel levels f5 and f4, to location l0, is it True or False that cargo c6 is located in vehicle v1, location l0 has a fuel level of f3, and location l1 does not have fuel f0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, with cargo c6 and c7 being at l0. Fuel f3 is available at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, and f6 is adjacent to f7. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f5 is a neighbor of f6, and f7 is a neighbor of f8. Location l1 is equipped with fuel f8. Spaces s0 and s1 are adjacent, and locations l0 and l1 are connected in both directions. Vehicle v0, which contains space s1, is currently at location l1, whereas vehicle v1, also containing space s1, is at location l0."}
{"question_id": "9d9d496a-73f8-4270-8f1b-27c972ef769f", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, if vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, is it True or False that fuel f2 exists in location l0, fuel f3 does not exist in location l0, vehicle v0 is not present at location l0 and vehicle v0 is present at location l1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has available spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f7 and f6, to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has available spaces s0 and s1. This process continues with vehicle v0 moving between locations l0 and l1, loading and unloading cargo c6, c7, and c8, and updating the fuel levels accordingly. The sequence of actions results in the current state. In this state, if vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, the question arises: is it True or False that fuel f2 exists in location l0, fuel f3 does not exist in location l0, vehicle v0 is not present at location l0, and vehicle v0 is present at location l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all found at l1. Fuel f6 is present in l1, and fuel f7 is present in l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, and space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "70636555-500f-43a6-aa93-ee14e13877b3", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, is it True or False that fuel f4 exists in location l0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, if vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, does fuel f4 exist in location l0, and is the answer True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, whereas cargo c10, cargo c2, and cargo c3 are all situated at l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f6 is found at l1, and fuel f7 is found at l0. The fuel levels are connected as follows: f4 is adjacent to f5, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, f1 and f2 are neighbors, f2 and f3 are neighbors, f3 and f4 are neighbors, and f5 and f6 are neighbors. Locations l0 and l1 are interconnected, and space s0 is adjacent to space s1. Vehicle v0, which contains space s1, is currently at location l1."}
