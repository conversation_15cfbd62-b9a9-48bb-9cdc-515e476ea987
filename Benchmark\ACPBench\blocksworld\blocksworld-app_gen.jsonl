{"id": -6696116886821139046, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_3 is on block_5, block_4 is on block_1, and block_5 is on block_4. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(pick-up block_2)", "(unstack block_3 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (handempty) (on block_3 block_5) (on block_4 block_1) (on block_5 block_4) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -7354470634378209855, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_7. The following block(s) are on the table: block_10, block_1, block_8, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_3 is on block_4, block_9 is on block_5, block_4 is on block_9, and block_6 is on block_1. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(stack block_7 block_3)", "(put-down block_7)", "(stack block_7 block_8)", "(stack block_7 block_10)", "(stack block_7 block_6)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_10) (clear block_3) (clear block_6) (clear block_8) (holding block_7) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_6 block_1) (on block_9 block_5) (ontable block_1) (ontable block_10) (ontable block_2) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 2527897604311247585, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5, block_3, and block_2. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_1 is on block_3. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(pick-up block_2)", "(unstack block_1 block_3)", "(unstack block_4 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_2) (clear block_4) (handempty) (on block_1 block_3) (on block_4 block_5) (ontable block_2) (ontable block_3) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 5937509122887891182, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_4, block_3, and block_2. The following block(s) is stacked on top of another block: block_1 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(stack block_5 block_4)", "(put-down block_5)", "(stack block_5 block_1)", "(stack block_5 block_3)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (clear block_4) (holding block_5) (on block_1 block_2) (ontable block_2) (ontable block_3) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": -3755301330084779258, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_15. The following block(s) are on the table: block_6, block_1, block_14, block_7, block_18, block_19, block_12, and block_10. The following block(s) are stacked on top of another block: block_20 is on block_8, block_11 is on block_1, block_5 is on block_14, block_4 is on block_11, block_8 is on block_7, block_13 is on block_4, block_16 is on block_20, block_2 is on block_18, block_3 is on block_13, block_17 is on block_12, and block_9 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(put-down block_15)", "(stack block_15 block_6)", "(stack block_15 block_3)", "(stack block_15 block_10)", "(stack block_15 block_5)", "(stack block_15 block_19)", "(stack block_15 block_9)", "(stack block_15 block_16)", "(stack block_15 block_17)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_10) (clear block_16) (clear block_17) (clear block_19) (clear block_3) (clear block_5) (clear block_6) (clear block_9) (holding block_15) (on block_11 block_1) (on block_13 block_4) (on block_16 block_20) (on block_17 block_12) (on block_2 block_18) (on block_20 block_8) (on block_3 block_13) (on block_4 block_11) (on block_5 block_14) (on block_8 block_7) (on block_9 block_2) (ontable block_1) (ontable block_10) (ontable block_12) (ontable block_14) (ontable block_18) (ontable block_19) (ontable block_6) (ontable block_7))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 7299944056031893958, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1 and block_4. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_4. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(put-down block_5)", "(stack block_5 block_2)", "(stack block_5 block_3)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (holding block_5) (on block_2 block_1) (on block_3 block_4) (ontable block_1) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": -1193545101541139709, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5, block_4, and block_3. The following block(s) is stacked on top of another block: block_1 is on block_3. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(put-down block_2)", "(stack block_2 block_5)", "(stack block_2 block_1)", "(stack block_2 block_4)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_4) (clear block_5) (holding block_2) (on block_1 block_3) (ontable block_3) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 8385548799004385931, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_5 and block_4. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_1 is on block_2. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(stack block_3 block_5)", "(stack block_3 block_1)", "(put-down block_3)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_5) (holding block_3) (on block_1 block_2) (on block_2 block_4) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 7085238290522280400, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_1, block_8, block_5, block_10, block_20, block_16, and block_3. The following block(s) are stacked on top of another block: block_14 is on block_20, block_11 is on block_1, block_2 is on block_14, block_4 is on block_11, block_19 is on block_16, block_18 is on block_5, block_13 is on block_4, block_12 is on block_7, block_7 is on block_10, block_17 is on block_12, block_15 is on block_9, and block_9 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(pick-up block_3)", "(pick-up block_6)", "(unstack block_19 block_16)", "(unstack block_18 block_5)", "(pick-up block_8)", "(unstack block_15 block_9)", "(unstack block_13 block_4)", "(unstack block_17 block_12)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_13) (clear block_15) (clear block_17) (clear block_18) (clear block_19) (clear block_3) (clear block_6) (clear block_8) (handempty) (on block_11 block_1) (on block_12 block_7) (on block_13 block_4) (on block_14 block_20) (on block_15 block_9) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16) (on block_2 block_14) (on block_4 block_11) (on block_7 block_10) (on block_9 block_2) (ontable block_1) (ontable block_10) (ontable block_16) (ontable block_20) (ontable block_3) (ontable block_5) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": -7129582027095494102, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_10, block_6, block_8, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_9 is on block_1, block_1 is on block_8, block_7 is on block_10, and block_4 is on block_6. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(stack block_3 block_4)", "(stack block_3 block_7)", "(stack block_3 block_5)", "(stack block_3 block_9)", "(put-down block_3)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_4) (clear block_5) (clear block_7) (clear block_9) (holding block_3) (on block_1 block_8) (on block_4 block_6) (on block_5 block_2) (on block_7 block_10) (on block_9 block_1) (ontable block_10) (ontable block_2) (ontable block_6) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -655892205003968799, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_1 is on block_4, block_2 is on block_1, block_3 is on block_2, and block_4 is on block_5. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(unstack block_3 block_2)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (handempty) (on block_1 block_4) (on block_2 block_1) (on block_3 block_2) (on block_4 block_5) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -4035159540315405325, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_1. The following block(s) are on the table: block_4 and block_3. The following block(s) are stacked on top of another block: block_5 is on block_4 and block_2 is on block_5. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(stack block_1 block_3)", "(put-down block_1)", "(stack block_1 block_2)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (holding block_1) (on block_2 block_5) (on block_5 block_4) (ontable block_3) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -8647664193922793459, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) is on the table: block_2. The following block(s) are stacked on top of another block: block_1 is on block_4, block_5 is on block_2, block_3 is on block_5, and block_4 is on block_3. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(unstack block_1 block_4)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (handempty) (on block_1 block_4) (on block_3 block_5) (on block_4 block_3) (on block_5 block_2) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -6050996462843416901, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2 and block_1 is on block_3. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(put-down block_4)", "(stack block_4 block_1)", "(stack block_4 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_5) (holding block_4) (on block_1 block_3) (on block_5 block_2) (ontable block_2) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 650777873551723047, "group": "applicable_actions_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_2 is on block_4, block_4 is on block_5, block_3 is on block_1, and block_1 is on block_2. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(unstack block_3 block_1)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (handempty) (on block_1 block_2) (on block_2 block_4) (on block_3 block_1) (on block_4 block_5) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
