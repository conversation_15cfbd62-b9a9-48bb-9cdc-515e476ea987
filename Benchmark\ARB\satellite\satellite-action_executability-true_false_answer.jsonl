{"question_id": "870ce869-ddcf-40d4-9394-144ef8699895", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns to groundstation5 from phenomenon10, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns to phenomenon16 from groundstation5, image of phenomenon16 is taken with instrument3 on satellite1 in image3, satellite1 turns to phenomenon17 from phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, image of planet11 is taken with instrument3 on satellite1 in image3, from planet11, satellite1 turns to planet13, satellite1's instrument3 takes an image of planet13 in image0, from groundstation2, satellite1 turns to groundstation7, satellite1's instrument3 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, instrument3 which is on satellite1 takes an image of star15 in image2, satellite0 turns from groundstation3 to star1, instrument0 is calibrated on satellite0 to star1 and satellite0 turns from star1 to phenomenon10. Is the action: satellite1 turns from groundstation2 to groundstation7 executable at step 13, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: the instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, the calibration of instrument3 on satellite1 to groundstation5 is completed, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 and stores it in image3, satellite1 then reorients from phenomenon17 to planet11, an image of planet11 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from planet11 to planet13, an image of planet13 is captured using instrument3 on satellite1 and stored in image0, satellite1 reorients from groundstation2 to groundstation7, an image of planet14 is captured using instrument3 on satellite1 and stored in image0, satellite1 reorients from planet14 to star15, instrument3 on satellite1 captures an image of star15 and stores it in image2, satellite0 reorients from groundstation3 to star1, instrument0 on satellite0 is calibrated to star1, and satellite0 reorients from star1 to phenomenon10. Is the action: satellite1 reorients from groundstation2 to groundstation7 executable at step 13, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and also has instrument0. Satellite0 has available power. Satellite1 carries instrument3 on board, has available power, and is currently pointing towards phenomenon10."}
{"question_id": "58fee7c0-6cb8-4593-b533-fe83148ea38f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: image of star12 is taken with instrument3 on satellite1 in spectrograph1, instrument3 that is on satellite1 is calibrated to star6, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, on satellite0, instrument0 is switched on, on satellite1, instrument3 is switched on, satellite1 turns from planet14 to star10, satellite1 turns to planet14 from star6, satellite1 turns to star12 from star10, satellite1 turns to star6 from groundstation4 and satellite1's instrument3 takes an image of planet14 in spectrograph1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: an image of star12 is captured using instrument3 on satellite1 with spectrograph1, instrument3 on satellite1 is calibrated with star6, instrument3 on satellite1 captures an image of star10 using spectrograph1, instrument0 is activated on satellite0, instrument3 is activated on satellite1, satellite1 reorients from planet14 to star10, then from star6 to planet14, then from star10 to star12, and finally from groundstation4 to star6, and instrument3 on satellite1 captures an image of planet14 using spectrograph1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, and instrument2 supports both spectrograph0 and spectrograph2. Moreover, instrument3 is calibrated for star6, instrument3 is on board satellite1, and instrument3 supports spectrograph1. The power status of the satellites is as follows: satellite0 has power available, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "c6a6753d-0407-48c7-a6f1-ea944b6b6622", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument1 on satellite0 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, instrument1 that is on satellite1 is calibrated to groundstation8, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 that is on satellite0 is turned off, on satellite0, instrument2 is switched on, satellite0 turns to star4 from star10, calibration of instrument2 which is on satellite0 to star4 is complete, from star4, satellite0 turns to star16, satellite0's instrument2 takes an image of star16 in image0 and on satellite0, instrument2 is switched off. Is the action: instrument1 that is on satellite1 is calibrated to groundstation8 executable at step 10, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, satellite0's instrument1 also captures an image of planet11 in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured with instrument1 on satellite0 in image5, another image of planet13 is captured with instrument1 on satellite0 in spectrograph2, instrument1 on satellite1 is calibrated to groundstation8, instrument1 on satellite0 captures an image of star10 in image6, satellite0's instrument1 also captures an image of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, calibration of instrument2 on satellite0 to star4 is completed, satellite0 then reorients from star4 to star16, satellite0's instrument2 captures an image of star16 in image0, and instrument2 on satellite0 is deactivated. Is the action: instrument1 on satellite1 is calibrated to groundstation8 executable at step 10, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports both image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, supporting image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4, while instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "5c27f458-6357-4bec-bd13-7499d33dddf8", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from phenomenon16, satellite1 turns to phenomenon17, from planet14, satellite1 turns to star15, image of phenomenon17 is taken with instrument3 on satellite1 in image3, image of planet13 is taken with instrument3 on satellite1 in image0, instrument0 that is on satellite0 is calibrated to star1, instrument3 is calibrated on satellite1 to groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, on satellite0, instrument0 is switched on, on satellite1, instrument3 is switched on, satellite0 turns from groundstation3 to star1, satellite0 turns to phenomenon10 from star1, satellite1 turns from phenomenon17 to planet11, satellite1 turns from planet11 to planet13, satellite1 turns to groundstation5 from phenomenon10, satellite1 turns to phenomenon16 from groundstation5, satellite1 turns to planet14 from planet13, satellite1's instrument3 takes an image of planet11 in image3, satellite1's instrument3 takes an image of planet14 in image0 and satellite1's instrument3 takes an image of star15 in image2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: starting from phenomenon16, satellite1 will move to phenomenon17, and from planet14, it will move to star15. Satellite1's instrument3 will capture an image of phenomenon17 in image3, and an image of planet13 in image0. Instrument0 on satellite0 will be calibrated to star1, while instrument3 on satellite1 will be calibrated to groundstation5. Instrument3 on satellite1 will also capture an image of phenomenon16 in image3. On satellite0, instrument0 will be activated, and on satellite1, instrument3 will be activated. Satellite0 will move from groundstation3 to star1, then to phenomenon10. Satellite1 will move from phenomenon17 to planet11, then to planet13, and from phenomenon10 to groundstation5, then to phenomenon16, and from planet13 to planet14. Instrument3 on satellite1 will capture images of planet11 in image3, planet14 in image0, and star15 in image2. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 also has power available, carries instrument3 on board, and is currently pointing towards phenomenon10."}
{"question_id": "9472ad25-88e0-4ce9-912b-983e0cb32cb1", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from phenomenon16, satellite1 turns to phenomenon17, instrument0 on satellite0 is switched on, instrument3 is calibrated on satellite1 to groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, instrument3 which is on satellite1 takes an image of planet11 in image3, on satellite1, instrument3 is switched on, satellite1 turns from groundstation5 to phenomenon16, satellite1 turns from phenomenon10 to groundstation5 and satellite1 turns from phenomenon17 to planet11. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: starting from phenomenon16, satellite1 will move to phenomenon17, instrument0 on satellite0 will be activated, instrument3 on satellite1 will be calibrated with groundstation5, instrument3 on satellite1 will capture an image of phenomenon16 in image3, instrument3 on satellite1 will capture an image of phenomenon17 in image3, instrument3 on satellite1 will capture an image of planet11 in image3, instrument3 on satellite1 will be activated, satellite1 will move from groundstation5 to phenomenon16, satellite1 will move from phenomenon10 to groundstation5, and satellite1 will move from phenomenon17 to planet11. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. Satellite0 is currently directed at groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 carries instrument3 on board, has power available, and is currently pointing at phenomenon10."}
{"question_id": "d9b70287-edb1-49cc-a1a0-abe8507dde1f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: calibration of instrument1 which is on satellite1 to groundstation5 is complete, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns to planet14 from star6, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10, image of star12 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star12 to star0, from star1, satellite0 turns to groundstation2, instrument0 is calibrated on satellite0 to groundstation2, from groundstation2, satellite0 turns to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and instrument0 which is on satellite0 takes an image of star13 in spectrograph0. Is the action: instrument1 is calibrated on satellite1 to groundstation5 executable at step 1, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: the calibration of instrument1 on satellite1 to groundstation5 is completed, instrument0 on satellite0 is activated, satellite1 is directed towards star6 from groundstation4, the calibration of instrument3 on satellite1 to star6 is completed, satellite1 is then redirected from star6 to planet14, instrument3 on satellite1 captures an image of planet14 using spectrograph1, satellite1 is then redirected from planet14 to star10, instrument3 on satellite1 captures an image of star10 using spectrograph1, satellite1 is then redirected from star10 to star12, an image of star12 is captured using instrument3 on satellite1 in spectrograph1, satellite1 is then redirected from star12 to star0, satellite0 is redirected from star1 to groundstation2, the calibration of instrument0 on satellite0 to groundstation2 is completed, satellite0 is then redirected from groundstation2 to phenomenon15, instrument0 on satellite0 captures an image of phenomenon15 using spectrograph0, satellite0 is then redirected from phenomenon15 to star11, instrument0 on satellite0 captures an image of star11 using thermograph4, satellite0 is then redirected from star11 to star13, and instrument0 on satellite0 captures an image of star13 using spectrograph0. Is the action: instrument1 is calibrated on satellite1 to groundstation5 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, and instrument2 is on board satellite1. Instrument1 supports spectrograph1, while instrument2 supports both spectrograph0 and spectrograph2. Instrument3, which is on board satellite1, is calibrated for star6 and supports spectrograph1. The power status of the satellites indicates that satellite0 has power available and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "5c0196b8-0219-472a-a9bc-f2df7ed327cf", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from phenomenon11, satellite1 turns to phenomenon5, from star1, satellite1 turns to phenomenon10, from star3, satellite1 turns to star1, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, on satellite1, instrument1 is switched on, satellite1 turns from phenomenon10 to phenomenon11 and satellite1 turns to phenomenon9 from phenomenon11. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: starting from phenomenon11, satellite1 will shift to phenomenon5, from star1, satellite1 will shift to phenomenon10, from star3, satellite1 will shift to star1, an image of phenomenon10 will be captured using instrument1 on satellite1 in spectrograph3, an image of phenomenon11 will be captured using instrument1 on satellite1 in spectrograph1, instrument1 on satellite1 will capture an image of phenomenon10 in image5, instrument1 on satellite1 will capture an image of phenomenon5 in image4, instrument1 will be activated on satellite1, satellite1 will shift from phenomenon10 to phenomenon11, and satellite1 will shift from phenomenon11 to phenomenon9. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "531a7812-fa28-49cc-b491-2c5256e3d318", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument1 that is on satellite0 is turned on, satellite0 turns from groundstation2 to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and satellite0 turns to star10 from planet13. Is the action: satellite0 turns from planet11 to planet13 executable at step 7, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: the instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, satellite0's instrument1 captures an image of planet11 in image5, instrument1 on satellite0 captures another image of planet11 in image6, satellite0 then reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 in image5, instrument1 on satellite0 captures an image of planet13 in spectrograph2, and finally satellite0 reorients from planet13 to star10. Is the action: satellite0 reorienting from planet11 to planet13 executable at step 7, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, which it supports image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4. Instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "4bcb7716-82a5-4ecf-9cb8-70e3f5e71b72", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns from groundstation0 to planet11, instrument2 which is on satellite1 takes an image of groundstation2 in image6, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and satellite0 turns from planet13 to star10. Is the action: satellite1's instrument2 takes an image of groundstation2 in image6 executable at step 5, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: on satellite0, instrument1 is activated, then satellite0 reorients from groundstation2 to groundstation0, followed by the calibration of instrument1 on satellite0 to groundstation0. Next, satellite0 reorients from groundstation0 to planet11, while on satellite1, instrument2 captures an image of groundstation2 in image6. At the same time, instrument1 on satellite0 captures an image of planet11 in image6. Satellite0 then reorients from planet11 to planet13, where instrument1 on satellite0 captures an image of planet13 in image5, and another image of planet13 in spectrograph2. Finally, satellite0 reorients from planet13 to star10. Is the action: satellite1's instrument2 capturing an image of groundstation2 in image6 executable at step 5, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Instrument1 supports image5 and image6. Infrared7 is compatible with instrument4. Instrument0 has been calibrated for groundstation7 and star3, and it is installed on satellite0, which also supports image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4. Instrument3 supports image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 on board and is currently pointing towards planet13. Additionally, thermograph3 is compatible with instrument3."}
{"question_id": "83de7d30-e677-42ff-8440-e0b177f6874c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from groundstation2, satellite0 turns to groundstation0, image of planet11 is taken with instrument1 on satellite0 in image6, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, instrument1 is calibrated on satellite0 to groundstation0, instrument1 on satellite0 is switched on, instrument1 which is on satellite0 takes an image of planet11 in image5, satellite0 turns from groundstation0 to planet11, satellite0 turns from planet13 to star10 and satellite0 turns to planet13 from planet11. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: starting from groundstation2, satellite0 will reorient to groundstation0, then capture an image of planet11 using instrument1 on satellite0 and store it in image6, capture an image of planet13 using instrument1 on satellite0 and store it in image5, capture an image of planet13 using instrument1 on satellite0 and store it in spectrograph2, calibrate instrument1 on satellite0 to groundstation0, activate instrument1 on satellite0, use instrument1 on satellite0 to capture an image of planet11 and store it in image5, reorient satellite0 from groundstation0 to planet11, reorient satellite0 from planet13 to star10, and finally reorient satellite0 from planet11 to planet13. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, supporting image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4. Instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "393e950f-6bd1-47f4-8be4-4d3a9dafa849", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: on satellite1, instrument3 is switched on. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled: instrument3 will be activated on satellite1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 also carries instrument3 on board, has power available, and is currently pointing towards phenomenon10."}
{"question_id": "feb6a2e0-8d76-4a21-b2f1-a212fb7d3947", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument1 that is on satellite1 is turned on, satellite1 turns from star3 to star1, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, satellite1's instrument1 takes an image of phenomenon5 in image4, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1 turns to phenomenon7 from phenomenon5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, satellite1 turns to phenomenon9 from phenomenon7, image of phenomenon9 is taken with instrument1 on satellite1 in image5, image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns from phenomenon9 to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5. Is the action: instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3 executable at step 6, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: the instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 turns to face phenomenon10, captures an image of phenomenon10 using image5 with instrument1, and also captures an image of phenomenon10 using spectrograph3 with instrument1, then satellite1 turns to face phenomenon11 from phenomenon10, captures an image of phenomenon11 using spectrograph1 with instrument1, turns to face phenomenon5 from phenomenon11, captures images of phenomenon5 using both image4 and image5 with instrument1, turns to face phenomenon7 from phenomenon5, captures images of phenomenon7 using both image0 and image4 with instrument1, turns to face phenomenon9 from phenomenon7, captures images of phenomenon9 using both image5 and spectrograph1 with instrument1, and finally turns to face planet8 from phenomenon9 and captures an image of planet8 using image5 with instrument1. Is the action: instrument1 on satellite1 captures an image of phenomenon10 in spectrograph3 executable at step 6, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and image2 is also supported by instrument0. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "c8d21922-05fb-4c1d-8b0b-d638a9818f9e", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, satellite1 turns from groundstation4 to star6, instrument3 is calibrated on satellite1 to star6, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, from planet14, satellite1 turns to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10, image of star12 is taken with instrument3 on satellite1 in spectrograph1, from star12, satellite1 turns to star0, from star1, satellite0 turns to groundstation2, instrument0 that is on satellite0 is calibrated to groundstation2, from groundstation2, satellite0 turns to phenomenon15, instrument0 which is on satellite0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and instrument0 which is on satellite0 takes an image of star13 in spectrograph0. Is the action: satellite1 turns to star10 from planet14 executable at step 7, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: on satellite1, instrument3 is activated, and on satellite0, instrument0 is activated. Satellite1 then reorients from groundstation4 to star6, followed by the calibration of instrument3 on satellite1 to star6. Next, satellite1 reorients from star6 to planet14, captures an image of planet14 using instrument3 and spectrograph1 on satellite1, and then reorients from planet14 to star10. An image of star10 is taken with instrument3 on satellite1 in spectrograph1. Satellite1 then reorients from star10 to star12, and an image of star12 is captured using instrument3 on satellite1 in spectrograph1. Satellite1 then reorients from star12 to star0. Meanwhile, satellite0 reorients from star1 to groundstation2, and instrument0 on satellite0 is calibrated to groundstation2. Satellite0 then reorients from groundstation2 to phenomenon15, captures an image of phenomenon15 using instrument0 on satellite0 in spectrograph0, reorients from phenomenon15 to star11, and captures an image of star11 using instrument0 on satellite0 in thermograph4. Finally, satellite0 reorients from star11 to star13 and captures an image of star13 using instrument0 on satellite0 in spectrograph0. Is the action: satellite1 turns to star10 from planet14 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0 and spectrograph2, and instrument3 is on board satellite1, supporting spectrograph1 and calibrated for star6. The power status of the satellites is as follows: satellite0 has power available, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. The compatibility of spectrographs with instruments is as follows: spectrograph0 is compatible with instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "dde203f2-bcac-4493-93a8-f75626b8cdb4", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: instrument1 that is on satellite0 is turned on. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: activating instrument1 located on satellite0. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for use at groundstation7 and star3, and it is also on board satellite0, providing support for image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4, while instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "8491036c-5bc2-4b8b-b169-f5596f2a723a", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: on satellite1, instrument3 is switched on. Is the action: instrument3 on satellite1 is switched on executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: on satellite1, instrument3 is activated. Is the action: instrument3 on satellite1 is activated executable at step 1, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 also carries instrument3 on board, has power available, and is currently pointing to phenomenon10."}
{"question_id": "9c0177f4-e504-447c-8b07-90bc18842d30", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: satellite1's instrument1 takes an image of phenomenon5 in image5. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: instrument1 of satellite1 captures an image of phenomenon5 in image5. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "b259af63-6e1a-4013-833d-a93edbcf545c", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: satellite0 turns from phenomenon16 to planet11. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: satellite0 will shift its focus from phenomenon16 to planet11. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 also has power available, carries instrument3 on board, and is currently pointing towards phenomenon10."}
{"question_id": "ea485949-73b6-488f-bf41-23be55ea7d42", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: on satellite1, instrument3 is switched on. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled: instrument3 will be activated on satellite1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0 and spectrograph2, and instrument3 is on board satellite1, supporting spectrograph1 and calibrated for star6. The power status of the satellites is as follows: satellite0 has available power, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "8676865d-ea5f-4934-b6b7-6d8be8a5a448", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: on satellite1, instrument3 is switched on, instrument0 that is on satellite0 is turned on, satellite1 turns from phenomenon10 to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, from groundstation5, satellite1 turns to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, satellite1's instrument3 takes an image of planet11 in image3, from planet11, satellite1 turns to planet13, image of planet13 is taken with instrument3 on satellite1 in image0, from planet13, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in image0, from planet14, satellite1 turns to star15, image of star15 is taken with instrument3 on satellite1 in image2, satellite0 turns to star1 from groundstation3, instrument0 is calibrated on satellite0 to star1 and from star1, satellite0 turns to phenomenon10. Is the action: image of phenomenon16 is taken with instrument3 on satellite1 in image3 executable at step 6, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: on satellite1, instrument3 is activated, and instrument0 on satellite0 is turned on. Satellite1 then reorients from phenomenon10 to groundstation5, followed by the calibration of instrument3 on satellite1 to groundstation5. Next, satellite1 reorients from groundstation5 to phenomenon16, and instrument3 on satellite1 captures an image of phenomenon16 in image3. Satellite1 then reorients from phenomenon16 to phenomenon17, and instrument3 on satellite1 captures an image of phenomenon17 in image3. This is followed by satellite1 reorienting from phenomenon17 to planet11, and instrument3 on satellite1 captures an image of planet11 in image3. Satellite1 then reorients from planet11 to planet13, and an image of planet13 is captured with instrument3 on satellite1 in image0. Next, satellite1 reorients from planet13 to planet14, and an image of planet14 is captured with instrument3 on satellite1 in image0. Satellite1 then reorients from planet14 to star15, and an image of star15 is captured with instrument3 on satellite1 in image2. Meanwhile, satellite0 reorients from groundstation3 to star1, instrument0 is calibrated on satellite0 to star1, and then satellite0 reorients from star1 to phenomenon10. Is the action: an image of phenomenon16 is taken with instrument3 on satellite1 in image3 executable at step 6, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and also has instrument0. Satellite0 has available power. Satellite1 carries instrument3 on board, has available power, and is currently pointing towards phenomenon10."}
{"question_id": "0ae8f47b-d863-45c6-a1e5-865c5da1eb1b", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, from phenomenon10, satellite1 turns to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1 turns to groundstation7 from phenomenon10. Is the action: from phenomenon10, satellite1 turns to groundstation7 executable at step 10, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, from phenomenon16, satellite1 reorients to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, satellite1 reorients from phenomenon17 to planet11, and satellite1 reorients from phenomenon10 to groundstation7. Is the action: from phenomenon10, satellite1 reorients to groundstation7 executable at step 10, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. The current target of satellite0 is groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is currently on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 carries instrument3 on board, has power available, and is currently pointing towards phenomenon10."}
{"question_id": "a850070b-6c01-4ec4-9a5a-9eeceab88c62", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 is calibrated on satellite1 to star1, satellite1 turns to phenomenon10 from star1, image of phenomenon10 is taken with instrument1 on satellite1 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5, satellite0 turns to star6 from groundstation0, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1 turns from phenomenon5 to phenomenon7, image of phenomenon7 is taken with instrument1 on satellite1 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, from phenomenon7, satellite1 turns to phenomenon9, satellite1's instrument1 takes an image of phenomenon9 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1, satellite1 turns from phenomenon9 to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5. Is the action: from groundstation0, satellite0 turns to star6 executable at step 10, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: instrument1 on satellite1 is activated, satellite1 rotates from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then rotates from star1 to phenomenon10, and captures an image of phenomenon10 using instrument1 in image5, followed by capturing an image of phenomenon10 with instrument1 on satellite1 in spectrograph3, satellite1 then rotates from phenomenon10 to phenomenon11, captures an image of phenomenon11 using instrument1 on satellite1 in spectrograph1, from phenomenon11, satellite1 rotates to phenomenon5, meanwhile satellite0 rotates from groundstation0 to star6, instrument1 on satellite1 captures an image of phenomenon5 in image5, satellite1 then rotates from phenomenon5 to phenomenon7, captures an image of phenomenon7 using instrument1 on satellite1 in image0, and another image of phenomenon7 in image4, from phenomenon7, satellite1 rotates to phenomenon9, captures an image of phenomenon9 using instrument1 on satellite1 in image5, and another image of phenomenon9 in spectrograph1, satellite1 then rotates from phenomenon9 to planet8, and instrument1 on satellite1 captures an image of planet8 in image5. Is the action: from groundstation0, satellite0 turns to star6 executable at step 10, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Additionally, instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5, and it has instrument0 onboard with available power. Satellite1, which also has power, is carrying instrument1 and is directed towards star3. Furthermore, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "ff71c551-4e12-40e9-a5db-e51cef08a80c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from groundstation0, satellite0 turns to planet11, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 on satellite0 is switched on, instrument1 that is on satellite0 is calibrated to groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns to groundstation0 from groundstation2, satellite0 turns to star10 from planet13 and satellite0 turns to star12 from phenomenon14. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: starting from groundstation0, satellite0 will first point towards planet11, then from planet11, it will point towards planet13, capture an image of planet13 using instrument1 on satellite0 and store it in image5, activate instrument1 on satellite0, calibrate instrument1 on satellite0 to groundstation0, use instrument1 on satellite0 to capture an image of planet11 in image5, use instrument1 on satellite0 to capture an image of planet13 in spectrograph2, redirect satellite0 from groundstation2 to groundstation0, redirect satellite0 from planet13 to star10, and finally redirect satellite0 from phenomenon14 to star12. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is installed on satellite0, which also supports image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4, while instrument3 supports both image1 and spectrograph4. Instrument4 has been calibrated for groundstation8 and supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "046df4ed-b2cd-4755-975a-9487f85e2228", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from phenomenon10, satellite1 turns to phenomenon11, from phenomenon11, satellite1 turns to phenomenon5, from phenomenon5, satellite1 turns to phenomenon7, from star3, satellite1 turns to star1, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, image of phenomenon5 is taken with instrument1 on satellite1 in image4, image of phenomenon5 is taken with instrument1 on satellite1 in image5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, instrument1 is calibrated on satellite1 to star1, instrument1 on satellite1 is switched on, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, satellite1 turns from phenomenon7 to phenomenon9, satellite1 turns from star1 to phenomenon10, satellite1 turns to planet8 from phenomenon9, satellite1's instrument0 takes an image of planet8 in spectrograph3, satellite1's instrument1 takes an image of phenomenon9 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1 and satellite1's instrument1 takes an image of planet8 in image5. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: starting from phenomenon10, satellite1 will move to phenomenon11, then from phenomenon11 to phenomenon5, and from phenomenon5 to phenomenon7. Additionally, satellite1 will move from star3 to star1. Satellite1's instrument1 will capture images of phenomenon11 in spectrograph1, phenomenon5 in image4 and image5, and phenomenon7 in image0. Furthermore, instrument1 on satellite1 will be calibrated to star1, switched on, and used to capture images of phenomenon10 in image5 and phenomenon7 in image4. Satellite1 will then move from phenomenon7 to phenomenon9, from star1 to phenomenon10, and from phenomenon9 to planet8. Images of planet8 will be captured by satellite1's instrument0 in spectrograph3 and by instrument1 in image5, and images of phenomenon9 will be captured by instrument1 in image5 and spectrograph1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and image2 is also supported by instrument0. Instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5. Satellite0 has instrument0 onboard and is operational with available power. Satellite1, which carries instrument1, also has power and is currently directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "278b5a8f-1795-4dfc-abbe-772a55e80d6e", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, satellite1 turns from phenomenon10 to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns to phenomenon16 from groundstation5, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1's instrument3 takes an image of planet11 in image3. Is the action: from phenomenon10, satellite1 turns to groundstation5 executable at step 3, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured by instrument3 on satellite1 and stored in image3, satellite1 then reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured by instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon17 to planet11, and an image of planet11 is captured by instrument3 on satellite1 and stored in image3. Is the action of reorienting satellite1 from phenomenon10 to groundstation5 executable at step 3, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. Satellite0 is currently directed at groundstation3. Image0 is compatible with instrument1 and is also supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and is supported by instrument3. Infrared1 is compatible with instrument0 and is supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and also has instrument0 on board, with power available. Satellite1 carries instrument3 on board, has power available, and is currently pointing at phenomenon10."}
{"question_id": "6a2e04fb-4c04-4efc-b031-cf5f1741bebb", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: calibration of instrument0 which is on satellite0 to star1 is complete, from groundstation3, satellite0 turns to star1, from star1, satellite0 turns to phenomenon10, image of phenomenon16 is taken with instrument3 on satellite1 in image3, image of star15 is taken with instrument3 on satellite1 in image2, instrument0 on satellite0 is switched on, instrument3 that is on satellite1 is calibrated to groundstation5, instrument3 that is on satellite1 is turned on, instrument3 which is on satellite1 takes an image of planet11 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1 turns from planet11 to planet13, satellite1 turns from planet14 to star15, satellite1 turns to phenomenon16 from groundstation5, satellite1 turns to planet11 from phenomenon17, satellite1 turns to planet14 from planet13, satellite1's instrument0 takes an image of groundstation0 in image2, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1's instrument3 takes an image of planet13 in image0 and satellite1's instrument3 takes an image of planet14 in image0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: the calibration of instrument0 on satellite0 to star1 is complete, then satellite0 will rotate from groundstation3 to star1, followed by a rotation from star1 to phenomenon10, meanwhile, satellite1 will capture images of phenomenon16 in image3 and star15 in image2 using instrument3, instrument0 on satellite0 will be activated, instrument3 on satellite1 will be calibrated to groundstation5 and turned on, then it will capture an image of planet11 in image3, satellite1 will then rotate from phenomenon16 to phenomenon17, from planet11 to planet13, from planet14 to star15, from groundstation5 to phenomenon16, from phenomenon17 to planet11, and from planet13 to planet14, subsequently, instrument0 on satellite1 will capture an image of groundstation0 in image2, and instrument3 on satellite1 will capture images of phenomenon17 in image3, planet13 in image0, and planet14 in image0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Instrument1 has been calibrated for groundstation0, while instrument0 has been calibrated for star9. Satellite0 is currently directed at groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 has been calibrated for star1 and supports image3. Instrument2 is on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 carries instrument3 on board, has power available, and is currently pointing at phenomenon10."}
{"question_id": "058f8516-cd98-41f2-a220-b3235bf2ac2c", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: calibration of instrument3 which is on satellite1 to star6 is complete, from groundstation4, satellite1 turns to star6, from star10, satellite1 turns to star12, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, image of star12 is taken with instrument3 on satellite1 in spectrograph1, instrument3 that is on satellite1 is turned on, satellite1 turns to star1 from groundstation5, satellite1 turns to star10 from planet14 and satellite1's instrument3 takes an image of star10 in spectrograph1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: the calibration of instrument3 on satellite1 to star6 is complete, satellite1 will rotate from groundstation4 to star6, then from star10 to star12, and from star6 to planet14, an image of planet14 will be captured using instrument3 on satellite1 in spectrograph1, an image of star12 will be captured using instrument3 on satellite1 in spectrograph1, instrument3 on satellite1 will be activated, satellite1 will rotate from groundstation5 to star1, then from planet14 to star10, and finally, satellite1's instrument3 will capture an image of star10 in spectrograph1. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0 and spectrograph2, and instrument3 is on board satellite1, supporting spectrograph1 and calibrated for star6. The power status of the satellites is as follows: satellite0 has available power, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "b08bcaf9-34ff-4051-af51-4a13b501da9e", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: instrument1 on satellite1 is switched on. Is the action: instrument1 on satellite1 is switched on executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include activating instrument1 on satellite1. Is the action of activating instrument1 on satellite1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "476081a9-2b78-4971-a9b2-aa54b087b174", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: calibration of instrument2 which is on satellite0 to star4 is complete, from groundstation2, satellite0 turns to groundstation0, from star10, satellite0 turns to star4, from star4, satellite0 turns to star16, image of planet11 is taken with instrument1 on satellite0 in image6, instrument1 is calibrated on satellite0 to groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of star10 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, instrument2 on satellite0 is switched off, instrument2 that is on satellite0 is turned on, on satellite0, instrument1 is switched off, on satellite0, instrument1 is switched on, satellite0 turns from planet11 to planet13, satellite0 turns to planet11 from groundstation0, satellite0 turns to star10 from planet13, satellite0's instrument1 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and satellite0's instrument2 takes an image of star16 in image0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for groundstation9 is complete, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image5 is supported by instrument1, image6 is supported by instrument1, infrared7 is compatible with instrument4, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 supports image1, instrument3 supports spectrograph4, instrument4 is calibrated for groundstation8, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: the calibration of instrument2 on satellite0 to star4 is complete, satellite0 rotates from groundstation2 to groundstation0, then from star10 to star4, and then from star4 to star16, an image of planet11 is captured using instrument1 on satellite0 in image6, instrument1 on satellite0 is calibrated to groundstation0, instrument1 on satellite0 captures an image of planet11 in image5, instrument1 on satellite0 captures an image of star10 in image6, instrument1 on satellite0 captures an image of star10 in spectrograph2, instrument2 on satellite0 is deactivated, instrument2 on satellite0 is activated, instrument1 on satellite0 is deactivated, instrument1 on satellite0 is activated, satellite0 rotates from planet11 to planet13, then from groundstation0 to planet11, and then from planet13 to star10, instrument1 on satellite0 captures an image of planet13 in image5, instrument1 on satellite0 captures an image of planet13 in spectrograph2, and instrument2 on satellite0 captures an image of star16 in image0. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, and the same has been completed for instrument3 at groundstation9. Currently, satellite0 is directed towards groundstation2. Image0 and image1 are both compatible with instrument2. Additionally, instrument1 supports image5 and image6. Infrared7 is found to be compatible with instrument4. Instrument0 has been calibrated for both groundstation7 and star3, and it is also on board satellite0, providing support for image6. Instrument1 has been calibrated for groundstation6 and supports spectrograph2. Instrument2 has been calibrated for star4, while instrument3 supports both image1 and spectrograph4. Instrument4, calibrated for groundstation8, also supports image1. Satellite0 has power available and carries instrument1, instrument2, and instrument3 on board. Satellite1, which also has power, carries instrument4 and is currently pointing towards planet13. Furthermore, thermograph3 is compatible with instrument3."}
{"question_id": "dd2ee378-40f0-4040-88c2-06b4b46462e3", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: satellite1 turns to star16 from groundstation9. Is the action: satellite1 turns from groundstation9 to star16 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: satellite1 turns to star16 from groundstation9. Is the action: satellite1 turns from groundstation9 to star16 executable at step 1, True or False?\n\nParaphrased text: \nGiven the initial condition, for steps 1 through 1 the following actions are planned to be performed: satellite1 turns to star16 from groundstation9. Can the action of satellite1 turning from groundstation9 to star16 be executed at step 1, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also confirmed that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, and instrument2 is on board satellite1. Instrument1 supports spectrograph1, while instrument2 supports both spectrograph0 and spectrograph2. Instrument3, which is on board satellite1, is calibrated for star6 and supports spectrograph1. The power status of the satellites is as follows: satellite0 has available power, and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks have confirmed that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "07c0a4c6-2bb2-4bd4-bf26-0c994f7452a3", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: on satellite1, instrument1 is switched on. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled: instrument1 on satellite1 will be activated. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "37536d9f-d7c0-4509-b663-343b0f1bbbc8", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: instrument0 which is on satellite1 takes an image of star8 in spectrograph2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: instrument0 on satellite1 is scheduled to capture an image of star8 using spectrograph2. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. For groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. Additionally, infrared3 is compatible with instrument2. Instrument0 is currently on board satellite0, and instrument1 is also on board satellite0, with instrument1 supporting spectrograph1. Instrument2 is on board satellite1, supporting both spectrograph0 and spectrograph2. Instrument3, which is on board satellite1, is calibrated for star6 and supports spectrograph1. Satellite0 has available power, and satellite1 has power as well, with satellite1 currently aimed towards groundstation4. Spectrograph0 is compatible with both instrument0 and instrument1, while spectrograph2 is compatible with instrument3. Satellite0 is currently pointed at star1, and instrument0 supports thermograph4."}
{"question_id": "6b8a1a1b-5918-4f27-85e9-80cbc21d4b30", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from groundstation0, satellite0 turns to star1. Is the action: satellite0 turns to star1 from groundstation0 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions are as follows: satellite0 is set to turn towards star1 from groundstation0. Is the action of satellite0 turning to star1 from groundstation0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and image2 is also supported by instrument0. Instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5. Satellite0 has instrument0 onboard and is operational with available power. Satellite1, which also has instrument1 onboard, is powered and directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "29a02dc6-63e7-4753-a725-64b4907cf089", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument1 that is on satellite1 is turned on, satellite1 turns to star1 from star3, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns to phenomenon10 from star1, image of phenomenon10 is taken with instrument1 on satellite1 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, instrument1 that is on satellite1 is calibrated to star1 and satellite1's instrument1 takes an image of phenomenon5 in image4. Is the action: calibration of instrument1 which is on satellite1 to star1 is complete executable at step 9, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: the instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then reorients from star1 to phenomenon10, an image of phenomenon10 is captured using instrument1 on satellite1 and stored in image5, instrument1 on satellite1 captures an image of phenomenon10 in spectrograph3, satellite1 reorients from phenomenon10 to phenomenon11, an image of phenomenon11 is captured using instrument1 on satellite1 in spectrograph1, instrument1 on satellite1 is recalibrated to star1, and finally, instrument1 on satellite1 captures an image of phenomenon5 in image4. Is the action: calibration of instrument1 on satellite1 to star1 complete executable at step 9, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has power, is carrying instrument1 and is directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "09579b98-12b9-4d15-80ed-8c63b114973e", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: satellite0 turns to groundstation3 from planet14. Is the action: satellite0 turns from planet14 to groundstation3 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for groundstation0, instrument1 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image0 is supported by instrument3, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is supported by instrument3, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 supports image3, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, satellite0 carries instrument1 on board, satellite0 has instrument0 on board, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: satellite0 is set to turn towards groundstation3 from planet14. Is the action of satellite0 turning from planet14 to groundstation3 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument2 has been completed for groundstation5, groundstation7, and star9. Instrument3 has also been calibrated for star6. Groundstation0 has a calibrated instrument1, while star9 has a calibrated instrument0. Satellite0 is currently directed at groundstation3. Image0 is compatible with instrument1 and supported by instrument3. Image2 is compatible with both instrument2 and instrument3. Image3 is compatible with instrument1, instrument2, and supported by instrument3. Infrared1 is compatible with instrument0 and supported by instrument1. Instrument0 is calibrated for star1 and supports image3. Instrument2 is on board satellite0. Instrument3 has been calibrated for groundstation5 and star8. Satellite0 carries instrument1 on board and has instrument0 on board, with power available. Satellite1 also carries instrument3 on board, has power available, and is currently pointing at phenomenon10."}
{"question_id": "7101bf1c-2abd-4671-ae2f-ef751780b56f", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11 and satellite1's instrument1 takes an image of phenomenon5 in image4. Is the action: instrument1 which is on satellite1 takes an image of phenomenon10 in image5 executable at step 5, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the instrument1 on satellite1 is activated, satellite1 rotates from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 turns from star1 to phenomenon10, captures an image of phenomenon10 using image5 with instrument1, takes a spectrograph of phenomenon10 using spectrograph3 with instrument1, turns from phenomenon10 to phenomenon11, captures an image of phenomenon11 using spectrograph1 with instrument1, turns from phenomenon11 to phenomenon5, and captures an image of phenomenon5 using image4 with instrument1. Is the action: instrument1 on satellite1 captures an image of phenomenon10 using image5 executable at step 5, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 also supports image2. Instrument1 is capable of supporting image4 and image5. The current focus of satellite0 is on phenomenon5. Satellite0 has instrument0 onboard and is currently powered. Satellite1, which also has power, is carrying instrument1 and is directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
{"question_id": "01ef05f0-56ed-4df6-bb6c-9cca43d2f657", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from groundstation2, satellite0 turns to phenomenon15, from star1, satellite0 turns to groundstation2, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, image of star10 is taken with instrument3 on satellite1 in spectrograph1, instrument0 is calibrated on satellite0 to groundstation2, instrument0 which is on satellite0 takes an image of star10 in thermograph4, instrument0 which is on satellite0 takes an image of star11 in thermograph4, instrument0 which is on satellite0 takes an image of star13 in spectrograph0, instrument3 on satellite1 is switched on, instrument3 that is on satellite1 is calibrated to star6, on satellite0, instrument0 is switched on, satellite0 turns from phenomenon15 to star11, satellite0 turns from star11 to star13, satellite1 turns from groundstation4 to star6, satellite1 turns from star12 to star0, satellite1 turns from star6 to planet14, satellite1 turns to star10 from planet14 and satellite1's instrument3 takes an image of star12 in spectrograph1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for groundstation4, instrument0 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is on board satellite0, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, satellite0 has power available, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph2 is compatible with instrument3, star1 is where satellite0 is pointed and thermograph4 is supported by instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: starting from groundstation2, satellite0 will orient towards phenomenon15, then from star1, it will reorient towards groundstation2. Satellite0 will capture an image of phenomenon15 using instrument0 in spectrograph0, while satellite1 will capture images of planet14 and star10 using instrument3 in spectrograph1. Additionally, instrument0 on satellite0 will be calibrated to groundstation2 and will capture images of star10, star11, and star13 using thermograph4 and spectrograph0, respectively. Instrument3 on satellite1 will be activated and calibrated to star6. Satellite0 will switch on instrument0, then reorient from phenomenon15 to star11 and from star11 to star13. Satellite1 will reorient from groundstation4 to star6, from star12 to star0, from star6 to planet14, and from planet14 to star10. Finally, instrument3 on satellite1 will capture an image of star12 in spectrograph1. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation2 has been finalized, instrument0's calibration for star0 is also complete, instrument1's calibration for groundstation2 is complete, and instrument1's calibration for star8 is complete. Furthermore, instrument2's calibration for groundstation4 and groundstation9 is complete. Additionally, for groundstation4, instrument0 has been calibrated, and for star7, instrument2 has been calibrated. It is also noted that infrared3 is compatible with instrument2. The current status of the instruments on board the satellites is as follows: instrument0 is on board satellite0, instrument1 is also on board satellite0, and instrument2 is on board satellite1. Instrument1 supports spectrograph1, while instrument2 supports both spectrograph0 and spectrograph2. Instrument3, which is on board satellite1, is calibrated for star6 and supports spectrograph1. The power status of the satellites indicates that satellite0 has power available and satellite1 has power. Satellite1 is currently aimed towards groundstation4. Compatibility checks reveal that spectrograph0 is compatible with both instrument0 and instrument1, and spectrograph2 is compatible with instrument3. The current position of satellite0 is pointed towards star1, and instrument0 supports thermograph4."}
{"question_id": "8f4c2e65-054d-49d7-8a44-966dd29fc4e4", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from phenomenon7, satellite1 turns to phenomenon9, from phenomenon9, satellite1 turns to planet8, image of phenomenon10 is taken with instrument1 on satellite1 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, image of planet8 is taken with instrument1 on satellite1 in image5, instrument1 that is on satellite1 is calibrated to star1, instrument1 which is on satellite1 takes an image of phenomenon7 in image0, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, on satellite1, instrument1 is switched on, satellite1 turns from phenomenon10 to phenomenon11, satellite1 turns from phenomenon11 to phenomenon5, satellite1 turns from star1 to phenomenon10, satellite1 turns from star3 to star1, satellite1 turns to phenomenon7 from phenomenon5, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1's instrument1 takes an image of phenomenon5 in image4, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1's instrument1 takes an image of phenomenon9 in image5 and satellite1's instrument1 takes an image of phenomenon9 in spectrograph1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument1, image2 is supported by instrument0, instrument1 supports image4, instrument1 supports image5, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: starting from phenomenon7, satellite1 will move to phenomenon9, then from phenomenon9, it will move to planet8, an image of phenomenon10 will be captured using instrument1 on satellite1 in image5, another image of phenomenon10 will be captured using instrument1 on satellite1 in spectrograph3, an image of planet8 will be captured using instrument1 on satellite1 in image5, instrument1 on satellite1 will be calibrated to star1, instrument1 on satellite1 will capture an image of phenomenon7 in image0, instrument1 on satellite1 will also capture an image of phenomenon7 in image4, instrument1 on satellite1 will be activated, satellite1 will move from phenomenon10 to phenomenon11, then from phenomenon11 to phenomenon5, satellite1 will move from star1 to phenomenon10, from star3 to star1, and from phenomenon5 back to phenomenon7, instrument1 on satellite1 will capture an image of phenomenon11 in spectrograph1, an image of phenomenon5 in image4, another image of phenomenon5 in image5, an image of phenomenon9 in image5, and an image of phenomenon9 in spectrograph1. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image0 and image2 are both compatible with instrument1, and instrument0 supports image2. Instrument1 is capable of supporting image4 and image5. The satellite0 is currently focused on phenomenon5. Satellite0 has instrument0 on board and is operational with available power. Satellite1, which also has instrument1 on board and sufficient power, is currently directed towards star3. Additionally, spectrograph1 is compatible with instrument1, and spectrograph3 is supported by instrument1."}
