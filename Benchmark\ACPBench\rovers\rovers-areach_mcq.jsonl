{"id": 6487160052682438399, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint1 to waypoint4. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint1, and waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode colour. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. take an image of the objective objective0 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint4. B. collect a soil sample from waypoint waypoint1 using rover rover1 and deposit it in the store store1. C. calibrate the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint1. D. communicate the image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["take an image of the objective objective0 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint4", "collect a soil sample from waypoint waypoint1 using rover rover1 and deposit it in the store store1", "calibrate the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint1", "communicate the image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -7198711353834196187, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective1. Camera camera2 supports low_res and colour. Camera camera1 supports colour. Camera camera0 supports colour and low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint5, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint0 to waypoint5, waypoint5 to waypoint0, waypoint4 to waypoint5, waypoint5 to waypoint4, waypoint3 to waypoint0, waypoint0 to waypoint3, waypoint5 to waypoint6, waypoint5 to waypoint1, waypoint5 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint4, waypoint4 to waypoint2, waypoint6 to waypoint0, waypoint5 to waypoint0, waypoint0 to waypoint3, waypoint1 to waypoint6, waypoint6 to waypoint1, waypoint3 to waypoint0, waypoint0 to waypoint6, waypoint0 to waypoint2, waypoint0 to waypoint5, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint3: waypoint2, waypoint6, waypoint1, waypoint5, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, waypoint4, waypoint1, and waypoint5. Waypoint(s) are visible from waypoint5: waypoint0, waypoint4, waypoint3, waypoint2, waypoint6, and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2, waypoint6, waypoint5, and waypoint3. Waypoint(s) are visible from waypoint4: waypoint2, waypoint6, waypoint1, waypoint5, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint3, waypoint4, waypoint1, waypoint5, and waypoint0. Waypoint(s) are visible from waypoint1: waypoint4, waypoint3, waypoint2, waypoint6, and waypoint5. Objective objective1 is visible from waypoint5, waypoint4, waypoint2, and waypoint0. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint5. Rocks can be sampled at the following location(s): waypoint4, waypoint6, and waypoint2. Soil can be sampled at the following location(s): waypoint1, waypoint4, waypoint6, and waypoint3. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode colour. Image objective0 was communicated in mode high_res. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective1 in mode colour. Rover rover1 has image objective0 in mode high_res. Rover rover1 has image objective1 in mode high_res. Rover rover1 has its camera camera0 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate rock data from rover rover0 at waypoint waypoint4 about waypoint waypoint4 to lander general at waypoint waypoint3. B. sample the rock at waypoint waypoint1 with rover rover0 and store it in store store1. C. communicate soil data from rover rover0 at waypoint waypoint4 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint2. D. communicate soil data from rover rover0 at waypoint waypoint3 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate rock data from rover rover0 at waypoint waypoint4 about waypoint waypoint4 to lander general at waypoint waypoint3", "sample the rock at waypoint waypoint1 with rover rover0 and store it in store store1", "communicate soil data from rover rover0 at waypoint waypoint4 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint2", "communicate soil data from rover rover0 at waypoint waypoint3 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint5"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -7915196621233420275, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has its camera camera0 calibrated. Rover rover1 has its camera camera2 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0 through waypoint waypoint1. B. transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint2 using soil analysis of waypoint waypoint1. C. capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1. D. sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0 through waypoint waypoint1", "transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint2 using soil analysis of waypoint waypoint1", "capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1", "sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 3013667983380637275, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint1; Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to the lander general at waypoint waypoint0. B. calibrate the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint1. C. communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint2. D. transmit image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to the lander general at waypoint waypoint0", "calibrate the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint1", "communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint2", "transmit image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1882865811202470280, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective1 in mode colour. Store(s) store1 and store0 are full. ", "question": "Which of the following actions can eventually be applied? A. communicate rock data from rover rover0 at waypoint waypoint0 about waypoint waypoint2 to lander general at waypoint waypoint1. B. take an image of the objective objective0 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint1. C. take an image of the objective objective2 in mode high_res using the camera camera2 on the rover rover1 from the waypoint waypoint1. D. sample soil at waypoint waypoint1 with rover rover1 and store in store store0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate rock data from rover rover0 at waypoint waypoint0 about waypoint waypoint2 to lander general at waypoint waypoint1", "take an image of the objective objective0 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint1", "take an image of the objective objective2 in mode high_res using the camera camera2 on the rover rover1 from the waypoint waypoint1", "sample soil at waypoint waypoint1 with rover rover1 and store in store store0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 6812860318522027324, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate the image data of target objective0 in mode colour from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0. B. sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store1. C. navigate the rover rover1 from waypoint waypoint0 to waypoint waypoint2. D. communicate the rock data from the rover rover0 at waypoint waypoint1 to the lander general at waypoint waypoint0 via waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the image data of target objective0 in mode colour from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0", "sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store1", "navigate the rover rover1 from waypoint waypoint0 to waypoint waypoint2", "communicate the rock data from the rover rover0 at waypoint waypoint1 to the lander general at waypoint waypoint0 via waypoint waypoint2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2834564015319690960, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint2. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover1 has image objective1 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. move the rover rover1 from waypoint waypoint2 to waypoint waypoint1. B. collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0. C. communicate the soil data from rover rover1 at waypoint waypoint2 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1. D. sample a rock at waypoint waypoint2 using rover rover0, then store it in the storage unit store0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the rover rover1 from waypoint waypoint2 to waypoint waypoint1", "collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0", "communicate the soil data from rover rover1 at waypoint waypoint2 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1", "sample a rock at waypoint waypoint2 using rover rover0, then store it in the storage unit store0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -1272646515910504541, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. take a picture of the objective objective1 in mode colour using the camera camera1 mounted on the rover rover1 from the waypoint waypoint0. B. sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1. C. sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store0. D. guide the rover rover0 from waypoint waypoint2 to waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["take a picture of the objective objective1 in mode colour using the camera camera1 mounted on the rover rover1 from the waypoint waypoint0", "sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1", "sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store0", "guide the rover rover0 from waypoint waypoint2 to waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -6397785812748491525, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Store(s) store1 and store0 are full. ", "question": "Which of the following actions can eventually be applied? A. take an image of the objective objective2 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint2. B. navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2. C. communicate the soil data from rover rover0 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint0. D. sample a rock at waypoint waypoint1 using rover rover1, then store it in the storage unit store0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["take an image of the objective objective2 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint2", "navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2", "communicate the soil data from rover rover0 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint0", "sample a rock at waypoint waypoint1 using rover rover1, then store it in the storage unit store0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1951659920792967428, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint1 to waypoint4. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint1, and waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. calibrate the camera camera0 on the rover rover1 for the objective objective1 at the waypoint waypoint3. B. sample soil at waypoint waypoint3 with rover rover0 and store in the store store1. C. communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0. D. calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["calibrate the camera camera0 on the rover rover1 for the objective objective1 at the waypoint waypoint3", "sample soil at waypoint waypoint3 with rover rover0 and store in the store store1", "communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0", "calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -439011978614845221, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint2 and waypoint0. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode high_res. Rover rover0 has image objective1 in mode high_res. Rover rover0 has image objective1 in mode low_res. Rover rover0 has its camera camera1 calibrated. Store(s) store1 and store0 are empty. ", "question": "Which of the following actions can eventually be applied? A. transmit soil data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2. B. sample the soil at waypoint waypoint1 using the rover rover1, then store it in the storage unit store1. C. navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2. D. collect a sample from the waypoint waypoint1 using the rover rover1 and store it in the store store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transmit soil data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2", "sample the soil at waypoint waypoint1 using the rover rover1, then store it in the storage unit store1", "navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2", "collect a sample from the waypoint waypoint1 using the rover rover1 and store it in the store store1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 4947089732618623982, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint1 and waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode high_res. Rover rover0 has image objective0 in mode high_res. Store(s) store1 and store0 are empty. ", "question": "Which of the following actions can eventually be applied? A. communicate rock data from rover rover1 at waypoint waypoint2 about waypoint waypoint1 to lander general at waypoint waypoint0. B. sample rock at waypoint waypoint1 with rover rover0 and store in store store1. C. communicate the image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint2. D. adjust the camera camera2 on the rover rover0 for the objective objective1 at waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate rock data from rover rover1 at waypoint waypoint2 about waypoint waypoint1 to lander general at waypoint waypoint0", "sample rock at waypoint waypoint1 with rover rover0 and store in store store1", "communicate the image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint2", "adjust the camera camera2 on the rover rover0 for the objective objective1 at waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 652544272078049770, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode low_res. Image objective1 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover0 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective1 in mode high_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate rock data from rover rover1 at waypoint waypoint1 about waypoint waypoint2 to lander general at waypoint waypoint0. B. communicate soil data from rover rover0 at waypoint waypoint2 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint0. C. navigate the rover rover0 from waypoint waypoint2 to waypoint waypoint1. D. take an image of objective objective0 in mode high_res using camera camera0 on rover rover0 from waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate rock data from rover rover1 at waypoint waypoint1 about waypoint waypoint2 to lander general at waypoint waypoint0", "communicate soil data from rover rover0 at waypoint waypoint2 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint0", "navigate the rover rover0 from waypoint waypoint2 to waypoint waypoint1", "take an image of objective objective0 in mode high_res using camera camera0 on rover rover0 from waypoint waypoint0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 200078832278288779, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint1; Image objective0 was communicated in mode low_res. Rover rover1 has soil analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover1 has its camera camera2 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. sample soil at waypoint waypoint2 with rover rover1 and store in the store store0. B. communicate the image data of target objective0 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1. C. calibrate the camera camera2 on the rover rover1 for the objective objective0 at the waypoint waypoint0. D. sample soil at waypoint waypoint2 with rover rover0 and store in the store store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sample soil at waypoint waypoint2 with rover rover1 and store in the store store0", "communicate the image data of target objective0 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1", "calibrate the camera camera2 on the rover rover1 for the objective objective0 at the waypoint waypoint0", "sample soil at waypoint waypoint2 with rover rover0 and store in the store store1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -8957607470194856174, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode low_res. Image objective1 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective1 in mode high_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. collect a rock sample from waypoint waypoint1 using rover rover1 and store it in store store0. B. collect a rock sample from waypoint waypoint1 using rover rover0 and store it in store store1. C. transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint2 through waypoint waypoint1. D. move the rover rover1 from waypoint waypoint1 to waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["collect a rock sample from waypoint waypoint1 using rover rover1 and store it in store store0", "collect a rock sample from waypoint waypoint1 using rover rover0 and store it in store store1", "transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint2 through waypoint waypoint1", "move the rover rover1 from waypoint waypoint1 to waypoint waypoint0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
