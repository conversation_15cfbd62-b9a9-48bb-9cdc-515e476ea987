{"id": 1236441038070247198, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f2-2f, f0-3f, and f2-1f. The following locations have soft rock: f1-3f, f0-2f, f2-3f, f0-1f, f1-2f, and f1-1f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, move to location f1-0f from location f0-0f, fire the laser from location f1-0f to location f1-1f, fire the laser from location f1-0f to location f1-1f, move to location f1-1f from location f1-0f, fire the laser from location f1-1f to location f1-2f, fire the laser from location f1-1f to location f1-2f, put down the laser at location f1-1f, move to location f1-0f from location f1-1f, move to location f0-0f from location f1-0f, pick up the bomb at loc f0-0f, move to location f1-0f from location f0-0f, move to location f1-1f from location f1-0f, move to location f1-2f from location f1-1f, detonate bomb at loc f1-2f connected to loc f1-3f, move to location f1-3f from location f1-2f, pick up gold at location f1-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. move to location f1-2f from location f1-1f. B. pick up gold at location f1-3f. C. pick up the bomb at loc f0-0f. D. fire the laser from location f1-1f to location f1-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move to location f1-2f from location f1-1f", "pick up gold at location f1-3f", "pick up the bomb at loc f0-0f", "fire the laser from location f1-1f to location f1-2f"]}, "query": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, move to location f1-0f from location f0-0f, fire the laser from location f1-0f to location f1-1f, fire the laser from location f1-0f to location f1-1f, move to location f1-1f from location f1-0f, fire the laser from location f1-1f to location f1-2f, fire the laser from location f1-1f to location f1-2f, put down the laser at location f1-1f, move to location f1-0f from location f1-1f, move to location f0-0f from location f1-0f, pick up the bomb at loc f0-0f, move to location f1-0f from location f0-0f, move to location f1-1f from location f1-0f, move to location f1-2f from location f1-1f, detonate bomb at loc f1-2f connected to loc f1-3f, move to location f1-3f from location f1-2f, pick up gold at location f1-3f\"; which action can be removed from this plan?", "answer": "D"}
{"id": -5447684216836377092, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f and f0-2f. The following locations have soft rock: f1-3f, f0-3f, f2-2f, f2-3f, f0-1f, f1-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at location f0-0f, fire laser from loc f0-0f to loc f0-1f, move to location f0-1f from location f0-0f, fire laser from loc f0-1f to loc f1-1f, fire laser from loc f0-1f to loc f1-1f, fire laser from loc f0-1f to loc f0-2f, put down laser at loc f0-1f, move to location f0-0f from location f0-1f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate bomb at loc f0-2f connected to loc f0-3f, move to location f0-3f from location f0-2f, pick up gold at location f0-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. fire laser from loc f0-1f to loc f0-2f. B. fire laser from loc f0-1f to loc f1-1f. C. move to location f0-0f from location f0-1f. D. move to location f0-0f from location f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fire laser from loc f0-1f to loc f0-2f", "fire laser from loc f0-1f to loc f1-1f", "move to location f0-0f from location f0-1f", "move to location f0-0f from location f1-0f"]}, "query": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at location f0-0f, fire laser from loc f0-0f to loc f0-1f, move to location f0-1f from location f0-0f, fire laser from loc f0-1f to loc f1-1f, fire laser from loc f0-1f to loc f1-1f, fire laser from loc f0-1f to loc f0-2f, put down laser at loc f0-1f, move to location f0-0f from location f0-1f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate bomb at loc f0-2f connected to loc f0-3f, move to location f0-3f from location f0-2f, pick up gold at location f0-3f\"; which action can be removed from this plan?", "answer": "B"}
{"id": -5672289382475270830, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f3-2f, f0-2f, f0-3f, f2-2f, f2-3f, f0-1f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up the laser at location f0-0f, fire the laser from location f0-0f to location f0-1f, move from loc f0-0f to loc f0-1f, fire the laser from location f0-1f to location f0-2f, fire the laser from location f0-1f to location f1-1f, place the laser at location f0-1f, move from loc f0-1f to loc f0-0f, retrieve the bomb from location f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move from loc f0-2f to loc f0-3f, retrieve gold from location f0-3f, move from loc f0-3f to loc f0-2f, move from loc f0-2f to loc f0-1f\"; which of the following actions can be removed from this plan and still have a valid plan? A. move from loc f2-0f to loc f1-0f. B. move from loc f0-3f to loc f0-2f. C. fire the laser from location f0-1f to location f1-1f. D. fire the laser from location f0-1f to location f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from loc f2-0f to loc f1-0f", "move from loc f0-3f to loc f0-2f", "fire the laser from location f0-1f to location f1-1f", "fire the laser from location f0-1f to location f0-2f"]}, "query": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up the laser at location f0-0f, fire the laser from location f0-0f to location f0-1f, move from loc f0-0f to loc f0-1f, fire the laser from location f0-1f to location f0-2f, fire the laser from location f0-1f to location f1-1f, place the laser at location f0-1f, move from loc f0-1f to loc f0-0f, retrieve the bomb from location f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move from loc f0-2f to loc f0-3f, retrieve gold from location f0-3f, move from loc f0-3f to loc f0-2f, move from loc f0-2f to loc f0-1f\"; which action can be removed from this plan?", "answer": "C"}
{"id": -4807133745930945361, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f3-2f, f0-2f, f0-3f, f2-2f, f2-3f, f0-1f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the laser at loc f0-0f, direct the laser from location f0-0f to location f0-1f, move from location f0-0f to location f0-1f, direct the laser from location f0-1f to location f0-2f, put down the laser at location f0-1f, move from location f0-1f to location f0-0f, retrieve the bomb from location f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f0-2f, detonate the bomb at location f0-2f connected to location f0-3f, move from location f0-2f to location f0-3f, move from location f0-3f to location f0-2f, move from location f0-2f to location f0-3f, pick up gold at loc f0-3f, move from location f0-3f to location f0-2f\"; which of the following actions can be removed from this plan and still have a valid plan? A. direct the laser from location f0-1f to location f0-2f. B. move from location f0-3f to location f0-2f. C. move from location f1-0f to location f0-0f. D. move from location f0-0f to location f0-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["direct the laser from location f0-1f to location f0-2f", "move from location f0-3f to location f0-2f", "move from location f1-0f to location f0-0f", "move from location f0-0f to location f0-1f"]}, "query": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the laser at loc f0-0f, direct the laser from location f0-0f to location f0-1f, move from location f0-0f to location f0-1f, direct the laser from location f0-1f to location f0-2f, put down the laser at location f0-1f, move from location f0-1f to location f0-0f, retrieve the bomb from location f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f0-2f, detonate the bomb at location f0-2f connected to location f0-3f, move from location f0-2f to location f0-3f, move from location f0-3f to location f0-2f, move from location f0-2f to location f0-3f, pick up gold at loc f0-3f, move from location f0-3f to location f0-2f\"; which action can be removed from this plan?", "answer": "B"}
{"id": -1911885321458898208, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f3-2f, f0-2f, f0-3f, f2-2f, f2-3f, f0-1f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f2-0f, move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the bomb at location f0-0f, detonate the bomb at location f0-0f connected to location f0-1f, pick up the bomb at location f0-0f, move from location f0-0f to location f0-1f, detonate the bomb at location f0-1f connected to location f0-2f, move from location f0-1f to location f0-0f, pick up the bomb at location f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f0-2f, detonate the bomb at location f0-2f connected to location f0-3f, move from location f0-2f to location f0-3f, retrieve gold from location f0-3f, move from location f0-3f to location f0-2f\"; which of the following actions can be removed from this plan and still have a valid plan? A. move from location f0-3f to location f0-2f. B. move from location f2-0f to location f1-0f. C. pick up the bomb at location f0-0f. D. move from location f0-0f to location f0-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from location f0-3f to location f0-2f", "move from location f2-0f to location f1-0f", "pick up the bomb at location f0-0f", "move from location f0-0f to location f0-1f"]}, "query": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f2-0f, move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the bomb at location f0-0f, detonate the bomb at location f0-0f connected to location f0-1f, pick up the bomb at location f0-0f, move from location f0-0f to location f0-1f, detonate the bomb at location f0-1f connected to location f0-2f, move from location f0-1f to location f0-0f, pick up the bomb at location f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f0-2f, detonate the bomb at location f0-2f connected to location f0-3f, move from location f0-2f to location f0-3f, retrieve gold from location f0-3f, move from location f0-3f to location f0-2f\"; which action can be removed from this plan?", "answer": "A"}
{"id": -962571908191549224, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f and f0-2f. The following locations have soft rock: f1-3f, f0-3f, f2-2f, f2-3f, f0-1f, f1-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up the laser at loc f0-0f, aim the laser from location f0-0f to location f0-1f, aim the laser from location f0-0f to location f0-1f, move from loc f0-0f to loc f0-1f, aim the laser from location f0-1f to location f0-2f, move from loc f0-1f to loc f0-2f, put down laser at loc f0-2f, move from loc f0-2f to loc f0-1f, move from loc f0-1f to loc f0-0f, pick up bomb at loc f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, detonate the bomb at location f0-2f connected to location f0-3f, move from loc f0-2f to loc f0-3f, pick up gold at location f0-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. aim the laser from location f0-0f to location f0-1f. B. move from loc f0-0f to loc f0-1f. C. move from loc f1-0f to loc f0-0f. D. aim the laser from location f0-1f to location f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["aim the laser from location f0-0f to location f0-1f", "move from loc f0-0f to loc f0-1f", "move from loc f1-0f to loc f0-0f", "aim the laser from location f0-1f to location f0-2f"]}, "query": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up the laser at loc f0-0f, aim the laser from location f0-0f to location f0-1f, aim the laser from location f0-0f to location f0-1f, move from loc f0-0f to loc f0-1f, aim the laser from location f0-1f to location f0-2f, move from loc f0-1f to loc f0-2f, put down laser at loc f0-2f, move from loc f0-2f to loc f0-1f, move from loc f0-1f to loc f0-0f, pick up bomb at loc f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, detonate the bomb at location f0-2f connected to location f0-3f, move from loc f0-2f to loc f0-3f, pick up gold at location f0-3f\"; which action can be removed from this plan?", "answer": "A"}
{"id": 1720585710965683555, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f3-2f, f0-2f, f0-3f, f2-2f, f2-3f, f0-1f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, acquire the laser from location f0-0f, fire the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, fire the laser from location f0-1f to location f0-2f, fire the laser from location f0-1f to location f1-1f, put down the laser at location f0-1f, move to location f0-0f from location f0-1f, pick up the bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate the bomb at location f0-2f connected to location f0-3f, move to location f0-3f from location f0-2f, pick up gold at loc f0-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. fire the laser from location f0-1f to location f1-1f. B. fire the laser from location f0-1f to location f0-2f. C. acquire the laser from location f0-0f. D. pick up gold at loc f0-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fire the laser from location f0-1f to location f1-1f", "fire the laser from location f0-1f to location f0-2f", "acquire the laser from location f0-0f", "pick up gold at loc f0-3f"]}, "query": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, acquire the laser from location f0-0f, fire the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, fire the laser from location f0-1f to location f0-2f, fire the laser from location f0-1f to location f1-1f, put down the laser at location f0-1f, move to location f0-0f from location f0-1f, pick up the bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate the bomb at location f0-2f connected to location f0-3f, move to location f0-3f from location f0-2f, pick up gold at loc f0-3f\"; which action can be removed from this plan?", "answer": "A"}
{"id": 6758016541651556423, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f3-2f, f0-2f, f0-3f, f2-2f, f2-3f, f0-1f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up laser at loc f0-0f, fire laser from loc f0-0f to loc f0-1f, move to location f0-1f from location f0-0f, fire laser from loc f0-1f to loc f0-2f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, pick up bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move to location f0-3f from location f0-2f, move to location f0-2f from location f0-3f, move to location f0-3f from location f0-2f, pick up the gold from location f0-3f, move to location f0-2f from location f0-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. move to location f0-2f from location f0-3f. B. pick up bomb at loc f0-0f. C. trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f. D. move to location f0-0f from location f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move to location f0-2f from location f0-3f", "pick up bomb at loc f0-0f", "trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f", "move to location f0-0f from location f1-0f"]}, "query": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up laser at loc f0-0f, fire laser from loc f0-0f to loc f0-1f, move to location f0-1f from location f0-0f, fire laser from loc f0-1f to loc f0-2f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, pick up bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move to location f0-3f from location f0-2f, move to location f0-2f from location f0-3f, move to location f0-3f from location f0-2f, pick up the gold from location f0-3f, move to location f0-2f from location f0-3f\"; which action can be removed from this plan?", "answer": "A"}
{"id": 1755016258468029022, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f2-2f, f0-3f, and f2-1f. The following locations have soft rock: f1-3f, f0-2f, f2-3f, f0-1f, f1-2f, and f1-1f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, move to location f1-0f from location f0-0f, fire laser from loc f1-0f to loc f1-1f, move to location f1-1f from location f1-0f, fire laser from loc f1-1f to loc f1-2f, fire laser from loc f1-1f to loc f1-2f, move to location f1-0f from location f1-1f, move to location f0-0f from location f1-0f, put down laser at loc f0-0f, retrieve the bomb from location f0-0f, move to location f1-0f from location f0-0f, move to location f1-1f from location f1-0f, move to location f1-2f from location f1-1f, detonate the bomb at location f1-2f connected to location f1-3f, move to location f1-3f from location f1-2f, retrieve gold from location f1-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. fire laser from loc f1-1f to loc f1-2f. B. pick up the laser at loc f0-0f. C. retrieve gold from location f1-3f. D. move to location f1-0f from location f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fire laser from loc f1-1f to loc f1-2f", "pick up the laser at loc f0-0f", "retrieve gold from location f1-3f", "move to location f1-0f from location f0-0f"]}, "query": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, move to location f1-0f from location f0-0f, fire laser from loc f1-0f to loc f1-1f, move to location f1-1f from location f1-0f, fire laser from loc f1-1f to loc f1-2f, fire laser from loc f1-1f to loc f1-2f, move to location f1-0f from location f1-1f, move to location f0-0f from location f1-0f, put down laser at loc f0-0f, retrieve the bomb from location f0-0f, move to location f1-0f from location f0-0f, move to location f1-1f from location f1-0f, move to location f1-2f from location f1-1f, detonate the bomb at location f1-2f connected to location f1-3f, move to location f1-3f from location f1-2f, retrieve gold from location f1-3f\"; which action can be removed from this plan?", "answer": "A"}
{"id": 7854970574006666376, "group": "action_justification_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f and f0-2f. The following locations have soft rock: f1-3f, f0-3f, f2-2f, f2-3f, f0-1f, f1-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the laser at location f0-0f, fire the laser from location f0-0f to location f0-1f, move from location f0-0f to location f0-1f, fire the laser from location f0-1f to location f0-2f, move from location f0-1f to location f0-2f, move from location f0-2f to location f0-1f, move from location f0-1f to location f0-0f, fire the laser from location f0-0f to location f0-1f, put down laser at loc f0-0f, pick up the bomb at loc f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f0-2f, detonate bomb at loc f0-2f connected to loc f0-3f, move from location f0-2f to location f0-3f, retrieve gold from location f0-3f\"; which of the following actions can be removed from this plan and still have a valid plan? A. move from location f0-1f to location f0-2f. B. fire the laser from location f0-0f to location f0-1f. C. retrieve gold from location f0-3f. D. fire the laser from location f0-1f to location f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from location f0-1f to location f0-2f", "fire the laser from location f0-0f to location f0-1f", "retrieve gold from location f0-3f", "fire the laser from location f0-1f to location f0-2f"]}, "query": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the laser at location f0-0f, fire the laser from location f0-0f to location f0-1f, move from location f0-0f to location f0-1f, fire the laser from location f0-1f to location f0-2f, move from location f0-1f to location f0-2f, move from location f0-2f to location f0-1f, move from location f0-1f to location f0-0f, fire the laser from location f0-0f to location f0-1f, put down laser at loc f0-0f, pick up the bomb at loc f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f0-2f, detonate bomb at loc f0-2f connected to loc f0-3f, move from location f0-2f to location f0-3f, retrieve gold from location f0-3f\"; which action can be removed from this plan?", "answer": "B"}
