{"id": -7531224921640943082, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 2 crates, 2 depots, 2 distributors, 4 pallets, 2 trucks, numbered consecutively. Currently, crate0, pallet2, crate1, and pallet1 are clear; hoist2, hoist0, hoist1, and hoist3 are available; pallet3 is at distributor1, pallet0 is at depot0, hoist3 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, crate1 is at distributor1, pallet1 is at depot1, truck1 is at depot0, hoist0 is at depot0, crate0 is at depot0, and hoist2 is at distributor0; crate0 is on pallet0 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Given the plan: \"drive the truck truck0 from distributor0 to distributor1, use the hoist hoist3 to lift the crate crate1 from the surface pallet3 at location distributor1, use hoist hoist3 to load crate crate1 into truck truck0 at place distributor1, drive the truck truck0 from distributor1 to depot0, use the hoist hoist0 to lift the crate crate0 from the surface pallet0 at location depot0, use hoist hoist0 to load crate crate0 into truck truck0 at place depot0, use the hoist hoist0 to unload the crate crate1 from the truck truck0 at location depot0, drop the crate crate1 from the hoist hoist0 onto the surface pallet0 at the place depot0, use the hoist hoist0 to unload the crate crate0 from the truck truck0 at location depot0, drive the truck truck0 from depot0 to distributor0, drive the truck truck0 from distributor0 to distributor1, drop the crate crate0 from the hoist hoist0 onto the surface crate1 at the place depot0\"; can the following action be removed from this plan and still have a valid plan: use the hoist hoist0 to lift the crate crate0 from the surface pallet0 at location depot0?", "answer": "no"}
{"id": -3572210206741853911, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 3 crates, 4 depots, 2 distributors, 6 pallets, 2 trucks, numbered consecutively. Currently, crate2, crate0, pallet2, crate1, pallet4, and pallet0 are clear; hoist5, hoist2, hoist0, hoist1, hoist3, and hoist4 are available; hoist3 is at depot3, pallet0 is at depot0, pallet5 is at distributor1, truck0 is at distributor1, pallet2 is at depot2, hoist4 is at distributor0, hoist1 is at depot1, hoist2 is at depot2, pallet3 is at depot3, crate2 is at depot1, crate1 is at distributor1, crate0 is at depot3, pallet1 is at depot1, truck1 is at depot0, pallet4 is at distributor0, hoist5 is at distributor1, and hoist0 is at depot0; crate0 is on pallet3, crate1 is on pallet5, and crate2 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate0 is on pallet1, and crate2 is on pallet4.", "question": "Given the plan: \"lift the crate crate2 from the surface pallet1 at place depot1 using the hoist hoist1, lift the crate crate1 from the surface pallet5 at place distributor1 using the hoist hoist5, drive truck truck0 from place distributor1 to place depot3, lift the crate crate0 from the surface pallet3 at place depot3 using the hoist hoist3, load the crate crate0 from place depot3 with hoist hoist3 into the truck truck0, drive truck truck0 from place depot3 to place distributor1, load the crate crate1 from place distributor1 with hoist hoist5 into the truck truck0, drive truck truck0 from place distributor1 to place depot1, load the crate crate2 from place depot1 with hoist hoist1 into the truck truck0, unload the crate crate0 from the truck truck0 at the place depot1 using the hoist hoist1, drive truck truck0 from place depot1 to place distributor0, unload the crate crate2 from the truck truck0 at the place distributor0 using the hoist hoist4, drop the crate crate2 from the hoist hoist4 onto the surface pallet4 at the place distributor0, drop the crate crate0 from the hoist hoist1 onto the surface pallet1 at the place depot1, unload the crate crate1 from the truck truck0 at the place distributor0 using the hoist hoist4, drop the crate crate1 from the hoist hoist4 onto the surface crate2 at the place distributor0\"; can the following action be removed from this plan and still have a valid plan: unload the crate crate0 from the truck truck0 at the place depot1 using the hoist hoist1?", "answer": "no"}
{"id": 1051590874440443652, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 crates, 10 depots, 2 distributors, 12 pallets, 2 trucks, numbered consecutively. Currently, pallet11, pallet3, pallet7, pallet5, crate0, pallet10, crate1, pallet1, pallet9, pallet4, pallet0, and pallet6 are clear; hoist8, hoist10, hoist5, hoist11, hoist2, hoist7, hoist9, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, truck0 is at depot0, hoist6 is at depot6, pallet0 is at depot0, crate0 is at depot8, pallet2 is at depot2, crate1 is at depot2, pallet6 is at depot6, pallet10 is at distributor0, hoist7 is at depot7, hoist1 is at depot1, hoist2 is at depot2, pallet8 is at depot8, pallet3 is at depot3, pallet7 is at depot7, pallet1 is at depot1, hoist5 is at depot5, hoist4 is at depot4, hoist9 is at depot9, pallet9 is at depot9, hoist0 is at depot0, hoist11 is at distributor1, pallet5 is at depot5, pallet11 is at distributor1, hoist8 is at depot8, truck1 is at depot4, hoist10 is at distributor0, and pallet4 is at depot4; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Given the plan: \"navigate the truck truck1 from place depot4 to place depot8, lift crate crate0 from surface pallet8 at place depot8 using hoist hoist8, use hoist hoist8 to load crate crate0 into truck truck1 at place depot8, navigate the truck truck1 from place depot8 to place depot2, lift crate crate1 from surface pallet2 at place depot2 using hoist hoist2, use hoist hoist2 to load crate crate1 into truck truck1 at place depot2, navigate the truck truck1 from place depot2 to place distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck1 at location distributor1, navigate the truck truck1 from place distributor1 to place depot6, navigate the truck truck1 from place depot6 to place depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9, lower the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9, lower the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1\"; can the following action be removed from this plan and still have a valid plan: use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9?", "answer": "no"}
{"id": -1423754374928579924, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 crates, 10 depots, 2 distributors, 12 pallets, 2 trucks, numbered consecutively. Currently, pallet11, pallet3, pallet7, pallet5, crate0, pallet10, crate1, pallet1, pallet9, pallet4, pallet0, and pallet6 are clear; hoist8, hoist10, hoist5, hoist11, hoist2, hoist7, hoist9, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, truck0 is at depot0, hoist6 is at depot6, pallet0 is at depot0, crate0 is at depot8, pallet2 is at depot2, crate1 is at depot2, pallet6 is at depot6, pallet10 is at distributor0, hoist7 is at depot7, hoist1 is at depot1, hoist2 is at depot2, pallet8 is at depot8, pallet3 is at depot3, pallet7 is at depot7, pallet1 is at depot1, hoist5 is at depot5, hoist4 is at depot4, hoist9 is at depot9, pallet9 is at depot9, hoist0 is at depot0, hoist11 is at distributor1, pallet5 is at depot5, pallet11 is at distributor1, hoist8 is at depot8, truck1 is at depot4, hoist10 is at distributor0, and pallet4 is at depot4; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Given the plan: \"navigate the truck truck0 from place depot0 to place depot1, navigate the truck truck0 from place depot1 to place depot0, navigate the truck truck0 from place depot0 to place depot2, use the hoist hoist2 to lift the crate crate1 from the surface pallet2 at location depot2, use hoist hoist2 to load crate crate1 into truck truck0 at place depot2, navigate the truck truck0 from place depot2 to place depot8, use the hoist hoist8 to lift the crate crate0 from the surface pallet8 at location depot8, use hoist hoist8 to load crate crate0 into truck truck0 at place depot8, navigate the truck truck0 from place depot8 to place depot9, unload the crate crate1 from the truck truck0 at the place depot9 using the hoist hoist9, navigate the truck truck0 from place depot9 to place depot5, navigate the truck truck0 from place depot5 to place distributor1, unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist11, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: navigate the truck truck0 from place depot0 to place depot1 and navigate the truck truck0 from place depot1 to place depot0?", "answer": "yes"}
{"id": 5220133523941080896, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 crates, 7 depots, 2 distributors, 9 pallets, 2 trucks, numbered consecutively. Currently, pallet7, pallet5, crate0, pallet2, crate1, pallet8, pallet4, pallet0, and pallet6 are clear; hoist8, hoist5, hoist2, hoist7, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, hoist6 is at depot6, pallet0 is at depot0, pallet2 is at depot2, pallet6 is at depot6, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, pallet8 is at distributor1, pallet3 is at depot3, truck0 is at depot2, pallet1 is at depot1, truck1 is at depot0, hoist5 is at depot5, hoist4 is at depot4, hoist0 is at depot0, pallet5 is at depot5, crate1 is at depot3, pallet7 is at distributor0, hoist7 is at distributor0, hoist8 is at distributor1, and pallet4 is at depot4; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Given the plan: \"use hoist hoist1 to lift crate crate0 from surface pallet1 at place depot1, navigate the truck truck1 from location depot0 to location depot1, load the crate crate0 into the truck truck1 at the place depot1 with the hoist hoist1, navigate the truck truck1 from location depot1 to location distributor1, use the hoist hoist8 to unload the crate crate0 from the truck truck1 at location distributor1, place the crate crate0 on the surface pallet8 at the place distributor1 using the hoist hoist8\"; can the following action be removed from this plan and still have a valid plan: place the crate crate0 on the surface pallet8 at the place distributor1 using the hoist hoist8?", "answer": "no"}
{"id": 9134780014846199492, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 crates, 7 depots, 2 distributors, 9 pallets, 2 trucks, numbered consecutively. Currently, pallet7, pallet5, crate0, pallet2, crate1, pallet8, pallet4, pallet0, and pallet6 are clear; hoist8, hoist5, hoist2, hoist7, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, hoist6 is at depot6, pallet0 is at depot0, pallet2 is at depot2, pallet6 is at depot6, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, pallet8 is at distributor1, pallet3 is at depot3, truck0 is at depot2, pallet1 is at depot1, truck1 is at depot0, hoist5 is at depot5, hoist4 is at depot4, hoist0 is at depot0, pallet5 is at depot5, crate1 is at depot3, pallet7 is at distributor0, hoist7 is at distributor0, hoist8 is at distributor1, and pallet4 is at depot4; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Given the plan: \"navigate the truck truck0 from the place depot2 to the place depot1, lift crate crate0 from surface pallet1 at place depot1 using hoist hoist1, load the crate crate0 into the truck truck0 at the place depot1 with the hoist hoist1, navigate the truck truck1 from the place depot0 to the place depot5, navigate the truck truck0 from the place depot1 to the place distributor1, navigate the truck truck1 from the place depot5 to the place depot4, unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist8, drop crate crate0 from hoist hoist8 onto surface pallet8 at place distributor1\"; can the following action be removed from this plan and still have a valid plan: navigate the truck truck1 from the place depot5 to the place depot4?", "answer": "yes"}
{"id": 7390257066645710159, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 crates, 10 depots, 2 distributors, 12 pallets, 2 trucks, numbered consecutively. Currently, pallet11, pallet3, pallet7, pallet5, crate0, pallet10, crate1, pallet1, pallet9, pallet4, pallet0, and pallet6 are clear; hoist8, hoist10, hoist5, hoist11, hoist2, hoist7, hoist9, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, truck0 is at depot0, hoist6 is at depot6, pallet0 is at depot0, crate0 is at depot8, pallet2 is at depot2, crate1 is at depot2, pallet6 is at depot6, pallet10 is at distributor0, hoist7 is at depot7, hoist1 is at depot1, hoist2 is at depot2, pallet8 is at depot8, pallet3 is at depot3, pallet7 is at depot7, pallet1 is at depot1, hoist5 is at depot5, hoist4 is at depot4, hoist9 is at depot9, pallet9 is at depot9, hoist0 is at depot0, hoist11 is at distributor1, pallet5 is at depot5, pallet11 is at distributor1, hoist8 is at depot8, truck1 is at depot4, hoist10 is at distributor0, and pallet4 is at depot4; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Given the plan: \"navigate the truck truck1 from place depot4 to place depot2, lift crate crate1 from surface pallet2 at place depot2 using hoist hoist2, load crate crate1 into truck truck1 at place depot2 with hoist hoist2, navigate the truck truck1 from place depot2 to place depot8, lift crate crate0 from surface pallet8 at place depot8 using hoist hoist8, load crate crate0 into truck truck1 at place depot8 with hoist hoist8, navigate the truck truck1 from place depot8 to place depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9, lower the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9, navigate the truck truck1 from place depot9 to place distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck1 at location distributor1, lower the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1\"; can the following action be removed from this plan and still have a valid plan: load crate crate1 into truck truck1 at place depot2 with hoist hoist2?", "answer": "no"}
{"id": -4170707118614639190, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 2 crates, 3 depots, 2 distributors, 5 pallets, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate1, pallet4, and pallet0 are clear; hoist2, hoist0, hoist1, hoist3, and hoist4 are available; crate1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, pallet4 is at distributor1, pallet3 is at distributor0, and truck0 is at depot1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Given the plan: \"drive the truck truck1 from distributor0 to depot1, drive the truck truck0 from depot1 to distributor0, use the hoist hoist1 to lift the crate crate1 from the surface crate0 at location depot1, load the crate crate1 from place depot1 with hoist hoist1 into the truck truck1, drive the truck truck1 from depot1 to depot2, unload the crate crate1 from the truck truck1 at the place depot2 using the hoist hoist2, drop the crate crate1 from the hoist hoist2 onto the surface pallet2 at the place depot2\"; can the following action be removed from this plan and still have a valid plan: load the crate crate1 from place depot1 with hoist hoist1 into the truck truck1?", "answer": "no"}
{"id": -4192225067679677274, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 2 crates, 3 depots, 2 distributors, 5 pallets, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate1, pallet4, and pallet0 are clear; hoist2, hoist0, hoist1, hoist3, and hoist4 are available; crate1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, pallet4 is at distributor1, pallet3 is at distributor0, and truck0 is at depot1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Given the plan: \"drive truck truck1 from place distributor0 to place distributor1, lift crate crate1 from surface crate0 at place depot1 using hoist hoist1, drive truck truck1 from place distributor1 to place depot1, load the crate crate1 from place depot1 with hoist hoist1 into the truck truck1, drive truck truck1 from place depot1 to place depot2, unload the crate crate1 from the truck truck1 at the place depot2 using the hoist hoist2, lower the crate crate1 from the hoist hoist2 onto the surface pallet2 at the place depot2\"; can the following action be removed from this plan and still have a valid plan: drive truck truck1 from place distributor0 to place distributor1?", "answer": "no"}
{"id": 3653706118193721188, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 2 crates, 3 depots, 2 distributors, 5 pallets, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate1, pallet4, and pallet0 are clear; hoist2, hoist0, hoist1, hoist3, and hoist4 are available; crate1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, pallet4 is at distributor1, pallet3 is at distributor0, and truck0 is at depot1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Given the plan: \"lift the crate crate1 from the surface crate0 at place depot1 using the hoist hoist1, load the crate crate1 from place depot1 with hoist hoist1 into the truck truck0, navigate the truck truck1 from location distributor0 to location depot2, navigate the truck truck0 from location depot1 to location depot0, navigate the truck truck0 from location depot0 to location depot2, navigate the truck truck1 from location depot2 to location distributor1, use the hoist hoist2 to unload the crate crate1 from the truck truck0 at location depot2, lower the crate crate1 from the hoist hoist2 onto the surface pallet2 at the place depot2\"; can the following action be removed from this plan and still have a valid plan: navigate the truck truck0 from location depot0 to location depot2?", "answer": "no"}
