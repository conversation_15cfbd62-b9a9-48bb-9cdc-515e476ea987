{"question_id": "36a6a9b8-a25d-4776-b526-1c7fc84ec0be", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns from groundstation0 to planet11, satellite0's instrument1 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, instrument1 which is on satellite0 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and satellite0 turns to star10 from planet13 to reach the current state. In this state, if satellite0's instrument1 takes an image of star10 in image6, is it True or False that image of star10 exists in image6?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: on satellite0, instrument1 is activated, then satellite0 reorients from groundstation2 to groundstation0, followed by the calibration of instrument1 on satellite0 to groundstation0. Satellite0 then reorients from groundstation0 to planet11, and instrument1 on satellite0 captures an image of planet11 in image5 and another in image6. Next, satellite0 reorients from planet11 to planet13, and instrument1 on satellite0 captures an image of planet13 in image5 and another in spectrograph2. Finally, satellite0 reorients from planet13 to star10, reaching the current state. In this state, if instrument1 on satellite0 captures an image of star10 in image6, is it True or False that an image of star10 exists in image6?", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and also supported by instrument2 and instrument3, image5 is compatible with instrument1, image6 is supported by instrument0 and instrument1. Furthermore, the following instrument calibrations have been completed: instrument0 for star3, instrument1 for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument3 are on board satellite0, while instrument4 is on board satellite1. Satellite0 has power available and is currently pointing towards groundstation2, whereas satellite1 has power and is aimed towards planet13. Instrument capabilities include: instrument4 supporting infrared7, spectrograph2 being supported by instrument1, spectrograph4 being compatible with instrument3, and thermograph3 being supported by instrument3."}
{"question_id": "ef094b86-f2bf-458c-af72-3862abcc0e17", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, from phenomenon10, satellite1 turns to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, from phenomenon17, satellite1 turns to planet11, instrument3 which is on satellite1 takes an image of planet11 in image3, from planet11, satellite1 turns to planet13, instrument3 which is on satellite1 takes an image of planet13 in image0, from planet13, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in image0, satellite1 turns to star15 from planet14, instrument3 which is on satellite1 takes an image of star15 in image2, satellite0 turns from groundstation3 to star1, calibration of instrument0 which is on satellite0 to star1 is complete and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, if satellite0's instrument0 takes an image of phenomenon10 in infrared1, is it True or False that image of phenomenon10 exists in infrared1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, and instrument0 is activated on satellite0. Satellite1 then reorients from phenomenon10 to groundstation5, where the calibration of instrument3 on satellite1 to groundstation5 is completed. Satellite1 then reorients from groundstation5 to phenomenon16, and instrument3 on satellite1 captures an image of phenomenon16, which is stored in image3. This process is repeated as satellite1 reorients from phenomenon16 to phenomenon17, capturing an image of phenomenon17 in image3, then from phenomenon17 to planet11, capturing an image of planet11 in image3, and from planet11 to planet13, capturing an image of planet13 in image0. Satellite1 continues to reorient from planet13 to planet14, capturing an image of planet14 in image0, then from planet14 to star15, capturing an image of star15 in image2. Meanwhile, satellite0 reorients from groundstation3 to star1, completes the calibration of instrument0 on satellite0 to star1, and then reorients from star1 to phenomenon10. In this final state, if satellite0's instrument0 captures an image of phenomenon10 in infrared1, the question arises: does an image of phenomenon10 exist in infrared1?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for star6 is also complete. For star1, instrument0 has been calibrated, while instrument3 has been calibrated for star8, and instrument0 for star9. The satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, image2 is compatible with instrument2, and also supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, and it supports both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 has instrument1 and instrument2 on board, and it has power available. Satellite1 has instrument3 on board, has power available, and is currently pointing towards phenomenon10."}
{"question_id": "00d7decb-e1d4-4c47-bc9b-45f13a6654ea", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns from groundstation2 to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, on satellite0, instrument1 is switched off, instrument2 on satellite0 is switched on, satellite0 turns from star10 to star4, instrument2 is calibrated on satellite0 to star4, from star4, satellite0 turns to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, if instrument3 that is on satellite0 is turned on, is it True or False that satellite0 has power?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured by instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured by instrument1 on satellite0 and stored in image5, instrument1 on satellite0 also captures an image of planet13 in spectrograph2, satellite0 reorients from planet13 to star10, instrument1 on satellite0 captures an image of star10 and stores it in image6, another image of star10 is taken by instrument1 on satellite0 and stored in spectrograph2, instrument1 on satellite0 is then deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 then reorients from star4 to star16, instrument2 on satellite0 captures an image of star16 and stores it in image0, and finally, instrument2 on satellite0 is deactivated to reach the current state. In this state, if instrument3 on satellite0 is activated, is it True or False that satellite0 has power?", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1, and image5 is compatible with instrument1, image1 is also supported by instrument2 and instrument3, and image6 is supported by instrument0 and instrument1. Furthermore, the following instrument calibrations have been confirmed: instrument0 for star3, instrument1 for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument2 are on board satellite0, while instrument4 is on board satellite1. Satellite0 has available power and is currently pointing towards groundstation2, whereas satellite1 has power and is aimed at planet13. The following instrument capabilities have been noted: instrument4 supports infrared7, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "048a68dc-f48b-4784-8e4c-781fa02ca16b", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, satellite1 turns from groundstation4 to star6, calibration of instrument3 which is on satellite1 to star6 is complete, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns to star10 from planet14, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, if from star12, satellite1 turns to star0, is it True or False that satellite0 is not aimed towards groundstation3 and star1 is not where satellite1 is pointed?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, and finally, satellite1 reorients from star10 to star12, capturing an image of star12 using instrument3 on satellite1 in spectrograph1, resulting in the current state. In this state, if satellite1 reorients from star12 to star0, is it True or False that satellite0 is not aimed at groundstation3 and star1 is not the target of satellite1?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 is also operational and carries instrument3 on board. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "e6c95c86-250e-4f89-9b83-b135e3c79c3b", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, satellite1 turns from star3 to star1, calibration of instrument1 which is on satellite1 to star1 is complete, satellite1 turns from star1 to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, satellite1 turns from phenomenon11 to phenomenon5 and image of phenomenon5 is taken with instrument1 on satellite1 in image4 to reach the current state. In this state, if instrument1 which is on satellite1 takes an image of phenomenon5 in image5, is it True or False that there is an image of phenomenon5 in image5?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: on satellite1, instrument1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then reorients from star1 to phenomenon10, and captures an image of phenomenon10 using instrument1 in both image5 and spectrograph3. Satellite1 then reorients from phenomenon10 to phenomenon11, and instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1. Finally, satellite1 reorients from phenomenon11 to phenomenon5 and captures an image of phenomenon5 using instrument1 in image4, resulting in the current state. In this state, if instrument1 on satellite1 captures an image of phenomenon5 in image5, is it True or False that an image of phenomenon5 exists in image5?", "initial_state_nl_paraphrased": "The calibration process for instrument0 in relation to star1 has been finalized. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has been calibrated for star1 and provides support for image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, which also has instrument1 on board, has power available and is currently directed towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "7e66c8c4-300f-44b0-803a-d70ab1048528", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, from groundstation2, satellite0 turns to groundstation0, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns from groundstation0 to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, satellite0 turns to star10 from planet13, image of star10 is taken with instrument1 on satellite0 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, on satellite0, instrument1 is switched off, on satellite0, instrument2 is switched on, satellite0 turns to star4 from star10, calibration of instrument2 which is on satellite0 to star4 is complete, satellite0 turns from star4 to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and instrument2 on satellite0 is switched off to reach the current state. In this state, if on satellite0, instrument3 is switched on, is it True or False that instrument3 is switched on?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite0, instrument1 is activated, then satellite0 reorients itself from groundstation2 to groundstation0, followed by the calibration of instrument1 on satellite0 with respect to groundstation0. Next, satellite0 reorients itself from groundstation0 to planet11, and instrument1 on satellite0 captures an image of planet11, which is stored in both image5 and image6. Satellite0 then reorients itself from planet11 to planet13, and instrument1 on satellite0 captures an image of planet13, which is stored in both image5 and spectrograph2. Subsequently, satellite0 reorients itself from planet13 to star10, and instrument1 on satellite0 captures an image of star10, which is stored in both image6 and spectrograph2. After that, instrument1 on satellite0 is deactivated. Following this, instrument2 on satellite0 is activated, and satellite0 reorients itself from star10 to star4, where the calibration of instrument2 on satellite0 with respect to star4 is completed. Satellite0 then reorients itself from star4 to star16, and instrument2 on satellite0 captures an image of star16, which is stored in image0. Finally, instrument2 on satellite0 is deactivated, resulting in the current state. In this state, if instrument3 on satellite0 is activated, is it True or False that instrument3 is switched on?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and instrument2's calibration for star4 is also complete. Additionally, instrument0 has been calibrated for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Furthermore, instrument2 supports image0, while instrument4 is compatible with image1, which is also supported by both instrument2 and instrument3. Image5 is compatible with instrument1, and image6 is supported by both instrument0 and instrument1. Instrument0 has been calibrated for star3, and instrument1 has been calibrated for groundstation0. Instrument1 is currently on board satellite0, which also carries instrument0, instrument2, and instrument3. Instrument4 supports infrared7. Satellite0 has available power and is currently pointing towards groundstation2. Satellite1, which carries instrument4, has power and is aimed at planet13. Spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "61574b88-2759-4e3b-b2cc-4126ef098ccf", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, satellite1 turns to star1 from star3, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5 and instrument1 which is on satellite1 takes an image of phenomenon5 in image4 to reach the current state. In this state, if image of phenomenon5 is taken with instrument1 on satellite1 in image5, is it True or False that there is no image of direction star6 in image0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 shifts from star1 to phenomenon10, captures an image of phenomenon10 using instrument1 in image5, and also captures an image of phenomenon10 using instrument1 in spectrograph3. Next, satellite1 moves from phenomenon10 to phenomenon11, and instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1. Finally, satellite1 transitions from phenomenon11 to phenomenon5 and captures an image of phenomenon5 using instrument1 in image4, resulting in the current state. In this state, if an image of phenomenon5 is captured with instrument1 on satellite1 in image5, is it True or False that there is no image of direction star6 in image0?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been completed. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has been calibrated for star1 and is capable of supporting image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, on the other hand, is equipped with instrument1, has available power, and is currently pointing towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "1b0fe1d5-e54a-43ae-826a-e5c4da69d8a2", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, is it True or False that instrument0 is turned on?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, is it True or False that instrument0 is activated?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 is also operational and carries instrument3 on board. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "e4f0a27f-ffff-440f-8a9e-586bd3c2fac0", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 that is on satellite1 is calibrated to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, image of planet11 is taken with instrument3 on satellite1 in image3, satellite1 turns from planet11 to planet13, instrument3 which is on satellite1 takes an image of planet13 in image0, satellite1 turns from planet13 to planet14, satellite1's instrument3 takes an image of planet14 in image0, satellite1 turns to star15 from planet14, image of star15 is taken with instrument3 on satellite1 in image2, from groundstation3, satellite0 turns to star1, instrument0 is calibrated on satellite0 to star1 and satellite0 turns to phenomenon10 from star1 to reach the current state. In this state, if satellite0's instrument0 takes an image of phenomenon10 in infrared1, is it True or False that there is an image of groundstation4 in image3?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: the instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, then satellite1 reorients from groundstation5 to phenomenon16, and an image of phenomenon16 is captured by instrument3 on satellite1 in image3. Satellite1 then reorients from phenomenon16 to phenomenon17, and instrument3 on satellite1 captures an image of phenomenon17 in image3. Next, satellite1 reorients from phenomenon17 to planet11, and an image of planet11 is taken by instrument3 on satellite1 in image3. Satellite1 then reorients from planet11 to planet13, and instrument3 on satellite1 captures an image of planet13 in image0. Following this, satellite1 reorients from planet13 to planet14, and instrument3 on satellite1 takes an image of planet14 in image0. Satellite1 then reorients from planet14 to star15, and an image of star15 is captured by instrument3 on satellite1 in image2. Meanwhile, from groundstation3, satellite0 reorients to star1, instrument0 on satellite0 is calibrated to star1, and then satellite0 reorients from star1 to phenomenon10 to reach the current state. In this state, if satellite0's instrument0 captures an image of phenomenon10 in infrared1, is it True or False that there is an image of groundstation4 in image3?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, supporting both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board, and it has available power. Satellite1 has instrument3 on board, available power, and is currently pointing towards phenomenon10."}
{"question_id": "62d7951e-4e94-4c3d-b478-ad8702bae17c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, if from groundstation2, satellite0 turns to groundstation0, is it True or False that satellite1 is aimed towards phenomenon14 and star10 is where satellite0 is pointed?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated to achieve the current state. In this state, if satellite0 switches from groundstation2 to groundstation0, is it True or False that satellite1 is directed towards phenomenon14 and star10 is the target of satellite0?", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and also supported by instrument2 and instrument3, image5 is compatible with instrument1, image6 is supported by instrument0 and instrument1. Furthermore, the following instrument calibrations have been completed: instrument0 for star3, instrument1 for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument3 are on board satellite0, while instrument4 is on board satellite1. Satellite0 has power available and is currently pointing towards groundstation2, whereas satellite1 has power and is aimed towards planet13. Instrument capabilities include: instrument4 supporting infrared7, spectrograph2 being supported by instrument1, spectrograph4 being compatible with instrument3, and thermograph3 being supported by instrument3."}
{"question_id": "8ea7b4ae-e75d-4493-82fe-bee4018b5133", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, from groundstation2, satellite0 turns to groundstation0, instrument1 is calibrated on satellite0 to groundstation0, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, satellite0 turns from planet13 to star10, satellite0's instrument1 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 that is on satellite0 is turned off, instrument2 on satellite0 is switched on, satellite0 turns from star10 to star4, instrument2 that is on satellite0 is calibrated to star4, satellite0 turns from star4 to star16, image of star16 is taken with instrument2 on satellite0 in image0 and instrument2 on satellite0 is switched off to reach the current state. In this state, if instrument3 that is on satellite0 is turned on, is it True or False that there is an image of groundstation7 in image1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 then reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, and another image of planet13 is captured using instrument1 on satellite0 and stored in spectrograph2, satellite0 reorients from planet13 to star10, instrument1 on satellite0 captures an image of star10 and stores it in image6, and another image of star10 is captured by instrument1 on satellite0 and stored in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, an image of star16 is captured using instrument2 on satellite0 and stored in image0, and instrument2 on satellite0 is deactivated, resulting in the current state. In this state, if instrument3 on satellite0 is activated, is it True or False that an image of groundstation7 is stored in image1?", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1, and image5 is compatible with instrument1, image1 is also supported by instrument2 and instrument3, and image6 is supported by instrument0 and instrument1. Furthermore, the calibration status of instruments on satellites is as follows: instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0 and is on board satellite0, and instrument4 supports infrared7. Satellite0 is equipped with instruments 0, 1, 2, and 3, has power available, and is currently pointing to groundstation2. Satellite1 carries instrument4 on board, has power, and is aimed towards planet13. Lastly, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "768168a1-d3b0-4022-986f-a51b058b6a20", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, is it True or False that image of planet13 does not exist in image0?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, is it True or False that the image of planet13 is not present in image0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and also supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, and it supports both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board. Satellite0 has available power, and satellite1, which carries instrument3, also has power and is currently pointing towards phenomenon10."}
{"question_id": "a66541e8-8757-4847-88d9-e286be5ead6f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 on satellite0 is switched on, satellite1 turns to star6 from groundstation4, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, from planet14, satellite1 turns to star10, image of star10 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star10 to star12 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, if satellite1 turns from star12 to star0, is it True or False that satellite1 is not pointing to star12 and star0 is where satellite1 is pointed?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, and instrument0 is activated on satellite0. Satellite1 then reorients itself from groundstation4 to star6, followed by the calibration of instrument3 on satellite1 to star6. Next, satellite1 reorients itself from star6 to planet14, and an image of planet14 is captured using instrument3 on satellite1 in spectrograph1. From planet14, satellite1 reorients itself to star10, and an image of star10 is captured using instrument3 on satellite1 in spectrograph1. Finally, satellite1 reorients itself from star10 to star12, and an image of star12 is captured using instrument3 on satellite1 in spectrograph1, resulting in the current state. In this state, if satellite1 reorients itself from star12 to star0, is it True or False that satellite1 is no longer pointing to star12 and is now pointing to star0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed towards groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 is equipped with instrument3 on board and has power. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "0f85e7ab-c621-4522-b5b4-5ca537b43694", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, if satellite1 turns from star3 to star1, is it True or False that satellite1 is aimed towards star1 and star3 is not where satellite1 is pointed?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is activated to achieve the current state. In this state, if satellite1 shifts its orientation from star3 to star1, is it True or False that satellite1 is now directed towards star1 and star3 is no longer the target of satellite1?", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been completed. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has been calibrated for star1 and is capable of supporting image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, on the other hand, is equipped with instrument1, has available power, and is currently pointing towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "863996d9-d2a7-4d34-8792-265800aa6950", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, satellite1 turns from phenomenon10 to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns to phenomenon16 from groundstation5, image of phenomenon16 is taken with instrument3 on satellite1 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1's instrument3 takes an image of planet11 in image3 to reach the current state. In this state, if satellite1 turns from planet11 to planet13, is it True or False that satellite0 is not aimed towards groundstation2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 and stores it in image3, and finally, satellite1 reorients from phenomenon17 to planet11, with instrument3 on satellite1 capturing an image of planet11 and storing it in image3, resulting in the current state. In this state, if satellite1 reorients from planet11 to planet13, is it True or False that satellite0 is not directed towards groundstation2?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, supporting both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board, and it has available power. Satellite1 has instrument3 on board, available power, and is currently pointing towards phenomenon10."}
{"question_id": "3fff78fc-bc2b-4a21-994e-ac1729f9be3e", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, if instrument1 which is on satellite0 takes an image of star10 in image6, is it True or False that image of star3 does not exist in spectrograph2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, satellite0 then reorients from groundstation0 to planet11, captures an image of planet11 using instrument1 and stores it in image5, takes another image of planet11 using instrument1 and stores it in image6, reorients from planet11 to planet13, captures an image of planet13 using instrument1 and stores it in image5, and captures another image of planet13 using instrument1 and stores it in spectrograph2, before finally reorienting from planet13 to star10 to reach the current state. In this state, if instrument1 on satellite0 captures an image of star10 and stores it in image6, is it True or False that there is no image of star3 stored in spectrograph2?", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and also supported by instrument2 and instrument3, image5 is compatible with instrument1, image6 is supported by instrument0 and instrument1. Furthermore, the following instrument calibrations have been completed: instrument0 for star3, instrument1 for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument3 are on board satellite0, while instrument4 is on board satellite1. Instrument4 supports infrared7. Satellite0 has power available and is currently pointing to groundstation2, whereas satellite1 has power and is aimed towards planet13. Lastly, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "a5023e58-3a3b-4c9c-bb47-b5ee5a724cbb", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, is it True or False that instrument0 is switched on?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, is it True or False that instrument0 is in an active state?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, supporting both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board, and it has available power. Satellite1 has instrument3 on board, available power, and is currently pointing towards phenomenon10."}
{"question_id": "d1d1151c-7566-4df2-a469-3e41b069002a", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, if instrument0 on satellite0 is switched on, is it True or False that satellite1 has power available?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to reach the current state. In this state, if instrument0 on satellite0 is activated, is it True or False that satellite1 has power available?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 works with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 also has instrument3 on board and is powered. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "7467e7f6-a43e-4f53-8d32-7b3576007db0", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to star6 from groundstation4, instrument3 is calibrated on satellite1 to star6, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, image of star10 is taken with instrument3 on satellite1 in spectrograph1, from star10, satellite1 turns to star12, instrument3 which is on satellite1 takes an image of star12 in spectrograph1, satellite1 turns from star12 to star0, from star1, satellite0 turns to groundstation2, instrument0 is calibrated on satellite0 to groundstation2, satellite0 turns to phenomenon15 from groundstation2, instrument0 which is on satellite0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and satellite0's instrument0 takes an image of star13 in spectrograph0 to reach the current state. In this state, if from star13, satellite0 turns to star16, is it True or False that satellite0 is not aimed towards star13 and satellite0 is pointing to star16?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, satellite1 then reorients from star10 to star12, an image of star12 is captured using instrument3 on satellite1 in spectrograph1, satellite1 reorients from star12 to star0, satellite0 reorients from star1 to groundstation2, instrument0 on satellite0 is calibrated to groundstation2, satellite0 reorients from groundstation2 to phenomenon15, an image of phenomenon15 is captured using instrument0 on satellite0 in spectrograph0, satellite0 reorients from phenomenon15 to star11, an image of star11 is captured using instrument0 on satellite0 in thermograph4, satellite0 reorients from star11 to star13 and captures an image of star13 in spectrograph0, resulting in the current state. In this state, if satellite0 reorients from star13 to star16, is it True or False that satellite0 is no longer aimed at star13 and is now pointing towards star16?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational, having power, and is oriented towards star1. Satellite1 is also operational and carries instrument3 on board. Spectrograph0 is compatible with both instrument0 and instrument1, as well as instrument2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instrument2 and instrument3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "61471809-ce5c-48bc-bf63-824ba636fe76", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, if on satellite0, instrument0 is switched on, is it True or False that image of star6 does not exist in spectrograph2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, is it True or False that the image of star6 is not present in spectrograph2?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 is also operational and carries instrument3 on board. Spectrograph0 is compatible with instruments 0, 1, and 2. Instrument3 supports spectrograph1, and both instrument2 and instrument3 support spectrograph2. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "cad64924-6a24-4b16-b8b8-11f5142ec36c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, from groundstation2, satellite0 turns to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, image of star10 is taken with instrument1 on satellite0 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, on satellite0, instrument1 is switched off, on satellite0, instrument2 is switched on, from star10, satellite0 turns to star4, instrument2 that is on satellite0 is calibrated to star4, from star4, satellite0 turns to star16, satellite0's instrument2 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, if on satellite0, instrument3 is switched on, is it True or False that power is not available for satellite0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5 and another in image6, satellite0 then reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 in image5 and another in spectrograph2, satellite0 reorients from planet13 to star10, an image of star10 is captured with instrument1 on satellite0 in image6, and another image of star10 is captured with instrument1 on satellite0 in spectrograph2, instrument1 on satellite0 is then deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, and instrument2 on satellite0 captures an image of star16 in image0, after which instrument2 on satellite0 is deactivated, resulting in the current state. In this state, if instrument3 on satellite0 is activated, is it True or False that power is not available for satellite0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and instrument2's calibration for star4 is also complete. Additionally, instrument0 has been calibrated for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Furthermore, instrument2 supports image0, while instrument4 is compatible with image1, which is also supported by both instrument2 and instrument3. Image5 is compatible with instrument1, and image6 is supported by both instrument0 and instrument1. Instrument0 has been calibrated for star3, and instrument1 has been calibrated for groundstation0. Instrument1 is currently on board satellite0, which also carries instrument0, instrument2, and instrument3. Instrument4 supports infrared7. Satellite0 has available power and is currently pointing towards groundstation2. Satellite1, which carries instrument4, has power and is aimed at planet13. Spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "97eb59cc-8785-4a78-b357-49d03f9beef1", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12, instrument3 which is on satellite1 takes an image of star12 in spectrograph1, from star12, satellite1 turns to star0, satellite0 turns from star1 to groundstation2, instrument0 that is on satellite0 is calibrated to groundstation2, satellite0 turns from groundstation2 to phenomenon15, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, from star11, satellite0 turns to star13 and satellite0's instrument0 takes an image of star13 in spectrograph0 to reach the current state. In this state, if from star13, satellite0 turns to star16, is it True or False that satellite0 is not aimed towards groundstation3?", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 is directed towards star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, then satellite1 is redirected from star6 to planet14, an image of planet14 is captured using spectrograph1 with instrument3 on satellite1, satellite1 is then redirected from planet14 to star10, an image of star10 is captured using spectrograph1 with instrument3 on satellite1, satellite1 is then redirected from star10 to star12, an image of star12 is captured using spectrograph1 with instrument3 on satellite1, and finally satellite1 is redirected from star12 to star0, meanwhile satellite0 is redirected from star1 to groundstation2, instrument0 on satellite0 is calibrated to groundstation2, satellite0 is then redirected from groundstation2 to phenomenon15, an image of phenomenon15 is captured using spectrograph0 with instrument0 on satellite0, satellite0 is then redirected from phenomenon15 to star11, an image of star11 is captured using thermograph4 with instrument0 on satellite0, and finally satellite0 is redirected from star11 to star13, capturing an image of star13 using spectrograph0 with instrument0 on satellite0 to reach the current state. In this state, if satellite0 is redirected from star13 to star16, is it True or False that satellite0 is not aimed towards groundstation3?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and has power, and it is currently aimed at star1. Satellite1 is also operational and has power, and it carries instrument3 on board. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "3613a46d-ef87-41ad-9e57-5911c1f79cae", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on to reach the current state. In this state, if satellite0 turns from groundstation2 to groundstation0, is it True or False that satellite0 is not aimed towards groundstation2 and satellite0 is pointing to groundstation0?", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is activated to achieve the current state. In this state, if satellite0 transitions from groundstation2 to groundstation0, is it True or False that satellite0 is no longer directed towards groundstation2 and is instead oriented towards groundstation0?", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and also supported by instrument2 and instrument3, image5 is compatible with instrument1, image6 is supported by instrument0 and instrument1. Furthermore, the following instrument calibrations have been completed: instrument0 for star3, instrument1 for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument3 are on board satellite0, while instrument4 is on board satellite1. Satellite0 has power available and is currently pointing towards groundstation2, whereas satellite1 has power and is aimed towards planet13. Instrument capabilities include: instrument4 supporting infrared7, spectrograph2 being supported by instrument1, spectrograph4 being compatible with instrument3, and thermograph3 being supported by instrument3."}
{"question_id": "c22d8593-1b7b-4a59-a4b9-9879dee20d6f", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, if instrument0 on satellite0 is switched on, is it True or False that power is available for satellite0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, is it True or False that power is available for satellite0?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, supporting both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board and has available power. Satellite1 has instrument3 on board, available power, and is currently pointing towards phenomenon10."}
