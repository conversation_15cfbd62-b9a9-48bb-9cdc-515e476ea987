{"id": 2941311238080170375, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-0f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-1 is at position f4-2f. Key key0-2 is at position f3-0f. Key key0-0 is at position f1-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-1 is at f3-2f location. B. Key key0-1 is at f4-3f location. C. Robot is holding key0-0. D. Key key0-0 is at f3-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f3-2f location", "Key key0-1 is at f4-3f location", "Robot is holding key0-0", "Key key0-0 is at f3-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 655464149194719030, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f3-1f and is holding key0-2. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-3 is at f1-1f location. B. Key key0-1 is at f2-1f location. C. Robot is holding key0-1. D. Key key0-3 is at f3-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-3 is at f1-1f location", "Key key0-1 is at f2-1f location", "Robot is holding key0-1", "Key key0-3 is at f3-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -1724825638758658297, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-2f and is holding key0-2. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-2 is at f3-3f location, and Key key0-0 is at f3-3f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-0f location. B. Robot is at f3-0f location. C. Robot is at f1-3f location. D. Key key0-0 is at f4-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-0f location", "Robot is at f3-0f location", "Robot is at f1-3f location", "Key key0-0 is at f4-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -133803207604662038, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-2 is at position f1-4f. Key key0-0 is at position f2-2f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is holding key0-1. B. Key key0-0 is at f1-0f location. C. Robot is at f4-1f location. D. Key key0-2 is at f0-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is holding key0-1", "Key key0-0 is at f1-0f location", "Robot is at f4-1f location", "Key key0-2 is at f0-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -8090760317253429613, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f4-3f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-1f location. B. Robot is at f3-2f location. C. Key key0-1 is at f3-4f location. D. Key key0-1 is at f0-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-1f location", "Robot is at f3-2f location", "Key key0-1 is at f3-4f location", "Key key0-1 is at f0-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7142182234195325005, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-3f and is holding key0-1. All the positions are open except the following: f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-0 is at position f0-1f. Key key0-2 is at position f4-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f4-3f location. B. Robot is at f1-4f location. C. Key key0-3 is at f2-3f location. D. Robot is at f2-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-3f location", "Robot is at f1-4f location", "Key key0-3 is at f2-3f location", "Robot is at f2-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 2392814088437140753, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-0 is at position f3-3f. Key key0-1 is at position f1-2f. Key key0-2 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-2 is at f3-3f location, and Key key0-0 is at f3-3f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f2-3f location. B. Robot is at f3-0f location. C. Key key0-1 is at f3-4f location. D. Robot is at f3-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f2-3f location", "Robot is at f3-0f location", "Key key0-1 is at f3-4f location", "Robot is at f3-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 7771150773807176329, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-1f and is holding key0-3. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f. Key key0-2 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-0 is at f0-1f location, Key key0-3 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-2f location. B. Robot is at f2-1f location. C. Key key0-0 is at f4-0f location. D. Robot is at f4-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-2f location", "Robot is at f2-1f location", "Key key0-0 is at f4-0f location", "Robot is at f4-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 9025789639356130383, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-2 is at position f1-4f. Key key0-0 is at position f2-2f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f3-2f location. B. Key key0-0 is at f4-2f location. C. Key key0-1 is at f4-0f location. D. Key key0-2 is at f2-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f3-2f location", "Key key0-0 is at f4-2f location", "Key key0-1 is at f4-0f location", "Key key0-2 is at f2-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -94078601785636705, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f4-3f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-2f location, Key key0-0 is at f2-2f location, and Key key0-2 is at f1-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-2 is at f1-2f location. B. Robot is holding key0-2. C. Key key0-1 is at f0-2f location. D. Key key0-1 is at f3-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-2 is at f1-2f location", "Robot is holding key0-2", "Key key0-1 is at f0-2f location", "Key key0-1 is at f3-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 6336520165476008643, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock. Key key0-1 is at position f1-3f. Key key0-0 is at position f3-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-3f location and Key key0-0 is at f2-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f2-0f location. B. Robot is at f2-3f location. C. Robot is at f3-0f location. D. Key key0-1 is at f2-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f2-0f location", "Robot is at f2-3f location", "Robot is at f3-0f location", "Key key0-1 is at f2-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 1314174531444296369, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f3-4f has shape0 shaped lock. Key key0-0 is at position f1-1f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Key key0-1 is at f4-0f location. B. Robot is holding key0-0. C. Key key0-1 is at f0-0f location. D. Key key0-1 is at f1-4f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f4-0f location", "Robot is holding key0-0", "Key key0-1 is at f0-0f location", "Key key0-1 is at f1-4f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -8052467584860449413, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-1f and is holding key0-0. All the positions are open except the following: f4-2f has shape0 shaped lock. Key key0-1 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-3f location and Key key0-0 is at f2-0f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f3-0f location. B. Robot is at f2-2f location. C. Key key0-1 is at f3-1f location. D. Robot is at f2-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f3-0f location", "Robot is at f2-2f location", "Key key0-1 is at f3-1f location", "Robot is at f2-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1677462433743273609, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f2-2f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-2 is at position f1-2f. Key key0-0 is at position f4-1f. Key key0-1 is at position f3-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f2-4f location, Key key0-0 is at f1-0f location, and Key key0-2 is at f1-1f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f4-3f location. B. Key key0-0 is at f2-1f location. C. Robot is at f2-1f location. D. Robot is at f1-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-3f location", "Key key0-0 is at f2-1f location", "Robot is at f2-1f location", "Robot is at f1-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5397520008519777080, "group": "landmarks_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-3f and is holding key0-1. All the positions are open except the following: f4-0f has shape0 shaped lock. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot is at f2-4f location. B. Key key0-1 is at f4-0f location. C. Robot is at f1-2f location. D. Key key0-0 is at f3-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f2-4f location", "Key key0-1 is at f4-0f location", "Robot is at f1-2f location", "Key key0-0 is at f3-2f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
