{"id": -5300480923743753954, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball3. Additionally, ball1 is at room2, ball2 is at room3, ball4 is at room5.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is carrying the ball ball4 in the left gripper and The left gripper of robot robot1 is free?", "answer": "no"}
{"id": -8351840878139143160, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball3 and ball4 are at room2, ball2 is at room1.", "question": "Is it possible to transition to a state where the following holds: Ball ball2 is at room2 location and Ball ball2 is in room room1?", "answer": "no"}
{"id": 2063787763986646885, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball4 and ball2 are at room2, ball1 is at room4, ball3 is at room10.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is at room2 location?", "answer": "yes"}
{"id": -3822123605812103040, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball7. Additionally, ball4, ball1, ball2, and ball3 are at room1, ball5 is at room3, ball6 is at room2.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is at room3 location and The right gripper of robot robot1 is free?", "answer": "yes"}
{"id": -7417836131478415900, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball3 and ball4 are at room2, ball1 and ball2 are at room1.", "question": "Is it possible to transition to a state where the following holds: Ball room2 is at room1 location and The left gripper of robot robot1 is free?", "answer": "no"}
{"id": 5297141015067430868, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball9, and right gripper is carrying the ball ball14. Additionally, ball3, ball13, ball12, and ball1 are at room2, ball4, ball5, ball2, ball15, ball6, and ball8 are at room1, ball7, ball10, and ball11 are at room3.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is carrying the ball ball1 in the right gripper and Robot robot1 is carrying the ball ball8 in the right gripper?", "answer": "no"}
{"id": 898910693672625741, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball3 is at room2, ball4 and ball2 are at room1.", "question": "Is it possible to transition to a state where the following holds: Ball left1 is in room room1?", "answer": "no"}
{"id": 9000560023870591173, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball2. Additionally, ball4 and ball1 are at room1.", "question": "Is it possible to transition to a state where the following holds: The right gripper of robot robot1 is free and Ball ball2 is in room room2?", "answer": "yes"}
{"id": -3752540588024094941, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room4, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room2, ball3 is at room5.", "question": "Is it possible to transition to a state where the following holds: Ball left1 is at room5 location?", "answer": "no"}
{"id": -1140742101112367148, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball3, ball2, and ball1 are at room2.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is carrying the ball ball1 in the right gripper and Robot robot1 is carrying the ball ball3 in the right gripper?", "answer": "no"}
{"id": 4266263960413465702, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball3. Additionally, ball4 is at room2, ball2 is at room1.", "question": "Is it possible to transition to a state where the following holds: Ball ball3 is in room room2 and Robot robot1 is in room room2?", "answer": "yes"}
{"id": -3431730014064061534, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball1. Additionally, ball3 is at room2, ball4 is at room3.", "question": "Is it possible to transition to a state where the following holds: Ball right1 is at room3 location?", "answer": "no"}
{"id": 1115746769585407893, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball4 is at room6, ball1 and ball3 are at room5.", "question": "Is it possible to transition to a state where the following holds: Ball room4 is at room5 location?", "answer": "no"}
{"id": 7773717068495872850, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball3 is at room2, ball4 and ball1 are at room3.", "question": "Is it possible to transition to a state where the following holds: Ball ball2 is at room3 location and Ball ball2 is in room room2?", "answer": "no"}
{"id": -1611079026423119195, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 is at room2, ball2 is at room1, ball3 is at room3.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is in room room3?", "answer": "yes"}
