{"id": -7716351019568131282, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0; c2 is at l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c1 is at location l0, Car c2 is at location l1, and Car c0 is at location l1.", "question": "Simplify the plan \"(sail l1 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (board c2 l1) (debark c2 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c2 l1)", "(debark c2 l1)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1)))\n)"}
{"id": 9182710835048010743, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c9, c6, and c2 are at l0; c0, c8, c7, c1, c5, and c4 are at l1; c3 is at l2. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c9 is at location l0, Car c8 is at location l2, Car c6 is at location l2, Car c5 is at location l2, Car c1 is at location l1, Car c4 is at location l0, Car c3 is at location l2, Car c7 is at location l0, and Car c0 is at location l2.", "question": "Simplify the plan \"(sail l2 l1) (board c7 l1) (debark c7 l1) (board c7 l1) (sail l1 l0) (debark c7 l0) (board c2 l0) (sail l0 l1) (debark c2 l1) (board c8 l1) (sail l1 l2) (debark c8 l2) (sail l2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c6 l0) (sail l0 l2) (debark c6 l2) (sail l2 l1) (board c0 l1) (sail l1 l2) (debark c0 l2) (sail l2 l1) (board c5 l1) (sail l1 l2) (debark c5 l2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c7 l1)", "(debark c7 l1)", "-1"], ["(debark c7 l1)", "(board c7 l1)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c3 l2) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -260566324428468931, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3.", "question": "Simplify the plan \"(board c1 l0) (sail l0 l3) (debark c1 l3) (sail l3 l4) (board c0 l4) (sail l4 l3) (debark c0 l3) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3) (sail l3 l0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l3 l0)", "1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 2596066978103388045, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3.", "question": "Simplify the plan \"(board c1 l0) (sail l0 l1) (sail l1 l0) (sail l0 l3) (debark c1 l3) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3) (sail l3 l4) (board c0 l4) (sail l4 l3) (debark c0 l3)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l0 l1)", "(sail l1 l0)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 8962674042529513792, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c9, c6, and c2 are at l0; c0, c8, c7, c1, c5, and c4 are at l1; c3 is at l2. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c9 is at location l0, Car c8 is at location l2, Car c6 is at location l2, Car c5 is at location l2, Car c1 is at location l1, Car c4 is at location l0, Car c3 is at location l2, Car c7 is at location l0, and Car c0 is at location l2.", "question": "Simplify the plan \"(sail l2 l1) (sail l1 l2) (sail l2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c6 l0) (sail l0 l2) (debark c6 l2) (sail l2 l1) (sail l1 l0) (board c2 l0) (sail l0 l1) (debark c2 l1) (board c5 l1) (sail l1 l2) (debark c5 l2) (sail l2 l1) (board c0 l1) (sail l1 l2) (debark c0 l2) (sail l2 l1) (board c8 l1) (sail l1 l2) (debark c8 l2) (sail l2 l1) (board c7 l1) (sail l1 l0) (debark c7 l0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l2 l1)", "(sail l1 l2)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c3 l2) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -6966458529974339613, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c40, c24, c21, c39, c13, c43, c5, c41, c48, c27, c34, c19, c49, c45, c46, c36, c28, c22, c31, c47, c25, c42, c12, c32, c4, c7, c38, c37, and c26 are at l1; c9, c15, c14, c3, c33, c0, c16, c30, c23, c29, c6, c44, c20, c8, c11, c1, c18, c17, c35, c2, and c10 are at l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c24 is at location l1, Car c9 is at location l0, Car c18 is at location l1, Car c33 is at location l1, Car c13 is at location l0, Car c14 is at location l0, Car c15 is at location l1, Car c45 is at location l0, Car c25 is at location l0, Car c17 is at location l1, Car c23 is at location l1, Car c26 is at location l0, Car c40 is at location l0, Car c39 is at location l1, Car c0 is at location l0, Car c30 is at location l0, Car c35 is at location l1, Car c43 is at location l0, Car c29 is at location l0, Car c1 is at location l1, Car c5 is at location l1, Car c41 is at location l1, Car c48 is at location l1, Car c27 is at location l1, Car c34 is at location l1, Car c44 is at location l0, Car c20 is at location l0, Car c21 is at location l0, Car c49 is at location l1, Car c8 is at location l0, Car c11 is at location l0, Car c28 is at location l1, Car c6 is at location l1, Car c31 is at location l1, Car c22 is at location l0, Car c36 is at location l0, Car c47 is at location l1, Car c16 is at location l1, Car c42 is at location l1, Car c37 is at location l0, Car c46 is at location l0, Car c12 is at location l0, Car c10 is at location l1, Car c32 is at location l1, Car c38 is at location l0, Car c7 is at location l1, Car c4 is at location l1, Car c2 is at location l0, Car c3 is at location l1, and Car c19 is at location l0.", "question": "Simplify the plan \"(board c12 l1) (sail l1 l0) (debark c12 l0) (board c1 l0) (sail l0 l1) (debark c1 l1) (board c13 l1) (sail l1 l0) (debark c13 l0) (board c10 l0) (sail l0 l1) (debark c10 l1) (board c19 l1) (sail l1 l0) (debark c19 l0) (board c15 l0) (sail l0 l1) (debark c15 l1) (board c21 l1) (sail l1 l0) (debark c21 l0) (board c16 l0) (sail l0 l1) (debark c16 l1) (board c22 l1) (sail l1 l0) (debark c22 l0) (board c17 l0) (sail l0 l1) (debark c17 l1) (board c25 l1) (sail l1 l0) (debark c25 l0) (board c18 l0) (sail l0 l1) (debark c18 l1) (board c26 l1) (sail l1 l0) (debark c26 l0) (board c23 l0) (sail l0 l1) (debark c23 l1) (board c36 l1) (sail l1 l0) (debark c36 l0) (board c3 l0) (sail l0 l1) (debark c3 l1) (board c37 l1) (sail l1 l0) (debark c37 l0) (board c33 l0) (sail l0 l1) (debark c33 l1) (board c38 l1) (sail l1 l0) (debark c38 l0) (board c35 l0) (sail l0 l1) (debark c35 l1) (board c40 l1) (sail l1 l0) (debark c40 l0) (board c6 l0) (sail l0 l1) (debark c6 l1) (board c43 l1) (sail l1 l0) (debark c43 l0) (sail l0 l1) (board c45 l1) (sail l1 l0) (debark c45 l0) (sail l0 l1) (board c46 l1) (sail l1 l0) (debark c46 l0) (board c30 l0) (debark c30 l0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c30 l0)", "(debark c30 l0)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c10 l0) (at c11 l0) (at c12 l1) (at c13 l1) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c20 l0) (at c21 l1) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 8996563983746190156, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c24, c33, c21, c13, c48, c34, c19, c44, c46, and c32 are at l1; c9, c25, c40, c6, c41, c11, c27, c1, c18, and c17 are at l0; c8, c16, c30, and c12 are at l2; c29, c4, c3, c49, c7, c23, c42, c15, c31, c43, c37, c22, c39, and c5 are at l3; c26, c38, c10, c20, c35, c2, c45, c36, c0, c28, c47, and c14 are at l4. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board car ?car at location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c42 is at location l0, Car c33 is at location l1, Car c25 is at location l3, Car c15 is at location l1, Car c43 is at location l2, Car c5 is at location l4, Car c35 is at location l2, Car c26 is at location l2, Car c39 is at location l2, Car c45 is at location l0, Car c8 is at location l4, Car c16 is at location l0, Car c38 is at location l4, Car c34 is at location l3, Car c40 is at location l2, Car c12 is at location l3, Car c11 is at location l1, Car c7 is at location l0, Car c22 is at location l2, Car c23 is at location l3, Car c9 is at location l4, Car c6 is at location l3, Car c19 is at location l1, Car c2 is at location l1, Car c24 is at location l0, Car c44 is at location l0, Car c49 is at location l1, Car c31 is at location l3, Car c20 is at location l3, Car c27 is at location l4, Car c47 is at location l0, Car c4 is at location l0, Car c3 is at location l2, Car c1 is at location l0, Car c36 is at location l0, Car c0 is at location l2, Car c46 is at location l4, Car c37 is at location l3, Car c18 is at location l0, Car c30 is at location l4, Car c28 is at location l4, Car c17 is at location l0, Car c32 is at location l1, Car c41 is at location l4, Car c13 is at location l3, Car c29 is at location l4, Car c14 is at location l4, Car c48 is at location l0, Car c10 is at location l0, and Car c21 is at location l2.", "question": "Simplify the plan \"(board c13 l1) (sail l1 l3) (debark c13 l3) (board c22 l3) (sail l3 l2) (debark c22 l2) (board c16 l2) (sail l2 l0) (debark c16 l0) (board c27 l0) (sail l0 l4) (debark c27 l4) (board c10 l4) (sail l4 l0) (debark c10 l0) (board c40 l0) (sail l0 l2) (debark c40 l2) (sail l2 l4) (board c2 l4) (sail l4 l1) (debark c2 l1) (board c21 l1) (sail l1 l2) (debark c21 l2) (sail l2 l4) (board c20 l4) (sail l4 l3) (debark c20 l3) (board c15 l3) (sail l3 l0) (sail l0 l1) (debark c15 l1) (board c24 l1) (sail l1 l0) (debark c24 l0) (board c41 l0) (sail l0 l4) (debark c41 l4) (board c36 l4) (sail l4 l0) (debark c36 l0) (board c9 l0) (sail l0 l4) (debark c9 l4) (board c45 l4) (sail l4 l0) (debark c45 l0) (board c11 l0) (sail l0 l1) (debark c11 l1) (board c34 l1) (sail l1 l0) (sail l0 l3) (debark c34 l3) (board c29 l3) (sail l3 l0) (sail l0 l4) (debark c29 l4) (board c47 l4) (sail l4 l0) (debark c47 l0) (board c25 l0) (sail l0 l1) (sail l1 l3) (debark c25 l3) (board c3 l3) (sail l3 l0) (sail l0 l2) (debark c3 l2) (board c12 l2) (sail l2 l3) (debark c12 l3) (board c39 l3) (sail l3 l0) (sail l0 l2) (debark c39 l2) (board c30 l2) (sail l2 l3) (sail l3 l4) (debark c30 l4) (board c26 l4) (sail l4 l2) (debark c26 l2) (board c8 l2) (sail l2 l3) (sail l3 l4) (debark c8 l4) (board c35 l4) (sail l4 l2) (debark c35 l2) (sail l2 l3) (board c4 l3) (sail l3 l0) (debark c4 l0) (board c6 l0) (sail l0 l1) (sail l1 l3) (debark c6 l3) (board c42 l3) (sail l3 l0) (debark c42 l0) (sail l0 l1) (board c44 l1) (sail l1 l0) (debark c44 l0) (sail l0 l1) (board c46 l1) (sail l1 l2) (sail l2 l4) (debark c46 l4) (board c0 l4) (sail l4 l2) (debark c0 l2) (sail l2 l3) (board c43 l3) (sail l3 l0) (sail l0 l2) (debark c43 l2) (sail l2 l3) (board c49 l3) (sail l3 l0) (sail l0 l1) (debark c49 l1) (board c48 l1) (sail l1 l0) (debark c48 l0) (sail l0 l3) (board c5 l3) (debark c5 l3) (board c7 l3) (sail l3 l0) (debark c7 l0) (sail l0 l3) (board c5 l3) (sail l3 l4) (debark c5 l4) (board c28 l4) (debark c28 l4)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c5 l3)", "(debark c5 l3)", "-1"], ["(board c28 l4)", "(debark c28 l4)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c10 l4) (at c11 l0) (at c12 l2) (at c13 l1) (at c14 l4) (at c15 l3) (at c16 l2) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l4) (at c20 l4) (at c21 l1) (at c22 l3) (at c23 l3) (at c24 l1) (at c25 l0) (at c26 l4) (at c27 l0) (at c28 l4) (at c29 l3) (at c3 l3) (at c30 l2) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l4) (at c36 l4) (at c37 l3) (at c38 l4) (at c39 l3) (at c4 l3) (at c40 l0) (at c41 l0) (at c42 l3) (at c43 l3) (at c44 l1) (at c45 l4) (at c46 l1) (at c47 l4) (at c48 l1) (at c49 l3) (at c5 l3) (at c6 l0) (at c7 l3) (at c8 l2) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l2) (at c1 l0) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l4) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c47 l0) (at c48 l0) (at c49 l1)))\n)"}
{"id": 8508503582580393722, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0; c2 is at l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c1 is at location l0, Car c2 is at location l1, and Car c0 is at location l1.", "question": "Simplify the plan \"(sail l1 l0) (board c0 l0) (debark c0 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (board c0 l1) (debark c0 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c0 l0)", "(debark c0 l0)", "-1"], ["(debark c0 l0)", "(board c0 l0)", "-1"], ["(debark c0 l1)", "(board c0 l1)", "-1"], ["(board c0 l1)", "(debark c0 l1)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1)))\n)"}
{"id": -4696199694846516236, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c40, c24, c21, c39, c13, c43, c5, c41, c48, c27, c34, c19, c49, c45, c46, c36, c28, c22, c31, c47, c25, c42, c12, c32, c4, c7, c38, c37, and c26 are at l1; c9, c15, c14, c3, c33, c0, c16, c30, c23, c29, c6, c44, c20, c8, c11, c1, c18, c17, c35, c2, and c10 are at l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c24 is at location l1, Car c9 is at location l0, Car c18 is at location l1, Car c33 is at location l1, Car c13 is at location l0, Car c14 is at location l0, Car c15 is at location l1, Car c45 is at location l0, Car c25 is at location l0, Car c17 is at location l1, Car c23 is at location l1, Car c26 is at location l0, Car c40 is at location l0, Car c39 is at location l1, Car c0 is at location l0, Car c30 is at location l0, Car c35 is at location l1, Car c43 is at location l0, Car c29 is at location l0, Car c1 is at location l1, Car c5 is at location l1, Car c41 is at location l1, Car c48 is at location l1, Car c27 is at location l1, Car c34 is at location l1, Car c44 is at location l0, Car c20 is at location l0, Car c21 is at location l0, Car c49 is at location l1, Car c8 is at location l0, Car c11 is at location l0, Car c28 is at location l1, Car c6 is at location l1, Car c31 is at location l1, Car c22 is at location l0, Car c36 is at location l0, Car c47 is at location l1, Car c16 is at location l1, Car c42 is at location l1, Car c37 is at location l0, Car c46 is at location l0, Car c12 is at location l0, Car c10 is at location l1, Car c32 is at location l1, Car c38 is at location l0, Car c7 is at location l1, Car c4 is at location l1, Car c2 is at location l0, Car c3 is at location l1, and Car c19 is at location l0.", "question": "Simplify the plan \"(board c12 l1) (sail l1 l0) (debark c12 l0) (board c1 l0) (sail l0 l1) (debark c1 l1) (board c13 l1) (sail l1 l0) (debark c13 l0) (board c10 l0) (sail l0 l1) (debark c10 l1) (board c19 l1) (sail l1 l0) (debark c19 l0) (board c15 l0) (sail l0 l1) (debark c15 l1) (board c21 l1) (sail l1 l0) (debark c21 l0) (board c16 l0) (sail l0 l1) (debark c16 l1) (board c22 l1) (sail l1 l0) (debark c22 l0) (board c17 l0) (sail l0 l1) (debark c17 l1) (board c25 l1) (sail l1 l0) (debark c25 l0) (board c18 l0) (sail l0 l1) (debark c18 l1) (board c26 l1) (sail l1 l0) (debark c26 l0) (board c23 l0) (sail l0 l1) (debark c23 l1) (board c36 l1) (sail l1 l0) (debark c36 l0) (board c3 l0) (sail l0 l1) (debark c3 l1) (board c37 l1) (sail l1 l0) (debark c37 l0) (board c33 l0) (sail l0 l1) (debark c33 l1) (board c38 l1) (sail l1 l0) (debark c38 l0) (board c35 l0) (sail l0 l1) (debark c35 l1) (board c40 l1) (sail l1 l0) (debark c40 l0) (board c6 l0) (sail l0 l1) (debark c6 l1) (board c43 l1) (sail l1 l0) (debark c43 l0) (sail l0 l1) (board c45 l1) (sail l1 l0) (debark c45 l0) (sail l0 l1) (board c46 l1) (sail l1 l0) (debark c46 l0) (sail l0 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l0 l1)", "14"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c10 l0) (at c11 l0) (at c12 l1) (at c13 l1) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c20 l0) (at c21 l1) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -5626617029132068598, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c24, c33, c21, c13, c48, c34, c19, c44, c46, and c32 are at l1; c9, c25, c40, c6, c41, c11, c27, c1, c18, and c17 are at l0; c8, c16, c30, and c12 are at l2; c29, c4, c3, c49, c7, c23, c42, c15, c31, c43, c37, c22, c39, and c5 are at l3; c26, c38, c10, c20, c35, c2, c45, c36, c0, c28, c47, and c14 are at l4. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c42 is at location l0, Car c33 is at location l1, Car c25 is at location l3, Car c15 is at location l1, Car c43 is at location l2, Car c5 is at location l4, Car c35 is at location l2, Car c26 is at location l2, Car c39 is at location l2, Car c45 is at location l0, Car c8 is at location l4, Car c16 is at location l0, Car c38 is at location l4, Car c34 is at location l3, Car c40 is at location l2, Car c12 is at location l3, Car c11 is at location l1, Car c7 is at location l0, Car c22 is at location l2, Car c23 is at location l3, Car c9 is at location l4, Car c6 is at location l3, Car c19 is at location l1, Car c2 is at location l1, Car c24 is at location l0, Car c44 is at location l0, Car c49 is at location l1, Car c31 is at location l3, Car c20 is at location l3, Car c27 is at location l4, Car c47 is at location l0, Car c4 is at location l0, Car c3 is at location l2, Car c1 is at location l0, Car c36 is at location l0, Car c0 is at location l2, Car c46 is at location l4, Car c37 is at location l3, Car c18 is at location l0, Car c30 is at location l4, Car c28 is at location l4, Car c17 is at location l0, Car c32 is at location l1, Car c41 is at location l4, Car c13 is at location l3, Car c29 is at location l4, Car c14 is at location l4, Car c48 is at location l0, Car c10 is at location l0, and Car c21 is at location l2.", "question": "Simplify the plan \"(board c13 l1) (sail l1 l3) (debark c13 l3) (board c22 l3) (sail l3 l2) (debark c22 l2) (board c16 l2) (sail l2 l0) (debark c16 l0) (board c27 l0) (sail l0 l4) (debark c27 l4) (board c10 l4) (sail l4 l0) (debark c10 l0) (board c40 l0) (sail l0 l2) (debark c40 l2) (sail l2 l4) (board c2 l4) (sail l4 l1) (debark c2 l1) (board c21 l1) (sail l1 l2) (debark c21 l2) (sail l2 l4) (board c20 l4) (sail l4 l3) (debark c20 l3) (board c15 l3) (sail l3 l0) (sail l0 l1) (debark c15 l1) (board c24 l1) (sail l1 l0) (debark c24 l0) (board c41 l0) (sail l0 l4) (debark c41 l4) (board c36 l4) (sail l4 l0) (debark c36 l0) (board c9 l0) (sail l0 l4) (debark c9 l4) (board c45 l4) (sail l4 l0) (debark c45 l0) (board c11 l0) (sail l0 l1) (debark c11 l1) (board c34 l1) (sail l1 l0) (sail l0 l3) (debark c34 l3) (board c5 l3) (sail l3 l0) (sail l0 l4) (debark c5 l4) (board c47 l4) (sail l4 l0) (debark c47 l0) (board c25 l0) (sail l0 l1) (sail l1 l3) (debark c25 l3) (board c3 l3) (sail l3 l0) (sail l0 l2) (debark c3 l2) (board c12 l2) (sail l2 l3) (debark c12 l3) (board c39 l3) (sail l3 l0) (sail l0 l2) (debark c39 l2) (board c30 l2) (sail l2 l3) (sail l3 l4) (debark c30 l4) (board c26 l4) (sail l4 l2) (debark c26 l2) (board c8 l2) (sail l2 l3) (sail l3 l4) (debark c8 l4) (board c35 l4) (sail l4 l2) (debark c35 l2) (sail l2 l3) (board c7 l3) (sail l3 l0) (debark c7 l0) (board c6 l0) (sail l0 l1) (sail l1 l3) (debark c6 l3) (board c4 l3) (sail l3 l0) (debark c4 l0) (sail l0 l1) (board c44 l1) (sail l1 l0) (debark c44 l0) (sail l0 l1) (board c46 l1) (sail l1 l2) (sail l2 l4) (debark c46 l4) (board c0 l4) (sail l4 l2) (debark c0 l2) (sail l2 l3) (board c43 l3) (sail l3 l0) (sail l0 l2) (debark c43 l2) (sail l2 l3) (board c49 l3) (sail l3 l0) (sail l0 l1) (debark c49 l1) (board c48 l1) (sail l1 l0) (debark c48 l0) (sail l0 l3) (board c29 l3) (debark c29 l3) (board c42 l3) (sail l3 l0) (debark c42 l0) (sail l0 l3) (board c29 l3) (sail l3 l4) (debark c29 l4) (sail l4 l0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l4 l0)", "5"], ["(board c29 l3)", "(debark c29 l3)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c10 l4) (at c11 l0) (at c12 l2) (at c13 l1) (at c14 l4) (at c15 l3) (at c16 l2) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l4) (at c20 l4) (at c21 l1) (at c22 l3) (at c23 l3) (at c24 l1) (at c25 l0) (at c26 l4) (at c27 l0) (at c28 l4) (at c29 l3) (at c3 l3) (at c30 l2) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l4) (at c36 l4) (at c37 l3) (at c38 l4) (at c39 l3) (at c4 l3) (at c40 l0) (at c41 l0) (at c42 l3) (at c43 l3) (at c44 l1) (at c45 l4) (at c46 l1) (at c47 l4) (at c48 l1) (at c49 l3) (at c5 l3) (at c6 l0) (at c7 l3) (at c8 l2) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l2) (at c1 l0) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l4) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c47 l0) (at c48 l0) (at c49 l1)))\n)"}
{"id": -1219355986766168268, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c1 and c0 are at l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry. The goal is to reach a state where the following facts hold: Car c1 is at location l1 and Car c0 is at location l1.", "question": "Simplify the plan \"(board c1 l0) (sail l0 l1) (sail l1 l0) (sail l0 l1) (debark c1 l1) (sail l1 l0) (sail l0 l1) (sail l1 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (board c1 l1) (debark c1 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l0 l1)", "(sail l1 l0)", "-1"], ["(sail l1 l0)", "(sail l0 l1)", "-1"], ["(sail l1 l0)", "(sail l0 l1)", "-1"], ["(sail l0 l1)", "(sail l1 l0)", "-1"], ["(board c1 l1)", "(debark c1 l1)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l1)))\n)"}
{"id": -3297558880456740561, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0, c1, c2, and c3 are at l0; c4 is at l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board car ?car at location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c0 is at location l1, Car c4 is at location l0, Car c1 is at location l0, and Car c3 is at location l0.", "question": "Simplify the plan \"(board c0 l0) (sail l0 l1) (debark c0 l1) (board c4 l1) (debark c4 l1) (board c4 l1) (debark c4 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c4 l0) (debark c4 l0) (board c2 l0) (sail l0 l1) (debark c2 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c4 l1)", "(debark c4 l1)", "-1"], ["(debark c4 l1)", "(board c4 l1)", "-1"], ["(board c4 l1)", "(debark c4 l1)", "-1"], ["(debark c4 l1)", "(board c4 l1)", "-1"], ["(debark c4 l0)", "(board c4 l0)", "-1"], ["(board c4 l0)", "(debark c4 l0)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": -8964911247857237428, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c1 and c0 are at l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c1 is at location l1 and Car c0 is at location l1.", "question": "Simplify the plan \"(board c0 l0) (debark c0 l0) (board c1 l0) (sail l0 l1) (debark c1 l1) (board c1 l1) (debark c1 l1) (sail l1 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (board c1 l1) (debark c1 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(board c0 l0)", "(debark c0 l0)", "-1"], ["(debark c1 l1)", "(board c1 l1)", "-1"], ["(board c1 l1)", "(debark c1 l1)", "-1"], ["(board c1 l1)", "(debark c1 l1)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l1)))\n)"}
{"id": -4707218070491295599, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 20 cars, numbered consecutively. \nCurrently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c9, c15, c14, c3, c0, c16, c6, c8, c11, c1, c18, c17, c2, and c10 are at l0; c13, c5, c19, c12, c7, and c4 are at l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - embark the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c18 is at location l1, Car c15 is at location l0, Car c13 is at location l0, Car c8 is at location l1, Car c17 is at location l1, Car c3 is at location l0, Car c0 is at location l0, Car c1 is at location l1, Car c5 is at location l1, Car c11 is at location l1, Car c19 is at location l1, Car c2 is at location l1, Car c14 is at location l1, Car c6 is at location l1, Car c16 is at location l1, Car c12 is at location l1, Car c7 is at location l1, Car c4 is at location l1, and Car c10 is at location l0.", "question": "Simplify the plan \"(board c13 l1) (sail l1 l0) (debark c13 l0) (board c1 l0) (sail l0 l1) (debark c1 l1) (sail l1 l0) (board c11 l0) (sail l0 l1) (debark c11 l1) (sail l1 l0) (board c14 l0) (sail l0 l1) (debark c14 l1) (sail l1 l0) (board c16 l0) (sail l0 l1) (debark c16 l1) (sail l1 l0) (board c17 l0) (sail l0 l1) (debark c17 l1) (sail l1 l0) (board c18 l0) (sail l0 l1) (debark c18 l1) (sail l1 l0) (board c2 l0) (sail l0 l1) (debark c2 l1) (sail l1 l0) (board c6 l0) (sail l0 l1) (debark c6 l1) (sail l1 l0) (board c8 l0) (sail l0 l1) (debark c8 l1) (sail l1 l0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(sail l1 l0)", "10"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c20)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c10 l0) (at c11 l0) (at c12 l1) (at c13 l1) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l1) (at c9 l0) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l1) (at c15 l0) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l1)))\n)"}
{"id": 1644043721885670796, "group": "action_justification_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0, c1, c2, and c3 are at l0; c4 is at l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board car ?car at location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c0 is at location l1, Car c4 is at location l0, Car c1 is at location l0, and Car c3 is at location l0.", "question": "Simplify the plan \"(board c0 l0) (sail l0 l1) (debark c0 l1) (board c0 l1) (debark c0 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c3 l0) (debark c3 l0) (board c2 l0) (sail l0 l1) (debark c2 l1) (board c2 l1) (debark c2 l1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(debark c0 l1)", "(board c0 l1)", "-1"], ["(board c0 l1)", "(debark c0 l1)", "-1"], ["(board c3 l0)", "(debark c3 l0)", "-1"], ["(debark c2 l1)", "(board c2 l1)", "-1"], ["(board c2 l1)", "(debark c2 l1)", "-1"]], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
