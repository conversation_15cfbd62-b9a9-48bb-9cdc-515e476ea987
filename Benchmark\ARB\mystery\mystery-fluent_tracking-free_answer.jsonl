{"question_id": "d0394b33-bc86-47d2-955d-1665a5775031", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for s1? Write None if there are none", "answer": "space s1 does not neighbour space s0, spaces s0 and s1 are neighbors, vehicle v0 does not contain space s1 and vehicle v1 contains space s1", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 travels from location l0, which has fuel levels f5 and f4, to location l1. Upon arrival at l1, cargo c0 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l1, which has fuel levels f7 and f6, back to l0. At l0, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 travels from l0, which has fuel levels f4 and f3, to l1. At l1, cargo c1 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l1, which has fuel levels f6 and f5, back to l0. At l0, cargo c1 is unloaded from vehicle v0, freeing up spaces s0 and s1. Finally, vehicle v0 travels from l0, which has fuel levels f3 and f2, to l1, where cargo c2 is loaded into vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for s1? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is located at l1, cargo c5 is positioned at l0, cargo c6 is situated at l1, cargo c7 is found at l1, cargo c8 is positioned at l0, fuel f5 is available in l0, fuel f7 is available in l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are linked, location l1 is linked to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
{"question_id": "706c2ae1-8898-450c-bb3f-0df09760f5a4", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state that involve negations for v1? Write None if there are none", "answer": "cargo c0 is not in vehicle v1, cargo c1 is not in vehicle v1, cargo c2 is not located in vehicle v1, cargo c3 is not located in vehicle v1, cargo c4 is not located in vehicle v1, cargo c5 is not located in vehicle v1, cargo c8 is not in vehicle v1, cargo c9 is not located in vehicle v1, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not contain space s0 and vehicle v1 is not present at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 travels from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l0, with fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, what are the valid properties that involve negations for v1? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is also situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is associated with fuel level f3, location l1 is associated with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection also exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is also equipped with space s1 and is currently at location l0."}
{"question_id": "b8496b61-e41c-45d0-bbe8-0e8354ef47db", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for c6? Write None if there are none", "answer": "cargo c6 is situated at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 relocates from location l0, which has fuel levels of f5 and f4, to location l1, resulting in the current state. In this state, what are the valid properties that do not involve negations for c6, or state None if none exist.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is located at l1, cargo c5 is positioned at l0, cargo c6 is situated at l1, cargo c7 is found at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are linked, location l1 is linked to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
{"question_id": "765492fb-281a-4bda-9fa2-8cd24e64a564", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for c1? Write None if there are none", "answer": "cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v0, cargo c1 is not located in vehicle v1 and cargo c1 is situated at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, what are the valid properties (including both affirmative and negated properties) for c1? If there are no valid properties, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is also situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection also exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "9a637926-fff9-42ad-98c6-485b8274a65b", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state that involve negations for f6? Write None if there are none", "answer": "fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f6, fuel level f7 does not neighbour fuel level f6, fuel-levels f3 and f6 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f8 and f6 are not neighbors, location l0 does not have fuel f6 and location l1 does not have a fuel-level of f6", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location l1, cargo c2 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, what are the valid state properties involving negations of f6? If none exist, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at location l0, cargo c1 is also found at location l0, cargo c2 is positioned at location l1, cargo c3 is situated at location l1, cargo c4 is located at location l1, cargo c5 is positioned at location l1, cargo c6 is found at location l0, cargo c7 is located at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "a6949d19-e913-4f00-b2f6-fc252dd65080", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for c5? Write None if there are none", "answer": "cargo c5 is not located in vehicle v0, cargo c5 is not situated at location l0 and cargo c5 is situated at location l1", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1 to location l0, which has fuel levels f6 and f5. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, vehicle v0 moves from location l0 back to location l1, which now has fuel levels f7 and f6. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1 to location l0, which has fuel levels f5 and f4. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0 back to location l1, which has fuel levels f6 and f5. At location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 travels from location l1 to location l0, which has fuel levels f4 and f3, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for c5? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are interconnected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "6d1860f3-0211-4bb9-a54c-99d03c783d55", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what are the valid properties of the state that involve negations for f0? Write None if there are none", "answer": "fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f0, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are not neighbors and location l1 does not have fuel f0", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, where the fuel levels are f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, at location l0, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. This process continues with the following actions: at location l1, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. The sequence of actions continues with cargo c1 being loaded onto vehicle v0 at location l0, then unloaded at location l1, followed by cargo c5 being loaded at location l1 and unloaded at location l0. Next, cargo c7 is loaded onto vehicle v0 at location l0 and unloaded at location l1. Finally, cargo c7 is unloaded from vehicle v0, and cargo c9 is loaded onto vehicle v0 at location l1, resulting in the current state. In this state, what are the valid properties that involve negations for f0? If there are none, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "77c8373a-6f60-4624-8bf8-3f79b7b0c57f", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for v0? Write None if there are none", "answer": "cargo c6 is located in vehicle v0, vehicle v0 has space s0 and vehicle v0 is present at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, vehicle v0 moves back to location l1, which now has fuel levels f7 and f6. At location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves to location l0, where the fuel levels are f5 and f4, and cargo c4 is unloaded with spaces s0 and s1. After that, vehicle v0 moves from location l0, which has fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, and vehicle v0 moves to location l0, where the fuel levels are f4 and f3, resulting in the current state. In this state, what are the valid properties of the state that do not involve negations for v0? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, c2, and c3 are all situated at l0. Additionally, cargo c4, c5, c6, c7, c8, and c9 are all located at l1. Fuel f7 is present at l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "4f97ff8d-c346-41dc-82b1-8ddac5544a70", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for f1? Write None if there are none", "answer": "fuel level f1 neighbors fuel level f2 and fuel-levels f0 and f1 are neighbors", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0, with fuel levels f8 and f7 at location l1, moves to location l0. Upon arrival, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1, at location l0. Next, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, with fuel levels f7 and f6, to location l0. Upon arrival, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, what are the valid properties of the state that do not involve negations for f1? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "b6ab8c4f-b06a-401a-8259-57ace9faa60f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for c9? Write None if there are none", "answer": "cargo c9 is not in vehicle v0, cargo c9 is not in vehicle v1, cargo c9 is not situated at location l0 and cargo c9 is situated at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: cargo c2 is loaded into vehicle v0, utilizing spaces s1 and s0 at location l1, resulting in the current state. In this state, what are the valid properties (including both affirmative and negated properties) for c9, or state 'None' if there are no valid properties.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, and c5 are all situated at location l1. Additionally, cargo c6 and c7 are present at l0, and cargo c8 and c9 are at l1. \n\nFuel levels are arranged in a sequence where f1 is adjacent to f2, and f2 is adjacent to f3. Furthermore, f5 is next to f6, and f0 and f1 are neighboring fuel levels. The sequence continues with f3 and f4 being neighbors, followed by f4 and f5, then f6 and f7, and finally f7 and f8. \n\nLocation l0 is associated with fuel level f3, whereas location l1 has fuel level f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1, as well as between l1 and l0. \n\nVehicle v0 is assigned space s1 and is currently at location l1. Similarly, vehicle v1 is also assigned space s1 and is present at location l0."}
{"question_id": "fff85634-3a03-451b-b951-6841227aa88c", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_10", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for v0? Write None if there are none", "answer": "cargo c0 is not in vehicle v0, cargo c3 is not in vehicle v0, cargo c4 is not in vehicle v0, cargo c6 is not located in vehicle v0, cargo c7 is not in vehicle v0, cargo c8 is not in vehicle v0, cargo c9 is not located in vehicle v0, vehicle v0 contains space s1, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c5, vehicle v0 does not contain space s0, vehicle v0 is not at location l1 and vehicle v0 is situated at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 moves from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, utilizing spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1, at which point cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0, followed by vehicle v0 moving from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0, using spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1, where cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, then vehicle v0 moves from location l1, with fuel levels f4 and f3, to location l0, where cargo c6 is unloaded from vehicle v0, utilizing spaces s0 and s1, next vehicle v0 moves from location l0, with fuel levels f5 and f4, back to location l1, where cargo c7 is loaded onto vehicle v0, occupying spaces s1 and s0, followed by vehicle v0 moving from location l1, with fuel levels f3 and f2, to location l0, where cargo c7 is unloaded from vehicle v0, using spaces s0 and s1, then vehicle v0 moves from location l0, with fuel levels f4 and f3, back to location l1, at which point cargo c8 is loaded onto vehicle v0, occupying spaces s1 and s0, then vehicle v0 moves from location l1, with fuel levels f2 and f1, to location l0, and finally cargo c8 is unloaded from vehicle v0, utilizing spaces s0 and s1 at location l0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for v0? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, c2, and c3 are all situated at location l0. Additionally, cargo c4, c5, c6, c7, c8, and c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "63f5a9b7-f97c-433c-8c50-f69dc89d7e4c", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for c4? Write None if there are none", "answer": "cargo c4 is at location l0, cargo c4 is not in vehicle v1, cargo c4 is not located in vehicle v0 and cargo c4 is not situated at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is present at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has fuel f3, space s1 neighbors space s2, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is present at location l1, vehicle v1 contains space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, what are the valid properties (including both affirmative and negated properties) for c4? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is situated at l0, cargo c4 is situated at l0, cargo c5 is located at l1, cargo c6 is situated at l0, cargo c7 is found at l0, cargo c8 is found at l1, cargo c9 is situated at l1, fuel level f0 is adjacent to fuel level f1, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel levels f1 and f2 are adjacent, locations l0 and l1 are linked, location l0 has a fuel level of f4, location l1 has fuel level f3, space s1 is adjacent to space s2, spaces s0 and s1 are adjacent, a connection exists between locations l1 and l0, vehicle v0 occupies space s2, vehicle v0 is currently at location l1, vehicle v1 contains space s2 and vehicle v1 is currently at location l1."}
{"question_id": "51dd7ef4-7cb2-45e0-9220-49c442a7a365", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for f5? Write None if there are none", "answer": "fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6 and location l1 has fuel f5", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 travels from location l0, which has fuel levels f5 and f4, to location l1. Upon arrival at l1, cargo c0 is loaded into vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from l1, which now has fuel levels f7 and f6, back to l0. At l0, cargo c0 is then unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then travels from l0, which has fuel levels f4 and f3, to l1. At l1, cargo c1 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l1, which has fuel levels f6 and f5, back to l0. Upon arrival at l0, cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. Finally, vehicle v0 travels from l0, which has fuel levels f3 and f2, to l1, where cargo c2 is loaded into vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, what are the valid properties that do not involve negations for f5? If none exist, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is present at l1, cargo c5 is located at l0, cargo c6 is situated at l1, cargo c7 is positioned at l1, cargo c8 is situated at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are connected, location l1 is also connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
{"question_id": "3f57965b-0b0a-433e-8347-d5be1eff9dd4", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for c1? Write None if there are none", "answer": "cargo c10 is present at location l0 and vehicle v0 contains cargo c1", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, what are the valid non-negated properties for c1, or state None if none exist?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "17a174f1-6029-4a41-91ca-a8de0a607de8", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state that involve negations for c7? Write None if there are none", "answer": "cargo c7 is not located in vehicle v0 and cargo c7 is not situated at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0, resulting in the current state. In this state, what are the valid properties that involve negations for c7, or state None if none exist?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, c2, and c3 are all situated at l0. Additionally, cargo c4, c5, c6, c7, c8, and c9 are all located at l1. Fuel f7 is present at l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "eaa56afb-bc8c-420e-9a08-66a5adcdbec9", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for f5? Write None if there are none", "answer": "fuel f5 does not exist in location l0, fuel f5 exists in location l1, fuel level f1 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f5, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f5, fuel-levels f0 and f5 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f6 are neighbors and fuel-levels f7 and f5 are not neighbors", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for f5? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "0d4b2e12-d2a0-4cdb-8404-94487fd289a3", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for s0? Write None if there are none", "answer": "space s1 does not neighbour space s0, spaces s0 and s1 are neighbors and vehicle v0 has space s0", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, vehicle v0 moves from location l0, now with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which again has spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1. Finally, at location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for s0? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "4c56bdcc-5e29-410e-a786-a430f8b69974", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for c2? Write None if there are none", "answer": "cargo c2 is not located in vehicle v0, cargo c2 is not situated at location l1 and cargo c2 is situated at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0, resulting in the current state. In this state, what are the valid properties (including both affirmative and negated properties) that apply to c2? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, c2, and c3 are all situated at l0. Additionally, cargo c4, c5, c6, c7, c8, and c9 are all located at l1. Fuel f7 is present at l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 is a neighbor of f1, and f3 is a neighbor of f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "d2da3e31-7210-438e-9376-f01c5ed0bc5e", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_10", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for f5? Write None if there are none", "answer": "fuel f5 does not exist in location l1, fuel level f2 does not neighbour fuel level f5, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel-levels f0 and f5 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors and location l0 does not have fuel f5", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is present at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has fuel f3, space s1 neighbors space s2, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is present at location l1, vehicle v1 contains space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 begins at location l1 with fuel levels f3 and f2, then moves to location l0. Upon arrival at l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l0 to l1, which has fuel levels f4 and f3. At l1, cargo c0 is unloaded from vehicle v0, vacating spaces s0 and s1, and cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0. Additionally, cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then moves from l1, which has fuel levels f2 and f1, back to l0. At l0, cargo c10 is unloaded from vehicle v0, vacating spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1. Furthermore, cargo c4 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l0 to l1, which has fuel levels f3 and f2. At l1, cargo c3 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c4 is also unloaded from vehicle v0, vacating spaces s1 and s2. Cargo c5 is then loaded into vehicle v0, occupying spaces s2 and s1, and cargo c9 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l1, which has fuel levels f1 and f0, back to l0. At l0, cargo c5 is unloaded from vehicle v0, vacating spaces s0 and s1, and cargo c6 is loaded into vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for f5? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is situated at l0, cargo c4 is situated at l0, cargo c5 is located at l1, cargo c6 is situated at l0, cargo c7 is found at l0, cargo c8 is found at l1, cargo c9 is situated at l1, fuel level f0 is adjacent to fuel level f1, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel levels f1 and f2 are adjacent, locations l0 and l1 are linked, location l0 has a fuel level of f4, location l1 has a fuel level of f3, space s1 is adjacent to space s2, spaces s0 and s1 are adjacent, a connection exists between locations l1 and l0, vehicle v0 occupies space s2, vehicle v0 is currently at location l1, vehicle v1 contains space s2 and vehicle v1 is currently at location l1."}
{"question_id": "e639ace7-116a-445b-b3d8-33d0ebce32c7", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for l1? Write None if there are none", "answer": "cargo c0 is not situated at location l1, cargo c1 is not situated at location l1, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not situated at location l1, cargo c5 is not situated at location l1, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c7 is situated at location l1, cargo c8 is not at location l1cargo c8 is not present at location l1, fuel f0 does not exist in location l1, fuel f5 does not exist in location l1, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f4, location l1 does not have fuel f1, location l1 does not have fuel f6, location l1 does not have fuel f7, location l1 has fuel f2, vehicle v0 is not at location l1 and vehicle v1 is situated at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing spaces s0 and s1. Next, vehicle v0 travels from location l0, with fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0. This is followed by vehicle v0 moving to location l0, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. The sequence continues with vehicle v0 relocating from location l0, with fuel levels f3 and f2, to location l1, where cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves to location l0, which has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0, freeing spaces s0 and s1. Vehicle v0 then travels from location l0, with fuel levels f2 and f1, to location l1, where cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves to location l0, which has fuel levels f4 and f3, and cargo c4 is unloaded from vehicle v0, releasing spaces s0 and s1. Finally, vehicle v0 relocates from location l0, with fuel levels f1 and f0, to location l1, where cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, and then moves to location l0, which has fuel levels f3 and f2, to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for l1? Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is located at l1, cargo c5 is positioned at l0, cargo c6 is situated at l1, cargo c7 is located at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are linked, location l1 is linked to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
