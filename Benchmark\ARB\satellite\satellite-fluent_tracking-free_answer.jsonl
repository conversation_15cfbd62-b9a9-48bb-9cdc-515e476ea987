{"question_id": "aea1842a-0917-4cad-b68c-ad330a4d7c1c", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_14", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for satellite0? Write None if there are none", "answer": "groundstation2 is not where satellite0 is pointed, groundstation3 is not where satellite0 is pointed, satellite0 does not have instrument2 on board, satellite0 does not have instrument3 on board, satellite0 does not have power available, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards planet14, satellite0 is not aimed towards star11, satellite0 is not aimed towards star13, satellite0 is not aimed towards star6, satellite0 is not pointing to phenomenon15, satellite0 is not pointing to star0, satellite0 is not pointing to star12, satellite0 is not pointing to star16, satellite0 is not pointing to star8, satellite0 is pointing to star1, star10 is not where satellite0 is pointed and star7 is not where satellite0 is pointed", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 on satellite0 is turned on, satellite1 is directed towards star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, then satellite1 is redirected from star6 to planet14, an image of planet14 is captured using spectrograph1 with instrument3 on satellite1, satellite1 is then redirected from planet14 to star10, an image of star10 is captured using spectrograph1 with instrument3 on satellite1, and finally, satellite1 is redirected from star10 to star12, where an image of star12 is captured using spectrograph1 with instrument3 on satellite1, resulting in the current state. In this state, what are the valid properties (including both affirmative and negative properties) for satellite0? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "48ff6545-c2d0-4c88-aed8-bf05f0996eeb", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, what are the valid properties of the state that do not involve negations for spectrograph2? Write None if there are none", "answer": "spectrograph2 is compatible with instrument2 and spectrograph2 is compatible with instrument3", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, what are the valid properties of the state for spectrograph2 that do not include negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "d749c33a-8afa-4b31-8816-387e2fc4cdf2", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns from phenomenon10 to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, what are the valid properties of the state that involve negations for satellite0? Write None if there are none", "answer": "groundstation7 is not where satellite0 is pointed, phenomenon10 is not where satellite0 is pointed, phenomenon17 is not where satellite0 is pointed, planet11 is not where satellite0 is pointed, satellite0 does not carry instrument3 on board, satellite0 does not have power, satellite0 is not aimed towards groundstation0, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards planet13, satellite0 is not aimed towards planet14, satellite0 is not aimed towards star8, satellite0 is not aimed towards star9, satellite0 is not pointing to groundstation4, satellite0 is not pointing to phenomenon16, satellite0 is not pointing to planet12, satellite0 is not pointing to star1, satellite0 is not pointing to star6 and star15 is not where satellite0 is pointed", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 with instrument3 in image3, resulting in the current state. In this state, what are the valid properties of the state that involve negations for satellite0? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current pointing direction being groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "7b634135-c613-44a2-8022-3cf32bc2628b", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_10", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, what are the valid properties of the state that involve negations for image0? Write None if there are none", "answer": "image of groundstation0 does not exist in image0, image of groundstation3 does not exist in image0, image of groundstation7 does not exist in image0, image of phenomenon10 does not exist in image0, image of phenomenon16 does not exist in image0, image of planet11 does not exist in image0, image of planet12 does not exist in image0, image of planet13 does not exist in image0, image of star1 does not exist in image0, image0 is not compatible with instrument0, instrument2 does not support image0, there is no image of direction groundstation2 in image0, there is no image of direction groundstation4 in image0, there is no image of direction groundstation5 in image0, there is no image of direction phenomenon17 in image0, there is no image of direction planet14 in image0, there is no image of direction star15 in image0, there is no image of direction star6 in image0, there is no image of direction star8 in image0 and there is no image of direction star9 in image0", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to achieve the current state. In this state, what are the valid properties of the state that involve negations for image0? Write None if there are none", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current direction towards groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "d8425b12-51cb-4196-9f7d-c65e572b350a", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns to planet11 from groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns to planet13 from planet11, satellite0's instrument1 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, what are the valid properties of the state that involve negations for spectrograph2? Write None if there are none", "answer": "image of groundstation0 does not exist in spectrograph2, image of groundstation2 does not exist in spectrograph2, image of groundstation6 does not exist in spectrograph2, image of groundstation8 does not exist in spectrograph2, image of planet11 does not exist in spectrograph2, image of star10 does not exist in spectrograph2, image of star16 does not exist in spectrograph2, image of star3 does not exist in spectrograph2, image of star4 does not exist in spectrograph2, instrument0 does not support spectrograph2, spectrograph2 is not supported by instrument2, spectrograph2 is not supported by instrument3, spectrograph2 is not supported by instrument4, there is no image of direction groundstation1 in spectrograph2, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation7 in spectrograph2, there is no image of direction groundstation9 in spectrograph2, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon15 in spectrograph2 and there is no image of direction star12 in spectrograph2", "plan_length": 10, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, another image of planet11 is taken using instrument1 on satellite0 in image6, satellite0 then reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 in image5, and another image of planet13 is taken using instrument1 on satellite0 in spectrograph2, finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, what are the valid properties of the state that involve negations for spectrograph2? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board satellite0."}
{"question_id": "5aaa0461-87ba-4ce8-a7b3-ab863f19a147", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, instrument3 that is on satellite1 is calibrated to star6, from star6, satellite1 turns to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, image of star10 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star10 to star12, image of star12 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns to star0 from star12, from star1, satellite0 turns to groundstation2, calibration of instrument0 which is on satellite0 to groundstation2 is complete, from groundstation2, satellite0 turns to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, satellite0 turns to star11 from phenomenon15, image of star11 is taken with instrument0 on satellite0 in thermograph4, satellite0 turns to star13 from star11 and instrument0 which is on satellite0 takes an image of star13 in spectrograph0 to reach the current state. In this state, what are the valid properties of the state that involve negations for groundstation4? Write None if there are none", "answer": "image of groundstation4 does not exist in infrared3, image of groundstation4 does not exist in spectrograph0, instrument3 is not calibrated for groundstation4, satellite0 is not aimed towards groundstation4, satellite1 is not aimed towards groundstation4, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph2 and there is no image of direction groundstation4 in thermograph4", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 rotates from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 rotates from star6 to planet14, an image of planet14 is captured by instrument3 on satellite1 using spectrograph1, satellite1 then rotates from planet14 to star10, an image of star10 is taken by instrument3 on satellite1 using spectrograph1, followed by a rotation from star10 to star12, where an image of star12 is captured by instrument3 on satellite1 using spectrograph1, and finally satellite1 rotates from star12 to star0, meanwhile, satellite0 rotates from star1 to groundstation2, completing the calibration of instrument0 on satellite0 to groundstation2, then satellite0 rotates from groundstation2 to phenomenon15, capturing an image of phenomenon15 with instrument0 on satellite0 using spectrograph0, satellite0 then rotates from phenomenon15 to star11, where an image of star11 is taken by instrument0 on satellite0 using thermograph4, and lastly, satellite0 rotates from star11 to star13, capturing an image of star13 with instrument0 on satellite0 using spectrograph0, resulting in the current state. In this state, what are the valid properties of the state that involve negations for groundstation4? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "8469ab37-b480-4c31-b3f7-c2385dc4f506", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, satellite0's instrument1 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, from planet13, satellite0 turns to star10, instrument1 which is on satellite0 takes an image of star10 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, instrument1 on satellite0 is switched off, on satellite0, instrument2 is switched on, satellite0 turns from star10 to star4, instrument2 is calibrated on satellite0 to star4, from star4, satellite0 turns to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for satellite0? Write None if there are none", "answer": "groundstation5 is not where satellite0 is pointed, groundstation8 is not where satellite0 is pointed, instrument1 is on board satellite0, phenomenon14 is not where satellite0 is pointed, phenomenon15 is not where satellite0 is pointed, power is available for satellite0, satellite0 does not carry instrument4 on board, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has instrument3 on board, satellite0 is not aimed towards groundstation1, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation7, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation6, satellite0 is not pointing to groundstation9, satellite0 is not pointing to planet11, satellite0 is not pointing to planet13, satellite0 is not pointing to star10, satellite0 is not pointing to star12, satellite0 is not pointing to star3, satellite0 is not pointing to star4 and satellite0 is pointing to star16", "plan_length": 19, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, captures an image of planet11 in image5 and image6 using instrument1, reorients from planet11 to planet13, captures an image of planet13 in image5 and spectrograph2 using instrument1, reorients from planet13 to star10, captures an image of star10 in image6 and spectrograph2 using instrument1, and deactivates instrument1. Subsequently, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 is calibrated to star4, reorients from star4 to star16, captures an image of star16 in image0 using instrument2, and deactivates instrument2, ultimately reaching the current state. In this state, what are the valid properties of the state (both with and without negations) for satellite0? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument3 has been completed at groundstation9. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, which is also supported by instrument1. Instrument1 has been calibrated for groundstation0 and supports spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3. Instrument4 is on board satellite1 and supports image1. Satellite0 has power available and carries instrument3 and instrument0 on board, and is aimed towards groundstation2. Satellite1 has power available and is aimed towards planet13, and also has instrument1 on board satellite0."}
{"question_id": "e22646ae-e958-482b-99f1-57e5e5a2d8ae", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and satellite0 turns from planet13 to star10 to reach the current state. In this state, what are the valid properties of the state that involve negations for groundstation0? Write None if there are none", "answer": "calibration of instrument2 for groundstation0 is incomplete, for groundstation0, instrument0 is not calibrated, for groundstation0, instrument3 is not calibrated, for groundstation0, instrument4 is not calibrated, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in image6, image of groundstation0 does not exist in thermograph3, satellite0 is not pointing to groundstation0, satellite1 is not aimed towards groundstation0, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation0 in spectrograph2 and there is no image of direction groundstation0 in spectrograph4", "plan_length": 10, "initial_state_nl": "Calibration of instrument3 for groundstation9 is complete, for groundstation6, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, infrared7 is supported by instrument4, instrument0 is calibrated for star3, instrument0 supports image6, instrument1 is calibrated for groundstation0, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument2 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is on board satellite1, instrument4 supports image1, power is available for satellite0, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation2, satellite1 has power available and satellite1 is aimed towards planet13.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 rotates from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 rotates from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5 and another in image6, next satellite0 rotates from planet11 to planet13, an image of planet13 is captured with instrument1 on satellite0 in image5, and instrument1 on satellite0 captures an image of planet13 in spectrograph2, finally satellite0 rotates from planet13 to star10, reaching the current state. In this state, what are the valid properties of the state that involve negations for groundstation0? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument3 at groundstation9 has been finalized. At groundstation6, instrument1 has been calibrated, while instrument0 has been calibrated at groundstation7, and instrument4 at groundstation8. Instrument2 supports image0 and is compatible with image1, which is also supported by instrument3. Instrument1 supports image5, and instrument4 supports infrared7. Instrument0 has been calibrated for star3 and supports image6, while instrument1 has been calibrated for groundstation0 and supports both image6 and spectrograph2. Instrument2 has been calibrated for star4 and is on board satellite0. Instrument3 supports spectrograph4 and thermograph3, and instrument4, which is on board satellite1, supports image1. Satellite0 has power available and carries instrument3 on board, in addition to having instrument0 and instrument1 on board, with its aim directed towards groundstation2. Satellite1 has power available and is aimed towards planet13."}
{"question_id": "d89c2bdd-3a82-41f6-9ebb-fd2d3a853f15", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, image of star10 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns to star12 from star10 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for star8? Write None if there are none", "answer": "for star8, instrument1 is calibrated", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, instrument3 on satellite1 captures an image of planet14 using spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, and finally, satellite1 reorients from star10 to star12, where instrument3 on satellite1 captures an image of star12 in spectrograph1, resulting in the current state. In this state, what are the valid properties that do not involve negations for star8? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3, spectrograph0, and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 and spectrograph1 are compatible with instrument1. Satellite0 is currently pointed towards star1, and thermograph4 is compatible with instrument0."}
{"question_id": "bf8dc9c2-1ecd-49d7-b4ed-010da81bd4ae", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, from star6, satellite1 turns to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12, image of star12 is taken with instrument3 on satellite1 in spectrograph1, from star12, satellite1 turns to star0, satellite0 turns to groundstation2 from star1, calibration of instrument0 which is on satellite0 to groundstation2 is complete, satellite0 turns from groundstation2 to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, from phenomenon15, satellite0 turns to star11, image of star11 is taken with instrument0 on satellite0 in thermograph4, from star11, satellite0 turns to star13 and image of star13 is taken with instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, what are the valid properties of the state that involve negations for star10? Write None if there are none", "answer": "calibration of instrument0 for star10 is incomplete, calibration of instrument2 for star10 is incomplete, for star10, instrument1 is not calibrated, for star10, instrument3 is not calibrated, image of star10 does not exist in thermograph4, satellite1 is not pointing to star10, star10 is not where satellite0 is pointed, there is no image of direction star10 in infrared3, there is no image of direction star10 in spectrograph0 and there is no image of direction star10 in spectrograph2", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 rotates to face star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, then satellite1 rotates to planet14 from star6, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 then rotates from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, satellite1 rotates from star10 to star12, an image of star12 is captured using instrument3 on satellite1 in spectrograph1, then satellite1 rotates from star12 to star0, satellite0 rotates from star1 to groundstation2, the calibration of instrument0 on satellite0 to groundstation2 is completed, satellite0 then rotates from groundstation2 to phenomenon15, an image of phenomenon15 is captured using instrument0 on satellite0 in spectrograph0, satellite0 rotates from phenomenon15 to star11, an image of star11 is captured using instrument0 on satellite0 in thermograph4, then satellite0 rotates from star11 to star13 and an image of star13 is captured using instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, what are the valid properties of the state that involve negations for star10? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has also been calibrated for groundstation2. Instrument0 is currently on board satellite0 and supports spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3, spectrograph0, and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is currently aimed at groundstation4. Spectrograph0 and spectrograph1 are compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "b6d4dab4-292e-4429-ab53-98bf72747fb9", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, from phenomenon10, satellite1 turns to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1's instrument3 takes an image of planet11 in image3 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for groundstation7? Write None if there are none", "answer": "calibration of instrument2 for groundstation7 is complete", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, captures an image of phenomenon16 using instrument3 and stores it in image3, reorients from phenomenon16 to phenomenon17, captures an image of phenomenon17 using instrument3 and stores it in image3, and finally reorients from phenomenon17 to planet11, capturing an image of planet11 using instrument3 and storing it in image3, resulting in the current state. In this state, what are the valid properties that do not involve negations for groundstation7? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current direction towards groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "607e7c39-1523-4f8a-9af7-e5fa6c94d6a8", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, what are the valid properties of the state that involve negations for groundstation0? Write None if there are none", "answer": "calibration of instrument0 for groundstation0 is incomplete, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in image3, image of groundstation0 does not exist in infrared1, instrument2 is not calibrated for groundstation0, instrument3 is not calibrated for groundstation0, satellite0 is not pointing to groundstation0, satellite1 is not aimed towards groundstation0 and there is no image of direction groundstation0 in image0", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, what are the valid state properties involving negations related to groundstation0? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, with additional support from instrument1. Instrument0 is compatible with image3 and supports it. Infrared1 is compatible with instrument0, which in turn supports image3. Instrument1 is installed on satellite0, supports infrared1, and instrument2 is calibrated for groundstation7. Instrument3 is calibrated for groundstation5 and star6, and supports image2. Satellite0 has instrument0 and instrument2 on board, has available power, and is currently pointing towards groundstation3. Satellite1 carries instrument3 on board, has power available, and is pointing towards phenomenon10."}
{"question_id": "a6b6342f-ff65-4ec5-a8ef-45940a143843", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_10", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, what are the valid properties of the state that involve negations for satellite0? Write None if there are none", "answer": "groundstation3 is not where satellite0 is pointed, groundstation4 is not where satellite0 is pointed, groundstation9 is not where satellite0 is pointed, satellite0 does not carry instrument2 on board, satellite0 does not carry instrument3 on board, satellite0 is not aimed towards phenomenon15, satellite0 is not aimed towards planet14, satellite0 is not aimed towards star0, satellite0 is not aimed towards star11, satellite0 is not aimed towards star12, satellite0 is not aimed towards star6, satellite0 is not aimed towards star8, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation5, satellite0 is not pointing to star13, satellite0 is not pointing to star16, star10 is not where satellite0 is pointed and star7 is not where satellite0 is pointed", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, what are the valid properties of the state that involve negations for satellite0? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "d45731ef-81b6-48c0-b35f-98ea4b7c02b4", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_14", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns from phenomenon10 to groundstation5, instrument3 that is on satellite1 is calibrated to groundstation5, satellite1 turns to phenomenon16 from groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, from phenomenon17, satellite1 turns to planet11 and satellite1's instrument3 takes an image of planet11 in image3 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for instrument0? Write None if there are none", "answer": "calibration of instrument0 for groundstation4 is incomplete, calibration of instrument0 for groundstation7 is incomplete, calibration of instrument0 for planet13 is incomplete, calibration of instrument0 for planet14 is incomplete, calibration of instrument0 for star15 is incomplete, calibration of instrument0 for star8 is incomplete, calibration of instrument0 for star9 is complete, for groundstation0, instrument0 is not calibrated, for groundstation3, instrument0 is not calibrated, for groundstation5, instrument0 is not calibrated, for phenomenon10, instrument0 is not calibrated, for phenomenon17, instrument0 is not calibrated, for planet12, instrument0 is not calibrated, for star1, instrument0 is calibrated, image3 is compatible with instrument0, infrared1 is compatible with instrument0, instrument0 does not support image0, instrument0 does not support image2, instrument0 is not calibrated, instrument0 is not calibrated for groundstation2, instrument0 is not calibrated for phenomenon16, instrument0 is not calibrated for planet11, instrument0 is not calibrated for star6, instrument0 is not on board satellite1, instrument0 is turned on and satellite0 carries instrument0 on board", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured using instrument3 on satellite1 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, where instrument3 on satellite1 takes an image of planet11 in image3, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for instrument0? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star9 has been finalized, instrument2's calibration for groundstation5 is also complete, and instrument2's calibration for star9 is complete as well. For groundstation0, the calibration of instrument1 is confirmed, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, while also being supported by instrument1. Instrument0 is compatible with infrared1 and supports image3. Instrument1, which is on board satellite0, supports infrared1. Instrument2 is calibrated for groundstation7, and instrument3 is calibrated for both groundstation5 and star6, while also supporting image2. Satellite0 has instrument0 and instrument2 on board, and it has power available, with its current direction towards groundstation3. Satellite1 carries instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "5ae48786-febb-4c64-a816-cfcb0aaff8a5", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_11", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from planet14 to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12, instrument3 which is on satellite1 takes an image of star12 in spectrograph1, satellite1 turns to star0 from star12, satellite0 turns from star1 to groundstation2, instrument0 that is on satellite0 is calibrated to groundstation2, satellite0 turns to phenomenon15 from groundstation2, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, satellite0 turns to star11 from phenomenon15, instrument0 which is on satellite0 takes an image of star11 in thermograph4, satellite0 turns from star11 to star13 and satellite0's instrument0 takes an image of star13 in spectrograph0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for planet14? Write None if there are none", "answer": "image of planet14 does not exist in infrared3, image of planet14 does not exist in spectrograph0, image of planet14 does not exist in thermograph4, instrument0 is not calibrated for planet14, instrument1 is not calibrated for planet14, instrument2 is not calibrated for planet14, instrument3 is not calibrated for planet14, satellite0 is not pointing to planet14, satellite1 is not aimed towards planet14, there is an image of planet14 in spectrograph1 and there is no image of direction planet14 in spectrograph2", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 on satellite0 is turned on, satellite1 is directed towards star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, then satellite1 is redirected to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 is then redirected to star10, an image of star10 is taken by instrument3 on satellite1 in spectrograph1, satellite1 is then redirected to star12, an image of star12 is captured by instrument3 on satellite1 in spectrograph1, satellite1 then turns to star0 from star12, satellite0 is redirected from star1 to groundstation2, instrument0 on satellite0 is calibrated to groundstation2, satellite0 is then redirected to phenomenon15, an image of phenomenon15 is captured using instrument0 on satellite0 in spectrograph0, satellite0 is then redirected to star11, an image of star11 is taken by instrument0 on satellite0 in thermograph4, and finally, satellite0 is redirected to star13 and an image of star13 is captured by instrument0 on satellite0 in spectrograph0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for planet14? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board and has power available. Satellite1 has instrument2 and instrument3 on board, has power, and is aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "5b9e26c7-0e06-4e00-a640-7e6d2a75ceb3", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, what are the valid properties of the state that do not involve negations for instrument1? Write None if there are none", "answer": "calibration of instrument1 for groundstation2 is complete, for groundstation4, instrument1 is calibrated, instrument1 is calibrated for star8, instrument1 is on board satellite0, instrument1 supports spectrograph0 and instrument1 supports spectrograph1", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is on board satellite0, instrument0 supports spectrograph0, instrument1 is calibrated for groundstation2, instrument2 is calibrated for groundstation4, instrument2 supports infrared3, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument3 supports spectrograph1, instrument3 supports spectrograph2, satellite0 has instrument1 on board, satellite0 has power available, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 has power, satellite1 is aimed towards groundstation4, spectrograph0 is compatible with instrument1, spectrograph1 is compatible with instrument1, star1 is where satellite0 is pointed and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, what are the valid properties that do not involve negations for instrument1, or state None if there are no such properties.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star0 has been completed. Similarly, instrument1 has been calibrated for groundstation4. Groundstation4 has instrument0 calibrated, while groundstation9 has instrument2 calibrated. Star6 has instrument3 calibrated, star7 has instrument2 calibrated, and star8 has instrument1 calibrated. Instrument0 has been calibrated for groundstation2, and it is also on board satellite0. Instrument0 is compatible with spectrograph0. Additionally, instrument1 has been calibrated for groundstation2, and instrument2 has been calibrated for groundstation4. Instrument2 supports infrared3 and is compatible with spectrograph0 and spectrograph2. Instrument3 supports spectrograph1 and spectrograph2. Satellite0 has instrument1 on board, and it has power available. Satellite1 has instrument2 and instrument3 on board, and it has power. Satellite1 is currently aimed at groundstation4. Spectrograph0 is compatible with instrument1, and spectrograph1 is also compatible with instrument1. Satellite0 is currently pointed at star1, and thermograph4 is compatible with instrument0."}
{"question_id": "8af9aa3c-206a-4123-9a2d-bb3043fc9f71", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, satellite1 turns from phenomenon10 to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, from groundstation5, satellite1 turns to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, from phenomenon17, satellite1 turns to planet11 and satellite1's instrument3 takes an image of planet11 in image3 to reach the current state. In this state, what are the valid properties of the state that involve negations for planet11? Write None if there are none", "answer": "calibration of instrument0 for planet11 is incomplete, for planet11, instrument1 is not calibrated, for planet11, instrument2 is not calibrated, for planet11, instrument3 is not calibrated, image of planet11 does not exist in image0, image of planet11 does not exist in infrared1, planet11 is not where satellite0 is pointed and there is no image of direction planet11 in image2", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for star9 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is supported by instrument2, image3 is compatible with instrument2, image3 is compatible with instrument3, image3 is supported by instrument1, infrared1 is compatible with instrument0, instrument0 supports image3, instrument1 is on board satellite0, instrument1 supports infrared1, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 supports image2, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite0 is pointing to groundstation3, satellite1 carries instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is switched on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured with instrument3 on satellite1 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, where instrument3 on satellite1 takes an image of planet11 in image3, resulting in the current state. In this state, what are the valid properties of the state that involve negations for planet11? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with star9 has been finalized, instrument2's calibration for groundstation5 is complete, and instrument2's calibration for star9 is also complete. For groundstation0, the calibration of instrument1 is done, instrument0 is calibrated for star1, and instrument3 is calibrated for star8. Image0 is found to be compatible with instrument3 and is supported by instrument1. Instrument2 supports image2, and image3 is compatible with both instrument2 and instrument3, with instrument1 providing support as well. Instrument0 is compatible with infrared1 and supports image3. Instrument1 is installed on satellite0 and supports infrared1. Instrument2 is calibrated for groundstation7, while instrument3 is calibrated for groundstation5 and star6, and it supports image2. Satellite0 has instrument0 and instrument2 on board, with power available and is currently pointing towards groundstation3. Satellite1 carries instrument3 and has power, with its current direction towards phenomenon10."}
