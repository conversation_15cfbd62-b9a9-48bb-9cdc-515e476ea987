{"id": 6194891727821342417, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p2 and t0 are at l0-1, a0 is at l0-0, p3 and p1 are at l2-0, p4 and t1 are at l1-0, t2 is at l2-1, p0 is in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p3 is at l2-1 and p3 is at l0-1. B. t1 is at l1-1. C. l0-2 is in p4. D. t1 is at l1-0 and t0 is at l2-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is at l2-1 and p3 is at l0-1", "t1 is at l1-1", "l0-2 is in p4", "t1 is at l1-0 and t0 is at l2-2"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4111531860873872566, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-6, l1-1, l1-4, l1-2, l1-7, l1-8, l1-0, l1-3, and l1-5 are in c1; l2-1, l2-3, l2-9, l2-0, l2-4, l2-6, l2-2, l2-5, l2-7, and l2-8 are in c2; l4-6, l4-5, l4-4, l4-0, l4-1, l4-3, l4-2, l4-8, l4-9, and l4-7 are in c4; l3-8, l3-0, l3-4, l3-1, l3-7, l3-5, l3-2, l3-9, l3-3, and l3-6 are in c3; l0-9, l0-3, l0-8, l0-5, l0-4, l0-0, l0-7, l0-6, l0-2, and l0-1 are in c0. Currently, p2 and t4 are at l4-8, t1, p0, and a0 are at l1-0, p1 and t2 are at l2-7, t3 is at l3-0, t0 is at l0-2, p3 is in t1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. l2-8 is in t4 and t4 is at l4-8. B. l4-5 is at l0-9. C. p1 is at l2-5 and p1 is at l4-0. D. t1 is at l1-8 and t2 is at l2-5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["l2-8 is in t4 and t4 is at l4-8", "l4-5 is at l0-9", "p1 is at l2-5 and p1 is at l4-0", "t1 is at l1-8 and t2 is at l2-5"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 5280329106889876102, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p4, t1, p0, and a0 are at l1-0, t2 is at l2-1, t0 is at l0-2, p1 and p3 are in a0, p2 is in t0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. t1 is at l1-2. B. t0 is in l0-1. C. t1 is at l1-0 and t0 is in l2-1. D. p0 is in t1 and p0 is in t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["t1 is at l1-2", "t0 is in l0-1", "t1 is at l1-0 and t0 is in l2-1", "p0 is in t1 and p0 is in t0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -3326286317526361133, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, a0 is at l0-0, p1 and t1 are at l1-1, t0 is at l0-1, p3 is in a0, p0 and p2 are in t0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. t1 is in t1. B. p2 is in l1-1. C. p0 is at l0-0 and p0 is in t1. D. p2 is at l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["t1 is in t1", "p2 is in l1-1", "p0 is at l0-0 and p0 is in t1", "p2 is at l0-1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 788178444313684963, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p1, p3, and p0 are at l1-1, t1 and a0 are at l1-0, p2 and t0 are at l0-0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p2 is in t0. B. l1-1 is at p3. C. c1 is at t0. D. p1 is at l0-1 and p1 is in a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is in t0", "l1-1 is at p3", "c1 is at t0", "p1 is at l0-1 and p1 is in a0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 6283080294290357241, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p2, p0, and t0 are at l0-1, a0 is at l0-0, t1 is at l1-0, p3 is in a0, p1 is in t1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p1 is in p1. B. p0 is in t0 and p0 is in a0. C. t1 is at l1-1 and a0 is at l1-0. D. t1 is at l1-0 and t0 is at p2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is in p1", "p0 is in t0 and p0 is in a0", "t1 is at l1-1 and a0 is at l1-0", "t1 is at l1-0 and t0 is at p2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 2421713025018886959, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l4-1, l4-2, and l4-0 are in c4; l2-2, l2-0, and l2-1 are in c2; l0-1, l0-0, and l0-2 are in c0; l3-0, l3-2, and l3-1 are in c3. Currently, t1 is at l1-2, p1 is at l3-1, t4 and a0 are at l4-0, t3 is at l3-0, t0 is at l0-2, t2 is at l2-0, p2 is in t2, p0 and p3 are in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. c2 is at c4 and t1 is at l1-2. B. p0 is at l3-2 and p0 is in a0. C. t1 is at l1-1 and t3 is at l3-1. D. l2-1 is at p1 and t0 is at l0-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["c2 is at c4 and t1 is at l1-2", "p0 is at l3-2 and p0 is in a0", "t1 is at l1-1 and t3 is at l3-1", "l2-1 is at p1 and t0 is at l0-2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3576794639122596064, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p2 and t0 are at l0-1, a0 is at l0-0, t1 is at l1-0, p3 is in a0, p0 is in t0, p1 is in t1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p3 is in a0 and p3 is in t0. B. p0 is at l0-1. C. t1 is at l1-0 and l1-0 is in c1. D. c0 is at a0 and p1 is in t1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is in a0 and p3 is in t0", "p0 is at l0-1", "t1 is at l1-0 and l1-0 is in c1", "c0 is at a0 and p1 is in t1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2323768416669865391, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l4-1, l4-2, and l4-0 are in c4; l2-2, l2-0, and l2-1 are in c2; l0-1, l0-0, and l0-2 are in c0; l3-0, l3-2, and l3-1 are in c3. Currently, p2 and t2 are at l2-1, p3 is at l4-1, t4 is at l4-0, t1 and a0 are at l1-0, t3 is at l3-0, t0 is at l0-2, p1 is in t3, p0 is in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. l2-2 is at l1-0. B. l2-0 is at p2 and a0 is at l1-0. C. p0 is in t1 and p0 is in t2. D. a0 is at l0-0 and t0 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["l2-2 is at l1-0", "l2-0 is at p2 and a0 is at l1-0", "p0 is in t1 and p0 is in t2", "a0 is at l0-0 and t0 is at l0-0"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 8659425604451728577, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t1 and p0 are at l1-2, p1 and a0 are at l1-0, t2 is at l2-1, p4 and t0 are at l0-0, p2 and p3 are in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p4 is in t2 and p4 is in t1. B. c2 is at p3 and a0 is at l1-0. C. l1-0 is in l0-2 and t1 is at l1-2. D. p2 is at l1-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p4 is in t2 and p4 is in t1", "c2 is at p3 and a0 is at l1-0", "l1-0 is in l0-2 and t1 is at l1-2", "p2 is at l1-0"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 1871150045240731895, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, t1 is at l1-2, p1 is at l1-1, a0 is at l1-0, t0 is at l0-0, p2, p0, and p3 are in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p0 is in a0 and l1-0 is in a0. B. p0 is at l0-2 and p0 is at l1-1. C. l0-1 is in l0-0 and t1 is at l1-2. D. t1 is at l1-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is in a0 and l1-0 is in a0", "p0 is at l0-2 and p0 is at l1-1", "l0-1 is in l0-0 and t1 is at l1-2", "t1 is at l1-1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -7265145608264892452, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p0 and t2 are at l2-1, t1 and a0 are at l1-0, t0 is at l0-1, p2 and p1 are in a0, p3 is in t2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. t1 is at l1-0 and l1-0 is in l2-1. B. c1 is at c0 and p0 is at l2-1. C. t2 is at l2-2. D. a0 is at l2-0 and a0 is at l1-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["t1 is at l1-0 and l1-0 is in l2-1", "c1 is at c0 and p0 is at l2-1", "t2 is at l2-2", "a0 is at l2-0 and a0 is at l1-0"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -8807401709270363052, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, a0 and p2 are at l0-0, t1 is at l1-1, t0 and p1 are at l0-1, p3 is in a0, p0 is in t0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. l0-0 is in p3. B. p3 is at l0-0 and p1 is in t0. C. p2 is at l1-0 and p2 is at l0-0. D. p1 is at c0 and p2 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["l0-0 is in p3", "p3 is at l0-0 and p1 is in t0", "p2 is at l1-0 and p2 is at l0-0", "p1 is at c0 and p2 is at l0-0"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 6424759326286407461, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p0 is at l2-1, p2, p3, a0, t2, and p1 are at l2-0, t1 is at l1-0, t0 is at l0-1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. l2-0 is at l2-0. B. p0 is in t0 and p0 is in t2. C. p3 is in t2 and t0 is at l0-2. D. t0 is at p1 and p3 is at l2-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["l2-0 is at l2-0", "p0 is in t0 and p0 is in t2", "p3 is in t2 and t0 is at l0-2", "t0 is at p1 and p3 is at l2-0"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3035955752785235591, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p0, p1, and t0 are at l0-1, t1, p2, and a0 are at l1-0, p3 is at l0-0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p1 is in t0 and p0 is in t0. B. l0-1 is in c1. C. t0 is at a0. D. p2 is at l1-0 and p2 is in t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is in t0 and p0 is in t0", "l0-1 is in c1", "t0 is at a0", "p2 is at l1-0 and p2 is in t0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
