{"id": -7680773247890956307, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball7, and right gripper is carrying the ball ball6. Additionally, ball1, ball2, ball4, and ball3 are at room1, ball5 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ball left1 is in room room2. B. Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free. C. Robot robot1 is at room1 location. D. Ball right1 is in room room1 and Robot robot1 is carrying the ball ball7 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball left1 is in room room2", "Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free", "Robot robot1 is at room1 location", "Ball right1 is in room room1 and Robot robot1 is carrying the ball ball7 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 76490776834059326, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball10. Additionally, ball1, ball3, ball13, and ball12 are at room2, ball2, ball6, ball9, ball5, ball11, ball4, and ball15 are at room1, ball8, ball7, and ball14 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free. B. Robot robot1 is in room room1. C. Robot robot1 is carrying the ball left1 in the left gripper and Ball ball2 is at room1 location. D. Robot robot1 is carrying the ball room2 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free", "Robot robot1 is in room room1", "Robot robot1 is carrying the ball left1 in the left gripper and Ball ball2 is at room1 location", "Robot robot1 is carrying the ball room2 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 9036893926056874407, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, right gripper is free, and left gripper is carrying the ball ball4. Additionally, ball3 is at room5, ball1 is at room1, ball2 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ball robot1 is at room5 location. B. Robot robot1 is carrying the ball ball3 in the right gripper and The left gripper of robot robot1 is free. C. Robot robot1 is carrying the ball ball4 in the right gripper and Ball ball4 is at room4 location. D. Ball room3 is at room3 location and The right gripper of robot robot1 is free.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball robot1 is at room5 location", "Robot robot1 is carrying the ball ball3 in the right gripper and The left gripper of robot robot1 is free", "Robot robot1 is carrying the ball ball4 in the right gripper and Ball ball4 is at room4 location", "Ball room3 is at room3 location and The right gripper of robot robot1 is free"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4511247375484983380, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball2, ball4, and ball3 are at room1, ball5, ball7, and ball1 are at room3, ball6 is at room2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball7 in the left gripper and Robot robot1 is at room2 location. B. Robot robot1 is in room room3 and Robot robot1 is at room1 location. C. Robot robot1 is carrying the ball room2 in the left gripper. D. Ball robot1 is in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball7 in the left gripper and Robot robot1 is at room2 location", "Robot robot1 is in room room3 and Robot robot1 is at room1 location", "Robot robot1 is carrying the ball room2 in the left gripper", "Ball robot1 is in room room3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 2588772264796788939, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball3. Additionally, ball15, ball13, ball12, and ball6 are at room2, ball2, ball9, ball8, ball14, ball10, ball11, and ball4 are at room1, ball5 and ball7 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is in room room3. B. Ball left1 is at room2 location. C. Ball ball2 is in room room2 and Robot robot1 is carrying the ball ball2 in the right gripper. D. Robot robot1 is carrying the ball robot1 in the left gripper and Ball ball8 is in room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is in room room3", "Ball left1 is at room2 location", "Ball ball2 is in room room2 and Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball robot1 in the left gripper and Ball ball8 is in room room1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -6458358088406060827, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball3 and ball1 are at room4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is at room4 location and The right gripper of robot robot1 is free. B. Ball ball1 is at room4 location and Ball robot1 is at room2 location. C. Robot robot1 is carrying the ball left1 in the right gripper. D. Robot robot1 is carrying the ball ball3 in the right gripper and Robot robot1 is carrying the ball ball1 in the right gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at room4 location and The right gripper of robot robot1 is free", "Ball ball1 is at room4 location and Ball robot1 is at room2 location", "Robot robot1 is carrying the ball left1 in the right gripper", "Robot robot1 is carrying the ball ball3 in the right gripper and Robot robot1 is carrying the ball ball1 in the right gripper"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 453115776915613540, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball3 is at room5, ball1 and ball2 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is in room room2 and Robot robot1 is in room room5. B. Ball robot1 is at room4 location. C. The right gripper of robot robot1 is free and Robot robot1 is carrying the ball ball2 in the left gripper. D. Ball robot1 is in room room3 and The left gripper of robot robot1 is free.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is in room room2 and Robot robot1 is in room room5", "Ball robot1 is at room4 location", "The right gripper of robot robot1 is free and Robot robot1 is carrying the ball ball2 in the left gripper", "Ball robot1 is in room room3 and The left gripper of robot robot1 is free"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3654626159221667911, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball3 is at room2, ball1 and ball4 are at room1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball robot1 in the right gripper. B. Robot robot1 is carrying the ball ball3 in the left gripper and Robot robot1 is in room room2. C. Ball ball1 is at room1 location and Robot robot1 is carrying the ball right1 in the left gripper. D. Ball ball4 is at room1 location and Robot robot1 is carrying the ball ball4 in the right gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball robot1 in the right gripper", "Robot robot1 is carrying the ball ball3 in the left gripper and Robot robot1 is in room room2", "Ball ball1 is at room1 location and Robot robot1 is carrying the ball right1 in the left gripper", "Ball ball4 is at room1 location and Robot robot1 is carrying the ball ball4 in the right gripper"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 5686286023996962349, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball5. Additionally, ball1, ball2, and ball4 are at room1, ball7 and ball6 are at room2, ball3 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball5 in the right gripper and Robot robot1 is carrying the ball ball2 in the right gripper. B. Robot robot1 is carrying the ball ball2 in the right gripper and Robot robot1 is in room room1. C. Robot robot1 is carrying the ball room1 in the right gripper. D. Ball right1 is in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball5 in the right gripper and Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball ball2 in the right gripper and Robot robot1 is in room room1", "Robot robot1 is carrying the ball room1 in the right gripper", "Ball right1 is in room room3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4679214027242730097, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball3 and ball1 are at room4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball room1 in the right gripper. B. Ball ball2 is in room room3 and Robot robot1 is in room room3. C. Robot robot1 is carrying the ball ball4 in the right gripper and The right gripper of robot robot1 is free. D. Robot robot1 is carrying the ball room3 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball room1 in the right gripper", "Ball ball2 is in room room3 and Robot robot1 is in room room3", "Robot robot1 is carrying the ball ball4 in the right gripper and The right gripper of robot robot1 is free", "Robot robot1 is carrying the ball room3 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4084225803706439743, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball1 and ball2 are at room2, ball3 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ball room2 is in room room1. B. Robot robot1 is carrying the ball ball3 in the right gripper and The right gripper of robot robot1 is free. C. Robot robot1 is in room room2. D. Ball robot1 is at room3 location and Ball ball3 is in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball room2 is in room room1", "Robot robot1 is carrying the ball ball3 in the right gripper and The right gripper of robot robot1 is free", "Robot robot1 is in room room2", "Ball robot1 is at room3 location and Ball ball3 is in room room3"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 4712623402002611588, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball3. Additionally, ball4 is at room1, ball1 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball3 in the right gripper and Robot robot1 is carrying the ball left1 in the right gripper. B. Robot robot1 is carrying the ball ball1 in the right gripper and The right gripper of robot robot1 is free. C. Ball ball1 is in room room3 and Ball right1 is in room room2. D. The left gripper of robot robot1 is free and Ball ball2 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball3 in the right gripper and Robot robot1 is carrying the ball left1 in the right gripper", "Robot robot1 is carrying the ball ball1 in the right gripper and The right gripper of robot robot1 is free", "Ball ball1 is in room room3 and Ball right1 is in room room2", "The left gripper of robot robot1 is free and Ball ball2 is at room3 location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -318977129426384211, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball2 is at room2, ball4 is at room6, ball1 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball1 in the left gripper and Ball ball1 is in room room5. B. Ball room4 is in room room2. C. Robot robot1 is in room room7. D. Ball room6 is in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball1 in the left gripper and Ball ball1 is in room room5", "Ball room4 is in room room2", "Robot robot1 is in room room7", "Ball room6 is in room room2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3223438131136234839, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room1, ball3 and ball1 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball robot1 in the left gripper and Ball ball3 is in room room3. B. Robot robot1 is in room room1. C. Robot robot1 is carrying the ball robot1 in the right gripper and Ball ball3 is in room room3. D. Ball ball3 is at room1 location and Ball ball3 is in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball robot1 in the left gripper and Ball ball3 is in room room3", "Robot robot1 is in room room1", "Robot robot1 is carrying the ball robot1 in the right gripper and Ball ball3 is in room room3", "Ball ball3 is at room1 location and Ball ball3 is in room room3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -5616401175555687182, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball3 is at room2, ball2 is at room1, ball1 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball2 in the left gripper. B. Ball ball2 is in room room1 and Ball room3 is in room room2. C. Robot robot1 is carrying the ball ball4 in the left gripper and Robot robot1 is carrying the ball ball4 in the right gripper. D. Robot robot1 is in room room1 and Robot robot1 is carrying the ball right1 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball2 in the left gripper", "Ball ball2 is in room room1 and Ball room3 is in room room2", "Robot robot1 is carrying the ball ball4 in the left gripper and Robot robot1 is carrying the ball ball4 in the right gripper", "Robot robot1 is in room room1 and Robot robot1 is carrying the ball right1 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
