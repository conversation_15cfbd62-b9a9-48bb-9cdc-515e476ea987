{"question_id": "648b4f05-e3d6-461b-9931-8f8f8a24ad67", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, spanner4 is picked up by bob from location2, from location2, bob picks up spanner3, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks from location4 to location5, bob walks from location5 to location6, spanner5 is picked up by bob from location6 and bob picks up spanner2 from location6 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location7 does not exist, a link between gate and location9 does not exist, a link between gate and shed does not exist, a link between location1 and gate does not exist, a link between location1 and location7 does not exist, a link between location1 and shed does not exist, a link between location2 and gate does not exist, a link between location2 and location4 does not exist, a link between location2 and location5 does not exist, a link between location2 and location9 does not exist, a link between location3 and gate does not exist, a link between location3 and location2 does not exist, a link between location3 and shed does not exist, a link between location4 and location1 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location4 does not exist, a link between location6 and location5 does not exist, a link between location7 and location2 does not exist, a link between location7 and location3 does not exist, a link between location7 and location5 does not exist, a link between location7 and location6 does not exist, a link between location8 and gate does not exist, a link between location8 and location4 does not exist, a link between location8 and shed does not exist, a link between location9 and location7 does not exist, a link between location9 and shed does not exist, a link between shed and gate does not exist, a link between shed and location5 does not exist, bob is not at location4, bob is not at location7, bob is not at shed, bob is not currently at location1, bob is not currently at location3, bob is not currently at location5, bob is not currently at location8, bob is not located at gate, bob is not located at location2, bob is not located at location9, gate and location1 are not linked, gate and location3 are not linked, gate and location4 are not linked, gate and location6 are not linked, gate is not linked to location2, gate is not linked to location5, gate is not linked to location8, location1 and location3 are not linked, location1 and location4 are not linked, location1 and location5 are not linked, location1 and location6 are not linked, location1 and location8 are not linked, location1 and location9 are not linked, location2 and location6 are not linked, location2 and location8 are not linked, location2 is not linked to location1, location2 is not linked to location7, location2 is not linked to shed, location3 and location6 are not linked, location3 and location8 are not linked, location3 and location9 are not linked, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location7, location4 and gate are not linked, location4 and location2 are not linked, location4 and location3 are not linked, location4 and location7 are not linked, location4 is not linked to location6, location4 is not linked to location8, location4 is not linked to shed, location5 and location3 are not linked, location5 and location7 are not linked, location5 is not linked to location2, location5 is not linked to location8, location5 is not linked to location9, location5 is not linked to shed, location6 and gate are not linked, location6 and location9 are not linked, location6 and shed are not linked, location6 is not linked to location1, location6 is not linked to location2, location6 is not linked to location3, location6 is not linked to location4, location6 is not linked to location8, location7 and location4 are not linked, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location9, location7 is not linked to shed, location8 and location1 are not linked, location8 and location5 are not linked, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location6, location8 is not linked to location7, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location3 are not linked, location9 is not linked to location4, location9 is not linked to location5, location9 is not linked to location6, location9 is not linked to location8, nut1 is not at location5, nut1 is not at location9, nut1 is not currently at location3, nut1 is not currently at location7, nut1 is not currently at location8, nut1 is not currently at shed, nut1 is not located at location1, nut1 is not located at location2, nut1 is not located at location4, nut1 is not located at location6, nut1 is not tightened, nut2 is not at location2, nut2 is not at location3, nut2 is not at location4, nut2 is not at location5, nut2 is not at location7, nut2 is not at location8, nut2 is not at shed, nut2 is not currently at location1, nut2 is not currently at location6, nut2 is not located at location9, nut3 is not at location1, nut3 is not at location3, nut3 is not at location4, nut3 is not at shed, nut3 is not located at location2, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location7, nut3 is not located at location8, nut3 is not located at location9, nut4 is not at location3, nut4 is not at location5, nut4 is not currently at location4, nut4 is not currently at location8, nut4 is not located at location1, nut4 is not located at location2, nut4 is not located at location6, nut4 is not located at location7, nut4 is not located at location9, nut4 is not located at shed, nut5 is not at location1, nut5 is not at location3, nut5 is not at location5, nut5 is not at location8, nut5 is not currently at location2, nut5 is not currently at location7, nut5 is not located at location4, nut5 is not located at location6, nut5 is not located at location9, nut5 is not located at shed, shed and location7 are not linked, shed is not linked to location2, shed is not linked to location3, shed is not linked to location4, shed is not linked to location6, shed is not linked to location8, shed is not linked to location9, spanner1 is not at location4, spanner1 is not at location6, spanner1 is not at shed, spanner1 is not carried by bob, spanner1 is not currently at location2, spanner1 is not currently at location3, spanner1 is not currently at location5, spanner1 is not located at gate, spanner1 is not located at location1, spanner1 is not located at location7, spanner1 is not located at location9, spanner2 is not at location6, spanner2 is not at location8, spanner2 is not currently at gate, spanner2 is not currently at location3, spanner2 is not currently at location5, spanner2 is not currently at location9, spanner2 is not currently at shed, spanner2 is not located at location1, spanner2 is not located at location2, spanner2 is not located at location4, spanner2 is not located at location7, spanner3 is not at gate, spanner3 is not at location2, spanner3 is not at location5, spanner3 is not at location7, spanner3 is not currently at location1, spanner3 is not currently at location4, spanner3 is not currently at location8, spanner3 is not located at location3, spanner3 is not located at location6, spanner3 is not located at location9, spanner3 is not located at shed, spanner4 is not at gate, spanner4 is not at location4, spanner4 is not at location8, spanner4 is not currently at location1, spanner4 is not currently at location2, spanner4 is not currently at location3, spanner4 is not currently at location5, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at location9, spanner4 is not located at shed, spanner5 is not at location7, spanner5 is not at shed, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not currently at location8, spanner5 is not located at gate, spanner5 is not located at location4, spanner5 is not located at location5, spanner5 is not located at location9, tightening of nut2 is incomplete, tightening of nut3 is incomplete, tightening of nut4 is incomplete and tightening of nut5 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, spanner1 is currently at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 can be used, spanner3 is currently at location2, spanner4 is at location2, spanner4 is usable, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He then proceeds to location3, followed by location4, then location5, and finally location6, where he picks up spanner5 and spanner2, resulting in the current state. In this state, list all valid properties that involve negations; if there are none, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, a connection between location8 and location9 exists, a connection between the shed and location1 is present, bob is situated at the shed, location1 is connected to location2, location2 is connected to location3, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 is connected to location8, location9 and the gate are connected, nut1 is situated at the gate, nut1 is not secure, nut2 is currently situated at the gate, nut2 is loose, nut3 is located at the gate, nut3 is loose, nut4 is located at the gate, nut4 is loose, nut5 is currently situated at the gate, nut5 is not secure, spanner1 is currently situated at location8, spanner1 is in working condition, spanner2 is at location6, spanner2 is in working order, spanner3 is in working condition, spanner3 is currently situated at location2, spanner4 is at location2, spanner4 is in working condition, spanner5 is in working order and spanner5 is located at location6."}
{"question_id": "bb62ffb6-1542-4dac-82e2-ea8e03b6f24b", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location2 does not exist, a link between location1 and location5 does not exist, a link between location1 and shed does not exist, a link between location2 and location1 does not exist, a link between location2 and location5 does not exist, a link between location2 and location7 does not exist, a link between location3 and gate does not exist, a link between location3 and location2 does not exist, a link between location3 and location7 does not exist, a link between location3 and location9 does not exist, a link between location3 and shed does not exist, a link between location4 and location3 does not exist, a link between location4 and location9 does not exist, a link between location4 and shed does not exist, a link between location5 and location3 does not exist, a link between location5 and location9 does not exist, a link between location5 and shed does not exist, a link between location6 and location1 does not exist, a link between location6 and location4 does not exist, a link between location6 and location5 does not exist, a link between location7 and location5 does not exist, a link between location8 and location1 does not exist, a link between location8 and location6 does not exist, a link between location8 and location7 does not exist, a link between location9 and location2 does not exist, a link between location9 and location4 does not exist, a link between location9 and location7 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, bob is not at location2, bob is not at location4, bob is not at location5, bob is not at location8, bob is not at shed, bob is not carrying spanner1, bob is not carrying spanner2, bob is not carrying spanner3, bob is not carrying spanner5, bob is not currently at location6, bob is not currently at location7, bob is not located at gate, bob is not located at location3, bob is not located at location9, gate and location1 are not linked, gate and location3 are not linked, gate and location7 are not linked, gate and location8 are not linked, gate and shed are not linked, gate is not linked to location4, gate is not linked to location5, gate is not linked to location6, gate is not linked to location9, location1 and location3 are not linked, location1 and location6 are not linked, location1 and location7 are not linked, location1 is not linked to gate, location1 is not linked to location4, location1 is not linked to location8, location1 is not linked to location9, location2 and gate are not linked, location2 and location9 are not linked, location2 and shed are not linked, location2 is not linked to location4, location2 is not linked to location6, location2 is not linked to location8, location3 and location6 are not linked, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location8, location4 and gate are not linked, location4 and location1 are not linked, location4 and location8 are not linked, location4 is not linked to location2, location4 is not linked to location6, location4 is not linked to location7, location5 and gate are not linked, location5 and location2 are not linked, location5 and location4 are not linked, location5 and location7 are not linked, location5 and location8 are not linked, location5 is not linked to location1, location6 and location9 are not linked, location6 and shed are not linked, location6 is not linked to gate, location6 is not linked to location2, location6 is not linked to location3, location6 is not linked to location8, location7 and location2 are not linked, location7 and location3 are not linked, location7 and location6 are not linked, location7 and shed are not linked, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location4, location7 is not linked to location9, location8 and gate are not linked, location8 and location4 are not linked, location8 and location5 are not linked, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to shed, location9 and location5 are not linked, location9 and location8 are not linked, location9 and shed are not linked, location9 is not linked to location1, location9 is not linked to location3, location9 is not linked to location6, nut1 is not at location6, nut1 is not at location7, nut1 is not at location8, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location5, nut1 is not currently at location9, nut1 is not currently at shed, nut1 is not located at location3, nut1 is not located at location4, nut2 is not at location1, nut2 is not at location2, nut2 is not at location6, nut2 is not at location7, nut2 is not currently at location4, nut2 is not currently at location8, nut2 is not located at location3, nut2 is not located at location5, nut2 is not located at location9, nut2 is not located at shed, nut3 is not at location7, nut3 is not at location8, nut3 is not at shed, nut3 is not currently at location1, nut3 is not currently at location2, nut3 is not currently at location4, nut3 is not located at location3, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location9, nut3 is not tightened, nut4 is not at location1, nut4 is not at location3, nut4 is not at location7, nut4 is not currently at location2, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not currently at shed, nut4 is not located at location4, nut4 is not located at location5, nut4 is not located at location8, nut4 is not tightened, nut5 is not at location1, nut5 is not at location5, nut5 is not at location7, nut5 is not at location8, nut5 is not currently at location6, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location2, nut5 is not located at location3, nut5 is not located at location4, shed and location5 are not linked, shed and location8 are not linked, shed and location9 are not linked, shed is not linked to gate, shed is not linked to location2, shed is not linked to location3, shed is not linked to location4, spanner1 is not at gate, spanner1 is not at location1, spanner1 is not at location4, spanner1 is not at shed, spanner1 is not currently at location5, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at location2, spanner1 is not located at location3, spanner1 is not located at location9, spanner2 is not at location3, spanner2 is not at location6, spanner2 is not at location9, spanner2 is not currently at location7, spanner2 is not currently at shed, spanner2 is not located at gate, spanner2 is not located at location1, spanner2 is not located at location2, spanner2 is not located at location4, spanner2 is not located at location5, spanner3 is not at location3, spanner3 is not at location5, spanner3 is not at location9, spanner3 is not currently at location2, spanner3 is not currently at location6, spanner3 is not currently at shed, spanner3 is not located at gate, spanner3 is not located at location1, spanner3 is not located at location4, spanner3 is not located at location7, spanner4 is not at location2, spanner4 is not at location3, spanner4 is not at location7, spanner4 is not carried by bob, spanner4 is not currently at gate, spanner4 is not currently at location9, spanner4 is not currently at shed, spanner4 is not located at location1, spanner4 is not located at location4, spanner4 is not located at location6, spanner4 is not located at location8, spanner5 is not at location1, spanner5 is not at location2, spanner5 is not currently at location3, spanner5 is not currently at location5, spanner5 is not currently at location8, spanner5 is not currently at location9, spanner5 is not located at gate, spanner5 is not located at location4, spanner5 is not located at location6, spanner5 is not located at shed, tightening of nut1 is incomplete, tightening of nut2 is incomplete and tightening of nut5 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: Bob moves from the shed to location1 to achieve the current state. In this state, identify all valid properties that include negations, or indicate None if there are no such properties.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a connection is present between the shed and location1. Bob is currently located at the shed. Location1 is connected to location2, and location2 is linked to location3. Furthermore, location3 is connected to location4, and location4 is linked to location5. Location7 is connected to location8, and location8 is linked to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is currently present and is loose. Similarly, nut2 is at the gate but is not secured, while nut3 is also at the gate and is loose. Nut4 is at the gate and is not secured, and nut5 is also at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and is situated at location8, while spanner3 is also usable and is located at location8. Spanner4 is at location5 and is functional, and spanner5 is also functional and is located at location7."}
{"question_id": "59b13332-5917-4950-8072-0c5a7361c756", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, from location1, bob picks up spanner4, bob walks from location1 to location2, bob walks from location2 to location3, from location3 to location4, bob walks, from location4, bob picks up spanner1, from location4 to location5, bob walks, bob walks to location6 from location5 and spanner3 is picked up by bob from location6 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location1 and location2 exists, a link between location2 and location3 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is carrying spanner3, bob is carrying spanner4, bob is located at location6, location3 is linked to location4, location4 and location5 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed and location1 are linked, spanner1 can be used, spanner1 is carried by bob, spanner2 can be used, spanner2 is at location7, spanner3 is usable, spanner4 is usable, spanner5 is carried by bob and spanner5 is usable", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, spanner1 is located at location4, spanner1 is usable, spanner2 is located at location7, spanner2 is usable, spanner3 can be used, spanner3 is located at location6, spanner4 is at location1, spanner4 is functional, spanner5 is currently at location1 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. Next, Bob walks to location2, then to location3, and subsequently to location4. From location4, Bob picks up spanner1 and proceeds to location5. He then walks to location6 from location5 and collects spanner3. To reach the current state, these actions are performed. Now, list all valid properties of the current state that do not involve negations. If there are none, state 'None'.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, another connection exists between location6 and location7, and a connection also exists between location7 and location8. Additionally, a link is present between the shed and location1. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is located at location4 and is in working condition, spanner2 is at location7 and is usable, spanner3 is located at location6 and can be used, spanner4 is at location1 and is functional, and spanner5 is currently at location1 and is usable."}
{"question_id": "66b45b26-7116-4c42-b9e6-8ba6880a98ae", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob walks from location2 to location3, bob walks from location3 to location4, bob walks to location5 from location4, spanner4 is picked up by bob from location5, from location5 to location6, bob walks, spanner1 is picked up by bob from location6, bob walks to location7 from location6, from location7, bob picks up spanner5, bob walks to location8 from location7, from location8, bob picks up spanner3, bob picks up spanner2 from location8, bob walks to location9 from location8, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location7 and location8 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is carrying spanner2, bob is carrying spanner3, bob is carrying spanner4, bob is currently at gate, location1 is linked to location2, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location5 is linked to location6, location6 is linked to location7, location8 is linked to location9, nut1 is at gate, nut1 is tightened, nut2 is at gate, nut2 is tightened, nut3 is located at gate, nut4 is located at gate, nut5 is currently at gate, nut5 is loose, spanner1 is carried by bob, spanner1 is usable, spanner5 is carried by bob, tightening of nut3 is complete and tightening of nut4 is complete", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, location4, and location5. At location5, Bob picks up spanner4 and proceeds to location6, where he collects spanner1. He then walks to location7, picks up spanner5, and continues to location8, where he gathers spanner3 and spanner2. From location8, Bob heads to location9 and then to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately achieving the current state. In this state, list all valid properties that do not involve negations. If none exist, state 'None'.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a connection is present between the shed and location1. Bob is currently located at the shed. Location1 is connected to location2, and location2 is linked to location3. Furthermore, location3 is connected to location4, and location4 is linked to location5. Location7 is connected to location8, and location8 is linked to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is currently present and is loose. Similarly, nut2 is at the gate but is not secured, while nut3 is also at the gate and is loose. Nut4 is at the gate and is not secured, and nut5 is also at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and is situated at location8. Spanner3 is also usable and is located at location8. Spanner4 is at location5 and is functional, and spanner5 is functional and located at location7."}
{"question_id": "916444ac-639c-4dbf-b143-567220e6a3d5", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1, bob picks up spanner5, spanner4 is picked up by bob from location1, bob walks to location2 from location1, bob walks to location3 from location2, from location3 to location4, bob walks, from location4, bob picks up spanner1, bob walks to location5 from location4, bob walks from location5 to location6 and spanner3 is picked up by bob from location6 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location2 does not exist, a link between gate and location6 does not exist, a link between location1 and location4 does not exist, a link between location1 and location9 does not exist, a link between location2 and location9 does not exist, a link between location3 and gate does not exist, a link between location4 and location1 does not exist, a link between location4 and location6 does not exist, a link between location4 and location7 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location8 does not exist, a link between location5 and shed does not exist, a link between location6 and location3 does not exist, a link between location6 and location4 does not exist, a link between location6 and location5 does not exist, a link between location6 and location8 does not exist, a link between location7 and location4 does not exist, a link between location8 and location3 does not exist, a link between location8 and location4 does not exist, a link between location9 and location3 does not exist, a link between shed and location2 does not exist, bob is not at location4, bob is not at location7, bob is not at location9, bob is not carrying spanner2, bob is not currently at gate, bob is not currently at location2, bob is not currently at location5, bob is not located at location1, bob is not located at location3, bob is not located at location8, bob is not located at shed, gate and location4 are not linked, gate and location9 are not linked, gate and shed are not linked, gate is not linked to location3, gate is not linked to location5, gate is not linked to location7, gate is not linked to location8, location1 and gate are not linked, location1 and location3 are not linked, location1 and location6 are not linked, location1 and shed are not linked, location1 is not linked to location5, location1 is not linked to location7, location1 is not linked to location8, location2 and location1 are not linked, location2 and location5 are not linked, location2 and location7 are not linked, location2 and shed are not linked, location2 is not linked to gate, location2 is not linked to location4, location2 is not linked to location6, location2 is not linked to location8, location3 and location6 are not linked, location3 and location7 are not linked, location3 is not linked to location1, location3 is not linked to location2, location3 is not linked to location5, location3 is not linked to location8, location3 is not linked to location9, location3 is not linked to shed, location4 and location2 are not linked, location4 and location8 are not linked, location4 and shed are not linked, location4 is not linked to gate, location4 is not linked to location3, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location4 are not linked, location5 and location7 are not linked, location5 is not linked to location1, location5 is not linked to location9, location6 and location2 are not linked, location6 is not linked to gate, location6 is not linked to location1, location6 is not linked to location9, location6 is not linked to shed, location7 and location6 are not linked, location7 and location9 are not linked, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location2, location7 is not linked to location3, location7 is not linked to location5, location7 is not linked to shed, location8 and gate are not linked, location8 and location1 are not linked, location8 and location2 are not linked, location8 and shed are not linked, location8 is not linked to location5, location8 is not linked to location6, location8 is not linked to location7, location9 and location1 are not linked, location9 and location5 are not linked, location9 and shed are not linked, location9 is not linked to location2, location9 is not linked to location4, location9 is not linked to location6, location9 is not linked to location7, location9 is not linked to location8, nut1 is not at location3, nut1 is not at location7, nut1 is not at location8, nut1 is not at shed, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location6, nut1 is not currently at location9, nut1 is not located at location4, nut1 is not located at location5, nut1 is not tightened, nut2 is not at location6, nut2 is not at location7, nut2 is not at location9, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location8, nut2 is not located at location3, nut2 is not located at location4, nut2 is not located at location5, nut2 is not located at shed, nut2 is not tightened, nut3 is not at location1, nut3 is not at location2, nut3 is not at location4, nut3 is not at location8, nut3 is not at location9, nut3 is not located at location3, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location7, nut3 is not located at shed, nut4 is not at location5, nut4 is not at shed, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location4, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not located at location7, nut4 is not located at location8, nut5 is not at location2, nut5 is not at location7, nut5 is not currently at location3, nut5 is not currently at location4, nut5 is not currently at location5, nut5 is not currently at location6, nut5 is not currently at location8, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location1, nut5 is not tightened, shed and gate are not linked, shed and location3 are not linked, shed and location4 are not linked, shed and location5 are not linked, shed is not linked to location6, shed is not linked to location7, shed is not linked to location8, shed is not linked to location9, spanner1 is not at location1, spanner1 is not currently at gate, spanner1 is not currently at location3, spanner1 is not currently at location4, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at location2, spanner1 is not located at location5, spanner1 is not located at location6, spanner1 is not located at location9, spanner1 is not located at shed, spanner2 is not at gate, spanner2 is not at location5, spanner2 is not at shed, spanner2 is not currently at location1, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location6, spanner2 is not located at location8, spanner2 is not located at location9, spanner3 is not at gate, spanner3 is not currently at location1, spanner3 is not currently at location3, spanner3 is not currently at location6, spanner3 is not currently at location7, spanner3 is not currently at shed, spanner3 is not located at location2, spanner3 is not located at location4, spanner3 is not located at location5, spanner3 is not located at location8, spanner3 is not located at location9, spanner4 is not at gate, spanner4 is not at location1, spanner4 is not at location5, spanner4 is not at location6, spanner4 is not at shed, spanner4 is not currently at location3, spanner4 is not currently at location7, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner4 is not located at location2, spanner4 is not located at location4, spanner5 is not at location1, spanner5 is not at location6, spanner5 is not at location8, spanner5 is not at shed, spanner5 is not currently at gate, spanner5 is not currently at location3, spanner5 is not located at location2, spanner5 is not located at location4, spanner5 is not located at location5, spanner5 is not located at location7, spanner5 is not located at location9, tightening of nut3 is incomplete and tightening of nut4 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, spanner1 is located at location4, spanner1 is usable, spanner2 is located at location7, spanner2 is usable, spanner3 can be used, spanner3 is located at location6, spanner4 is at location1, spanner4 is functional, spanner5 is currently at location1 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1, proceeds to location2, then to location3, and from there to location4. At location4, Bob picks up spanner1, walks to location5, then to location6, and finally collects spanner3 from location6 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, another connection exists between location6 and location7, and a connection also exists between location7 and location8. Additionally, a link is present between the shed and location1. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is located at location4 and is in working condition, spanner2 is at location7 and is usable, spanner3 is located at location6 and can be used, spanner4 is at location1 and is functional, and spanner5 is currently at location1 and is usable."}
{"question_id": "2fb6b812-416a-43bf-a94a-913809592e8f", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location9 does not exist, a link between gate and shed does not exist, a link between location1 and location3 does not exist, a link between location1 and location4 does not exist, a link between location1 and location6 does not exist, a link between location2 and location3 exists, a link between location2 and location5 does not exist, a link between location2 and location9 does not exist, a link between location3 and location8 does not exist, a link between location3 and shed does not exist, a link between location4 and location3 does not exist, a link between location4 and location5 exists, a link between location4 and location6 does not exist, a link between location4 and location7 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location6 exists, a link between location6 and location3 does not exist, a link between location6 and location8 does not exist, a link between location6 and shed does not exist, a link between location7 and gate does not exist, a link between location7 and location2 does not exist, a link between location7 and location3 does not exist, a link between location8 and location2 does not exist, a link between location8 and location3 does not exist, a link between location8 and location5 does not exist, a link between location8 and location6 does not exist, a link between location9 and location1 does not exist, a link between location9 and location6 does not exist, a link between shed and gate does not exist, a link between shed and location2 does not exist, a link between shed and location9 does not exist, bob is located at location1, bob is not at location4, bob is not at location9, bob is not carrying spanner1, bob is not carrying spanner2, bob is not currently at gate, bob is not currently at location2, bob is not currently at location6, bob is not currently at location8, bob is not currently at shed, bob is not located at location3, bob is not located at location5, bob is not located at location7, gate and location1 are not linked, gate and location2 are not linked, gate and location3 are not linked, gate and location6 are not linked, gate and location7 are not linked, gate is not linked to location4, gate is not linked to location5, gate is not linked to location8, location1 and location5 are not linked, location1 and location7 are not linked, location1 and shed are not linked, location1 is linked to location2, location1 is not linked to gate, location1 is not linked to location8, location1 is not linked to location9, location2 and location4 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to gate, location2 is not linked to location1, location2 is not linked to location6, location3 and gate are not linked, location3 and location4 are linked, location3 and location5 are not linked, location3 and location6 are not linked, location3 and location7 are not linked, location3 is not linked to location1, location3 is not linked to location2, location3 is not linked to location9, location4 and gate are not linked, location4 and location1 are not linked, location4 and location2 are not linked, location4 and shed are not linked, location5 and location1 are not linked, location5 and location2 are not linked, location5 and location3 are not linked, location5 is not linked to location4, location5 is not linked to location7, location5 is not linked to location8, location5 is not linked to location9, location5 is not linked to shed, location6 and location1 are not linked, location6 and location5 are not linked, location6 and location7 are linked, location6 and location9 are not linked, location6 is not linked to gate, location6 is not linked to location2, location6 is not linked to location4, location7 and location4 are not linked, location7 and location6 are not linked, location7 and location8 are linked, location7 is not linked to location1, location7 is not linked to location5, location7 is not linked to location9, location7 is not linked to shed, location8 and location1 are not linked, location8 and shed are not linked, location8 is linked to location9, location8 is not linked to gate, location8 is not linked to location4, location8 is not linked to location7, location9 and gate are linked, location9 and location4 are not linked, location9 and location8 are not linked, location9 and shed are not linked, location9 is not linked to location2, location9 is not linked to location3, location9 is not linked to location5, location9 is not linked to location7, nut1 is currently at gate, nut1 is not at location5, nut1 is not currently at location6, nut1 is not currently at location7, nut1 is not currently at location8, nut1 is not located at location1, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location9, nut1 is not located at shed, nut1 is not secured, nut1 is not tightened, nut2 is currently at gate, nut2 is loose, nut2 is not at location1, nut2 is not at location4, nut2 is not at location5, nut2 is not at location8, nut2 is not currently at location2, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not located at location3, nut2 is not located at location9, nut2 is not located at shed, nut3 is at gate, nut3 is loose, nut3 is not at location1, nut3 is not at location3, nut3 is not at location8, nut3 is not at shed, nut3 is not currently at location7, nut3 is not currently at location9, nut3 is not located at location2, nut3 is not located at location4, nut3 is not located at location5, nut3 is not located at location6, nut4 is at gate, nut4 is not at location3, nut4 is not at location8, nut4 is not at shed, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not located at location1, nut4 is not located at location2, nut4 is not located at location7, nut4 is not secured, nut5 is currently at gate, nut5 is not at location5, nut5 is not at location6, nut5 is not at location7, nut5 is not currently at location4, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location1, nut5 is not located at location2, nut5 is not located at location3, nut5 is not located at location8, nut5 is not secured, nut5 is not tightened, shed and location1 are linked, shed and location3 are not linked, shed and location4 are not linked, shed and location5 are not linked, shed and location8 are not linked, shed is not linked to location6, shed is not linked to location7, spanner1 can be used, spanner1 is located at location6, spanner1 is not at location1, spanner1 is not at location2, spanner1 is not at location7, spanner1 is not at shed, spanner1 is not currently at location3, spanner1 is not currently at location5, spanner1 is not currently at location8, spanner1 is not currently at location9, spanner1 is not located at gate, spanner1 is not located at location4, spanner2 is functional, spanner2 is located at location8, spanner2 is not at location1, spanner2 is not at location4, spanner2 is not at shed, spanner2 is not currently at gate, spanner2 is not currently at location3, spanner2 is not currently at location6, spanner2 is not currently at location9, spanner2 is not located at location2, spanner2 is not located at location5, spanner2 is not located at location7, spanner3 can be used, spanner3 is at location8, spanner3 is not at location1, spanner3 is not at location4, spanner3 is not at location5, spanner3 is not at location7, spanner3 is not carried by bob, spanner3 is not currently at gate, spanner3 is not currently at location2, spanner3 is not currently at shed, spanner3 is not located at location3, spanner3 is not located at location6, spanner3 is not located at location9, spanner4 is at location5, spanner4 is not at gate, spanner4 is not at location4, spanner4 is not carried by bob, spanner4 is not currently at location1, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner4 is not located at location2, spanner4 is not located at location3, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at shed, spanner4 is usable, spanner5 is at location7, spanner5 is not at gate, spanner5 is not at location6, spanner5 is not at location8, spanner5 is not carried by bob, spanner5 is not currently at location2, spanner5 is not currently at location4, spanner5 is not currently at shed, spanner5 is not located at location1, spanner5 is not located at location3, spanner5 is not located at location5, spanner5 is not located at location9, spanner5 is usable, tightening of nut2 is incomplete, tightening of nut3 is incomplete and tightening of nut4 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: bob moves from the shed to location1 to attain the current state. In this state, enumerate all applicable properties (including both affirmative and negated properties). Indicate None if there are no properties.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a link is present between the shed and location1. Bob is currently located at the shed. Furthermore, location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is linked to location5. Moreover, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is currently present and is loose, while nut2 is also at the gate but not secured. Similarly, nut3 is at the gate and is loose, nut4 is at the gate but not secured, and nut5 is at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and can be found at location8, while spanner3 is also usable and located at location8. Spanner4 is at location5 and is functional, and spanner5 is functional and located at location7."}
{"question_id": "6101806f-ae17-461f-bf72-cb7e83e3498c", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob picks up spanner5 from location1, bob picks up spanner4 from location1, from location1 to location2, bob walks, from location2 to location3, bob walks, from location3 to location4, bob walks, bob picks up spanner1 from location4, from location4 to location5, bob walks, from location5 to location6, bob walks, from location6, bob picks up spanner3, bob walks from location6 to location7, bob picks up spanner2 from location7, bob walks from location7 to location8, from location8 to location9, bob walks, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location4 and location5 exists, a link between location7 and location8 exists, bob is carrying spanner2, bob is currently at gate, location1 and location2 are linked, location2 and location3 are linked, location3 and location4 are linked, location5 and location6 are linked, location6 and location7 are linked, location8 is linked to location9, location9 is linked to gate, nut1 is located at gate, nut1 is tightened, nut2 is located at gate, nut2 is tightened, nut3 is located at gate, nut3 is tightened, nut4 is currently at gate, nut4 is tightened, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is carried by bob, spanner3 is carried by bob, spanner4 is carried by bob and spanner5 is carried by bob", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, spanner1 is located at location4, spanner1 is usable, spanner2 is located at location7, spanner2 is usable, spanner3 can be used, spanner3 is located at location6, spanner4 is at location1, spanner4 is functional, spanner5 is currently at location1 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob starts at the shed and walks to location1, where he collects spanner5 and spanner4. He then proceeds to location2, followed by location3, and then location4, where he picks up spanner1. From location4, Bob walks to location5, then to location6, where he collects spanner3. He then walks to location7, picks up spanner2, and continues to location8, then location9, and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, list all valid properties that do not involve negations. If there are none, state None.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, another connection exists between location6 and location7, and a connection also exists between location7 and location8. Additionally, a link is present between the shed and location1. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is located at location4 and is in working condition, spanner2 is at location7 and is usable, spanner3 is located at location6 and can be used, spanner4 is at location1 and is functional, and spanner5 is currently at location1 and is usable."}
{"question_id": "babea64c-315b-4fb0-ac2d-943053b9c2a5", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, from location3, bob picks up spanner5, bob picks up spanner1 from location3, from location3 to location4, bob walks, bob walks from location4 to location5, bob picks up spanner2 from location5 and bob walks from location5 to location6 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location3 does not exist, a link between gate and location5 does not exist, a link between gate and location6 does not exist, a link between gate and location9 does not exist, a link between location1 and gate does not exist, a link between location1 and location5 does not exist, a link between location1 and shed does not exist, a link between location2 and location4 does not exist, a link between location2 and location8 does not exist, a link between location2 and shed does not exist, a link between location3 and location5 does not exist, a link between location3 and location7 does not exist, a link between location3 and shed does not exist, a link between location4 and gate does not exist, a link between location4 and location1 does not exist, a link between location4 and location3 does not exist, a link between location4 and location6 does not exist, a link between location4 and location9 does not exist, a link between location5 and location8 does not exist, a link between location6 and location2 does not exist, a link between location6 and location5 does not exist, a link between location6 and location8 does not exist, a link between location7 and gate does not exist, a link between location7 and location1 does not exist, a link between location7 and location3 does not exist, a link between location8 and location3 does not exist, a link between location8 and location4 does not exist, a link between location8 and location5 does not exist, a link between location8 and location9 exists, a link between location9 and location1 does not exist, a link between location9 and location2 does not exist, a link between location9 and location4 does not exist, a link between location9 and shed does not exist, a link between shed and gate does not exist, bob is carrying spanner1, bob is located at location6, bob is not at location2, bob is not at location5, bob is not at location9, bob is not at shed, bob is not carrying spanner4, bob is not currently at gate, bob is not currently at location1, bob is not currently at location3, bob is not currently at location4, bob is not located at location7, bob is not located at location8, gate and location2 are not linked, gate and location8 are not linked, gate and shed are not linked, gate is not linked to location1, gate is not linked to location4, gate is not linked to location7, location1 and location2 are linked, location1 and location3 are not linked, location1 and location4 are not linked, location1 and location8 are not linked, location1 and location9 are not linked, location1 is not linked to location6, location1 is not linked to location7, location2 and location3 are linked, location2 and location5 are not linked, location2 and location6 are not linked, location2 is not linked to gate, location2 is not linked to location1, location2 is not linked to location7, location2 is not linked to location9, location3 and gate are not linked, location3 and location1 are not linked, location3 is linked to location4, location3 is not linked to location2, location3 is not linked to location6, location3 is not linked to location8, location3 is not linked to location9, location4 and location2 are not linked, location4 and location7 are not linked, location4 and location8 are not linked, location4 and shed are not linked, location4 is linked to location5, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location4 are not linked, location5 and location6 are linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location7, location6 and location3 are not linked, location6 and location4 are not linked, location6 and location7 are linked, location6 is not linked to gate, location6 is not linked to location1, location6 is not linked to location9, location6 is not linked to shed, location7 and location5 are not linked, location7 and shed are not linked, location7 is linked to location8, location7 is not linked to location2, location7 is not linked to location4, location7 is not linked to location6, location7 is not linked to location9, location8 and gate are not linked, location8 and location1 are not linked, location8 and location2 are not linked, location8 and shed are not linked, location8 is not linked to location6, location8 is not linked to location7, location9 and gate are linked, location9 and location7 are not linked, location9 and location8 are not linked, location9 is not linked to location3, location9 is not linked to location5, location9 is not linked to location6, nut1 is currently at gate, nut1 is not at location1, nut1 is not at location2, nut1 is not at location4, nut1 is not at location6, nut1 is not at shed, nut1 is not currently at location3, nut1 is not currently at location7, nut1 is not currently at location9, nut1 is not located at location5, nut1 is not located at location8, nut1 is not secured, nut1 is not tightened, nut2 is at gate, nut2 is not at location1, nut2 is not at location2, nut2 is not at location7, nut2 is not currently at location3, nut2 is not currently at location6, nut2 is not currently at location9, nut2 is not currently at shed, nut2 is not located at location4, nut2 is not located at location5, nut2 is not located at location8, nut2 is not secured, nut2 is not tightened, nut3 is at gate, nut3 is not at location1, nut3 is not at location8, nut3 is not currently at location2, nut3 is not currently at location3, nut3 is not currently at location5, nut3 is not currently at shed, nut3 is not located at location4, nut3 is not located at location6, nut3 is not located at location7, nut3 is not located at location9, nut3 is not secured, nut3 is not tightened, nut4 is currently at gate, nut4 is not at location7, nut4 is not at shed, nut4 is not currently at location5, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not located at location1, nut4 is not located at location2, nut4 is not located at location3, nut4 is not located at location4, nut4 is not located at location8, nut4 is not secured, nut5 is at gate, nut5 is not at location3, nut5 is not at location5, nut5 is not at location8, nut5 is not currently at location4, nut5 is not currently at location6, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location1, nut5 is not located at location2, nut5 is not located at location7, nut5 is not secured, nut5 is not tightened, shed and location2 are not linked, shed and location3 are not linked, shed and location4 are not linked, shed and location7 are not linked, shed and location8 are not linked, shed and location9 are not linked, shed is linked to location1, shed is not linked to location5, shed is not linked to location6, spanner1 is functional, spanner1 is not at location5, spanner1 is not at location6, spanner1 is not currently at location2, spanner1 is not currently at location4, spanner1 is not located at gate, spanner1 is not located at location1, spanner1 is not located at location3, spanner1 is not located at location7, spanner1 is not located at location8, spanner1 is not located at location9, spanner1 is not located at shed, spanner2 is carried by bob, spanner2 is not at location2, spanner2 is not at location4, spanner2 is not at location6, spanner2 is not at location7, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not currently at location8, spanner2 is not located at gate, spanner2 is not located at location1, spanner2 is not located at location3, spanner2 is not located at location5, spanner2 is usable, spanner3 is carried by bob, spanner3 is not at location3, spanner3 is not at location5, spanner3 is not at location8, spanner3 is not currently at gate, spanner3 is not currently at location6, spanner3 is not currently at location9, spanner3 is not located at location1, spanner3 is not located at location2, spanner3 is not located at location4, spanner3 is not located at location7, spanner3 is not located at shed, spanner3 is usable, spanner4 can be used, spanner4 is located at location6, spanner4 is not at location4, spanner4 is not at location5, spanner4 is not at shed, spanner4 is not currently at gate, spanner4 is not currently at location1, spanner4 is not currently at location2, spanner4 is not currently at location8, spanner4 is not located at location3, spanner4 is not located at location7, spanner4 is not located at location9, spanner5 is carried by bob, spanner5 is functional, spanner5 is not at location1, spanner5 is not at location2, spanner5 is not at location4, spanner5 is not at location8, spanner5 is not at location9, spanner5 is not at shed, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not currently at location7, spanner5 is not located at gate, spanner5 is not located at location5 and tightening of nut4 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location8 and location9 exists, a link between location9 and gate exists, bob is at shed, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location6 is linked to location7, location7 and location8 are linked, nut1 is currently at gate, nut1 is not secured, nut2 is at gate, nut2 is loose, nut3 is at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is currently at location5, spanner2 is functional, spanner3 is located at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is at location3 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he collects spanner3. Next, he proceeds from location2 to location3, picks up spanner5 and spanner1 at location3, and then walks to location4. From location4, he moves to location5, collects spanner2, and finally walks from location5 to location6, resulting in the current state. In this state, list all valid properties (including both affirmative and negated properties). If there are no properties, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location8 and location9, and a connection also exists between location9 and the gate. Bob is currently at the shed. Additionally, location3 is connected to location4, location4 is linked to location5, location5 is connected to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is loose, nut3 is not secured, nut4 is loose, and nut5 is also loose. The shed is connected to location1. Spanner1 is at location3 and is in working condition, spanner2 is at location5 and functional, spanner3 is at location2 and usable, spanner4 is at location6 and functional, and spanner5 is at location3 and usable."}
{"question_id": "c3501c8a-62f4-4ddb-960e-f1ea0830a3f9", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, from location2, bob picks up spanner4, spanner3 is picked up by bob from location2, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks to location5 from location4, from location5 to location6, bob walks, bob picks up spanner5 from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location4 and location5 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, bob is carrying spanner2, bob is carrying spanner4, bob is located at location6, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location5 is linked to location6, location7 and location8 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is at gate, nut3 is not secured, nut4 is at gate, nut4 is loose, nut5 is at gate, nut5 is not secured, shed and location1 are linked, spanner1 is currently at location8, spanner1 is functional, spanner2 can be used, spanner3 is carried by bob, spanner3 is usable, spanner4 is functional, spanner5 is carried by bob and spanner5 is functional", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, spanner1 is currently at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 can be used, spanner3 is currently at location2, spanner4 is at location2, spanner4 is usable, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1, then from location1 to location2, where he picks up both spanner4 and spanner3. He then proceeds to walk from location2 to location3, followed by location4, and then location5. From location5, he walks to location6, where he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, list all valid properties that do not involve negations. If there are none, write None.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, a connection between location8 and location9 exists, a connection between the shed and location1 is present, bob is situated at the shed, location1 is connected to location2, location2 is connected to location3, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 is connected to location8, location9 and the gate are connected, nut1 is situated at the gate, nut1 is not secure, nut2 is currently situated at the gate, nut2 is loose, nut3 is located at the gate, nut3 is loose, nut4 is located at the gate, nut4 is loose, nut5 is currently situated at the gate, nut5 is not secure, spanner1 is currently situated at location8, spanner1 is in working condition, spanner2 is at location6, spanner2 is in working order, spanner3 is in working condition, spanner3 is currently at location2, spanner4 is at location2, spanner4 is in working condition, spanner5 is in working order and spanner5 is located at location6."}
{"question_id": "65c665d7-4d9f-46a3-9277-7843e584ad74", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob walks to location3 from location2, bob walks to location4 from location3, from location4 to location5, bob walks, from location5, bob picks up spanner4, bob walks from location5 to location6, spanner1 is picked up by bob from location6, from location6 to location7, bob walks, spanner5 is picked up by bob from location7, bob walks from location7 to location8, from location8, bob picks up spanner3, spanner2 is picked up by bob from location8, from location8 to location9, bob walks, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location2 does not exist, a link between gate and location3 does not exist, a link between gate and location4 does not exist, a link between gate and location7 does not exist, a link between location1 and location3 does not exist, a link between location1 and location7 does not exist, a link between location1 and location8 does not exist, a link between location1 and location9 does not exist, a link between location1 and shed does not exist, a link between location2 and location1 does not exist, a link between location2 and location6 does not exist, a link between location3 and gate does not exist, a link between location3 and location1 does not exist, a link between location3 and location6 does not exist, a link between location3 and location7 does not exist, a link between location3 and location8 does not exist, a link between location3 and shed does not exist, a link between location4 and location7 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location6 and gate does not exist, a link between location6 and location1 does not exist, a link between location6 and location8 does not exist, a link between location7 and location1 does not exist, a link between location7 and location6 does not exist, a link between location7 and location9 does not exist, a link between location8 and location4 does not exist, a link between location8 and shed does not exist, a link between location9 and location8 does not exist, a link between shed and location9 does not exist, bob is not at location1, bob is not at location9, bob is not currently at location4, bob is not currently at location8, bob is not currently at shed, bob is not located at location2, bob is not located at location3, bob is not located at location5, bob is not located at location6, bob is not located at location7, gate and location1 are not linked, gate and location5 are not linked, gate and location6 are not linked, gate and location8 are not linked, gate is not linked to location9, gate is not linked to shed, location1 and gate are not linked, location1 and location4 are not linked, location1 and location5 are not linked, location1 and location6 are not linked, location2 and gate are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to location4, location2 is not linked to location5, location2 is not linked to location9, location3 and location2 are not linked, location3 and location5 are not linked, location3 and location9 are not linked, location4 and location3 are not linked, location4 is not linked to gate, location4 is not linked to location1, location4 is not linked to location2, location4 is not linked to location6, location4 is not linked to location8, location4 is not linked to shed, location5 and location3 are not linked, location5 and location4 are not linked, location5 and location7 are not linked, location5 and shed are not linked, location5 is not linked to location1, location5 is not linked to location2, location5 is not linked to location8, location5 is not linked to location9, location6 and location9 are not linked, location6 is not linked to location2, location6 is not linked to location3, location6 is not linked to location4, location6 is not linked to location5, location6 is not linked to shed, location7 and location2 are not linked, location7 and shed are not linked, location7 is not linked to gate, location7 is not linked to location3, location7 is not linked to location4, location7 is not linked to location5, location8 and location1 are not linked, location8 and location5 are not linked, location8 and location7 are not linked, location8 is not linked to gate, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location6, location9 and location4 are not linked, location9 and location5 are not linked, location9 and location6 are not linked, location9 is not linked to location1, location9 is not linked to location2, location9 is not linked to location3, location9 is not linked to location7, location9 is not linked to shed, nut1 is not at location2, nut1 is not at location5, nut1 is not currently at location3, nut1 is not currently at location6, nut1 is not currently at location8, nut1 is not currently at shed, nut1 is not located at location1, nut1 is not located at location4, nut1 is not located at location7, nut1 is not located at location9, nut1 is secured, nut2 is not at location1, nut2 is not at location3, nut2 is not at location4, nut2 is not at location8, nut2 is not at location9, nut2 is not currently at location2, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not located at location5, nut2 is not located at shed, nut2 is secured, nut3 is not at location3, nut3 is not at location6, nut3 is not at location8, nut3 is not currently at location1, nut3 is not currently at location5, nut3 is not currently at location7, nut3 is not currently at location9, nut3 is not located at location2, nut3 is not located at location4, nut3 is not located at shed, nut3 is not loose, nut4 is not at location1, nut4 is not at location6, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location7, nut4 is not currently at location8, nut4 is not currently at location9, nut4 is not located at shed, nut4 is not loose, nut5 is not at location4, nut5 is not at shed, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location5, nut5 is not currently at location6, nut5 is not currently at location7, nut5 is not currently at location8, nut5 is not located at location3, nut5 is not located at location9, nut5 is not tightened, shed and gate are not linked, shed and location3 are not linked, shed and location4 are not linked, shed and location5 are not linked, shed and location7 are not linked, shed and location8 are not linked, shed is not linked to location2, shed is not linked to location6, spanner1 is not at location2, spanner1 is not at location6, spanner1 is not at location9, spanner1 is not at shed, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location4, spanner1 is not currently at location5, spanner1 is not located at location3, spanner1 is not located at location7, spanner1 is not located at location8, spanner2 is not at location1, spanner2 is not at location2, spanner2 is not at location3, spanner2 is not at location5, spanner2 is not at location6, spanner2 is not at location7, spanner2 is not currently at gate, spanner2 is not currently at location8, spanner2 is not currently at shed, spanner2 is not functional, spanner2 is not located at location4, spanner2 is not located at location9, spanner3 is not at gate, spanner3 is not at location3, spanner3 is not at location7, spanner3 is not at location8, spanner3 is not currently at location2, spanner3 is not currently at location5, spanner3 is not currently at location9, spanner3 is not functional, spanner3 is not located at location1, spanner3 is not located at location4, spanner3 is not located at location6, spanner3 is not located at shed, spanner4 is not at gate, spanner4 is not at location4, spanner4 is not at location6, spanner4 is not currently at location5, spanner4 is not currently at location8, spanner4 is not currently at shed, spanner4 is not located at location1, spanner4 is not located at location2, spanner4 is not located at location3, spanner4 is not located at location7, spanner4 is not located at location9, spanner4 is not usable, spanner5 is not at location1, spanner5 is not at location7, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location4, spanner5 is not currently at location5, spanner5 is not currently at shed, spanner5 is not located at gate, spanner5 is not located at location6, spanner5 is not located at location8, spanner5 is not located at location9 and spanner5 is not usable", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and subsequently location5. From location5, Bob proceeds to location6, where he collects spanner1. Next, he walks to location7, picks up spanner5, and then heads to location8. At location8, Bob collects spanner3 and spanner2. He then walks to location9 and finally reaches the gate. Upon arrival, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, list all valid properties that involve negations. If none exist, state 'None'.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a connection is present between the shed and location1. Bob is currently located at the shed. Location1 is connected to location2, and location2 is linked to location3. Furthermore, location3 is connected to location4, and location4 is linked to location5. Location7 is connected to location8, and location8 is linked to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is currently present and is loose. Similarly, nut2 is at the gate but is not secured, while nut3 is also at the gate and is loose. Nut4 is at the gate and is not secured, and nut5 is also at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and is situated at location8, while spanner3 is also usable and is located at location8. Spanner4 is at location5 and is functional, and spanner5 is also functional and is located at location7."}
{"question_id": "000d7e23-be8c-4860-9840-3f5d6f109a5e", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location3 and location4 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at location1, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location7 and location8 are linked, location8 is linked to location9, location9 is linked to gate, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 can be used, spanner1 is at location4, spanner2 is functional, spanner2 is located at location7, spanner3 is at location6, spanner3 is usable, spanner4 is located at location1, spanner4 is usable, spanner5 is currently at location1 and spanner5 is usable", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, spanner1 is located at location4, spanner1 is usable, spanner2 is located at location7, spanner2 is usable, spanner3 can be used, spanner3 is located at location6, spanner4 is at location1, spanner4 is functional, spanner5 is currently at location1 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: bob moves from the shed to location1 to attain the current state. In this state, identify all valid properties that do not include negations. If there are no such properties, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, another connection exists between location6 and location7, and a connection also exists between location7 and location8. Additionally, a link is present between the shed and location1. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is located at location4 and is in working condition, spanner2 is at location7 and is usable, spanner3 is located at location6 and can be used, spanner4 is at location1 and is functional, and spanner5 is currently at location1 and is usable."}
{"question_id": "600ddeb6-b7fc-4ddb-8d92-c8e8f9021d78", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location7 does not exist, a link between gate and location8 does not exist, a link between gate and shed does not exist, a link between location1 and location3 does not exist, a link between location1 and location5 does not exist, a link between location1 and location7 does not exist, a link between location1 and location9 does not exist, a link between location2 and location6 does not exist, a link between location2 and location7 does not exist, a link between location2 and location8 does not exist, a link between location2 and location9 does not exist, a link between location3 and location1 does not exist, a link between location3 and location5 does not exist, a link between location3 and location6 does not exist, a link between location3 and location8 does not exist, a link between location3 and shed does not exist, a link between location4 and gate does not exist, a link between location4 and location2 does not exist, a link between location4 and location3 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location3 does not exist, a link between location5 and location7 does not exist, a link between location5 and location9 does not exist, a link between location5 and shed does not exist, a link between location6 and location1 does not exist, a link between location6 and location4 does not exist, a link between location6 and location5 does not exist, a link between location6 and location8 does not exist, a link between location7 and location3 does not exist, a link between location8 and gate does not exist, a link between location8 and location3 does not exist, a link between location8 and location5 does not exist, a link between location9 and location1 does not exist, a link between location9 and location5 does not exist, a link between location9 and location8 does not exist, a link between shed and location2 does not exist, a link between shed and location4 does not exist, bob is not at location2, bob is not at location4, bob is not at location7, bob is not at location8, bob is not at location9, bob is not carrying spanner2, bob is not carrying spanner3, bob is not carrying spanner4, bob is not carrying spanner5, bob is not currently at gate, bob is not currently at location6, bob is not located at location3, bob is not located at location5, bob is not located at shed, gate and location4 are not linked, gate and location6 are not linked, gate is not linked to location2, gate is not linked to location3, gate is not linked to location5, gate is not linked to location9, location1 and gate are not linked, location1 and location4 are not linked, location1 and location8 are not linked, location1 and shed are not linked, location1 is not linked to location6, location2 and gate are not linked, location2 and location4 are not linked, location2 and location5 are not linked, location2 is not linked to location1, location2 is not linked to shed, location3 and gate are not linked, location3 and location2 are not linked, location3 is not linked to location7, location3 is not linked to location9, location4 and location6 are not linked, location4 and location7 are not linked, location4 is not linked to location1, location4 is not linked to shed, location5 and location4 are not linked, location5 is not linked to location2, location5 is not linked to location8, location6 and gate are not linked, location6 and location2 are not linked, location6 and location3 are not linked, location6 and location9 are not linked, location6 and shed are not linked, location7 and location2 are not linked, location7 and shed are not linked, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location4, location7 is not linked to location5, location7 is not linked to location6, location7 is not linked to location9, location8 and location4 are not linked, location8 and location6 are not linked, location8 is not linked to location1, location8 is not linked to location2, location8 is not linked to location7, location8 is not linked to shed, location9 and location4 are not linked, location9 and location7 are not linked, location9 is not linked to location2, location9 is not linked to location3, location9 is not linked to location6, location9 is not linked to shed, nut1 is not at location9, nut1 is not currently at location1, nut1 is not currently at location5, nut1 is not currently at location7, nut1 is not currently at shed, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location6, nut1 is not located at location8, nut1 is not tightened, nut2 is not at location4, nut2 is not at location5, nut2 is not at location6, nut2 is not at location7, nut2 is not at shed, nut2 is not currently at location9, nut2 is not located at location1, nut2 is not located at location2, nut2 is not located at location3, nut2 is not located at location8, nut2 is not tightened, nut3 is not at location1, nut3 is not at location6, nut3 is not at location7, nut3 is not at shed, nut3 is not currently at location4, nut3 is not located at location2, nut3 is not located at location3, nut3 is not located at location5, nut3 is not located at location8, nut3 is not located at location9, nut3 is not tightened, nut4 is not at location2, nut4 is not at location3, nut4 is not at location5, nut4 is not at location6, nut4 is not at location8, nut4 is not currently at shed, nut4 is not located at location1, nut4 is not located at location4, nut4 is not located at location7, nut4 is not located at location9, nut4 is not tightened, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location5, nut5 is not currently at location7, nut5 is not currently at location8, nut5 is not located at location3, nut5 is not located at location4, nut5 is not located at location6, nut5 is not located at location9, nut5 is not located at shed, nut5 is not tightened, shed and gate are not linked, shed and location3 are not linked, shed and location8 are not linked, shed is not linked to location5, shed is not linked to location6, shed is not linked to location7, shed is not linked to location9, spanner1 is not at location5, spanner1 is not at location7, spanner1 is not carried by bob, spanner1 is not currently at gate, spanner1 is not currently at location6, spanner1 is not currently at location8, spanner1 is not currently at location9, spanner1 is not located at location1, spanner1 is not located at location2, spanner1 is not located at location4, spanner1 is not located at shed, spanner2 is not at location1, spanner2 is not at location8, spanner2 is not at shed, spanner2 is not currently at gate, spanner2 is not currently at location6, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location7, spanner2 is not located at location9, spanner3 is not at gate, spanner3 is not at location6, spanner3 is not at location9, spanner3 is not at shed, spanner3 is not currently at location1, spanner3 is not currently at location4, spanner3 is not currently at location5, spanner3 is not currently at location7, spanner3 is not currently at location8, spanner3 is not located at location3, spanner4 is not at gate, spanner4 is not at location1, spanner4 is not at location9, spanner4 is not currently at location3, spanner4 is not currently at location4, spanner4 is not currently at location7, spanner4 is not currently at location8, spanner4 is not currently at shed, spanner4 is not located at location2, spanner4 is not located at location5, spanner5 is not at location2, spanner5 is not at location9, spanner5 is not currently at location4, spanner5 is not currently at location5, spanner5 is not currently at location6, spanner5 is not currently at location7, spanner5 is not located at gate, spanner5 is not located at location1, spanner5 is not located at location8 and spanner5 is not located at shed", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location8 and location9 exists, a link between location9 and gate exists, bob is at shed, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location6 is linked to location7, location7 and location8 are linked, nut1 is currently at gate, nut1 is not secured, nut2 is at gate, nut2 is loose, nut3 is at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is currently at location5, spanner2 is functional, spanner3 is located at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is at location3 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: Bob moves from the shed to location1 to achieve the current state. In this state, identify all valid properties of the state that include negations. If there are no such properties, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location8 and location9, and a connection also exists between location9 and the gate. Bob is currently at the shed. Additionally, location3 is connected to location4, location4 is linked to location5, location5 is connected to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is loose, nut3 is not secured, nut4 is loose, and nut5 is also loose. The shed is connected to location1. Spanner1 is at location3 and is in working condition, spanner2 is at location5 and functional, spanner3 is at location2 and usable, spanner4 is at location6 and functional, and spanner5 is at location3 and usable."}
{"question_id": "6a4fa9e0-2024-42ed-83b0-423147e95037", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, bob walks from location2 to location3, bob walks to location4 from location3, bob walks from location4 to location5, spanner4 is picked up by bob from location5, bob walks to location6 from location5, bob picks up spanner1 from location6, bob walks to location7 from location6, from location7, bob picks up spanner5, bob walks from location7 to location8, bob picks up spanner3 from location8, from location8, bob picks up spanner2, bob walks to location9 from location8, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location4 does not exist, a link between gate and location7 does not exist, a link between gate and location8 does not exist, a link between location2 and location4 does not exist, a link between location2 and location5 does not exist, a link between location2 and shed does not exist, a link between location3 and gate does not exist, a link between location3 and location1 does not exist, a link between location3 and location2 does not exist, a link between location3 and location7 does not exist, a link between location3 and location9 does not exist, a link between location3 and shed does not exist, a link between location4 and location3 does not exist, a link between location4 and location8 does not exist, a link between location5 and location7 does not exist, a link between location6 and location3 does not exist, a link between location6 and location5 does not exist, a link between location6 and location9 does not exist, a link between location7 and location3 does not exist, a link between location7 and location9 does not exist, a link between location7 and shed does not exist, a link between location8 and location2 does not exist, a link between location8 and location6 does not exist, a link between location8 and location7 does not exist, a link between location9 and location1 does not exist, a link between location9 and location4 does not exist, a link between location9 and location7 does not exist, a link between location9 and shed does not exist, a link between shed and location2 does not exist, a link between shed and location8 does not exist, bob is carrying spanner1, bob is carrying spanner2, bob is carrying spanner4, bob is currently at gate, bob is not at location5, bob is not at location9, bob is not at shed, bob is not currently at location1, bob is not currently at location2, bob is not currently at location4, bob is not currently at location6, bob is not currently at location8, bob is not located at location3, bob is not located at location7, gate and location3 are not linked, gate is not linked to location1, gate is not linked to location2, gate is not linked to location5, gate is not linked to location6, gate is not linked to location9, gate is not linked to shed, location1 and location2 are linked, location1 and location9 are not linked, location1 and shed are not linked, location1 is not linked to gate, location1 is not linked to location3, location1 is not linked to location4, location1 is not linked to location5, location1 is not linked to location6, location1 is not linked to location7, location1 is not linked to location8, location2 and gate are not linked, location2 and location1 are not linked, location2 and location8 are not linked, location2 and location9 are not linked, location2 is linked to location3, location2 is not linked to location6, location2 is not linked to location7, location3 and location5 are not linked, location3 is linked to location4, location3 is not linked to location6, location3 is not linked to location8, location4 and location1 are not linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 is linked to location5, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location6, location4 is not linked to shed, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location4 are not linked, location5 and location8 are not linked, location5 is linked to location6, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location9, location5 is not linked to shed, location6 and location1 are not linked, location6 and location7 are linked, location6 and location8 are not linked, location6 and shed are not linked, location6 is not linked to gate, location6 is not linked to location2, location6 is not linked to location4, location7 and location1 are not linked, location7 and location2 are not linked, location7 and location8 are linked, location7 is not linked to gate, location7 is not linked to location4, location7 is not linked to location5, location7 is not linked to location6, location8 and location1 are not linked, location8 and location4 are not linked, location8 and location5 are not linked, location8 and location9 are linked, location8 is not linked to gate, location8 is not linked to location3, location8 is not linked to shed, location9 and location3 are not linked, location9 and location6 are not linked, location9 is linked to gate, location9 is not linked to location2, location9 is not linked to location5, location9 is not linked to location8, nut1 is at gate, nut1 is not at location2, nut1 is not at location5, nut1 is not at location8, nut1 is not at shed, nut1 is not currently at location3, nut1 is not currently at location4, nut1 is not currently at location7, nut1 is not located at location1, nut1 is not located at location6, nut1 is not located at location9, nut1 is not loose, nut2 is currently at gate, nut2 is not at location2, nut2 is not at location5, nut2 is not at location6, nut2 is not at location7, nut2 is not at location9, nut2 is not currently at location3, nut2 is not currently at location4, nut2 is not currently at shed, nut2 is not located at location1, nut2 is not located at location8, nut2 is secured, nut2 is tightened, nut3 is located at gate, nut3 is not at location2, nut3 is not at location5, nut3 is not at location7, nut3 is not at location8, nut3 is not currently at location3, nut3 is not currently at location6, nut3 is not currently at location9, nut3 is not located at location1, nut3 is not located at location4, nut3 is not located at shed, nut3 is secured, nut4 is currently at gate, nut4 is not at location4, nut4 is not at location5, nut4 is not at location8, nut4 is not at shed, nut4 is not currently at location2, nut4 is not currently at location6, nut4 is not located at location1, nut4 is not located at location3, nut4 is not located at location7, nut4 is not located at location9, nut4 is secured, nut5 is at gate, nut5 is not at location4, nut5 is not at location7, nut5 is not at location8, nut5 is not at shed, nut5 is not currently at location2, nut5 is not currently at location3, nut5 is not currently at location5, nut5 is not located at location1, nut5 is not located at location6, nut5 is not located at location9, nut5 is not secured, shed and location4 are not linked, shed and location6 are not linked, shed and location7 are not linked, shed and location9 are not linked, shed is linked to location1, shed is not linked to gate, shed is not linked to location3, shed is not linked to location5, spanner1 can be used, spanner1 is not at location1, spanner1 is not at location3, spanner1 is not at location6, spanner1 is not at location8, spanner1 is not currently at gate, spanner1 is not currently at location2, spanner1 is not currently at location7, spanner1 is not currently at shed, spanner1 is not located at location4, spanner1 is not located at location5, spanner1 is not located at location9, spanner2 is not at gate, spanner2 is not at location1, spanner2 is not at location3, spanner2 is not at location8, spanner2 is not at shed, spanner2 is not currently at location4, spanner2 is not currently at location7, spanner2 is not located at location2, spanner2 is not located at location5, spanner2 is not located at location6, spanner2 is not located at location9, spanner2 is not usable, spanner3 is carried by bob, spanner3 is not at gate, spanner3 is not at location2, spanner3 is not at location3, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not currently at location1, spanner3 is not currently at location8, spanner3 is not currently at shed, spanner3 is not functional, spanner3 is not located at location5, spanner4 is not at location5, spanner4 is not at location6, spanner4 is not at location8, spanner4 is not currently at location3, spanner4 is not currently at location4, spanner4 is not currently at location7, spanner4 is not currently at location9, spanner4 is not functional, spanner4 is not located at gate, spanner4 is not located at location1, spanner4 is not located at location2, spanner4 is not located at shed, spanner5 is carried by bob, spanner5 is not at location1, spanner5 is not at location4, spanner5 is not at location6, spanner5 is not at location8, spanner5 is not at location9, spanner5 is not currently at shed, spanner5 is not functional, spanner5 is not located at gate, spanner5 is not located at location2, spanner5 is not located at location3, spanner5 is not located at location5, spanner5 is not located at location7, tightening of nut1 is complete, tightening of nut3 is complete, tightening of nut4 is complete and tightening of nut5 is incomplete", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, then to location4 from location3, and subsequently to location5 from location4. At location5, Bob picks up spanner4, proceeds to location6, and collects spanner1. He then moves to location7, where he picks up spanner5, and continues to location8. At location8, Bob collects spanner3 and spanner2. From location8, he heads to location9 and then to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, list all valid properties (including those with and without negations). If there are none, indicate 'None'.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a link is present between the shed and location1. Bob is currently located at the shed. Furthermore, location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is linked to location5. Moreover, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is currently present and is loose, while nut2 is also at the gate but not secured. Similarly, nut3 is at the gate and is loose, nut4 is at the gate but not secured, and nut5 is at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and can be found at location8, while spanner3 is also usable and located at location8. Spanner4 is at location5 and is functional, and spanner5 is functional and located at location7."}
{"question_id": "bd6cdd7b-d2fa-46a4-a558-c80f7f2a4cf6", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, spanner5 is picked up by bob from location1, bob picks up spanner4 from location1, bob walks to location2 from location1, bob walks to location3 from location2, bob walks from location3 to location4, bob picks up spanner1 from location4, bob walks from location4 to location5, from location5 to location6, bob walks and bob picks up spanner3 from location6 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location2 does not exist, a link between gate and location5 does not exist, a link between gate and location6 does not exist, a link between gate and shed does not exist, a link between location1 and location5 does not exist, a link between location1 and location7 does not exist, a link between location2 and gate does not exist, a link between location2 and location4 does not exist, a link between location2 and location6 does not exist, a link between location2 and location9 does not exist, a link between location3 and location1 does not exist, a link between location3 and location6 does not exist, a link between location4 and location3 does not exist, a link between location4 and location5 exists, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location4 does not exist, a link between location5 and location7 does not exist, a link between location5 and location8 does not exist, a link between location6 and location2 does not exist, a link between location6 and location3 does not exist, a link between location6 and location7 exists, a link between location6 and shed does not exist, a link between location7 and location3 does not exist, a link between location7 and location5 does not exist, a link between location7 and location6 does not exist, a link between location8 and location5 does not exist, a link between location8 and location6 does not exist, a link between location8 and location9 exists, a link between location9 and gate exists, a link between location9 and location6 does not exist, a link between shed and location3 does not exist, a link between shed and location5 does not exist, a link between shed and location9 does not exist, bob is carrying spanner1, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, bob is currently at location6, bob is not at location3, bob is not carrying spanner2, bob is not currently at location2, bob is not currently at location4, bob is not currently at location5, bob is not currently at location7, bob is not currently at location9, bob is not currently at shed, bob is not located at gate, bob is not located at location1, bob is not located at location8, gate and location3 are not linked, gate and location4 are not linked, gate and location7 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate is not linked to location1, location1 and gate are not linked, location1 and location2 are linked, location1 and location4 are not linked, location1 and location6 are not linked, location1 and location8 are not linked, location1 is not linked to location3, location1 is not linked to location9, location1 is not linked to shed, location2 and location5 are not linked, location2 and location7 are not linked, location2 is linked to location3, location2 is not linked to location1, location2 is not linked to location8, location2 is not linked to shed, location3 and location4 are linked, location3 and location9 are not linked, location3 and shed are not linked, location3 is not linked to gate, location3 is not linked to location2, location3 is not linked to location5, location3 is not linked to location7, location3 is not linked to location8, location4 and location1 are not linked, location4 and location7 are not linked, location4 and shed are not linked, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location6, location5 and gate are not linked, location5 and location6 are linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is not linked to location3, location6 and gate are not linked, location6 and location1 are not linked, location6 and location4 are not linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 is not linked to location5, location7 and location2 are not linked, location7 and location9 are not linked, location7 and shed are not linked, location7 is linked to location8, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location4, location8 and gate are not linked, location8 and location1 are not linked, location8 and location2 are not linked, location8 and location3 are not linked, location8 and shed are not linked, location8 is not linked to location4, location8 is not linked to location7, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location4 are not linked, location9 and location5 are not linked, location9 and location7 are not linked, location9 and shed are not linked, location9 is not linked to location1, location9 is not linked to location8, nut1 is located at gate, nut1 is loose, nut1 is not at location2, nut1 is not at location3, nut1 is not at location5, nut1 is not at location6, nut1 is not at location9, nut1 is not currently at location4, nut1 is not currently at location7, nut1 is not currently at location8, nut1 is not located at location1, nut1 is not located at shed, nut1 is not tightened, nut2 is located at gate, nut2 is loose, nut2 is not at location1, nut2 is not at location2, nut2 is not at location9, nut2 is not at shed, nut2 is not currently at location4, nut2 is not currently at location5, nut2 is not currently at location6, nut2 is not located at location3, nut2 is not located at location7, nut2 is not located at location8, nut3 is currently at gate, nut3 is loose, nut3 is not currently at location5, nut3 is not currently at location6, nut3 is not currently at location7, nut3 is not currently at location9, nut3 is not located at location1, nut3 is not located at location2, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location8, nut3 is not located at shed, nut3 is not tightened, nut4 is at gate, nut4 is not at location1, nut4 is not at location2, nut4 is not at location3, nut4 is not at location5, nut4 is not at location6, nut4 is not at location8, nut4 is not at location9, nut4 is not currently at location7, nut4 is not currently at shed, nut4 is not located at location4, nut4 is not secured, nut4 is not tightened, nut5 is at gate, nut5 is loose, nut5 is not at location6, nut5 is not currently at location2, nut5 is not currently at location9, nut5 is not located at location1, nut5 is not located at location3, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at location8, nut5 is not located at shed, nut5 is not tightened, shed and gate are not linked, shed and location1 are linked, shed and location2 are not linked, shed and location8 are not linked, shed is not linked to location4, shed is not linked to location6, shed is not linked to location7, spanner1 is functional, spanner1 is not at location2, spanner1 is not at location5, spanner1 is not at location6, spanner1 is not at location7, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location4, spanner1 is not currently at location8, spanner1 is not currently at shed, spanner1 is not located at location3, spanner1 is not located at location9, spanner2 is located at location7, spanner2 is not at gate, spanner2 is not at location3, spanner2 is not at location8, spanner2 is not at shed, spanner2 is not currently at location1, spanner2 is not currently at location2, spanner2 is not currently at location5, spanner2 is not currently at location9, spanner2 is not located at location4, spanner2 is not located at location6, spanner2 is usable, spanner3 is functional, spanner3 is not at location7, spanner3 is not at shed, spanner3 is not currently at location1, spanner3 is not currently at location4, spanner3 is not currently at location5, spanner3 is not currently at location9, spanner3 is not located at gate, spanner3 is not located at location2, spanner3 is not located at location3, spanner3 is not located at location6, spanner3 is not located at location8, spanner4 can be used, spanner4 is not at location1, spanner4 is not at location4, spanner4 is not at location8, spanner4 is not currently at gate, spanner4 is not currently at location2, spanner4 is not currently at location5, spanner4 is not located at location3, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at location9, spanner4 is not located at shed, spanner5 is functional, spanner5 is not at location7, spanner5 is not currently at gate, spanner5 is not currently at location1, spanner5 is not currently at location3, spanner5 is not currently at location4, spanner5 is not currently at location5, spanner5 is not currently at shed, spanner5 is not located at location2, spanner5 is not located at location6, spanner5 is not located at location8, spanner5 is not located at location9 and tightening of nut2 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, spanner1 is located at location4, spanner1 is usable, spanner2 is located at location7, spanner2 is usable, spanner3 can be used, spanner3 is located at location6, spanner4 is at location1, spanner4 is functional, spanner5 is currently at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then picks up spanner5 and spanner4 from location1. He proceeds to walk to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1, and then walks to location5 and then to location6, where he picks up spanner3, ultimately reaching the current state. In this state, list all valid properties (including those with and without negations) that hold true, or indicate None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, another connection exists between location6 and location7, and a connection also exists between location7 and location8. Additionally, a connection is present between the shed and location1. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location8 is connected to location9, and location9 is connected to the gate. At the gate, nut1 is present and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is located at location4 and is in working condition, spanner2 is at location7 and is usable, spanner3 is located at location6 and can be used, spanner4 is at location1 and is functional, and spanner5 is currently at location1 and is usable."}
{"question_id": "d8bc5a56-52ba-4935-b8c5-b145fa83b834", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location2 does not exist, a link between gate and location4 does not exist, a link between gate and location7 does not exist, a link between gate and location8 does not exist, a link between location1 and location2 exists, a link between location1 and location3 does not exist, a link between location1 and location8 does not exist, a link between location1 and location9 does not exist, a link between location2 and location3 exists, a link between location2 and location6 does not exist, a link between location2 and location8 does not exist, a link between location2 and location9 does not exist, a link between location3 and gate does not exist, a link between location3 and location4 exists, a link between location3 and location7 does not exist, a link between location3 and location8 does not exist, a link between location3 and shed does not exist, a link between location4 and gate does not exist, a link between location4 and location3 does not exist, a link between location4 and location7 does not exist, a link between location4 and shed does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location7 does not exist, a link between location5 and location8 does not exist, a link between location5 and location9 does not exist, a link between location5 and shed does not exist, a link between location6 and location3 does not exist, a link between location6 and location5 does not exist, a link between location6 and location7 exists, a link between location6 and location8 does not exist, a link between location7 and location2 does not exist, a link between location8 and gate does not exist, a link between location8 and location1 does not exist, a link between location8 and location3 does not exist, a link between location8 and location5 does not exist, a link between location9 and location2 does not exist, a link between location9 and location7 does not exist, a link between shed and location3 does not exist, a link between shed and location6 does not exist, a link between shed and location8 does not exist, bob is at location1, bob is not at gate, bob is not at location9, bob is not currently at location3, bob is not currently at location7, bob is not currently at shed, bob is not located at location2, bob is not located at location4, bob is not located at location5, bob is not located at location6, bob is not located at location8, gate and location9 are not linked, gate is not linked to location1, gate is not linked to location3, gate is not linked to location5, gate is not linked to location6, gate is not linked to shed, location1 and gate are not linked, location1 and location5 are not linked, location1 and location7 are not linked, location1 is not linked to location4, location1 is not linked to location6, location1 is not linked to shed, location2 and location1 are not linked, location2 and location4 are not linked, location2 is not linked to gate, location2 is not linked to location5, location2 is not linked to location7, location2 is not linked to shed, location3 and location2 are not linked, location3 and location6 are not linked, location3 and location9 are not linked, location3 is not linked to location1, location3 is not linked to location5, location4 and location2 are not linked, location4 and location5 are linked, location4 and location8 are not linked, location4 and location9 are not linked, location4 is not linked to location1, location4 is not linked to location6, location5 and location3 are not linked, location5 and location4 are not linked, location5 is linked to location6, location6 is not linked to gate, location6 is not linked to location1, location6 is not linked to location2, location6 is not linked to location4, location6 is not linked to location9, location6 is not linked to shed, location7 and gate are not linked, location7 and location1 are not linked, location7 and location4 are not linked, location7 and location5 are not linked, location7 and location6 are not linked, location7 and shed are not linked, location7 is linked to location8, location7 is not linked to location3, location7 is not linked to location9, location8 and location2 are not linked, location8 and location7 are not linked, location8 and location9 are linked, location8 and shed are not linked, location8 is not linked to location4, location8 is not linked to location6, location9 and location8 are not linked, location9 and shed are not linked, location9 is linked to gate, location9 is not linked to location1, location9 is not linked to location3, location9 is not linked to location4, location9 is not linked to location5, location9 is not linked to location6, nut1 is at gate, nut1 is loose, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location5, nut1 is not currently at location8, nut1 is not currently at location9, nut1 is not currently at shed, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location6, nut1 is not located at location7, nut1 is not tightened, nut2 is at gate, nut2 is loose, nut2 is not at location4, nut2 is not at location5, nut2 is not at location9, nut2 is not at shed, nut2 is not currently at location3, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not located at location1, nut2 is not located at location2, nut2 is not located at location8, nut2 is not tightened, nut3 is currently at gate, nut3 is loose, nut3 is not at location2, nut3 is not currently at location1, nut3 is not currently at location4, nut3 is not currently at location5, nut3 is not currently at location6, nut3 is not currently at location7, nut3 is not currently at location9, nut3 is not located at location3, nut3 is not located at location8, nut3 is not located at shed, nut3 is not tightened, nut4 is located at gate, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not located at location1, nut4 is not located at location2, nut4 is not located at location3, nut4 is not located at location4, nut4 is not located at location5, nut4 is not located at location7, nut4 is not located at location8, nut4 is not located at shed, nut4 is not secured, nut5 is currently at gate, nut5 is loose, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location3, nut5 is not currently at location4, nut5 is not currently at location5, nut5 is not currently at location6, nut5 is not currently at location7, nut5 is not currently at shed, nut5 is not located at location2, nut5 is not tightened, shed and gate are not linked, shed and location9 are not linked, shed is linked to location1, shed is not linked to location2, shed is not linked to location4, shed is not linked to location5, shed is not linked to location7, spanner1 is currently at location4, spanner1 is not at location2, spanner1 is not at location3, spanner1 is not at location6, spanner1 is not at location9, spanner1 is not carried by bob, spanner1 is not currently at gate, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not currently at shed, spanner1 is not located at location1, spanner1 is not located at location5, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner2 is not at location1, spanner2 is not at location8, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not carried by bob, spanner2 is not currently at gate, spanner2 is not currently at location2, spanner2 is not currently at location3, spanner2 is not currently at location4, spanner2 is not currently at location5, spanner2 is not located at location6, spanner3 can be used, spanner3 is located at location6, spanner3 is not at location3, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not carried by bob, spanner3 is not currently at location1, spanner3 is not currently at location2, spanner3 is not currently at location4, spanner3 is not currently at location5, spanner3 is not located at gate, spanner3 is not located at location8, spanner3 is not located at shed, spanner4 is at location1, spanner4 is functional, spanner4 is not at location2, spanner4 is not at location4, spanner4 is not at location8, spanner4 is not carried by bob, spanner4 is not currently at gate, spanner4 is not currently at location3, spanner4 is not currently at location5, spanner4 is not currently at location6, spanner4 is not located at location7, spanner4 is not located at location9, spanner4 is not located at shed, spanner5 is located at location1, spanner5 is not at location5, spanner5 is not carried by bob, spanner5 is not currently at gate, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not currently at location7, spanner5 is not currently at location9, spanner5 is not currently at shed, spanner5 is not located at location2, spanner5 is not located at location4, spanner5 is not located at location8, spanner5 is usable and tightening of nut4 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, spanner1 is located at location4, spanner1 is usable, spanner2 is located at location7, spanner2 is usable, spanner3 can be used, spanner3 is located at location6, spanner4 is at location1, spanner4 is functional, spanner5 is currently at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: bob moves from the shed to location1 to attain the current state. In this state, enumerate all applicable properties of the state (including both affirmative and negated properties). Indicate None if there are no such properties.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, another connection exists between location6 and location7, and a connection also exists between location7 and location8. Additionally, a link is present between the shed and location1. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is located at location4 and is in working condition, spanner2 is at location7 and is usable, spanner3 is located at location6 and can be used, spanner4 is at location1 and is functional, and spanner5 is currently at location1 and is usable."}
{"question_id": "7f4f7f14-cbe3-488b-88d6-903030ed06af", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, spanner3 is picked up by bob from location2, bob walks to location3 from location2, bob picks up spanner5 from location3, from location3, bob picks up spanner1, bob walks to location4 from location3, bob walks to location5 from location4, bob picks up spanner2 from location5 and from location5 to location6, bob walks to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between location9 and gate exists, bob is at location6, bob is carrying spanner2, bob is carrying spanner3, location2 is linked to location3, location7 and location8 are linked, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is not secured, shed and location1 are linked, spanner1 is carried by bob, spanner1 is usable, spanner2 is functional, spanner3 is functional, spanner4 is currently at location6, spanner4 is functional, spanner5 is carried by bob and spanner5 is usable", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location8 and location9 exists, a link between location9 and gate exists, bob is at shed, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location6 is linked to location7, location7 and location8 are linked, nut1 is currently at gate, nut1 is not secured, nut2 is at gate, nut2 is loose, nut3 is at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is currently at location5, spanner2 is functional, spanner3 is located at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is at location3 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. Next, he walks from location2 to location3, picks up spanner5, and also collects spanner1 from location3. He then proceeds to location4 from location3, followed by location5 from location4, where he picks up spanner2. Finally, he walks from location5 to location6 to reach the current state. In this state, list all valid properties that do not involve negations. If there are none, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location8 and location9, and a connection also exists between location9 and the gate. Bob is currently at the shed. Additionally, location3 is connected to location4, location4 is linked to location5, location5 is connected to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is loose, nut3 is not secured, nut4 is loose, and nut5 is also loose. The shed is connected to location1. Spanner1 is at location3 and is in working condition, spanner2 is at location5 and functional, spanner3 is at location2 and usable, spanner4 is at location6 and functional, and spanner5 is at location3 and usable."}
{"question_id": "81c985b9-46da-4298-b4ff-c2976af5785e", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, from location2, bob picks up spanner4, from location2, bob picks up spanner3, bob walks to location3 from location2, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks to location6 from location5, spanner5 is picked up by bob from location6, bob picks up spanner2 from location6, from location6 to location7, bob walks, from location7 to location8, bob walks, bob picks up spanner1 from location8, bob walks from location8 to location9, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location5 does not exist, a link between gate and location6 does not exist, a link between gate and shed does not exist, a link between location1 and location4 does not exist, a link between location1 and location5 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location4 does not exist, a link between location2 and shed does not exist, a link between location3 and location6 does not exist, a link between location3 and location7 does not exist, a link between location3 and location9 does not exist, a link between location4 and gate does not exist, a link between location4 and location9 does not exist, a link between location4 and shed does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and location4 does not exist, a link between location5 and location8 does not exist, a link between location5 and shed does not exist, a link between location6 and location4 does not exist, a link between location7 and location2 does not exist, a link between location7 and location3 does not exist, a link between location7 and location5 does not exist, a link between location8 and gate does not exist, a link between location9 and location1 does not exist, a link between location9 and location7 does not exist, a link between location9 and location8 does not exist, a link between shed and location4 does not exist, a link between shed and location5 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, a link between shed and location9 does not exist, bob is not at location5, bob is not at location6, bob is not at location9, bob is not currently at location2, bob is not currently at location3, bob is not currently at location4, bob is not located at location1, bob is not located at location7, bob is not located at location8, bob is not located at shed, gate and location2 are not linked, gate and location3 are not linked, gate and location7 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate is not linked to location1, gate is not linked to location4, location1 and location7 are not linked, location1 and location8 are not linked, location1 and shed are not linked, location1 is not linked to gate, location1 is not linked to location3, location1 is not linked to location6, location2 and location5 are not linked, location2 and location6 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 is not linked to location1, location2 is not linked to location9, location3 and location2 are not linked, location3 and shed are not linked, location3 is not linked to gate, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location8, location4 and location2 are not linked, location4 and location3 are not linked, location4 and location7 are not linked, location4 is not linked to location1, location4 is not linked to location6, location4 is not linked to location8, location5 and location9 are not linked, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location7, location6 and gate are not linked, location6 and location2 are not linked, location6 and location3 are not linked, location6 and location5 are not linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 is not linked to location1, location6 is not linked to shed, location7 and location4 are not linked, location7 and location9 are not linked, location7 and shed are not linked, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location6, location8 and location2 are not linked, location8 and location3 are not linked, location8 and location5 are not linked, location8 is not linked to location1, location8 is not linked to location4, location8 is not linked to location6, location8 is not linked to location7, location8 is not linked to shed, location9 and location3 are not linked, location9 and location5 are not linked, location9 is not linked to location2, location9 is not linked to location4, location9 is not linked to location6, location9 is not linked to shed, nut1 is not at location4, nut1 is not at location5, nut1 is not at shed, nut1 is not currently at location2, nut1 is not located at location1, nut1 is not located at location3, nut1 is not located at location6, nut1 is not located at location7, nut1 is not located at location8, nut1 is not located at location9, nut1 is not loose, nut2 is not at location1, nut2 is not at location3, nut2 is not at location4, nut2 is not at location5, nut2 is not at location7, nut2 is not at location8, nut2 is not at shed, nut2 is not currently at location9, nut2 is not located at location2, nut2 is not located at location6, nut2 is not loose, nut3 is not at location1, nut3 is not at location4, nut3 is not at location9, nut3 is not currently at location3, nut3 is not currently at shed, nut3 is not located at location2, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location7, nut3 is not located at location8, nut3 is not loose, nut4 is not at location3, nut4 is not at location8, nut4 is not currently at location2, nut4 is not currently at location7, nut4 is not currently at location9, nut4 is not currently at shed, nut4 is not located at location1, nut4 is not located at location4, nut4 is not located at location5, nut4 is not located at location6, nut4 is not loose, nut5 is not at location1, nut5 is not at location2, nut5 is not at location3, nut5 is not at location6, nut5 is not at location7, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location4, nut5 is not currently at shed, nut5 is not located at location5, shed and gate are not linked, shed and location8 are not linked, shed is not linked to location2, shed is not linked to location3, spanner1 is not at gate, spanner1 is not at location2, spanner1 is not at location5, spanner1 is not at location8, spanner1 is not currently at location1, spanner1 is not currently at location4, spanner1 is not currently at location6, spanner1 is not currently at location7, spanner1 is not currently at location9, spanner1 is not currently at shed, spanner1 is not located at location3, spanner2 can't be used, spanner2 is not at location1, spanner2 is not at location4, spanner2 is not at location8, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not currently at location2, spanner2 is not currently at location3, spanner2 is not located at gate, spanner2 is not located at location5, spanner2 is not located at location6, spanner2 is not located at location7, spanner3 is not at gate, spanner3 is not at location2, spanner3 is not at location6, spanner3 is not at location8, spanner3 is not currently at location3, spanner3 is not currently at location4, spanner3 is not functional, spanner3 is not located at location1, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at location9, spanner3 is not located at shed, spanner4 can't be used, spanner4 is not at location2, spanner4 is not at location7, spanner4 is not at location9, spanner4 is not currently at gate, spanner4 is not currently at location1, spanner4 is not currently at location3, spanner4 is not currently at location8, spanner4 is not currently at shed, spanner4 is not located at location4, spanner4 is not located at location5, spanner4 is not located at location6, spanner5 is not at gate, spanner5 is not at location3, spanner5 is not at location7, spanner5 is not at location8, spanner5 is not currently at location2, spanner5 is not currently at location5, spanner5 is not currently at location6, spanner5 is not currently at shed, spanner5 is not functional, spanner5 is not located at location1, spanner5 is not located at location4, spanner5 is not located at location9 and tightening of nut5 is incomplete", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, spanner1 is currently at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 can be used, spanner3 is currently at location2, spanner4 is at location2, spanner4 is usable, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. From location2, Bob proceeds to location3, then to location4, and subsequently to location5. He continues walking to location6, where he picks up spanner5 and spanner2. Bob then walks to location7 and then to location8, where he collects spanner1. He proceeds to location9 and then to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, list all valid properties that involve negations; if none exist, state None.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, a connection between location8 and location9 exists, a connection between the shed and location1 is present, bob is situated at the shed, location1 is connected to location2, location2 is connected to location3, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 is connected to location8, location9 and the gate are connected, nut1 is situated at the gate, nut1 is not secure, nut2 is currently situated at the gate, nut2 is loose, nut3 is located at the gate, nut3 is loose, nut4 is located at the gate, nut4 is loose, nut5 is currently situated at the gate, nut5 is not secure, spanner1 is currently situated at location8, spanner1 is in working condition, spanner2 is at location6, spanner2 is in good working order, spanner3 is in working condition, spanner3 is currently situated at location2, spanner4 is at location2, spanner4 is in working condition, spanner5 is in good working order and spanner5 is located at location6."}
{"question_id": "41c559a4-9a72-4245-a6de-68c67205acd4", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "a link between location9 and gate exists, bob is currently at location1, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, nut1 is at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 can be used, spanner1 is located at location6, spanner2 can be used, spanner2 is at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is at location7 and spanner5 is usable", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: from the shed to location1, Bob walks to attain the current state. In this state, list all the valid properties that do not include negations. If there are no such properties, write None.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a connection is present between the shed and location1. Bob is currently located at the shed. Location1 is connected to location2, and location2 is linked to location3. Furthermore, location3 is connected to location4, and location4 is linked to location5. Location7 is connected to location8, and location8 is linked to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is currently present and is loose. Similarly, nut2 is at the gate but is not secured, while nut3 is also at the gate and is loose. Nut4 is at the gate and is not secured, and nut5 is also at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and is situated at location8, while spanner3 is also usable and is located at location8. Spanner4 is at location5 and is functional, and spanner5 is also functional and is located at location7."}
{"question_id": "966e70d5-1589-446b-8126-692ee13438df", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location4 does not exist, a link between gate and location5 does not exist, a link between gate and location8 does not exist, a link between gate and location9 does not exist, a link between gate and shed does not exist, a link between location1 and gate does not exist, a link between location1 and shed does not exist, a link between location2 and location5 does not exist, a link between location2 and location7 does not exist, a link between location2 and location8 does not exist, a link between location2 and location9 does not exist, a link between location3 and gate does not exist, a link between location3 and location7 does not exist, a link between location3 and location9 does not exist, a link between location4 and location5 exists, a link between location4 and location7 does not exist, a link between location4 and location9 does not exist, a link between location5 and location4 does not exist, a link between location5 and location6 exists, a link between location6 and location4 does not exist, a link between location6 and location7 exists, a link between location6 and shed does not exist, a link between location7 and location1 does not exist, a link between location7 and location4 does not exist, a link between location7 and location5 does not exist, a link between location7 and location6 does not exist, a link between location8 and location2 does not exist, a link between location8 and location4 does not exist, a link between location8 and location6 does not exist, a link between location8 and location9 exists, a link between location8 and shed does not exist, a link between location9 and gate exists, a link between location9 and location6 does not exist, a link between location9 and location8 does not exist, a link between shed and gate does not exist, a link between shed and location3 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, bob is at location1, bob is not at shed, bob is not carrying spanner1, bob is not carrying spanner3, bob is not carrying spanner4, bob is not carrying spanner5, bob is not currently at gate, bob is not currently at location6, bob is not currently at location9, bob is not located at location2, bob is not located at location3, bob is not located at location4, bob is not located at location5, bob is not located at location7, bob is not located at location8, gate and location1 are not linked, gate is not linked to location2, gate is not linked to location3, gate is not linked to location6, gate is not linked to location7, location1 and location3 are not linked, location1 and location5 are not linked, location1 and location6 are not linked, location1 and location7 are not linked, location1 and location8 are not linked, location1 and location9 are not linked, location1 is linked to location2, location1 is not linked to location4, location2 and gate are not linked, location2 and location4 are not linked, location2 and location6 are not linked, location2 and shed are not linked, location2 is linked to location3, location2 is not linked to location1, location3 and location4 are linked, location3 and location6 are not linked, location3 and shed are not linked, location3 is not linked to location1, location3 is not linked to location2, location3 is not linked to location5, location3 is not linked to location8, location4 and location1 are not linked, location4 and location6 are not linked, location4 and shed are not linked, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location3, location4 is not linked to location8, location5 and location3 are not linked, location5 and location7 are not linked, location5 and location8 are not linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location2, location6 and location5 are not linked, location6 is not linked to gate, location6 is not linked to location1, location6 is not linked to location2, location6 is not linked to location3, location6 is not linked to location8, location6 is not linked to location9, location7 and gate are not linked, location7 and location3 are not linked, location7 and location8 are linked, location7 is not linked to location2, location7 is not linked to location9, location7 is not linked to shed, location8 and location3 are not linked, location8 and location5 are not linked, location8 is not linked to gate, location8 is not linked to location1, location8 is not linked to location7, location9 and location5 are not linked, location9 and location7 are not linked, location9 and shed are not linked, location9 is not linked to location1, location9 is not linked to location2, location9 is not linked to location3, location9 is not linked to location4, nut1 is at gate, nut1 is loose, nut1 is not at location2, nut1 is not at location4, nut1 is not at location5, nut1 is not at location6, nut1 is not at shed, nut1 is not currently at location1, nut1 is not currently at location3, nut1 is not currently at location8, nut1 is not located at location7, nut1 is not located at location9, nut1 is not tightened, nut2 is at gate, nut2 is loose, nut2 is not at location4, nut2 is not at location7, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location9, nut2 is not located at location3, nut2 is not located at location5, nut2 is not located at location6, nut2 is not located at location8, nut2 is not located at shed, nut3 is at gate, nut3 is not at location2, nut3 is not at location5, nut3 is not at location6, nut3 is not at location8, nut3 is not at location9, nut3 is not currently at location1, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location7, nut3 is not located at shed, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut4 is not at location2, nut4 is not at location3, nut4 is not at location4, nut4 is not at location5, nut4 is not currently at location1, nut4 is not currently at location9, nut4 is not located at location6, nut4 is not located at location7, nut4 is not located at location8, nut4 is not located at shed, nut4 is not tightened, nut5 is currently at gate, nut5 is not at location4, nut5 is not at location6, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location3, nut5 is not located at location2, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at location8, nut5 is not located at shed, nut5 is not secured, shed and location4 are not linked, shed and location8 are not linked, shed is linked to location1, shed is not linked to location2, shed is not linked to location5, shed is not linked to location9, spanner1 is at location3, spanner1 is not at gate, spanner1 is not at location2, spanner1 is not currently at location1, spanner1 is not currently at location5, spanner1 is not currently at location9, spanner1 is not currently at shed, spanner1 is not located at location4, spanner1 is not located at location6, spanner1 is not located at location7, spanner1 is not located at location8, spanner1 is usable, spanner2 is at location5, spanner2 is not at location1, spanner2 is not at location2, spanner2 is not at location7, spanner2 is not at shed, spanner2 is not carried by bob, spanner2 is not currently at gate, spanner2 is not currently at location3, spanner2 is not currently at location8, spanner2 is not located at location4, spanner2 is not located at location6, spanner2 is not located at location9, spanner2 is usable, spanner3 is currently at location2, spanner3 is functional, spanner3 is not at gate, spanner3 is not at location1, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at location8, spanner3 is not currently at location7, spanner3 is not currently at location9, spanner3 is not currently at shed, spanner3 is not located at location3, spanner3 is not located at location5, spanner4 is at location6, spanner4 is functional, spanner4 is not at location7, spanner4 is not at location8, spanner4 is not at location9, spanner4 is not currently at location1, spanner4 is not currently at location2, spanner4 is not currently at location3, spanner4 is not currently at shed, spanner4 is not located at gate, spanner4 is not located at location4, spanner4 is not located at location5, spanner5 is functional, spanner5 is located at location3, spanner5 is not at location2, spanner5 is not at location7, spanner5 is not currently at location4, spanner5 is not currently at location6, spanner5 is not currently at location8, spanner5 is not currently at location9, spanner5 is not located at gate, spanner5 is not located at location1, spanner5 is not located at location5, spanner5 is not located at shed, tightening of nut2 is incomplete, tightening of nut3 is incomplete and tightening of nut5 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location8 and location9 exists, a link between location9 and gate exists, bob is at shed, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location6 is linked to location7, location7 and location8 are linked, nut1 is currently at gate, nut1 is not secured, nut2 is at gate, nut2 is loose, nut3 is at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is currently at location5, spanner2 is functional, spanner3 is located at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is at location3 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: from the shed to location1, Bob proceeds to the current state. In this state, enumerate all applicable properties (including both affirmative and negated ones). If none exist, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location8 and location9, and a connection also exists between location9 and the gate. Bob is currently at the shed. Additionally, location3 is connected to location4, location4 is linked to location5, location5 is connected to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is loose, nut3 is not secured, nut4 is loose, and nut5 is also loose. The shed is connected to location1. Spanner1 is at location3 and is in working condition, spanner2 is at location5 and functional, spanner3 is at location2 and usable, spanner4 is at location6 and functional, and spanner5 is at location3 and usable."}
{"question_id": "7434d221-e3e8-481b-a4a6-5060d8c4df1d", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "a link between gate and location4 does not exist, a link between gate and location5 does not exist, a link between gate and location7 does not exist, a link between gate and location9 does not exist, a link between location1 and location6 does not exist, a link between location1 and location7 does not exist, a link between location1 and location8 does not exist, a link between location1 and location9 does not exist, a link between location1 and shed does not exist, a link between location2 and shed does not exist, a link between location3 and location8 does not exist, a link between location4 and gate does not exist, a link between location4 and location1 does not exist, a link between location4 and location3 does not exist, a link between location4 and location7 does not exist, a link between location5 and gate does not exist, a link between location5 and location3 does not exist, a link between location5 and shed does not exist, a link between location6 and gate does not exist, a link between location6 and location1 does not exist, a link between location6 and location2 does not exist, a link between location7 and location1 does not exist, a link between location7 and location4 does not exist, a link between location7 and location5 does not exist, a link between location7 and location6 does not exist, a link between location8 and location3 does not exist, a link between location8 and location5 does not exist, a link between location9 and location1 does not exist, a link between location9 and location2 does not exist, a link between location9 and location3 does not exist, a link between location9 and location4 does not exist, a link between location9 and location8 does not exist, a link between location9 and shed does not exist, a link between shed and location2 does not exist, a link between shed and location5 does not exist, a link between shed and location9 does not exist, bob is not at gate, bob is not at location5, bob is not at location6, bob is not carrying spanner3, bob is not currently at location2, bob is not currently at location3, bob is not currently at location4, bob is not currently at location7, bob is not located at location8, bob is not located at location9, bob is not located at shed, gate and location1 are not linked, gate and location2 are not linked, gate and location3 are not linked, gate and location6 are not linked, gate and location8 are not linked, gate and shed are not linked, location1 and gate are not linked, location1 and location5 are not linked, location1 is not linked to location3, location1 is not linked to location4, location2 and location1 are not linked, location2 and location5 are not linked, location2 and location6 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 is not linked to gate, location2 is not linked to location4, location2 is not linked to location9, location3 and gate are not linked, location3 and location7 are not linked, location3 and location9 are not linked, location3 is not linked to location1, location3 is not linked to location2, location3 is not linked to location5, location3 is not linked to location6, location3 is not linked to shed, location4 and location2 are not linked, location4 and location6 are not linked, location4 is not linked to location8, location4 is not linked to location9, location4 is not linked to shed, location5 and location4 are not linked, location5 and location9 are not linked, location5 is not linked to location1, location5 is not linked to location2, location5 is not linked to location7, location5 is not linked to location8, location6 and location4 are not linked, location6 and location5 are not linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 is not linked to location3, location6 is not linked to shed, location7 and gate are not linked, location7 and location3 are not linked, location7 and shed are not linked, location7 is not linked to location2, location7 is not linked to location9, location8 and location4 are not linked, location8 and location6 are not linked, location8 and shed are not linked, location8 is not linked to gate, location8 is not linked to location1, location8 is not linked to location2, location8 is not linked to location7, location9 and location6 are not linked, location9 and location7 are not linked, location9 is not linked to location5, nut1 is not at location1, nut1 is not at location4, nut1 is not at location6, nut1 is not at location7, nut1 is not currently at location3, nut1 is not currently at location8, nut1 is not located at location2, nut1 is not located at location5, nut1 is not located at location9, nut1 is not located at shed, nut2 is not at location7, nut2 is not at location9, nut2 is not currently at location2, nut2 is not currently at location3, nut2 is not currently at location4, nut2 is not currently at location6, nut2 is not currently at location8, nut2 is not currently at shed, nut2 is not located at location1, nut2 is not located at location5, nut3 is not at location2, nut3 is not at shed, nut3 is not currently at location1, nut3 is not currently at location4, nut3 is not currently at location7, nut3 is not located at location3, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location8, nut3 is not located at location9, nut3 is not tightened, nut4 is not at location1, nut4 is not at location2, nut4 is not at location7, nut4 is not currently at location4, nut4 is not currently at location9, nut4 is not currently at shed, nut4 is not located at location3, nut4 is not located at location5, nut4 is not located at location6, nut4 is not located at location8, nut5 is not at location1, nut5 is not at location3, nut5 is not at location4, nut5 is not at location5, nut5 is not at location8, nut5 is not at location9, nut5 is not at shed, nut5 is not currently at location2, nut5 is not currently at location6, nut5 is not currently at location7, nut5 is not tightened, shed and location3 are not linked, shed and location4 are not linked, shed and location8 are not linked, shed is not linked to gate, shed is not linked to location6, shed is not linked to location7, spanner1 is not at location3, spanner1 is not at location6, spanner1 is not at location9, spanner1 is not carried by bob, spanner1 is not currently at location1, spanner1 is not currently at location2, spanner1 is not currently at location5, spanner1 is not currently at location7, spanner1 is not currently at shed, spanner1 is not located at gate, spanner1 is not located at location4, spanner2 is not at location3, spanner2 is not at location7, spanner2 is not at shed, spanner2 is not carried by bob, spanner2 is not currently at location1, spanner2 is not currently at location8, spanner2 is not currently at location9, spanner2 is not located at gate, spanner2 is not located at location2, spanner2 is not located at location4, spanner2 is not located at location5, spanner3 is not currently at gate, spanner3 is not currently at location3, spanner3 is not currently at location5, spanner3 is not currently at location7, spanner3 is not currently at shed, spanner3 is not located at location1, spanner3 is not located at location4, spanner3 is not located at location6, spanner3 is not located at location8, spanner3 is not located at location9, spanner4 is not at gate, spanner4 is not at location1, spanner4 is not at location5, spanner4 is not at location6, spanner4 is not at location9, spanner4 is not carried by bob, spanner4 is not currently at location4, spanner4 is not currently at location7, spanner4 is not currently at shed, spanner4 is not located at location3, spanner4 is not located at location8, spanner5 is not at location4, spanner5 is not at location5, spanner5 is not at location8, spanner5 is not at location9, spanner5 is not carried by bob, spanner5 is not currently at gate, spanner5 is not currently at location2, spanner5 is not currently at location7, spanner5 is not currently at shed, spanner5 is not located at location1, spanner5 is not located at location3, tightening of nut1 is incomplete, tightening of nut2 is incomplete and tightening of nut4 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, spanner1 is currently at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 can be used, spanner3 is currently at location2, spanner4 is at location2, spanner4 is usable, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: from the shed to location1, bob moves to attain the current state. In this state, identify all valid properties of the state that include negations, or specify None if there are no such properties.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, a connection between location8 and location9 exists, a connection between the shed and location1 is present, bob is situated at the shed, location1 is connected to location2, location2 is connected to location3, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 is connected to location8, location9 and the gate are connected, nut1 is situated at the gate, nut1 is not secure, nut2 is currently situated at the gate, nut2 is loose, nut3 is located at the gate, nut3 is loose, nut4 is located at the gate, nut4 is loose, nut5 is currently situated at the gate, nut5 is not secure, spanner1 is currently situated at location8, spanner1 is in working condition, spanner2 is at location6, spanner2 is in good working order, spanner3 is in working condition, spanner3 is currently at location2, spanner4 is at location2, spanner4 is in working condition, spanner5 is in good working order and spanner5 is located at location6."}
{"question_id": "8158fa95-7cbd-4b9d-8011-c4c7768cfc20", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, from location4 to location5, bob walks, from location5, bob picks up spanner2, from location5 to location6, bob walks, spanner4 is picked up by bob from location6, from location6 to location7, bob walks, bob walks to location8 from location7, bob walks from location8 to location9, bob walks to gate from location9, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between location1 and location2 exists, a link between location1 and location4 does not exist, a link between location1 and location8 does not exist, a link between location2 and location4 does not exist, a link between location2 and location5 does not exist, a link between location2 and location6 does not exist, a link between location2 and location8 does not exist, a link between location2 and shed does not exist, a link between location3 and location1 does not exist, a link between location3 and location2 does not exist, a link between location3 and location4 exists, a link between location3 and location5 does not exist, a link between location3 and location7 does not exist, a link between location4 and location6 does not exist, a link between location4 and location7 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location4 and shed does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and location6 exists, a link between location5 and location8 does not exist, a link between location5 and shed does not exist, a link between location6 and location3 does not exist, a link between location6 and location5 does not exist, a link between location6 and location9 does not exist, a link between location7 and gate does not exist, a link between location7 and location6 does not exist, a link between location7 and location9 does not exist, a link between location8 and gate does not exist, a link between location8 and location2 does not exist, a link between location8 and location3 does not exist, a link between location8 and location6 does not exist, a link between location8 and location9 exists, a link between location9 and gate exists, a link between location9 and location1 does not exist, a link between location9 and location4 does not exist, a link between location9 and location8 does not exist, a link between shed and location3 does not exist, a link between shed and location4 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, bob is at gate, bob is carrying spanner1, bob is carrying spanner2, bob is carrying spanner5, bob is not at location3, bob is not at location4, bob is not at location9, bob is not currently at location1, bob is not currently at location6, bob is not currently at location8, bob is not located at location2, bob is not located at location5, bob is not located at location7, bob is not located at shed, gate and location2 are not linked, gate and location3 are not linked, gate and location5 are not linked, gate and location6 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate and shed are not linked, gate is not linked to location4, gate is not linked to location7, location1 and gate are not linked, location1 and location3 are not linked, location1 and location5 are not linked, location1 and location6 are not linked, location1 and location9 are not linked, location1 is not linked to location7, location1 is not linked to shed, location2 and gate are not linked, location2 and location1 are not linked, location2 and location3 are linked, location2 and location7 are not linked, location2 and location9 are not linked, location3 and location6 are not linked, location3 and location8 are not linked, location3 and shed are not linked, location3 is not linked to gate, location3 is not linked to location9, location4 and location1 are not linked, location4 is linked to location5, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location3, location5 and location7 are not linked, location5 and location9 are not linked, location5 is not linked to gate, location5 is not linked to location4, location6 and gate are not linked, location6 and location7 are linked, location6 and shed are not linked, location6 is not linked to location1, location6 is not linked to location2, location6 is not linked to location4, location6 is not linked to location8, location7 and location1 are not linked, location7 and location3 are not linked, location7 and location4 are not linked, location7 and location5 are not linked, location7 and location8 are linked, location7 and shed are not linked, location7 is not linked to location2, location8 and location1 are not linked, location8 and location4 are not linked, location8 and location5 are not linked, location8 and location7 are not linked, location8 is not linked to shed, location9 and location6 are not linked, location9 and shed are not linked, location9 is not linked to location2, location9 is not linked to location3, location9 is not linked to location5, location9 is not linked to location7, nut1 is at gate, nut1 is not at location2, nut1 is not at location5, nut1 is not at location8, nut1 is not currently at location4, nut1 is not currently at location6, nut1 is not currently at location7, nut1 is not currently at location9, nut1 is not located at location1, nut1 is not located at location3, nut1 is not located at shed, nut1 is secured, nut2 is currently at gate, nut2 is not at location2, nut2 is not at location4, nut2 is not at location8, nut2 is not currently at location1, nut2 is not currently at location5, nut2 is not currently at location9, nut2 is not currently at shed, nut2 is not located at location3, nut2 is not located at location6, nut2 is not located at location7, nut2 is not loose, nut2 is tightened, nut3 is at gate, nut3 is not at location5, nut3 is not at location9, nut3 is not currently at location2, nut3 is not currently at location3, nut3 is not currently at location6, nut3 is not currently at location8, nut3 is not currently at shed, nut3 is not located at location1, nut3 is not located at location4, nut3 is not located at location7, nut3 is not loose, nut4 is currently at gate, nut4 is not at location4, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location6, nut4 is not currently at shed, nut4 is not located at location1, nut4 is not located at location5, nut4 is not located at location7, nut4 is not located at location8, nut4 is not located at location9, nut4 is secured, nut5 is located at gate, nut5 is loose, nut5 is not at location2, nut5 is not currently at location1, nut5 is not currently at location3, nut5 is not currently at location4, nut5 is not currently at location6, nut5 is not currently at location7, nut5 is not currently at location9, nut5 is not located at location5, nut5 is not located at location8, nut5 is not located at shed, nut5 is not tightened, shed and gate are not linked, shed and location1 are linked, shed and location5 are not linked, shed is not linked to location2, shed is not linked to location8, shed is not linked to location9, spanner1 is functional, spanner1 is not at location1, spanner1 is not at location4, spanner1 is not at location5, spanner1 is not at location6, spanner1 is not currently at location2, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not currently at location9, spanner1 is not located at gate, spanner1 is not located at location3, spanner1 is not located at shed, spanner2 is not at gate, spanner2 is not at location4, spanner2 is not at location7, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not currently at location1, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location5, spanner2 is not located at location6, spanner2 is not located at location8, spanner2 is not usable, spanner3 is carried by bob, spanner3 is not at location7, spanner3 is not at location8, spanner3 is not at shed, spanner3 is not currently at location2, spanner3 is not currently at location4, spanner3 is not currently at location5, spanner3 is not currently at location6, spanner3 is not currently at location9, spanner3 is not located at gate, spanner3 is not located at location1, spanner3 is not located at location3, spanner3 is not usable, spanner4 can't be used, spanner4 is carried by bob, spanner4 is not at gate, spanner4 is not at location2, spanner4 is not at location6, spanner4 is not currently at location1, spanner4 is not currently at location3, spanner4 is not currently at location4, spanner4 is not currently at location5, spanner4 is not currently at location7, spanner4 is not currently at shed, spanner4 is not located at location8, spanner4 is not located at location9, spanner5 can't be used, spanner5 is not at location5, spanner5 is not at location6, spanner5 is not at location7, spanner5 is not at location8, spanner5 is not currently at gate, spanner5 is not currently at location1, spanner5 is not currently at location3, spanner5 is not currently at location9, spanner5 is not located at location2, spanner5 is not located at location4, spanner5 is not located at shed, tightening of nut1 is complete, tightening of nut3 is complete and tightening of nut4 is complete", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location8 and location9 exists, a link between location9 and gate exists, bob is at shed, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location6 is linked to location7, location7 and location8 are linked, nut1 is currently at gate, nut1 is not secured, nut2 is at gate, nut2 is loose, nut3 is at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is currently at location5, spanner2 is functional, spanner3 is located at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is at location3 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he picks up spanner3. Next, Bob proceeds from location2 to location3, where he collects spanner5 and spanner1. He then walks from location3 to location4 and subsequently to location5, where he picks up spanner2. From location5, Bob moves to location6, where he collects spanner4, and then walks to location7. He continues walking from location7 to location8, then to location9, and finally to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, list all valid properties (including both affirmative and negative properties). If there are none, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location8 and location9, and a connection exists between location9 and the gate. Bob is currently at the shed. Location3 and location4 are connected, location4 is linked to location5, location5 is connected to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is loose, nut3 is not secured, nut4 is loose, and nut5 is also loose. The shed is connected to location1. Spanner1 is functional and located at location3, spanner2 is functional and at location5, spanner3 is usable and at location2, spanner4 is functional and at location6, and spanner5 is usable and at location3."}
{"question_id": "8927bfcd-246e-4ec3-888f-fe148925f2df", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks to location5 from location4, bob walks from location5 to location6, bob picks up spanner5 from location6, spanner2 is picked up by bob from location6, bob walks to location7 from location6, bob walks to location8 from location7, bob picks up spanner1 from location8, from location8 to location9, bob walks, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location4 does not exist, a link between gate and location5 does not exist, a link between location1 and location2 exists, a link between location1 and location8 does not exist, a link between location1 and location9 does not exist, a link between location1 and shed does not exist, a link between location2 and gate does not exist, a link between location2 and shed does not exist, a link between location3 and location2 does not exist, a link between location3 and location5 does not exist, a link between location3 and shed does not exist, a link between location4 and location1 does not exist, a link between location4 and location5 exists, a link between location4 and location8 does not exist, a link between location5 and location3 does not exist, a link between location5 and location4 does not exist, a link between location5 and location6 exists, a link between location5 and location8 does not exist, a link between location5 and shed does not exist, a link between location6 and location5 does not exist, a link between location6 and location7 exists, a link between location7 and location3 does not exist, a link between location7 and location5 does not exist, a link between location8 and gate does not exist, a link between location8 and location2 does not exist, a link between location8 and location3 does not exist, a link between location8 and location6 does not exist, a link between location8 and shed does not exist, a link between location9 and gate exists, a link between location9 and location1 does not exist, a link between location9 and location5 does not exist, a link between location9 and location6 does not exist, a link between location9 and shed does not exist, a link between shed and location2 does not exist, a link between shed and location4 does not exist, a link between shed and location7 does not exist, a link between shed and location9 does not exist, bob is at gate, bob is carrying spanner2, bob is carrying spanner3, bob is carrying spanner4, bob is not at location2, bob is not at location6, bob is not at location8, bob is not at location9, bob is not currently at location1, bob is not currently at location4, bob is not currently at location5, bob is not currently at location7, bob is not located at location3, bob is not located at shed, gate and location8 are not linked, gate and shed are not linked, gate is not linked to location2, gate is not linked to location3, gate is not linked to location6, gate is not linked to location7, gate is not linked to location9, location1 and gate are not linked, location1 and location3 are not linked, location1 and location4 are not linked, location1 and location5 are not linked, location1 and location6 are not linked, location1 is not linked to location7, location2 and location6 are not linked, location2 and location9 are not linked, location2 is linked to location3, location2 is not linked to location1, location2 is not linked to location4, location2 is not linked to location5, location2 is not linked to location7, location2 is not linked to location8, location3 and gate are not linked, location3 and location4 are linked, location3 and location6 are not linked, location3 and location7 are not linked, location3 and location9 are not linked, location3 is not linked to location1, location3 is not linked to location8, location4 and gate are not linked, location4 and location3 are not linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 and shed are not linked, location4 is not linked to location2, location4 is not linked to location6, location5 and gate are not linked, location5 and location2 are not linked, location5 is not linked to location1, location5 is not linked to location7, location5 is not linked to location9, location6 and gate are not linked, location6 and location2 are not linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 and shed are not linked, location6 is not linked to location1, location6 is not linked to location3, location6 is not linked to location4, location7 and gate are not linked, location7 and location8 are linked, location7 is not linked to location1, location7 is not linked to location2, location7 is not linked to location4, location7 is not linked to location6, location7 is not linked to location9, location7 is not linked to shed, location8 and location4 are not linked, location8 is linked to location9, location8 is not linked to location1, location8 is not linked to location5, location8 is not linked to location7, location9 and location2 are not linked, location9 and location4 are not linked, location9 and location7 are not linked, location9 is not linked to location3, location9 is not linked to location8, nut1 is at gate, nut1 is not at location3, nut1 is not at location4, nut1 is not at location6, nut1 is not at shed, nut1 is not currently at location2, nut1 is not currently at location5, nut1 is not currently at location9, nut1 is not located at location1, nut1 is not located at location7, nut1 is not located at location8, nut1 is secured, nut1 is tightened, nut2 is currently at gate, nut2 is not at location5, nut2 is not at location6, nut2 is not at shed, nut2 is not currently at location1, nut2 is not currently at location4, nut2 is not currently at location8, nut2 is not located at location2, nut2 is not located at location3, nut2 is not located at location7, nut2 is not located at location9, nut2 is not loose, nut2 is tightened, nut3 is at gate, nut3 is not at location1, nut3 is not at location2, nut3 is not at location3, nut3 is not at location7, nut3 is not currently at location4, nut3 is not currently at location5, nut3 is not currently at location9, nut3 is not located at location6, nut3 is not located at location8, nut3 is not located at shed, nut3 is not loose, nut4 is at gate, nut4 is not at location8, nut4 is not at location9, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location6, nut4 is not located at location3, nut4 is not located at location7, nut4 is not located at shed, nut4 is secured, nut5 is at gate, nut5 is loose, nut5 is not at location5, nut5 is not currently at location2, nut5 is not currently at location3, nut5 is not currently at location7, nut5 is not currently at location8, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location1, nut5 is not located at location4, nut5 is not located at location6, shed and location1 are linked, shed and location8 are not linked, shed is not linked to gate, shed is not linked to location3, shed is not linked to location5, shed is not linked to location6, spanner1 can be used, spanner1 is carried by bob, spanner1 is not at gate, spanner1 is not at location2, spanner1 is not at location5, spanner1 is not at shed, spanner1 is not currently at location1, spanner1 is not currently at location3, spanner1 is not currently at location4, spanner1 is not currently at location6, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at location9, spanner2 can't be used, spanner2 is not at location2, spanner2 is not at location6, spanner2 is not at location7, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not currently at gate, spanner2 is not currently at location1, spanner2 is not currently at location3, spanner2 is not currently at location4, spanner2 is not currently at location5, spanner2 is not currently at location8, spanner3 is not at location2, spanner3 is not at location3, spanner3 is not at location6, spanner3 is not currently at shed, spanner3 is not located at gate, spanner3 is not located at location1, spanner3 is not located at location4, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at location8, spanner3 is not located at location9, spanner3 is not usable, spanner4 can't be used, spanner4 is not at gate, spanner4 is not at location3, spanner4 is not at shed, spanner4 is not currently at location5, spanner4 is not currently at location6, spanner4 is not currently at location7, spanner4 is not located at location1, spanner4 is not located at location2, spanner4 is not located at location4, spanner4 is not located at location8, spanner4 is not located at location9, spanner5 can't be used, spanner5 is carried by bob, spanner5 is not at location9, spanner5 is not currently at gate, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not currently at location7, spanner5 is not currently at location8, spanner5 is not currently at shed, spanner5 is not located at location4, spanner5 is not located at location5, tightening of nut3 is complete, tightening of nut4 is complete and tightening of nut5 is incomplete", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, spanner1 is currently at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 can be used, spanner3 is currently at location2, spanner4 is at location2, spanner4 is usable, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He proceeds to location3, then location4, and subsequently walks to location5. From there, he heads to location6, where he picks up spanner5 and spanner2. Bob then walks to location7 and then to location8, where he collects spanner1. He continues to location9 and finally reaches the gate. At the gate, he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, list all valid properties (including those with and without negations) that apply. If none exist, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, a connection between location8 and location9 exists, a connection between the shed and location1 is present, bob is situated at the shed, location1 is connected to location2, location2 is connected to location3, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 is connected to location8, location9 and the gate are connected, nut1 is situated at the gate, nut1 is not secure, nut2 is currently situated at the gate, nut2 is loose, nut3 is located at the gate, nut3 is loose, nut4 is located at the gate, nut4 is loose, nut5 is currently situated at the gate, nut5 is not secure, spanner1 is currently situated at location8, spanner1 is in working condition, spanner2 is at location6, spanner2 is in good working order, spanner3 is in working condition, spanner3 is currently at location2, spanner4 is at location2, spanner4 is in working condition, spanner5 is in good working order and spanner5 is located at location6."}
{"question_id": "bf97d68d-ebbb-47e2-8a5d-7f969b4a6e7c", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, from location5, bob picks up spanner4, from location5 to location6, bob walks, from location6, bob picks up spanner1, bob walks from location6 to location7 and spanner5 is picked up by bob from location7 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "a link between gate and location8 does not exist, a link between gate and location9 does not exist, a link between gate and shed does not exist, a link between location1 and location2 exists, a link between location1 and shed does not exist, a link between location2 and location1 does not exist, a link between location2 and location6 does not exist, a link between location2 and location7 does not exist, a link between location3 and location2 does not exist, a link between location3 and location4 exists, a link between location3 and location6 does not exist, a link between location3 and location9 does not exist, a link between location4 and gate does not exist, a link between location4 and location3 does not exist, a link between location4 and shed does not exist, a link between location5 and location1 does not exist, a link between location5 and location6 exists, a link between location6 and location2 does not exist, a link between location6 and location4 does not exist, a link between location6 and location5 does not exist, a link between location6 and location7 exists, a link between location6 and shed does not exist, a link between location7 and gate does not exist, a link between location7 and location2 does not exist, a link between location7 and location3 does not exist, a link between location7 and location6 does not exist, a link between location8 and location4 does not exist, a link between location8 and location6 does not exist, a link between location8 and shed does not exist, a link between location9 and location3 does not exist, a link between location9 and location6 does not exist, a link between location9 and location7 does not exist, a link between shed and gate does not exist, a link between shed and location5 does not exist, bob is carrying spanner5, bob is located at location7, bob is not at location3, bob is not at location5, bob is not at location8, bob is not carrying spanner2, bob is not carrying spanner3, bob is not currently at location1, bob is not currently at location4, bob is not currently at shed, bob is not located at gate, bob is not located at location2, bob is not located at location6, bob is not located at location9, gate and location1 are not linked, gate and location2 are not linked, gate and location4 are not linked, gate and location5 are not linked, gate and location7 are not linked, gate is not linked to location3, gate is not linked to location6, location1 and location3 are not linked, location1 and location5 are not linked, location1 is not linked to gate, location1 is not linked to location4, location1 is not linked to location6, location1 is not linked to location7, location1 is not linked to location8, location1 is not linked to location9, location2 is linked to location3, location2 is not linked to gate, location2 is not linked to location4, location2 is not linked to location5, location2 is not linked to location8, location2 is not linked to location9, location2 is not linked to shed, location3 and location7 are not linked, location3 is not linked to gate, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location8, location3 is not linked to shed, location4 and location5 are linked, location4 and location6 are not linked, location4 and location7 are not linked, location4 is not linked to location1, location4 is not linked to location2, location4 is not linked to location8, location4 is not linked to location9, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location7 are not linked, location5 and location8 are not linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is not linked to gate, location5 is not linked to location4, location6 and gate are not linked, location6 and location1 are not linked, location6 and location9 are not linked, location6 is not linked to location3, location6 is not linked to location8, location7 and location1 are not linked, location7 and location5 are not linked, location7 and location8 are linked, location7 and location9 are not linked, location7 and shed are not linked, location7 is not linked to location4, location8 and location9 are linked, location8 is not linked to gate, location8 is not linked to location1, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location5, location8 is not linked to location7, location9 and gate are linked, location9 and location5 are not linked, location9 and shed are not linked, location9 is not linked to location1, location9 is not linked to location2, location9 is not linked to location4, location9 is not linked to location8, nut1 is located at gate, nut1 is loose, nut1 is not at location2, nut1 is not at location5, nut1 is not at location9, nut1 is not currently at location1, nut1 is not currently at location3, nut1 is not currently at location4, nut1 is not currently at location7, nut1 is not currently at location8, nut1 is not currently at shed, nut1 is not located at location6, nut1 is not tightened, nut2 is at gate, nut2 is not at location4, nut2 is not at location5, nut2 is not at location6, nut2 is not currently at location1, nut2 is not currently at location3, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not currently at location9, nut2 is not located at location2, nut2 is not located at shed, nut2 is not secured, nut2 is not tightened, nut3 is at gate, nut3 is loose, nut3 is not at location2, nut3 is not at location6, nut3 is not at location7, nut3 is not currently at location3, nut3 is not currently at location4, nut3 is not currently at location8, nut3 is not currently at location9, nut3 is not located at location1, nut3 is not located at location5, nut3 is not located at shed, nut4 is located at gate, nut4 is loose, nut4 is not at location1, nut4 is not at location7, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location6, nut4 is not currently at shed, nut4 is not located at location2, nut4 is not located at location3, nut4 is not located at location8, nut4 is not located at location9, nut5 is located at gate, nut5 is not at location1, nut5 is not at location6, nut5 is not at shed, nut5 is not currently at location2, nut5 is not currently at location3, nut5 is not currently at location8, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at location9, nut5 is not secured, nut5 is not tightened, shed and location2 are not linked, shed and location6 are not linked, shed and location7 are not linked, shed and location9 are not linked, shed is linked to location1, shed is not linked to location3, shed is not linked to location4, shed is not linked to location8, spanner1 is carried by bob, spanner1 is not currently at location5, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at gate, spanner1 is not located at location1, spanner1 is not located at location2, spanner1 is not located at location3, spanner1 is not located at location4, spanner1 is not located at location6, spanner1 is not located at location9, spanner1 is not located at shed, spanner1 is usable, spanner2 is located at location8, spanner2 is not at location5, spanner2 is not at location7, spanner2 is not currently at location2, spanner2 is not currently at location4, spanner2 is not currently at location6, spanner2 is not currently at location9, spanner2 is not currently at shed, spanner2 is not located at gate, spanner2 is not located at location1, spanner2 is not located at location3, spanner2 is usable, spanner3 is functional, spanner3 is located at location8, spanner3 is not at gate, spanner3 is not at location5, spanner3 is not at location6, spanner3 is not at shed, spanner3 is not currently at location7, spanner3 is not currently at location9, spanner3 is not located at location1, spanner3 is not located at location2, spanner3 is not located at location3, spanner3 is not located at location4, spanner4 can be used, spanner4 is carried by bob, spanner4 is not at location5, spanner4 is not at location6, spanner4 is not at location8, spanner4 is not currently at location2, spanner4 is not currently at location3, spanner4 is not currently at location9, spanner4 is not currently at shed, spanner4 is not located at gate, spanner4 is not located at location1, spanner4 is not located at location4, spanner4 is not located at location7, spanner5 is not at gate, spanner5 is not at location4, spanner5 is not at location5, spanner5 is not at location6, spanner5 is not at location7, spanner5 is not at location8, spanner5 is not at location9, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at shed, spanner5 is not located at location3, spanner5 is usable, tightening of nut3 is incomplete and tightening of nut4 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location6 and location7 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 and location5 are linked, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is loose, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is at location6, spanner1 is usable, spanner2 can be used, spanner2 is located at location8, spanner3 can be used, spanner3 is at location8, spanner4 is at location5, spanner4 is functional, spanner5 is functional and spanner5 is located at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and finally location5. At location5, Bob collects spanner4. He then proceeds to location6, where he picks up spanner1. From location6, Bob walks to location7, and upon arrival, he collects spanner5, ultimately reaching the current state. In this state, list all valid properties (including both affirmative and negated properties). If there are none, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, and another connection exists between location6 and location7. Additionally, a connection is present between the shed and location1. Bob is currently located at the shed. Location1 is connected to location2, and location2 is linked to location3. Furthermore, location3 is connected to location4, and location4 is linked to location5. Location7 is connected to location8, and location8 is linked to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is currently present and is loose. Similarly, nut2 is at the gate but is not secured, while nut3 is also at the gate and is loose. Nut4 is at the gate and is not secured, and nut5 is also at the gate and is loose. Spanner1 is located at location6 and is in working condition. Spanner2 is usable and is situated at location8, while spanner3 is also usable and is located at location8. Spanner4 is at location5 and is functional, and spanner5 is also functional and is located at location7."}
