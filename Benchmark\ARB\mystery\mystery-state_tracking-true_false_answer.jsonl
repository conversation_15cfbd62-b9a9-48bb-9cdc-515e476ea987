{"question_id": "86737824-8133-4100-a637-dfe9d070f48f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is not in vehicle v1, cargo c3 is not situated at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v0, cargo c4 is not located in vehicle v1, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not located in vehicle v0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not located in vehicle v0, cargo c6 is not located in vehicle v1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not in vehicle v1, cargo c7 is not located in vehicle v0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v1, cargo c8 is not located in vehicle v0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not located in vehicle v0, fuel f0 does not exist in location l0, fuel f0 does not exist in location l1, fuel f2 does not exist in location l1, fuel f3 does not exist in location l1, fuel f4 does not exist in location l0, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f3, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f6 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 does not have fuel f3, location l0 does not have fuel f8, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f7, location l1 does not have fuel f4, location l1 does not have fuel f5, location l1 does not have fuel f8, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c3, vehicle v0 does not have space s1, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c9, vehicle v1 does not have space s0 and vehicle v1 is not present at location l1. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1. Then, at location l0, cargo c0 is loaded into vehicle v0 with spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1. Cargo c3 is then loaded into vehicle v0 with space s1 and space s0 at location l1. Vehicle v0 then moves to location l0 from location l1, which has fuel levels f7 and f6. At location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, and at location l0, cargo c1 is loaded into vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? \n\ncargo c0 is not located at location l0, \ncargo c0 is not present at location l0, \ncargo c0 is not located in vehicle v0, \ncargo c0 is not located in vehicle v1, \ncargo c1 is not located at location l0, \ncargo c1 is not present at location l0, \ncargo c1 is not located at location l1, \ncargo c1 is not present at location l1, \ncargo c2 is not in vehicle v1, \ncargo c2 is not situated at location l1, \ncargo c3 is not in vehicle v1, \ncargo c3 is not situated at location l1, \ncargo c4 is not located at location l0, \ncargo c4 is not present at location l0, \ncargo c4 is not in vehicle v0, \ncargo c4 is not located in vehicle v1, \ncargo c5 is not located at location l0, \ncargo c5 is not present at location l0, \ncargo c5 is not located in vehicle v0, \ncargo c6 is not located at location l1, \ncargo c6 is not present at location l1, \ncargo c6 is not located in vehicle v0, \ncargo c6 is not located in vehicle v1, \ncargo c7 is not located at location l1, \ncargo c7 is not present at location l1, \ncargo c7 is not in vehicle v1, \ncargo c7 is not located in vehicle v0, \ncargo c8 is not located at location l0, \ncargo c8 is not present at location l0, \ncargo c8 is not in vehicle v1, \ncargo c8 is not located in vehicle v0, \ncargo c9 is not located at location l0, \ncargo c9 is not present at location l0, \ncargo c9 is not located in vehicle v0, \nfuel f0 does not exist in location l0, \nfuel f0 does not exist in location l1, \nfuel f2 does not exist in location l1, \nfuel f3 does not exist in location l1, \nfuel f4 does not exist in location l0, \nfuel f5 does not exist in location l0, \nfuel level f0 does not neighbor fuel level f4, \nfuel level f0 does not neighbor fuel level f5, \nfuel level f0 does not neighbor fuel level f6, \nfuel level f0 does not neighbor fuel level f8, \nfuel level f1 does not neighbor fuel level f5, \nfuel level f1 does not neighbor fuel level f8, \nfuel level f2 does not neighbor fuel level f8, \nfuel level f3 does not neighbor fuel level f6, \nfuel level f3 does not neighbor fuel level f7, \nfuel level f3 does not neighbor fuel level f8, \nfuel level f4 does not neighbor fuel level f1, \nfuel level f4 does not neighbor fuel level f2, \nfuel level f5 does not neighbor fuel level f2, \nfuel level f6 does not neighbor fuel level f0, \nfuel level f6 does not neighbor fuel level f2, \nfuel level f6 does not neighbor fuel level f8, \nfuel level f7 does not neighbor fuel level f0, \nfuel level f7 does not neighbor fuel level f1, \nfuel level f7 does not neighbor fuel level f2, \nfuel level f7 does not neighbor fuel level f3, \nfuel level f7 does not neighbor fuel level f6, \nfuel level f8 does not neighbor fuel level f0, \nfuel level f8 does not neighbor fuel level f3, \nfuel levels f0 and f2 are not neighbors, \nfuel levels f0 and f3 are not neighbors, \nfuel levels f0 and f7 are not neighbors, \nfuel levels f1 and f0 are not neighbors, \nfuel levels f1 and f3 are not neighbors, \nfuel levels f1 and f4 are not neighbors, \nfuel levels f1 and f6 are not neighbors, \nfuel levels f1 and f7 are not neighbors, \nfuel levels f2 and f0 are not neighbors, \nfuel levels f2 and f1 are not neighbors, \nfuel levels f2 and f4 are not neighbors, \nfuel levels f2 and f5 are not neighbors, \nfuel levels f2 and f6 are not neighbors, \nfuel levels f2 and f7 are not neighbors, \nfuel levels f3 and f0 are not neighbors, \nfuel levels f3 and f1 are not neighbors, \nfuel levels f3 and f2 are not neighbors, \nfuel levels f3 and f5 are not neighbors, \nfuel levels f4 and f0 are not neighbors, \nfuel levels f4 and f3 are not neighbors, \nfuel levels f4 and f6 are not neighbors, \nfuel levels f4 and f7 are not neighbors, \nfuel levels f4 and f8 are not neighbors, \nfuel levels f5 and f0 are not neighbors, \nfuel levels f5 and f1 are not neighbors, \nfuel levels f5 and f3 are not neighbors, \nfuel levels f5 and f4 are not neighbors, \nfuel levels f5 and f7 are not neighbors, \nfuel levels f5 and f8 are not neighbors, \nfuel levels f6 and f1 are not neighbors, \nfuel levels f6 and f3 are not neighbors, \nfuel levels f6 and f4 are not neighbors, \nfuel levels f6 and f5 are not neighbors, \nfuel levels f7 and f4 are not neighbors, \nfuel levels f7 and f5 are not neighbors, \nfuel levels f8 and f1 are not neighbors, \nfuel levels f8 and f2 are not neighbors, \nfuel levels f8 and f4 are not neighbors, \nfuel levels f8 and f5 are not neighbors, \nfuel levels f8 and f6 are not neighbors, \nfuel levels f8 and f7 are not neighbors, \nlocation l0 does not have a fuel level of f1, \nlocation l0 does not have a fuel level of f6, \nlocation l0 does not have a fuel level of f7, \nlocation l0 does not have fuel f3, \nlocation l0 does not have fuel f8, \nlocation l1 does not have a fuel level of f1, \nlocation l1 does not have a fuel level of f7, \nlocation l1 does not have fuel f4, \nlocation l1 does not have fuel f5, \nlocation l1 does not have fuel f8, \nspaces s1 and s0 are not neighbors, \nvehicle v0 does not contain cargo c2, \nvehicle v0 does not contain cargo c3, \nvehicle v0 does not have space s1, \nvehicle v0 is not at location l1, \nvehicle v1 does not contain cargo c1, \nvehicle v1 does not contain cargo c5, \nvehicle v1 does not contain cargo c9, \nvehicle v1 does not have space s0, and \nvehicle v1 is not present at location l1. \n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at l1, with cargo c6 and c7 being at l0. Fuel f3 is found at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f4 is adjacent to f5, and f7 is adjacent to f8. Additionally, f0 is adjacent to f1, f2 is adjacent to f3, f3 is adjacent to f4, f5 is adjacent to f6, and f6 is adjacent to f7. Locations l0 and l1 are interconnected. Location l1 contains fuel f8. Spaces s0 and s1 are adjacent to each other. There is a connection between locations l0 and l1. Vehicle v0 is positioned at l1 and has space s1, while vehicle v1 is at l0 and also has space s1."}
{"question_id": "201ededd-4a6a-4eb7-9f2b-37e539da970f", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is located in vehicle v0, cargo c0 is present at location l0, cargo c1 is present at location l1, cargo c10 is not situated at location l1, cargo c2 is present at location l1, cargo c3 is located in vehicle v0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c5 is not located in vehicle v0, cargo c5 is not situated at location l0, cargo c6 is present at location l0, cargo c6 is present at location l1, cargo c7 is in vehicle v0, cargo c7 is not situated at location l0, cargo c8 is present at location l0, cargo c9 is at location l0, fuel f0 does not exist in location l1, fuel f6 does not exist in location l0, fuel f6 does not exist in location l1, fuel f8 exists in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 neighbors fuel level f6, fuel level f0 neighbors fuel level f7, fuel level f1 does not neighbour fuel level f5, fuel level f1 neighbors fuel level f6, fuel level f1 neighbors fuel level f7, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f7, fuel level f2 neighbors fuel level f0, fuel level f2 neighbors fuel level f6, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f7, fuel level f3 neighbors fuel level f1, fuel level f3 neighbors fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f7, fuel level f4 neighbors fuel level f0, fuel level f4 neighbors fuel level f8, fuel level f5 does not neighbour fuel level f0, fuel level f5 neighbors fuel level f1, fuel level f5 neighbors fuel level f7, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f3, fuel level f7 neighbors fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f3, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are neighbors, fuel-levels f1 and f3 are neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are neighbors, fuel-levels f4 and f3 are neighbors, fuel-levels f4 and f6 are neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are neighbors, fuel-levels f5 and f4 are neighbors, fuel-levels f5 and f8 are neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f4 are neighbors, fuel-levels f7 and f5 are neighbors, fuel-levels f8 and f1 are neighbors, fuel-levels f8 and f2 are neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f6 are not neighbors, fuel-levels f8 and f7 are neighbors, location l0 does not have fuel f1, location l0 does not have fuel f7, location l0 has a fuel-level of f2, location l0 has a fuel-level of f3, location l0 has a fuel-level of f4, location l0 has fuel f0, location l1 does not have a fuel-level of f1, location l1 does not have fuel f5, location l1 has a fuel-level of f2, location l1 has a fuel-level of f4, location l1 has a fuel-level of f8, location l1 has fuel f7, space s1 does not neighbour space s0, vehicle v0 contains cargo c1, vehicle v0 contains cargo c10, vehicle v0 contains cargo c2, vehicle v0 contains cargo c9, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c8, vehicle v0 has space s1 and vehicle v0 is not at location l1. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and then vehicle v0 moves from location l0, which has fuel levels f7 and f6, to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1, and then vehicle v0 moves from location l0, which has fuel levels f6 and f5, to location l1. At location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f4 and f3, to location l0, resulting in the current state. \n\nIn this state, are the following properties involving negations valid? Cargo c0 is not in vehicle v0, cargo c0 is not at location l0, cargo c1 is not at location l1, cargo c10 is not at location l1, cargo c2 is not at location l1, cargo c3 is not in vehicle v0, cargo c3 is not at location l1, cargo c4 is at location l1, cargo c5 is not in vehicle v0, cargo c5 is not at location l0, cargo c6 is not at location l0, cargo c6 is not at location l1, cargo c7 is not in vehicle v0, cargo c7 is at location l0, cargo c8 is not at location l0, cargo c9 is not at location l0, fuel f0 exists in location l1, fuel f6 exists in location l0, fuel f6 exists in location l1, fuel f8 does not exist in location l0, fuel level f0 does not neighbor fuel level f6, fuel level f0 does not neighbor fuel level f7, fuel level f0 neighbors fuel level f2, fuel level f1 neighbors fuel level f5, fuel level f1 does not neighbor fuel level f6, fuel level f1 does not neighbor fuel level f7, fuel level f2 neighbors fuel level f1, fuel level f2 neighbors fuel level f4, fuel level f2 neighbors fuel level f7, fuel level f2 does not neighbor fuel level f0, fuel level f2 does not neighbor fuel level f6, fuel level f3 neighbors fuel level f2, fuel level f3 neighbors fuel level f7, fuel level f3 does not neighbor fuel level f1, fuel level f3 does not neighbor fuel level f8, fuel level f4 neighbors fuel level f1, fuel level f4 neighbors fuel level f2, fuel level f4 neighbors fuel level f7, fuel level f4 does not neighbor fuel level f0, fuel level f4 does not neighbor fuel level f8, fuel level f5 does not neighbor fuel level f1, fuel level f5 does not neighbor fuel level f7, fuel level f5 neighbors fuel level f0, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f0, fuel level f6 neighbors fuel level f3, fuel level f7 neighbors fuel level f1, fuel level f7 neighbors fuel level f3, fuel level f7 does not neighbor fuel level f6, fuel level f8 neighbors fuel level f0, fuel level f8 neighbors fuel level f3, fuel-levels f0 and f3 are neighbors, fuel-levels f0 and f4 are neighbors, fuel-levels f0 and f5 are neighbors, fuel-levels f0 and f8 are neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are neighbors, fuel-levels f1 and f8 are neighbors, fuel-levels f2 and f5 are neighbors, fuel-levels f2 and f8 are neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f5 are neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f2 are neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f1 are neighbors, fuel-levels f6 and f2 are neighbors, fuel-levels f6 and f4 are neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are neighbors, fuel-levels f7 and f0 are neighbors, fuel-levels f7 and f2 are neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f4 are neighbors, fuel-levels f8 and f5 are neighbors, fuel-levels f8 and f6 are neighbors, fuel-levels f8 and f7 are not neighbors, location l0 has fuel f1, location l0 has fuel f7, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f3, location l0 does not have a fuel-level of f4, location l0 does not have fuel f0, location l1 has a fuel-level of f1, location l1 has fuel f5, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f8, location l1 does not have fuel f7, space s1 neighbors space s0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c9, vehicle v0 contains cargo c4, vehicle v0 contains cargo c8, vehicle v0 does not have space s1 and vehicle v0 is at location l1.\n\nResponse: False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "490f7f40-5fa5-430f-8270-bed461df503c", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is situated at location l0, cargo c5 is situated at location l0, cargo c6 is located in vehicle v0, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f0, location l1 and location l0 are connected, location l1 has fuel f2, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s0, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l1. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded into vehicle v0, utilizing spaces s1 and s0. Then, vehicle v0 moves back to location l0, which has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, occupying spaces s0 and s1. Next, vehicle v0 travels from location l0, with fuel levels f4 and f3, to location l1, where cargo c1 is loaded into vehicle v0, using spaces s1 and s0. This is followed by vehicle v0 moving to location l0, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, occupying spaces s0 and s1. The sequence continues with vehicle v0 moving from location l0, with fuel levels f3 and f2, to location l1, where cargo c2 is loaded into vehicle v0, utilizing spaces s1 and s0. Then, vehicle v0 relocates to location l0, which has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0, occupying spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1, where cargo c4 is loaded into vehicle v0, using spaces s1 and s0. Next, vehicle v0 travels to location l0, which has fuel levels f4 and f3, and cargo c4 is unloaded from vehicle v0, occupying spaces s0 and s1. The sequence concludes with vehicle v0 moving from location l0, with fuel levels f1 and f0, to location l1, where cargo c6 is loaded into vehicle v0, utilizing spaces s1 and s0, and finally, vehicle v0 relocates from location l1, with fuel levels f3 and f2, to location l0, reaching the current state. In this state, are the following properties valid, excluding those involving negations? cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is situated at location l0, cargo c5 is situated at location l0, cargo c6 is located in vehicle v0, cargo c7 is situated at location l1, cargo c8 is at location l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, location l0 has fuel f0, locations l0 and l1 are connected, location l1 has fuel f2, spaces s0 and s1 are adjacent, there is a connection between locations l0 and l1, vehicle v0 contains space s0, vehicle v0 is at location l0, vehicle v1 contains space s1, and vehicle v1 is present at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "b26c8916-f02a-4c07-93ae-4bbb5f23419f", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? cargo c0 is in vehicle v0, cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c0 is not in vehicle v1, cargo c0 is situated at location l0, cargo c1 is in vehicle v1, cargo c1 is not located in vehicle v0, cargo c1 is not situated at location l1, cargo c1 is present at location l0, cargo c2 is not located in vehicle v0, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l1, cargo c2 is present at location l0, cargo c3 is in vehicle v0, cargo c3 is not located in vehicle v1, cargo c3 is not situated at location l0, cargo c3 is not situated at location l1, cargo c4 is in vehicle v1, cargo c4 is not in vehicle v0, cargo c4 is not situated at location l0, cargo c4 is not situated at location l1, cargo c5 is at location l0, cargo c5 is in vehicle v0, cargo c5 is located in vehicle v1, cargo c5 is present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not located in vehicle v1, cargo c6 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l0, cargo c7 is in vehicle v1, cargo c7 is located in vehicle v0, cargo c7 is not situated at location l1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not in vehicle v1, cargo c8 is not situated at location l0, cargo c9 is not situated at location l1, cargo c9 is present at location l0, fuel f0 exists in location l0, fuel f0 exists in location l1, fuel f1 exists in location l0, fuel f1 exists in location l1, fuel f4 does not exist in location l1, fuel f5 does not exist in location l1, fuel f8 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f0, fuel level f1 neighbors fuel level f8, fuel level f2 neighbors fuel level f0, fuel level f2 neighbors fuel level f3, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f6, fuel level f3 neighbors fuel level f7, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f8, fuel level f4 neighbors fuel level f0, fuel level f4 neighbors fuel level f1, fuel level f4 neighbors fuel level f6, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel level f5 neighbors fuel level f1, fuel level f5 neighbors fuel level f3, fuel level f5 neighbors fuel level f8, fuel level f6 neighbors fuel level f2, fuel level f6 neighbors fuel level f7, fuel level f7 does not neighbour fuel level f4, fuel level f7 neighbors fuel level f0, fuel level f7 neighbors fuel level f2, fuel level f7 neighbors fuel level f8, fuel level f8 does not neighbour fuel level f0, fuel level f8 neighbors fuel level f3, fuel level f8 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f2 are neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f6 are neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f2 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are neighbors, fuel-levels f2 and f6 are neighbors, fuel-levels f2 and f7 are neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f1 are neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f4 are not neighbors, fuel-levels f3 and f8 are neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f5 are not neighbors, fuel-levels f5 and f0 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f5 and f7 are neighbors, fuel-levels f6 and f0 are neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f3 are neighbors, fuel-levels f6 and f4 are neighbors, fuel-levels f6 and f5 are neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f1 are neighbors, fuel-levels f7 and f3 are neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are neighbors, fuel-levels f8 and f7 are neighbors, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f5, location l0 does not have fuel f6, location l0 does not have fuel f7, location l0 has a fuel-level of f3, location l0 has a fuel-level of f4, location l0 is not connected to location l1, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f8, location l1 does not have fuel f7, location l1 has fuel f2, location l1 has fuel f6, location l1 is connected to location l0, spaces s0 and s1 are not neighbors, spaces s1 and s0 are neighbors, vehicle v0 contains cargo c8, vehicle v0 contains cargo c9, vehicle v0 does not have space s0, vehicle v0 has space s1, vehicle v0 is not present at location l0, vehicle v0 is not situated at location l1, vehicle v1 contains space s0, vehicle v1 does not contain cargo c9, vehicle v1 has space s1, vehicle v1 is not at location l0 and vehicle v1 is present at location l1. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? \n\n- Is cargo c0 in vehicle v0, \n- Is cargo c0 not at location l1, \n- Is cargo c0 not present at location l1, \n- Is cargo c0 not in vehicle v1, \n- Is cargo c0 situated at location l0, \n- Is cargo c1 in vehicle v1, \n- Is cargo c1 not located in vehicle v0, \n- Is cargo c1 not situated at location l1, \n- Is cargo c1 present at location l0, \n- Is cargo c2 not located in vehicle v0, \n- Is cargo c2 not located in vehicle v1, \n- Is cargo c2 not situated at location l1, \n- Is cargo c2 present at location l0, \n- Is cargo c3 in vehicle v0, \n- Is cargo c3 not located in vehicle v1, \n- Is cargo c3 not situated at location l0, \n- Is cargo c3 not situated at location l1, \n- Is cargo c4 in vehicle v1, \n- Is cargo c4 not in vehicle v0, \n- Is cargo c4 not situated at location l0, \n- Is cargo c4 not situated at location l1, \n- Is cargo c5 at location l0, \n- Is cargo c5 in vehicle v0, \n- Is cargo c5 located in vehicle v1, \n- Is cargo c5 present at location l1, \n- Is cargo c6 not in vehicle v0, \n- Is cargo c6 not located in vehicle v1, \n- Is cargo c6 present at location l0, \n- Is cargo c6 situated at location l1, \n- Is cargo c7 at location l0, \n- Is cargo c7 in vehicle v1, \n- Is cargo c7 located in vehicle v0, \n- Is cargo c7 not situated at location l1, \n- Is cargo c8 not at location l1, \n- Is cargo c8 not present at location l1, \n- Is cargo c8 not in vehicle v1, \n- Is cargo c8 not situated at location l0, \n- Is cargo c9 not situated at location l1, \n- Is cargo c9 present at location l0, \n- Does fuel f0 exist in location l0, \n- Does fuel f0 exist in location l1, \n- Does fuel f1 exist in location l0, \n- Does fuel f1 exist in location l1, \n- Does fuel f4 not exist in location l1, \n- Does fuel f5 not exist in location l1, \n- Does fuel f8 not exist in location l0, \n- Do fuel levels f0 and f4 not neighbour, \n- Do fuel levels f0 and f5 not neighbour, \n- Do fuel levels f0 and f8 not neighbour, \n- Do fuel levels f1 and f0 not neighbour, \n- Do fuel levels f1 and f8 neighbour, \n- Do fuel levels f2 and f0 neighbour, \n- Do fuel levels f2 and f3 neighbour, \n- Do fuel levels f3 and f0 not neighbour, \n- Do fuel levels f3 and f5 not neighbour, \n- Do fuel levels f3 and f6 not neighbour, \n- Do fuel levels f3 and f7 neighbour, \n- Do fuel levels f4 and f3 not neighbour, \n- Do fuel levels f4 and f7 not neighbour, \n- Do fuel levels f4 and f8 not neighbour, \n- Do fuel levels f4 and f0 neighbour, \n- Do fuel levels f4 and f1 neighbour, \n- Do fuel levels f4 and f6 neighbour, \n- Do fuel levels f5 and f2 not neighbour, \n- Do fuel levels f5 and f4 not neighbour, \n- Do fuel levels f5 and f1 neighbour, \n- Do fuel levels f5 and f3 neighbour, \n- Do fuel levels f5 and f8 neighbour, \n- Do fuel levels f6 and f2 neighbour, \n- Do fuel levels f6 and f7 neighbour, \n- Do fuel levels f7 and f4 not neighbour, \n- Do fuel levels f7 and f0 neighbour, \n- Do fuel levels f7 and f2 neighbour, \n- Do fuel levels f7 and f8 neighbour, \n- Do fuel levels f8 and f0 not neighbour, \n- Do fuel levels f8 and f3 neighbour, \n- Do fuel levels f8 and f6 neighbour, \n- Are fuel-levels f0 and f1 neighbours, \n- Are fuel-levels f0 and f2 neighbours, \n- Are fuel-levels f0 and f3 not neighbours, \n- Are fuel-levels f0 and f6 neighbours, \n- Are fuel-levels f0 and f7 not neighbours, \n- Are fuel-levels f1 and f2 not neighbours, \n- Are fuel-levels f1 and f3 not neighbours, \n- Are fuel-levels f1 and f4 not neighbours, \n- Are fuel-levels f1 and f5 not neighbours, \n- Are fuel-levels f1 and f6 not neighbours, \n- Are fuel-levels f1 and f7 not neighbours, \n- Are fuel-levels f2 and f1 not neighbours, \n- Are fuel-levels f2 and f4 not neighbours, \n- Are fuel-levels f2 and f5 neighbours, \n- Are fuel-levels f2 and f6 neighbours, \n- Are fuel-levels f2 and f7 neighbours, \n- Are fuel-levels f2 and f8 not neighbours, \n- Are fuel-levels f3 and f1 neighbours, \n- Are fuel-levels f3 and f2 not neighbours, \n- Are fuel-levels f3 and f4 not neighbours, \n- Are fuel-levels f3 and f8 neighbours, \n- Are fuel-levels f4 and f2 not neighbours, \n- Are fuel-levels f4 and f5 not neighbours, \n- Are fuel-levels f5 and f0 neighbours, \n- Are fuel-levels f5 and f6 neighbours, \n- Are fuel-levels f5 and f7 neighbours, \n- Are fuel-levels f6 and f0 neighbours, \n- Are fuel-levels f6 and f1 not neighbours, \n- Are fuel-levels f6 and f3 neighbours, \n- Are fuel-levels f6 and f4 neighbours, \n- Are fuel-levels f6 and f5 neighbours, \n- Are fuel-levels f6 and f8 not neighbours, \n- Are fuel-levels f7 and f1 neighbours, \n- Are fuel-levels f7 and f3 neighbours, \n- Are fuel-levels f7 and f5 not neighbours, \n- Are fuel-levels f7 and f6 neighbours, \n- Are fuel-levels f8 and f1 not neighbours, \n- Are fuel-levels f8 and f2 not neighbours, \n- Are fuel-levels f8 and f4 not neighbours, \n- Are fuel-levels f8 and f5 neighbours, \n- Are fuel-levels f8 and f7 neighbours, \n- Does location l0 not have a fuel-level of f2, \n- Does location l0 not have a fuel-level of f5, \n- Does location l0 not have fuel f6, \n- Does location l0 not have fuel f7, \n- Does location l0 have a fuel-level of f3, \n- Does location l0 have a fuel-level of f4, \n- Is location l0 not connected to location l1, \n- Does location l1 not have a fuel-level of f3, \n- Does location l1 not have a fuel-level of f8, \n- Does location l1 not have fuel f7, \n- Does location l1 have fuel f2, \n- Does location l1 have fuel f6, \n- Is location l1 connected to location l0, \n- Are spaces s0 and s1 not neighbours, \n- Are spaces s1 and s0 neighbours, \n- Does vehicle v0 contain cargo c8, \n- Does vehicle v0 contain cargo c9, \n- Does vehicle v0 not have space s0, \n- Does vehicle v0 have space s1, \n- Is vehicle v0 not present at location l0, \n- Is vehicle v0 not situated at location l1, \n- Does vehicle v1 contain space s0, \n- Does vehicle v1 not contain cargo c9, \n- Does vehicle v1 have space s1, \n- Is vehicle v1 not at location l0 and \n- Is vehicle v1 present at location l1.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at l1, with cargo c6 and c7 at l0. Fuel f3 is found at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f4 is adjacent to f5, and f7 is adjacent to f8. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f3 is a neighbor of f4, f5 is a neighbor of f6, and f6 is a neighbor of f7. Locations l0 and l1 are interconnected. Location l1 contains fuel f8. Spaces s0 and s1 are adjacent. There is a connection between l0 and l1. Vehicle v0 is positioned at l1 and has space s1, while vehicle v1 is at l0 and also has space s1."}
{"question_id": "3e368b48-f213-459c-b486-12d190ade928", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? cargo c0 is at location l0, cargo c0 is located in vehicle v0, cargo c0 is not situated at location l1, cargo c1 is at location l0, cargo c1 is located in vehicle v1, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c10 is in vehicle v1, cargo c10 is located in vehicle v0, cargo c10 is not situated at location l1, cargo c10 is situated at location l0, cargo c2 is in vehicle v1, cargo c2 is not located in vehicle v0, cargo c2 is not situated at location l0, cargo c2 is situated at location l1, cargo c3 is located in vehicle v0, cargo c3 is located in vehicle v1, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not situated at location l1, cargo c4 is at location l0, cargo c4 is at location l1, cargo c4 is not located in vehicle v0, cargo c5 is located in vehicle v0, cargo c5 is located in vehicle v1, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is present at location l0, cargo c6 is in vehicle v1, cargo c6 is present at location l1, cargo c6 is situated at location l0, cargo c7 is in vehicle v0, cargo c7 is located in vehicle v1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is situated at location l0, cargo c8 is not situated at location l0, cargo c8 is not situated at location l1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not in vehicle v1, cargo c9 is not located in vehicle v0, cargo c9 is not situated at location l1, fuel f3 does not exist in location l0, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f1, fuel level f0 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f3, fuel level f2 neighbors fuel level f4, fuel level f3 does not neighbour fuel level f2, fuel level f3 neighbors fuel level f0, fuel level f3 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 does not neighbour fuel level f1, fuel level f4 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f2, fuel level f5 neighbors fuel level f0, fuel level f5 neighbors fuel level f1, fuel-levels f0 and f2 are neighbors, fuel-levels f0 and f3 are neighbors, fuel-levels f0 and f5 are neighbors, fuel-levels f1 and f2 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f5 are neighbors, fuel-levels f3 and f5 are neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f5 and f3 are neighbors, fuel-levels f5 and f4 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f0, location l0 does not have fuel f1, location l0 does not have fuel f2, location l0 has fuel f4, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f5, location l1 has a fuel-level of f2, location l1 has fuel f0, space s0 does not neighbour space s1, space s1 neighbors space s2, space s2 does not neighbour space s0, space s2 does not neighbour space s1, spaces s0 and s2 are not neighbors, spaces s1 and s0 are neighbors, there is no connection between locations l1 and l0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 does not contain space s2, vehicle v0 has space s0, vehicle v0 is at location l0, vehicle v0 is not at location l1, vehicle v1 contains cargo c4, vehicle v1 contains cargo c8, vehicle v1 does not contain cargo c0, vehicle v1 does not contain space s1, vehicle v1 does not contain space s2, vehicle v1 has space s0, vehicle v1 is not situated at location l1 and vehicle v1 is present at location l0. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial state, the following sequence of actions is executed: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded into vehicle v0, which has available spaces s2 and s1. Then, at location l0, cargo c2 is loaded into vehicle v0, which has available spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has available spaces s0 and s1. Then, at location l1, cargo c10 is loaded into vehicle v0, which has available spaces s1 and s0. At location l1, cargo c2 is unloaded from vehicle v0, which has available spaces s0 and s1. After that, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0. At location l0, cargo c10 is unloaded from vehicle v0, which has available spaces s1 and s2, and cargo c3 is loaded into vehicle v0, which has available spaces s2 and s1, resulting in the current state. \n\nIn this state, are the following properties true or false (both with and without negations)? \n\n- Is cargo c0 at location l0? \n- Is cargo c0 in vehicle v0? \n- Is cargo c0 not at location l1? \n- Is cargo c1 at location l0? \n- Is cargo c1 in vehicle v1? \n- Is cargo c1 not at location l1? \n- Is cargo c1 not present at location l1? \n- Is cargo c10 in vehicle v1? \n- Is cargo c10 in vehicle v0? \n- Is cargo c10 not at location l1? \n- Is cargo c10 at location l0? \n- Is cargo c2 in vehicle v1? \n- Is cargo c2 not in vehicle v0? \n- Is cargo c2 not at location l0? \n- Is cargo c2 at location l1? \n- Is cargo c3 in vehicle v0? \n- Is cargo c3 in vehicle v1? \n- Is cargo c3 not at location l0? \n- Is cargo c3 not present at location l0? \n- Is cargo c3 not at location l1? \n- Is cargo c4 at location l0? \n- Is cargo c4 at location l1? \n- Is cargo c4 not in vehicle v0? \n- Is cargo c5 in vehicle v0? \n- Is cargo c5 in vehicle v1? \n- Is cargo c5 not at location l1? \n- Is cargo c5 not present at location l1? \n- Is cargo c5 at location l0? \n- Is cargo c6 in vehicle v1? \n- Is cargo c6 at location l1? \n- Is cargo c6 at location l0? \n- Is cargo c7 in vehicle v0? \n- Is cargo c7 in vehicle v1? \n- Is cargo c7 not at location l1? \n- Is cargo c7 not present at location l1? \n- Is cargo c7 at location l0? \n- Is cargo c8 not at location l0? \n- Is cargo c8 not at location l1? \n- Is cargo c9 not at location l0? \n- Is cargo c9 not present at location l0? \n- Is cargo c9 not in vehicle v1? \n- Is cargo c9 not in vehicle v0? \n- Is cargo c9 not at location l1? \n- Does fuel f3 not exist at location l0? \n- Does fuel f5 not exist at location l0? \n- Do fuel levels f0 and f1 not neighbor each other? \n- Do fuel levels f0 and f4 not neighbor each other? \n- Do fuel levels f1 and f0 not neighbor each other? \n- Do fuel levels f1 and f4 not neighbor each other? \n- Do fuel levels f2 and f1 not neighbor each other? \n- Do fuel levels f2 and f3 not neighbor each other? \n- Do fuel levels f2 and f4 neighbor each other? \n- Do fuel levels f3 and f2 not neighbor each other? \n- Do fuel levels f3 and f0 neighbor each other? \n- Do fuel levels f3 and f1 neighbor each other? \n- Do fuel levels f3 and f4 neighbor each other? \n- Do fuel levels f4 and f1 not neighbor each other? \n- Do fuel levels f4 and f2 neighbor each other? \n- Do fuel levels f4 and f5 neighbor each other? \n- Do fuel levels f5 and f2 not neighbor each other? \n- Do fuel levels f5 and f0 neighbor each other? \n- Do fuel levels f5 and f1 neighbor each other? \n- Are fuel levels f0 and f2 neighbors? \n- Are fuel levels f0 and f3 neighbors? \n- Are fuel levels f0 and f5 neighbors? \n- Are fuel levels f1 and f2 not neighbors? \n- Are fuel levels f1 and f3 not neighbors? \n- Are fuel levels f1 and f5 not neighbors? \n- Are fuel levels f2 and f0 not neighbors? \n- Are fuel levels f2 and f5 neighbors? \n- Are fuel levels f3 and f5 neighbors? \n- Are fuel levels f4 and f0 not neighbors? \n- Are fuel levels f4 and f3 not neighbors? \n- Are fuel levels f5 and f3 neighbors? \n- Are fuel levels f5 and f4 not neighbors? \n- Are locations l0 and l1 connected? \n- Does location l0 not have a fuel level of f0? \n- Does location l0 not have fuel f1? \n- Does location l0 not have fuel f2? \n- Does location l0 have fuel f4? \n- Does location l1 not have a fuel level of f1? \n- Does location l1 not have a fuel level of f3? \n- Does location l1 not have a fuel level of f4? \n- Does location l1 not have a fuel level of f5? \n- Does location l1 have a fuel level of f2? \n- Does location l1 have fuel f0? \n- Do spaces s0 and s1 not neighbor each other? \n- Do spaces s1 and s2 neighbor each other? \n- Do spaces s2 and s0 not neighbor each other? \n- Do spaces s2 and s1 not neighbor each other? \n- Are spaces s0 and s2 not neighbors? \n- Are spaces s1 and s0 neighbors? \n- Is there no connection between locations l1 and l0? \n- Does vehicle v0 not contain cargo c1? \n- Does vehicle v0 not contain cargo c6? \n- Does vehicle v0 not contain cargo c8? \n- Does vehicle v0 not contain space s1? \n- Does vehicle v0 not contain space s2? \n- Does vehicle v0 have space s0? \n- Is vehicle v0 at location l0? \n- Is vehicle v0 not at location l1? \n- Does vehicle v1 contain cargo c4? \n- Does vehicle v1 contain cargo c8? \n- Does vehicle v1 not contain cargo c0? \n- Does vehicle v1 not contain space s1? \n- Does vehicle v1 not contain space s2? \n- Does vehicle v1 have space s0? \n- Is vehicle v1 not situated at location l1? \n- Is vehicle v1 present at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is located at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1, and fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. Location l0 is equipped with fuel f4, and it is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0.\n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Similarly, vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "756cb56b-f278-48a8-a8a9-8b47f8935fc8", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is at location l0, cargo c1 is at location l0, cargo c1 is not located in vehicle v1, cargo c1 is situated at location l1, cargo c2 is at location l1, cargo c2 is not in vehicle v1, cargo c2 is not located in vehicle v0, cargo c3 is in vehicle v1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not located in vehicle v0, cargo c4 is in vehicle v0, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c5 is located in vehicle v0, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not in vehicle v1, cargo c6 is in vehicle v1, cargo c6 is present at location l1, cargo c7 is in vehicle v0, cargo c7 is located in vehicle v1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c8 is at location l0, cargo c8 is not located in vehicle v0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not in vehicle v0, fuel f0 does not exist in location l0, fuel f1 exists in location l1, fuel f2 exists in location l1, fuel f3 exists in location l1, fuel f5 does not exist in location l1, fuel f6 exists in location l0, fuel f7 exists in location l1, fuel level f0 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f8, fuel level f1 neighbors fuel level f0, fuel level f2 neighbors fuel level f4, fuel level f2 neighbors fuel level f5, fuel level f2 neighbors fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f2, fuel level f4 neighbors fuel level f0, fuel level f4 neighbors fuel level f1, fuel level f4 neighbors fuel level f3, fuel level f5 does not neighbour fuel level f8, fuel level f5 neighbors fuel level f4, fuel level f6 does not neighbour fuel level f3, fuel level f6 neighbors fuel level f0, fuel level f6 neighbors fuel level f5, fuel level f6 neighbors fuel level f8, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f7, fuel level f8 neighbors fuel level f2, fuel-levels f0 and f2 are neighbors, fuel-levels f0 and f3 are neighbors, fuel-levels f0 and f4 are neighbors, fuel-levels f0 and f5 are neighbors, fuel-levels f0 and f6 are neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f0 are neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f6 are neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are neighbors, fuel-levels f7 and f0 are neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are neighbors, fuel-levels f7 and f3 are neighbors, fuel-levels f7 and f4 are neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f3 are neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are neighbors, fuel-levels f8 and f6 are not neighbors, location l0 does not have fuel f3, location l0 does not have fuel f4, location l0 has a fuel-level of f1, location l0 has a fuel-level of f7, location l0 has fuel f5, location l0 has fuel f8, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f8, space s1 does not neighbour space s0, vehicle v0 contains cargo c0, vehicle v0 contains space s1, vehicle v0 does not contain cargo c6, vehicle v0 is at location l1, vehicle v1 contains cargo c0, vehicle v1 contains cargo c9, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c8, vehicle v1 has space s0 and vehicle v1 is present at location l1. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, and cargo c0 is loaded into vehicle v0 with spaces s1 and s0 at location l0, then vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, where cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, and cargo c3 is loaded into vehicle v0 with spaces s1 and s0 at location l1, then vehicle v0 moves from location l1, which has fuel levels f7 and f6, to location l0, where cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, and at location l0, cargo c1 is loaded into vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is located at location l0, cargo c1 is located at location l0, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l1, cargo c2 is located at location l1, cargo c2 is not in vehicle v1, cargo c2 is not in vehicle v0, cargo c3 is not in vehicle v1, cargo c3 is not at location l1, cargo c3 is not in vehicle v0, cargo c4 is not in vehicle v0, cargo c4 is not at location l0, cargo c5 is not in vehicle v0, cargo c5 is not at location l0, cargo c5 is not in vehicle v1, cargo c6 is not in vehicle v1, cargo c6 is not at location l1, cargo c7 is not in vehicle v0, cargo c7 is not in vehicle v1, cargo c7 is not at location l1, cargo c8 is at location l0, cargo c8 is not in vehicle v0, cargo c9 is not at location l0, cargo c9 is not in vehicle v0, fuel f0 does not exist at location l0, fuel f1 exists at location l1, fuel f2 exists at location l1, fuel f3 exists at location l1, fuel f5 does not exist at location l1, fuel f6 exists at location l0, fuel f7 exists at location l1, fuel level f0 does not neighbor fuel level f7, fuel level f1 does not neighbor fuel levels f3, f4, f6, f7, or f8, fuel level f1 neighbors fuel level f0, fuel level f2 neighbors fuel levels f4, f5, and f7, fuel level f3 does not neighbor fuel levels f0, f1, or f8, fuel level f4 does not neighbor fuel level f2, fuel level f4 neighbors fuel levels f0, f1, and f3, fuel level f5 does not neighbor fuel level f8, fuel level f5 neighbors fuel level f4, fuel level f6 does not neighbor fuel level f3, fuel level f6 neighbors fuel levels f0, f5, and f8, fuel level f7 does not neighbor fuel level f6, fuel level f8 does not neighbor fuel levels f0, f1, or f7, fuel level f8 neighbors fuel level f2, fuel levels f0 and f2 are neighbors, fuel levels f0 and f3 are neighbors, fuel levels f0 and f4 are neighbors, fuel levels f0 and f5 are neighbors, fuel levels f0 and f6 are neighbors, fuel levels f0 and f8 are not neighbors, fuel levels f1 and f5 are not neighbors, fuel levels f2 and f0 are neighbors, fuel levels f2 and f1 are not neighbors, fuel levels f2 and f6 are not neighbors, fuel levels f2 and f8 are not neighbors, fuel levels f3 and f2 are not neighbors, fuel levels f3 and f5 are not neighbors, fuel levels f3 and f6 are neighbors, fuel levels f3 and f7 are not neighbors, fuel levels f4 and f6 are neighbors, fuel levels f4 and f7 are not neighbors, fuel levels f4 and f8 are neighbors, fuel levels f5 and f0 are not neighbors, fuel levels f5 and f1 are not neighbors, fuel levels f5 and f2 are neighbors, fuel levels f5 and f3 are not neighbors, fuel levels f5 and f7 are not neighbors, fuel levels f6 and f1 are not neighbors, fuel levels f6 and f2 are not neighbors, fuel levels f6 and f4 are neighbors, fuel levels f7 and f0 are neighbors, fuel levels f7 and f1 are not neighbors, fuel levels f7 and f2 are neighbors, fuel levels f7 and f3 are neighbors, fuel levels f7 and f4 are neighbors, fuel levels f7 and f5 are not neighbors, fuel levels f8 and f3 are neighbors, fuel levels f8 and f4 are not neighbors, fuel levels f8 and f5 are neighbors, fuel levels f8 and f6 are not neighbors, location l0 does not have fuel f3, location l0 does not have fuel f4, location l0 has a fuel level of f1, location l0 has a fuel level of f7, location l0 has fuel f5, location l0 has fuel f8, location l1 does not have a fuel level of f0, location l1 does not have a fuel level of f4, location l1 does not have a fuel level of f8, space s1 does not neighbor space s0, vehicle v0 contains cargo c0, vehicle v0 contains space s1, vehicle v0 does not contain cargo c6, vehicle v0 is at location l1, vehicle v1 contains cargo c0, vehicle v1 contains cargo c9, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c8, vehicle v1 has space s0 and vehicle v1 is present at location l1. Respond with True or False.\n\nAnswer: False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "e4c0269d-1dc4-4587-8f43-52844b8c3850", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l0, cargo c10 is not located in vehicle v0, cargo c10 is not located in vehicle v1, cargo c10 is not situated at location l1, cargo c2 is not in vehicle v0, cargo c2 is not situated at location l0, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c4 is not in vehicle v0, cargo c4 is not located in vehicle v1, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l0, cargo c6 is not in vehicle v0, cargo c6 is not situated at location l1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not in vehicle v1, cargo c7 is not located in vehicle v0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v0, cargo c8 is not in vehicle v1, cargo c9 is not in vehicle v1, cargo c9 is not located in vehicle v0, cargo c9 is not situated at location l0, fuel f0 does not exist in location l0, fuel f1 does not exist in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f4, fuel-levels f0 and f5 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, location l0 does not have fuel f2, location l0 does not have fuel f4, location l0 does not have fuel f5, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f5, spaces s0 and s2 are not neighbors, spaces s1 and s0 are not neighbors, spaces s2 and s0 are not neighbors, spaces s2 and s1 are not neighbors, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain space s0, vehicle v0 does not have space s2, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c0, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c6, vehicle v1 does not contain space s0, vehicle v1 does not have space s1 and vehicle v1 is not situated at location l0. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded into vehicle v0, which has spaces s2 and s1. Then, at location l0, cargo c2 is loaded into vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Cargo c10 is then loaded into vehicle v0, which has spaces s1 and s0, at location l1. Cargo c2 is then unloaded from vehicle v0, which has spaces s0 and s1, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, to location l0. At location l0, cargo c10 is unloaded from vehicle v0, which has spaces s1 and s2, and cargo c3 is loaded into vehicle v0, which has spaces s2 and s1, to reach the current state. In this state, are all of the following valid properties of the state that involve negations? \n\ncargo c0 is not at location l0, \ncargo c0 is not present at location l0, \ncargo c1 is not in vehicle v1, \ncargo c1 is not situated at location l0, \ncargo c10 is not located in vehicle v0, \ncargo c10 is not located in vehicle v1, \ncargo c10 is not situated at location l1, \ncargo c2 is not in vehicle v0, \ncargo c2 is not situated at location l0, \ncargo c3 is not at location l0, \ncargo c3 is not present at location l0, \ncargo c3 is not at location l1, \ncargo c3 is not present at location l1, \ncargo c4 is not at location l1, \ncargo c4 is not present at location l1, \ncargo c4 is not in vehicle v0, \ncargo c4 is not located in vehicle v1, \ncargo c5 is not in vehicle v0, \ncargo c5 is not located in vehicle v1, \ncargo c5 is not situated at location l0, \ncargo c6 is not in vehicle v0, \ncargo c6 is not situated at location l1, \ncargo c7 is not at location l1, \ncargo c7 is not present at location l1, \ncargo c7 is not in vehicle v1, \ncargo c7 is not located in vehicle v0, \ncargo c8 is not at location l0, \ncargo c8 is not present at location l0, \ncargo c8 is not in vehicle v0, \ncargo c8 is not in vehicle v1, \ncargo c9 is not in vehicle v1, \ncargo c9 is not located in vehicle v0, \ncargo c9 is not situated at location l0, \nfuel f0 does not exist in location l0, \nfuel f1 does not exist in location l0, \nfuel level f0 does not neighbour fuel level f2, \nfuel level f0 does not neighbour fuel level f3, \nfuel level f0 does not neighbour fuel level f4, \nfuel level f1 does not neighbour fuel level f5, \nfuel level f2 does not neighbour fuel level f0, \nfuel level f2 does not neighbour fuel level f1, \nfuel level f2 does not neighbour fuel level f4, \nfuel level f2 does not neighbour fuel level f5, \nfuel level f3 does not neighbour fuel level f0, \nfuel level f3 does not neighbour fuel level f2, \nfuel level f3 does not neighbour fuel level f5, \nfuel level f4 does not neighbour fuel level f0, \nfuel level f4 does not neighbour fuel level f2, \nfuel level f4 does not neighbour fuel level f3, \nfuel level f5 does not neighbour fuel level f0, \nfuel level f5 does not neighbour fuel level f1, \nfuel level f5 does not neighbour fuel level f4, \nfuel-levels f0 and f5 are not neighbors, \nfuel-levels f1 and f0 are not neighbors, \nfuel-levels f1 and f3 are not neighbors, \nfuel-levels f1 and f4 are not neighbors, \nfuel-levels f3 and f1 are not neighbors, \nfuel-levels f4 and f1 are not neighbors, \nfuel-levels f5 and f2 are not neighbors, \nfuel-levels f5 and f3 are not neighbors, \nlocation l0 does not have fuel f2, \nlocation l0 does not have fuel f4, \nlocation l0 does not have fuel f5, \nlocation l1 does not have a fuel-level of f0, \nlocation l1 does not have a fuel-level of f2, \nlocation l1 does not have a fuel-level of f3, \nlocation l1 does not have a fuel-level of f4, \nlocation l1 does not have a fuel-level of f5, \nspaces s0 and s2 are not neighbors, \nspaces s1 and s0 are not neighbors, \nspaces s2 and s0 are not neighbors, \nspaces s2 and s1 are not neighbors, \nvehicle v0 does not contain cargo c0, \nvehicle v0 does not contain cargo c1, \nvehicle v0 does not contain space s0, \nvehicle v0 does not have space s2, \nvehicle v0 is not at location l1, \nvehicle v1 does not contain cargo c0, \nvehicle v1 does not contain cargo c2, \nvehicle v1 does not contain cargo c3, \nvehicle v1 does not contain cargo c6, \nvehicle v1 does not contain space s0, \nvehicle v1 does not have space s1, and \nvehicle v1 is not situated at location l0. \n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is located at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1, and fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. Location l0 is equipped with fuel f4, and it is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0.\n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Similarly, vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "1312feb2-2064-4ee0-9a1c-57627bc79b15", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c0 is not in vehicle v1, cargo c0 is present at location l0, cargo c1 is not located in vehicle v1, cargo c1 is not situated at location l1, cargo c1 is present at location l0, cargo c2 is located in vehicle v0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l0, cargo c3 is at location l1, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not in vehicle v0, cargo c3 is not in vehicle v1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is present at location l1, cargo c5 is not situated at location l0, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not located in vehicle v0, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not in vehicle v0, cargo c7 is not located in vehicle v1, cargo c7 is situated at location l0, cargo c8 is not in vehicle v1, cargo c8 is not situated at location l0, cargo c8 is present at location l1, cargo c9 is not in vehicle v0, cargo c9 is not situated at location l0, cargo c9 is present at location l1, fuel f1 does not exist in location l0, fuel f2 does not exist in location l0, fuel f2 does not exist in location l1, fuel f3 exists in location l0, fuel f4 does not exist in location l1, fuel f8 does not exist in location l0, fuel f8 exists in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f7, fuel level f0 does not neighbour fuel level f8, fuel level f0 neighbors fuel level f1, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 neighbors fuel level f4, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f8, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f2, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f6, fuel level f7 neighbors fuel level f8, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f3 are not neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f4, location l0 does not have a fuel-level of f5, location l0 does not have fuel f6, location l0 does not have fuel f7, location l1 and location l0 are connected, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f7, location l1 does not have fuel f5, location l1 does not have fuel f6, space s0 neighbors space s1, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c5, vehicle v0 does not contain cargo c8, vehicle v0 does not have space s1, vehicle v0 has space s0, vehicle v0 is not situated at location l0, vehicle v0 is present at location l1, vehicle v1 contains space s1, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c9, vehicle v1 does not have space s0, vehicle v1 is not at location l1 and vehicle v1 is situated at location l0. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? \n\n- Is cargo c0 not located at location l1? \n- Is cargo c0 not present at location l1? \n- Is cargo c0 not in vehicle v1? \n- Is cargo c0 present at location l0? \n- Is cargo c1 not located in vehicle v1? \n- Is cargo c1 not situated at location l1? \n- Is cargo c1 present at location l0? \n- Is cargo c2 located in vehicle v0? \n- Is cargo c2 not at location l1? \n- Is cargo c2 not present at location l1? \n- Is cargo c2 not in vehicle v1? \n- Is cargo c2 not situated at location l0? \n- Is cargo c3 at location l1? \n- Is cargo c3 not at location l0? \n- Is cargo c3 not present at location l0? \n- Is cargo c3 not in vehicle v0? \n- Is cargo c3 not in vehicle v1? \n- Is cargo c4 not at location l0? \n- Is cargo c4 not present at location l0? \n- Is cargo c4 present at location l1? \n- Is cargo c5 not situated at location l0? \n- Is cargo c5 situated at location l1? \n- Is cargo c6 at location l0? \n- Is cargo c6 not at location l1? \n- Is cargo c6 not present at location l1? \n- Is cargo c6 not located in vehicle v0? \n- Is cargo c7 not at location l1? \n- Is cargo c7 not present at location l1? \n- Is cargo c7 not in vehicle v0? \n- Is cargo c7 not located in vehicle v1? \n- Is cargo c7 situated at location l0? \n- Is cargo c8 not in vehicle v1? \n- Is cargo c8 not situated at location l0? \n- Is cargo c8 present at location l1? \n- Is cargo c9 not in vehicle v0? \n- Is cargo c9 not situated at location l0? \n- Is cargo c9 present at location l1? \n- Does fuel f1 not exist in location l0? \n- Does fuel f2 not exist in location l0? \n- Does fuel f2 not exist in location l1? \n- Does fuel f3 exist in location l0? \n- Does fuel f4 not exist in location l1? \n- Does fuel f8 not exist in location l0? \n- Does fuel f8 exist in location l1? \n- Do fuel levels f0 and f2 not neighbor each other? \n- Do fuel levels f0 and f4 not neighbor each other? \n- Do fuel levels f0 and f5 not neighbor each other? \n- Do fuel levels f0 and f6 not neighbor each other? \n- Do fuel levels f0 and f7 not neighbor each other? \n- Do fuel levels f0 and f8 not neighbor each other? \n- Do fuel levels f0 and f1 neighbor each other? \n- Do fuel levels f1 and f0 not neighbor each other? \n- Do fuel levels f1 and f4 not neighbor each other? \n- Do fuel levels f1 and f5 not neighbor each other? \n- Do fuel levels f2 and f1 not neighbor each other? \n- Do fuel levels f2 and f4 not neighbor each other? \n- Do fuel levels f2 and f6 not neighbor each other? \n- Do fuel levels f2 and f8 not neighbor each other? \n- Do fuel levels f3 and f1 not neighbor each other? \n- Do fuel levels f3 and f6 not neighbor each other? \n- Do fuel levels f3 and f7 not neighbor each other? \n- Do fuel levels f3 and f4 neighbor each other? \n- Do fuel levels f4 and f0 not neighbor each other? \n- Do fuel levels f4 and f1 not neighbor each other? \n- Do fuel levels f4 and f6 not neighbor each other? \n- Do fuel levels f4 and f8 not neighbor each other? \n- Do fuel levels f4 and f5 neighbor each other? \n- Do fuel levels f5 and f2 not neighbor each other? \n- Do fuel levels f5 and f6 neighbor each other? \n- Do fuel levels f6 and f0 not neighbor each other? \n- Do fuel levels f6 and f2 not neighbor each other? \n- Do fuel levels f6 and f4 not neighbor each other? \n- Do fuel levels f6 and f5 not neighbor each other? \n- Do fuel levels f6 and f8 not neighbor each other? \n- Do fuel levels f7 and f1 not neighbor each other? \n- Do fuel levels f7 and f3 not neighbor each other? \n- Do fuel levels f7 and f5 not neighbor each other? \n- Do fuel levels f7 and f6 not neighbor each other? \n- Do fuel levels f7 and f8 neighbor each other? \n- Do fuel levels f8 and f3 not neighbor each other? \n- Do fuel levels f8 and f6 not neighbor each other? \n- Do fuel levels f8 and f7 not neighbor each other? \n- Are fuel levels f0 and f3 not neighbors? \n- Are fuel levels f1 and f2 neighbors? \n- Are fuel levels f1 and f3 not neighbors? \n- Are fuel levels f1 and f6 not neighbors? \n- Are fuel levels f1 and f7 not neighbors? \n- Are fuel levels f1 and f8 not neighbors? \n- Are fuel levels f2 and f0 not neighbors? \n- Are fuel levels f2 and f3 neighbors? \n- Are fuel levels f2 and f5 not neighbors? \n- Are fuel levels f2 and f7 not neighbors? \n- Are fuel levels f3 and f0 not neighbors? \n- Are fuel levels f3 and f2 not neighbors? \n- Are fuel levels f3 and f5 not neighbors? \n- Are fuel levels f3 and f8 not neighbors? \n- Are fuel levels f4 and f2 not neighbors? \n- Are fuel levels f4 and f3 not neighbors? \n- Are fuel levels f4 and f7 not neighbors? \n- Are fuel levels f5 and f0 not neighbors? \n- Are fuel levels f5 and f1 not neighbors? \n- Are fuel levels f5 and f3 not neighbors? \n- Are fuel levels f5 and f4 not neighbors? \n- Are fuel levels f5 and f7 not neighbors? \n- Are fuel levels f5 and f8 not neighbors? \n- Are fuel levels f6 and f1 not neighbors? \n- Are fuel levels f6 and f3 not neighbors? \n- Are fuel levels f6 and f7 neighbors? \n- Are fuel levels f7 and f0 not neighbors? \n- Are fuel levels f7 and f2 not neighbors? \n- Are fuel levels f7 and f4 not neighbors? \n- Are fuel levels f8 and f0 not neighbors? \n- Are fuel levels f8 and f1 not neighbors? \n- Are fuel levels f8 and f2 not neighbors? \n- Are fuel levels f8 and f4 not neighbors? \n- Are fuel levels f8 and f5 not neighbors? \n- Are location l0 and location l1 connected? \n- Does location l0 not have a fuel level of f0? \n- Does location l0 not have a fuel level of f4? \n- Does location l0 not have a fuel level of f5? \n- Does location l0 not have fuel f6? \n- Does location l0 not have fuel f7? \n- Are location l1 and location l0 connected? \n- Does location l1 not have a fuel level of f0? \n- Does location l1 not have a fuel level of f1? \n- Does location l1 not have a fuel level of f3? \n- Does location l1 not have a fuel level of f7? \n- Does location l1 not have fuel f5? \n- Does location l1 not have fuel f6? \n- Do space s0 and space s1 neighbor each other? \n- Are spaces s1 and s0 not neighbors? \n- Does vehicle v0 not contain cargo c0? \n- Does vehicle v0 not contain cargo c1? \n- Does vehicle v0 not contain cargo c4? \n- Does vehicle v0 not contain cargo c5? \n- Does vehicle v0 not contain cargo c8? \n- Does vehicle v0 not have space s1? \n- Does vehicle v0 have space s0? \n- Is vehicle v0 not situated at location l0? \n- Is vehicle v0 present at location l1? \n- Does vehicle v1 contain space s1? \n- Does vehicle v1 not contain cargo c4? \n- Does vehicle v1 not contain cargo c5? \n- Does vehicle v1 not contain cargo c6? \n- Does vehicle v1 not contain cargo c9? \n- Does vehicle v1 not have space s0? \n- Is vehicle v1 not at location l1? \n- Is vehicle v1 situated at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring pairs. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "3ce07a2d-3087-4c72-9695-fcab5e6e4210", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is not situated at location l1, cargo c1 is not located in vehicle v0, cargo c10 is not situated at location l0, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c6 is not situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is not at location l1cargo c9 is not present at location l1, fuel f7 exists in location l0, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel-levels f1 and f2 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are not neighbors, fuel-levels f5 and f6 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 is connected to location l1, location l1 has a fuel-level of f6, location l1 is not connected to location l0, space s0 does not neighbour space s1, vehicle v0 does not contain space s0 and vehicle v0 is not present at location l1. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: cargo c1 is loaded into vehicle v0 with spaces s1 and s0 at location l1 to reach the current state. In this state, are all of the following properties of the state valid, excluding those that involve negations? cargo c0 is not at location l1, cargo c1 is not in vehicle v0, cargo c10 is not at location l0, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is not at location l1, cargo c5 is not present at location l1, cargo c6 is not at location l1, cargo c7 is at location l1, cargo c8 is at location l1, cargo c9 is not at location l1, cargo c9 is not present at location l1, fuel f7 is at location l0, fuel level f0 is adjacent to fuel level f1, fuel level f2 is adjacent to fuel level f3, fuel levels f1 and f2 are not adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are not adjacent, fuel levels f5 and f6 are not adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is connected to location l1, location l1 has a fuel level of f6, location l1 is not connected to location l0, space s0 does not neighbor space s1, vehicle v0 does not contain space s0, and vehicle v0 is not at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "f4adbf13-692f-4adc-8e8d-414b5b091a25", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not in vehicle v0, cargo c1 is not located in vehicle v1, cargo c10 is not located in vehicle v0, cargo c10 is not situated at location l1, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l0, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not in vehicle v1, cargo c3 is not located in vehicle v0, cargo c4 is not located in vehicle v0, cargo c4 is not located in vehicle v1, cargo c4 is not situated at location l0, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l1, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not in vehicle v1, cargo c6 is not situated at location l1, cargo c7 is not in vehicle v0, cargo c7 is not situated at location l1, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v1, cargo c8 is not located in vehicle v0, cargo c9 is not located in vehicle v1, cargo c9 is not situated at location l0, cargo c9 is not situated at location l1, fuel f0 does not exist in location l0, fuel f1 does not exist in location l1, fuel f3 does not exist in location l0, fuel f5 does not exist in location l0, fuel f5 does not exist in location l1, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, location l0 does not have a fuel-level of f1, location l0 does not have fuel f4, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f3, location l1 does not have fuel f4, space s0 does not neighbour space s2, space s2 does not neighbour space s0, space s2 does not neighbour space s1, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain space s1, vehicle v0 does not contain space s2, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c10, vehicle v1 does not contain cargo c7, vehicle v1 does not contain space s1, vehicle v1 does not have space s0 and vehicle v1 is not present at location l0. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 with fuel levels f3 and f2 to location l0, cargo c0 is loaded into vehicle v0 with available spaces s2 and s1 at location l0, cargo c2 is loaded into vehicle v0 with available spaces s1 and s0 at location l0, vehicle v0 then moves from location l0 with fuel levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with available spaces s0 and s1, cargo c10 is loaded into vehicle v0 with available spaces s1 and s0 at location l1, cargo c2 is unloaded from vehicle v0 with available spaces s0 and s1 at location l1, vehicle v0 then moves from location l1 with fuel levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with available spaces s1 and s2, cargo c3 is loaded into vehicle v0 with available spaces s2 and s1 at location l0, cargo c4 is loaded into vehicle v0 with available spaces s1 and s0 at location l0, vehicle v0 then moves from location l0 with fuel levels f3 and f2 to location l1, at location l1, cargo c3 is unloaded from vehicle v0 with available spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with available spaces s1 and s2 at location l1, cargo c5 is loaded into vehicle v0 with available spaces s2 and s1 at location l1, cargo c9 is loaded into vehicle v0 with available spaces s1 and s0 at location l1, vehicle v0 then moves from location l1 with fuel levels f1 and f0 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with available spaces s0 and s1, and cargo c6 is loaded into vehicle v0 with available spaces s1 and s0 at location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not in vehicle v1, cargo c0 is not at location l0, cargo c1 is not at location l0, cargo c1 is not in vehicle v0, cargo c1 is not in vehicle v1, cargo c10 is not in vehicle v0, cargo c10 is not at location l1, cargo c2 is not in vehicle v1, cargo c2 is not at location l0, cargo c3 is not at location l0, cargo c3 is not in vehicle v1, cargo c3 is not in vehicle v0, cargo c4 is not in vehicle v0, cargo c4 is not in vehicle v1, cargo c4 is not at location l0, cargo c5 is not in vehicle v0, cargo c5 is not in vehicle v1, cargo c5 is not at location l1, cargo c6 is not at location l0, cargo c6 is not in vehicle v1, cargo c6 is not at location l1, cargo c7 is not in vehicle v0, cargo c7 is not at location l1, cargo c8 is not at location l0, cargo c8 is not in vehicle v1, cargo c8 is not in vehicle v0, cargo c9 is not in vehicle v1, cargo c9 is not at location l0, cargo c9 is not at location l1, fuel f0 does not exist at location l0, fuel f1 does not exist at location l1, fuel f3 does not exist at location l0, fuel f5 does not exist at location l0, fuel f5 does not exist at location l1, fuel level f0 is not adjacent to fuel level f5, fuel level f1 is not adjacent to fuel level f5, fuel level f2 is not adjacent to fuel level f0, fuel level f2 is not adjacent to fuel level f1, fuel level f2 is not adjacent to fuel level f5, fuel level f3 is not adjacent to fuel level f0, fuel level f3 is not adjacent to fuel level f2, fuel level f3 is not adjacent to fuel level f5, fuel level f4 is not adjacent to fuel level f0, fuel level f4 is not adjacent to fuel level f2, fuel level f4 is not adjacent to fuel level f3, fuel level f5 is not adjacent to fuel level f0, fuel level f5 is not adjacent to fuel level f3, fuel level f5 is not adjacent to fuel level f4, fuel levels f0 and f2 are not adjacent, fuel levels f0 and f3 are not adjacent, fuel levels f0 and f4 are not adjacent, fuel levels f1 and f0 are not adjacent, fuel levels f1 and f3 are not adjacent, fuel levels f1 and f4 are not adjacent, fuel levels f2 and f4 are not adjacent, fuel levels f3 and f1 are not adjacent, fuel levels f4 and f1 are not adjacent, fuel levels f5 and f1 are not adjacent, fuel levels f5 and f2 are not adjacent, location l0 does not have a fuel level of f1, location l0 does not have fuel f4, location l1 does not have a fuel level of f2, location l1 does not have a fuel level of f3, location l1 does not have fuel f4, space s0 is not adjacent to space s2, space s2 is not adjacent to space s0, space s2 is not adjacent to space s1, spaces s1 and s0 are not adjacent, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain space s1, vehicle v0 does not contain space s2, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c10, vehicle v1 does not contain cargo c7, vehicle v1 does not contain space s1, vehicle v1 does not have space s0, and vehicle v1 is not at location l0. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is located at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1. Similarly, fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. \n\nLocation l0 is equipped with fuel f4 and is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0. Space s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "0d4b2972-e90f-40e8-9014-0f9e4eedc235", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c0 is not in vehicle v1, cargo c0 is present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v1, cargo c1 is situated at location l0, cargo c2 is not located in vehicle v0, cargo c2 is not situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c3 is not in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not in vehicle v1, cargo c4 is not located in vehicle v0, cargo c4 is not situated at location l1, cargo c4 is present at location l0, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l1, cargo c5 is situated at location l0, cargo c6 is located in vehicle v0, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not situated at location l1, cargo c7 is not in vehicle v0, cargo c7 is not situated at location l0, cargo c7 is situated at location l1, cargo c8 is not located in vehicle v1, cargo c8 is not situated at location l1, cargo c8 is situated at location l0, fuel f1 does not exist in location l0, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f6 neighbors fuel level f7, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 does not have fuel f2, location l0 does not have fuel f3, location l0 does not have fuel f4, location l0 has a fuel-level of f0, location l0 is connected to location l1, location l1 and location l0 are connected, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f3, location l1 does not have fuel f0, location l1 does not have fuel f4, location l1 does not have fuel f5, location l1 does not have fuel f6, location l1 does not have fuel f7, location l1 has a fuel-level of f2, space s0 neighbors space s1, space s1 does not neighbour space s0, vehicle v0 contains space s0, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 is not present at location l1, vehicle v0 is present at location l0, vehicle v1 contains space s1, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0, vehicle v1 is not at location l0 and vehicle v1 is situated at location l1. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is performed: vehicle v0 moves from location l0 with fuel levels f5 and f4 to location l1, then cargo c0 is loaded into vehicle v0 with spaces s1 and s0 at location l1. Next, vehicle v0 moves from location l1 with fuel levels f7 and f6 to location l0, and cargo c0 is unloaded from vehicle v0 with spaces s0 and s1 at location l0. This sequence of actions is repeated for different cargo and fuel levels, ultimately resulting in the current state.\n\nIn this state, are the following properties valid (both with and without negations)?\n\n- Cargo c0 is not at location l1, and it is not present at location l1. Instead, cargo c0 is present at location l0. \n- Cargo c1 is not at location l1, and it is not present at location l1. Instead, cargo c1 is situated at location l0.\n- Cargo c2 is not located in vehicle v0, and it is not situated at location l1. Instead, cargo c2 is present at location l0.\n- Cargo c3 is at location l0, and it is not in vehicle v0. Additionally, cargo c3 is not situated at location l1.\n- Cargo c4 is not in vehicle v1, and it is not located in vehicle v0. Instead, cargo c4 is not situated at location l1, and it is present at location l0.\n- Cargo c5 is not in vehicle v0, and it is not located in vehicle v1. Instead, cargo c5 is not situated at location l1, and it is situated at location l0.\n- Cargo c6 is located in vehicle v0, and it is not at location l0. Additionally, cargo c6 is not present at location l0, and it is not situated at location l1.\n- Cargo c7 is not in vehicle v0, and it is not situated at location l0. Instead, cargo c7 is situated at location l1.\n- Cargo c8 is not located in vehicle v1, and it is not situated at location l1. Instead, cargo c8 is situated at location l0.\n- Fuel f1 does not exist in location l0, and fuel f5 does not exist in location l0.\n- Fuel level f0 does not neighbor fuel level f5, and fuel level f1 does not neighbor fuel level f0.\n- Fuel level f1 does not neighbor fuel level f3, fuel level f1 does not neighbor fuel level f4, fuel level f1 does not neighbor fuel level f5, fuel level f1 does not neighbor fuel level f6, and fuel level f1 does not neighbor fuel level f7. However, fuel level f1 neighbors fuel level f2.\n- Fuel level f2 does not neighbor fuel level f0, fuel level f2 does not neighbor fuel level f1, fuel level f2 does not neighbor fuel level f4, fuel level f2 does not neighbor fuel level f6, and fuel level f2 does not neighbor fuel level f7.\n- Fuel level f3 does not neighbor fuel level f0, fuel level f3 does not neighbor fuel level f6, and fuel level f3 does not neighbor fuel level f7.\n- Fuel level f4 does not neighbor fuel level f1, fuel level f4 does not neighbor fuel level f3. However, fuel level f4 neighbors fuel level f5.\n- Fuel level f5 does not neighbor fuel level f7, and fuel level f6 does not neighbor fuel level f0.\n- Fuel level f6 does not neighbor fuel level f1, fuel level f6 does not neighbor fuel level f2, fuel level f6 does not neighbor fuel level f3, and fuel level f6 does not neighbor fuel level f4. However, fuel level f6 neighbors fuel level f7.\n- Fuel level f7 does not neighbor fuel level f0, fuel level f7 does not neighbor fuel level f3, fuel level f7 does not neighbor fuel level f4, and fuel level f7 does not neighbor fuel level f5.\n- Fuel-levels f0 and f1 are neighbors, but fuel-levels f0 and f2 are not neighbors.\n- Fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, and fuel-levels f0 and f6 are not neighbors.\n- Fuel-levels f0 and f7 are not neighbors, but fuel-levels f2 and f3 are neighbors.\n- Fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f1 are not neighbors, and fuel-levels f3 and f2 are not neighbors.\n- However, fuel-levels f3 and f4 are neighbors, but fuel-levels f3 and f5 are not neighbors.\n- Fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, and fuel-levels f4 and f6 are not neighbors.\n- Fuel-levels f4 and f7 are not neighbors, but fuel-levels f5 and f0 are not neighbors.\n- Fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, and fuel-levels f5 and f3 are not neighbors.\n- Fuel-levels f5 and f4 are not neighbors, but fuel-levels f5 and f6 are neighbors.\n- However, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f1 are not neighbors, and fuel-levels f7 and f2 are not neighbors.\n- Fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f6, and location l0 does not have a fuel-level of f7.\n- Location l0 does not have fuel f2, location l0 does not have fuel f3, and location l0 does not have fuel f4.\n- However, location l0 has a fuel-level of f0, and location l0 is connected to location l1.\n- Location l1 and location l0 are connected, but location l1 does not have a fuel-level of f1.\n- Location l1 does not have a fuel-level of f3, location l1 does not have fuel f0, and location l1 does not have fuel f4.\n- Location l1 does not have fuel f5, location l1 does not have fuel f6, and location l1 does not have fuel f7.\n- However, location l1 has a fuel-level of f2, space s0 neighbors space s1, but space s1 does not neighbor space s0.\n- Vehicle v0 contains space s0, but vehicle v0 does not contain cargo c0.\n- Vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c8, and vehicle v0 does not contain space s1.\n- Vehicle v0 is not present at location l1, but vehicle v0 is present at location l0.\n- Vehicle v1 contains space s1, but vehicle v1 does not contain cargo c2.\n- Vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c6, and vehicle v1 does not contain cargo c7.\n- Vehicle v1 does not have space s0, vehicle v1 is not at location l0, and vehicle v1 is situated at location l1.\n\nResponse: True", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "3e125051-47e2-466c-87f9-b1c6ffad357d", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is present at location l1, cargo c1 is at location l1, cargo c2 is present at location l1, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c4 is at location l1, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is not at location l0cargo c8 is not present at location l0, fuel level f0 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f4, fuel level f4 does not neighbour fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are not neighbors, fuel-levels f5 and f6 are neighbors, location l0 does not have a fuel-level of f4, location l1 and location l0 are not connected, location l1 has a fuel-level of f7, spaces s0 and s1 are not neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is situated at location l1, vehicle v1 does not have space s1 and vehicle v1 is not present at location l1. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, are the following properties valid, excluding those that involve negations? Cargo c0 is located at l1, cargo c1 is at l1, cargo c2 is present at l1, cargo c3 is not at location l0, cargo c4 is at l1, cargo c5 is not at location l0, cargo c6 is at l1, cargo c7 is at l1, cargo c8 is not at location l0, fuel level f0 is not adjacent to fuel level f1, fuel level f3 is not adjacent to fuel level f4, fuel level f4 is not adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are not adjacent, fuel levels f5 and f6 are adjacent, location l0 does not have a fuel level of f4, locations l0 and l1 are not connected, location l1 has a fuel level of f7, spaces s0 and s1 are not adjacent, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is at location l1, vehicle v1 does not contain space s1, and vehicle v1 is not at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, and cargo c8 is positioned at l0. Fuel f5 is available at l0. Fuel levels are arranged in a sequence where f2 is adjacent to f3, f3 is adjacent to f4, f5 is adjacent to f6, and f6 is adjacent to f7. Additionally, f0 and f1 are adjacent, f1 and f2 are adjacent, and f4 and f5 are adjacent. Locations l0 and l1 are interconnected, and l1 is also connected to l0. Location l1 has fuel f7. Spaces s0 and s1 are adjacent to each other. Vehicle v0 contains space s1 and is currently at l0, while vehicle v1 contains space s1 and is at l1."}
{"question_id": "58a62d10-ebf6-4c2f-9507-75a3e66cee12", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s2 and s1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is in vehicle v0, cargo c0 is situated at location l0, cargo c1 is in vehicle v0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c10 is not located in vehicle v0, cargo c10 is present at location l1, cargo c2 is in vehicle v1, cargo c2 is not located in vehicle v0, cargo c2 is not situated at location l0, cargo c3 is at location l0, cargo c3 is not located in vehicle v0, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is situated at location l1, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not situated at location l0, cargo c7 is located in vehicle v1, cargo c7 is not situated at location l1, cargo c8 is in vehicle v1, cargo c8 is not in vehicle v0, cargo c8 is present at location l0, cargo c9 is at location l0, cargo c9 is at location l1, cargo c9 is in vehicle v1, fuel f1 does not exist in location l0, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 neighbors fuel level f3, fuel level f0 neighbors fuel level f4, fuel level f0 neighbors fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f2, fuel level f3 neighbors fuel level f1, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f1, fuel-levels f1 and f3 are neighbors, fuel-levels f1 and f5 are neighbors, fuel-levels f2 and f0 are neighbors, fuel-levels f2 and f1 are neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f5 are neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, location l0 does not have a fuel-level of f0, location l0 has a fuel-level of f4, location l0 has fuel f3, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f3, location l1 does not have fuel f4, location l1 has a fuel-level of f5, location l1 has fuel f2, space s1 does not neighbour space s0, space s2 neighbors space s1, spaces s0 and s2 are not neighbors, spaces s2 and s0 are neighbors, vehicle v0 contains cargo c4, vehicle v0 contains cargo c7, vehicle v0 has space s1, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains cargo c0, vehicle v1 contains cargo c10, vehicle v1 contains cargo c3, vehicle v1 contains cargo c6, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c4, vehicle v1 does not have space s1, vehicle v1 has space s0 and vehicle v1 is not present at location l0. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial state, the following sequence of actions is executed: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 is loaded into vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c0 is unloaded from vehicle v0, freeing spaces s0 and s1, and cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0. Cargo c2 is then unloaded from vehicle v0, freeing spaces s0 and s1. Vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0, where cargo c10 is unloaded from vehicle v0, freeing spaces s1 and s2. At location l0, cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c4 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves to location l1 from location l0, which has fuel levels f3 and f2. At location l1, cargo c3 is unloaded from vehicle v0, freeing spaces s0 and s1, and cargo c4 is unloaded from vehicle v0, freeing spaces s1 and s2. Cargo c5 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c9 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 moves to location l0 from location l1, which has fuel levels f1 and f0. At location l0, cargo c5 is unloaded from vehicle v0, freeing spaces s0 and s1, and cargo c6 is loaded into vehicle v0, occupying spaces s1 and s0, resulting in the current state. \n\nIn this state, are all of the following valid properties that involve negations true? \n- Cargo c0 is not in vehicle v0.\n- Cargo c0 is not situated at location l0.\n- Cargo c1 is not in vehicle v0.\n- Cargo c1 is not at location l0.\n- Cargo c1 is not present at location l0.\n- Cargo c10 is not located in vehicle v0.\n- Cargo c10 is present at location l1.\n- Cargo c2 is not in vehicle v0.\n- Cargo c2 is not situated at location l0.\n- Cargo c3 is not at location l0.\n- Cargo c3 is not located in vehicle v0.\n- Cargo c4 is not at location l0.\n- Cargo c4 is not present at location l0.\n- Cargo c5 is not in vehicle v0.\n- Cargo c5 is not located in vehicle v1.\n- Cargo c5 is situated at location l1.\n- Cargo c6 is not at location l1.\n- Cargo c6 is not present at location l1.\n- Cargo c6 is situated at location l0.\n- Cargo c7 is not located in vehicle v1.\n- Cargo c7 is not situated at location l1.\n- Cargo c8 is not in vehicle v1.\n- Cargo c8 is not in vehicle v0.\n- Cargo c8 is not present at location l0.\n- Cargo c9 is not at location l0.\n- Cargo c9 is not at location l1.\n- Cargo c9 is not in vehicle v1.\n- Fuel f1 does not exist in location l0.\n- Fuel f5 does not exist in location l0.\n- Fuel level f0 does not neighbour fuel level f2.\n- Fuel level f0 neighbours fuel level f3.\n- Fuel level f0 neighbours fuel level f4.\n- Fuel level f0 neighbours fuel level f5.\n- Fuel level f1 does not neighbour fuel level f0.\n- Fuel level f1 does not neighbour fuel level f4.\n- Fuel level f2 does not neighbour fuel level f5.\n- Fuel level f3 does not neighbour fuel level f2.\n- Fuel level f3 neighbours fuel level f1.\n- Fuel level f4 does not neighbour fuel level f1.\n- Fuel level f4 does not neighbour fuel level f3.\n- Fuel level f5 does not neighbour fuel level f1.\n- Fuel-levels f1 and f3 are neighbours.\n- Fuel-levels f1 and f5 are neighbours.\n- Fuel-levels f2 and f0 are neighbours.\n- Fuel-levels f2 and f1 are neighbours.\n- Fuel-levels f2 and f4 are not neighbours.\n- Fuel-levels f3 and f0 are not neighbours.\n- Fuel-levels f3 and f5 are neighbours.\n- Fuel-levels f4 and f0 are not neighbours.\n- Fuel-levels f4 and f2 are neighbours.\n- Fuel-levels f5 and f0 are not neighbours.\n- Fuel-levels f5 and f2 are not neighbours.\n- Fuel-levels f5 and f3 are not neighbours.\n- Fuel-levels f5 and f4 are not neighbours.\n- Location l0 does not have a fuel-level of f0.\n- Location l0 has a fuel-level of f4.\n- Location l0 has fuel f3.\n- Location l1 does not have a fuel-level of f1.\n- Location l1 does not have a fuel-level of f3.\n- Location l1 does not have fuel f4.\n- Location l1 has a fuel-level of f5.\n- Location l1 has fuel f2.\n- Space s1 does not neighbour space s0.\n- Space s2 neighbours space s1.\n- Spaces s0 and s2 are not neighbours.\n- Spaces s2 and s0 are neighbours.\n- Vehicle v0 contains cargo c4.\n- Vehicle v0 contains cargo c7.\n- Vehicle v0 has space s1.\n- Vehicle v0 has space s2.\n- Vehicle v0 is situated at location l1.\n- Vehicle v1 contains cargo c0.\n- Vehicle v1 contains cargo c10.\n- Vehicle v1 contains cargo c3.\n- Vehicle v1 contains cargo c6.\n- Vehicle v1 does not contain cargo c1.\n- Vehicle v1 does not contain cargo c4.\n- Vehicle v1 does not have space s1.\n- Vehicle v1 has space s0.\n- Vehicle v1 is not present at location l0.\n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1. Similarly, fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. \n\nLocation l0 is equipped with fuel f4 and is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0. Space s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "3bbbb135-56a8-4a03-8069-55af31acfa2c", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is at location l1, cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c1 is at location l1, cargo c1 is in vehicle v0, cargo c1 is not located in vehicle v1, cargo c2 is at location l1, cargo c3 is in vehicle v1, cargo c3 is not situated at location l1, cargo c4 is located in vehicle v0, cargo c4 is located in vehicle v1, cargo c4 is present at location l1, cargo c5 is not in vehicle v0, cargo c5 is not in vehicle v1, cargo c5 is situated at location l1, cargo c6 is not located in vehicle v1, cargo c6 is present at location l1, cargo c6 is situated at location l0, cargo c7 is at location l0, cargo c8 is not in vehicle v0, cargo c8 is not located in vehicle v1, cargo c8 is not situated at location l1, fuel f2 exists in location l0, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel f5 exists in location l1, fuel f6 exists in location l0, fuel f7 exists in location l1, fuel level f0 does not neighbour fuel level f4, fuel level f0 neighbors fuel level f7, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f2 neighbors fuel level f0, fuel level f2 neighbors fuel level f4, fuel level f3 does not neighbour fuel level f7, fuel level f3 neighbors fuel level f5, fuel level f3 neighbors fuel level f6, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f2, fuel level f5 neighbors fuel level f0, fuel level f5 neighbors fuel level f3, fuel level f5 neighbors fuel level f7, fuel level f6 does not neighbour fuel level f1, fuel level f6 neighbors fuel level f4, fuel level f7 does not neighbour fuel level f1, fuel level f7 neighbors fuel level f4, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are neighbors, fuel-levels f1 and f3 are neighbors, fuel-levels f1 and f4 are neighbors, fuel-levels f2 and f1 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are neighbors, fuel-levels f3 and f2 are neighbors, fuel-levels f4 and f0 are neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f2 are neighbors, fuel-levels f6 and f3 are neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f5 are neighbors, fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f7, location l0 does not have fuel f3, location l1 does not have a fuel-level of f0, location l1 does not have fuel f3, location l1 has a fuel-level of f6, location l1 has fuel f1, space s1 neighbors space s0, vehicle v0 contains cargo c2, vehicle v0 contains cargo c3, vehicle v0 does not contain cargo c7, vehicle v0 has space s1, vehicle v0 is situated at location l1, vehicle v1 contains cargo c2, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0 and vehicle v1 is not at location l0. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 with fuel levels f5 and f4 to location l1, then cargo c0 is loaded into vehicle v0 with spaces s1 and s0 at location l1. Next, vehicle v0 moves back to location l0, which has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0 with spaces s0 and s1 at location l0. Vehicle v0 then moves from location l0 with fuel levels f4 and f3 to location l1, and cargo c1 is loaded into vehicle v0 with spaces s1 and s0 at location l1. This is followed by vehicle v0 moving from location l1 with fuel levels f6 and f5 to location l0, and cargo c1 is unloaded from vehicle v0 with spaces s0 and s1 at location l0. Vehicle v0 then moves to location l1 from location l0, which has fuel levels f3 and f2, and cargo c2 is loaded into vehicle v0 with spaces s1 and s0 at location l1. Next, vehicle v0 moves to location l0 from location l1, which has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0 with spaces s0 and s1 at location l0. Vehicle v0 then moves from location l0 with fuel levels f2 and f1 to location l1, and cargo c4 is loaded into vehicle v0 with spaces s1 and s0 at location l1. This is followed by vehicle v0 moving from location l1 with fuel levels f4 and f3 to location l0, and cargo c4 is unloaded from vehicle v0 with spaces s0 and s1 at location l0. Vehicle v0 then moves to location l1 from location l0, which has fuel levels f1 and f0, and cargo c6 is loaded into vehicle v0 with spaces s1 and s0 at location l1. Finally, vehicle v0 moves from location l1 with fuel levels f3 and f2 to location l0 to reach the current state. \n\nIn this state, are all of the following valid properties of the state that involve negations? \n- cargo c0 is at location l1, \n- cargo c0 is not in vehicle v0, \n- cargo c0 is not in vehicle v1, \n- cargo c1 is at location l1, \n- cargo c1 is in vehicle v0, \n- cargo c1 is not in vehicle v1, \n- cargo c2 is at location l1, \n- cargo c3 is in vehicle v1, \n- cargo c3 is not at location l1, \n- cargo c4 is in vehicle v0, \n- cargo c4 is in vehicle v1, \n- cargo c4 is at location l1, \n- cargo c5 is not in vehicle v0, \n- cargo c5 is not in vehicle v1, \n- cargo c5 is at location l1, \n- cargo c6 is not in vehicle v1, \n- cargo c6 is at location l1, \n- cargo c6 is at location l0, \n- cargo c7 is at location l0, \n- cargo c8 is not in vehicle v0, \n- cargo c8 is not in vehicle v1, \n- cargo c8 is not at location l1, \n- fuel f2 exists in location l0, \n- fuel f4 does not exist in location l0, \n- fuel f4 does not exist in location l1, \n- fuel f5 exists in location l1, \n- fuel f6 exists in location l0, \n- fuel f7 exists in location l1, \n- fuel level f0 does not neighbor fuel level f4, \n- fuel level f0 neighbors fuel level f7, \n- fuel level f1 does not neighbor fuel level f0, \n- fuel level f1 does not neighbor fuel level f5, \n- fuel level f1 does not neighbor fuel level f6, \n- fuel level f1 does not neighbor fuel level f7, \n- fuel level f2 neighbors fuel level f0, \n- fuel level f2 neighbors fuel level f4, \n- fuel level f3 does not neighbor fuel level f7, \n- fuel level f3 neighbors fuel level f5, \n- fuel level f3 neighbors fuel level f6, \n- fuel level f5 does not neighbor fuel level f1, \n- fuel level f5 does not neighbor fuel level f2, \n- fuel level f5 neighbors fuel level f0, \n- fuel level f5 neighbors fuel level f3, \n- fuel level f5 neighbors fuel level f7, \n- fuel level f6 does not neighbor fuel level f1, \n- fuel level f6 neighbors fuel level f4, \n- fuel level f7 does not neighbor fuel level f1, \n- fuel level f7 neighbors fuel level f4, \n- fuel levels f0 and f2 are not neighbors, \n- fuel levels f0 and f3 are neighbors, \n- fuel levels f0 and f5 are not neighbors, \n- fuel levels f0 and f6 are neighbors, \n- fuel levels f1 and f3 are neighbors, \n- fuel levels f1 and f4 are neighbors, \n- fuel levels f2 and f1 are neighbors, \n- fuel levels f2 and f5 are not neighbors, \n- fuel levels f2 and f6 are neighbors, \n- fuel levels f2 and f7 are not neighbors, \n- fuel levels f3 and f0 are not neighbors, \n- fuel levels f3 and f1 are neighbors, \n- fuel levels f3 and f2 are neighbors, \n- fuel levels f4 and f0 are neighbors, \n- fuel levels f4 and f1 are not neighbors, \n- fuel levels f4 and f2 are not neighbors, \n- fuel levels f4 and f3 are neighbors, \n- fuel levels f4 and f6 are not neighbors, \n- fuel levels f4 and f7 are not neighbors, \n- fuel levels f5 and f4 are not neighbors, \n- fuel levels f6 and f0 are not neighbors, \n- fuel levels f6 and f2 are neighbors, \n- fuel levels f6 and f3 are neighbors, \n- fuel levels f6 and f5 are not neighbors, \n- fuel levels f7 and f0 are not neighbors, \n- fuel levels f7 and f2 are not neighbors, \n- fuel levels f7 and f3 are not neighbors, \n- fuel levels f7 and f5 are neighbors, \n- fuel levels f7 and f6 are not neighbors, \n- location l0 does not have a fuel level of f1, \n- location l0 does not have a fuel level of f5, \n- location l0 does not have a fuel level of f7, \n- location l0 does not have fuel f3, \n- location l1 does not have a fuel level of f0, \n- location l1 does not have fuel f3, \n- location l1 has a fuel level of f6, \n- location l1 has fuel f1, \n- space s1 neighbors space s0, \n- vehicle v0 contains cargo c2, \n- vehicle v0 contains cargo c3, \n- vehicle v0 does not contain cargo c7, \n- vehicle v0 has space s1, \n- vehicle v0 is situated at location l1, \n- vehicle v1 contains cargo c2, \n- vehicle v1 does not contain cargo c7, \n- vehicle v1 does not have space s0, \n- vehicle v1 is not at location l0.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "92f1753b-ef2c-43da-ba56-ff7dcbaae1a4", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c1 is not located in vehicle v0, cargo c1 is not located in vehicle v1, cargo c1 is not situated at location l1, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l0, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not in vehicle v1, cargo c3 is not located in vehicle v0, cargo c4 is not in vehicle v1, cargo c4 is not located in vehicle v0, cargo c4 is not situated at location l0, cargo c5 is not in vehicle v0, cargo c5 is not in vehicle v1, cargo c5 is not situated at location l0, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l1, cargo c7 is not in vehicle v0, cargo c7 is not situated at location l1, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v0, cargo c8 is not in vehicle v1, cargo c9 is not located in vehicle v1, cargo c9 is not situated at location l0, fuel f0 does not exist in location l0, fuel f1 does not exist in location l1, fuel f2 does not exist in location l0, fuel f4 does not exist in location l0, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f6 are not neighbors, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f7, location l0 does not have a fuel-level of f8, location l0 does not have fuel f6, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f7, location l1 does not have fuel f0, location l1 does not have fuel f3, location l1 does not have fuel f4, location l1 does not have fuel f5, location l1 does not have fuel f6, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s1, vehicle v0 is not at location l0, vehicle v1 does not contain cargo c0, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not contain space s0 and vehicle v1 is not at location l1. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? \n\ncargo c0 is not located at l1, \ncargo c0 is not present at l1, \ncargo c1 is not in vehicle v0, \ncargo c1 is not in vehicle v1, \ncargo c1 is not at l1, \ncargo c2 is not at l1, \ncargo c2 is not present at l1, \ncargo c2 is not in vehicle v1, \ncargo c2 is not at l0, \ncargo c3 is not at l0, \ncargo c3 is not present at l0, \ncargo c3 is not in vehicle v1, \ncargo c3 is not in vehicle v0, \ncargo c4 is not in vehicle v1, \ncargo c4 is not in vehicle v0, \ncargo c4 is not at l0, \ncargo c5 is not in vehicle v0, \ncargo c5 is not in vehicle v1, \ncargo c5 is not at l0, \ncargo c6 is not in vehicle v0, \ncargo c6 is not at l1, \ncargo c7 is not in vehicle v0, \ncargo c7 is not at l1, \ncargo c8 is not at l0, \ncargo c8 is not present at l0, \ncargo c8 is not in vehicle v0, \ncargo c8 is not in vehicle v1, \ncargo c9 is not in vehicle v1, \ncargo c9 is not at l0, \nfuel f0 does not exist at l0, \nfuel f1 does not exist at l1, \nfuel f2 does not exist at l0, \nfuel f4 does not exist at l0, \nfuel level f0 is not adjacent to fuel level f5, \nfuel level f0 is not adjacent to fuel level f7, \nfuel level f1 is not adjacent to fuel level f0, \nfuel level f1 is not adjacent to fuel level f3, \nfuel level f1 is not adjacent to fuel level f5, \nfuel level f1 is not adjacent to fuel level f7, \nfuel level f1 is not adjacent to fuel level f8, \nfuel level f2 is not adjacent to fuel level f1, \nfuel level f2 is not adjacent to fuel level f4, \nfuel level f2 is not adjacent to fuel level f8, \nfuel level f3 is not adjacent to fuel level f0, \nfuel level f3 is not adjacent to fuel level f2, \nfuel level f3 is not adjacent to fuel level f6, \nfuel level f4 is not adjacent to fuel level f6, \nfuel level f4 is not adjacent to fuel level f7, \nfuel level f5 is not adjacent to fuel level f3, \nfuel level f5 is not adjacent to fuel level f4, \nfuel level f6 is not adjacent to fuel level f1, \nfuel level f6 is not adjacent to fuel level f3, \nfuel level f6 is not adjacent to fuel level f4, \nfuel level f6 is not adjacent to fuel level f5, \nfuel level f7 is not adjacent to fuel level f1, \nfuel level f7 is not adjacent to fuel level f2, \nfuel level f7 is not adjacent to fuel level f3, \nfuel level f7 is not adjacent to fuel level f4, \nfuel level f7 is not adjacent to fuel level f5, \nfuel level f7 is not adjacent to fuel level f6, \nfuel level f8 is not adjacent to fuel level f1, \nfuel level f8 is not adjacent to fuel level f2, \nfuel level f8 is not adjacent to fuel level f3, \nfuel level f8 is not adjacent to fuel level f4, \nfuel level f8 is not adjacent to fuel level f7, \nfuel levels f0 and f2 are not adjacent, \nfuel levels f0 and f3 are not adjacent, \nfuel levels f0 and f4 are not adjacent, \nfuel levels f0 and f6 are not adjacent, \nfuel levels f0 and f8 are not adjacent, \nfuel levels f1 and f4 are not adjacent, \nfuel levels f1 and f6 are not adjacent, \nfuel levels f2 and f0 are not adjacent, \nfuel levels f2 and f5 are not adjacent, \nfuel levels f2 and f6 are not adjacent, \nfuel levels f2 and f7 are not adjacent, \nfuel levels f3 and f1 are not adjacent, \nfuel levels f3 and f5 are not adjacent, \nfuel levels f3 and f7 are not adjacent, \nfuel levels f3 and f8 are not adjacent, \nfuel levels f4 and f0 are not adjacent, \nfuel levels f4 and f1 are not adjacent, \nfuel levels f4 and f2 are not adjacent, \nfuel levels f4 and f3 are not adjacent, \nfuel levels f4 and f8 are not adjacent, \nfuel levels f5 and f0 are not adjacent, \nfuel levels f5 and f1 are not adjacent, \nfuel levels f5 and f2 are not adjacent, \nfuel levels f5 and f7 are not adjacent, \nfuel levels f5 and f8 are not adjacent, \nfuel levels f6 and f0 are not adjacent, \nfuel levels f6 and f2 are not adjacent, \nfuel levels f6 and f8 are not adjacent, \nfuel levels f7 and f0 are not adjacent, \nfuel levels f8 and f0 are not adjacent, \nfuel levels f8 and f5 are not adjacent, \nfuel levels f8 and f6 are not adjacent, \nlocation l0 does not have fuel level f1, \nlocation l0 does not have fuel level f5, \nlocation l0 does not have fuel level f7, \nlocation l0 does not have fuel level f8, \nlocation l0 does not have fuel f6, \nlocation l1 does not have fuel level f2, \nlocation l1 does not have fuel level f7, \nlocation l1 does not have fuel f0, \nlocation l1 does not have fuel f3, \nlocation l1 does not have fuel f4, \nlocation l1 does not have fuel f5, \nlocation l1 does not have fuel f6, \nspaces s1 and s0 are not adjacent, \nvehicle v0 does not contain cargo c0, \nvehicle v0 does not contain cargo c9, \nvehicle v0 does not have space s1, \nvehicle v0 is not at location l0, \nvehicle v1 does not contain cargo c0, \nvehicle v1 does not contain cargo c6, \nvehicle v1 does not contain cargo c7, \nvehicle v1 does not contain space s0 and \nvehicle v1 is not at location l1. \n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "c51ad453-e56d-40f7-8251-fd485871c327", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c1 is present at location l0, cargo c10 is not at location l0cargo c10 is not present at location l0, cargo c2 is present at location l0, cargo c3 is not situated at location l0, cargo c4 is present at location l0, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c6 is present at location l0, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c8 is at location l0, cargo c9 is at location l1, fuel level f0 does not neighbour fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f3 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f6, fuel level f7 does not neighbour fuel level f8, fuel-levels f1 and f2 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are not connected, location l0 has fuel f3, location l1 has fuel f1, space s0 does not neighbour space s1, there is no connection between locations l1 and l0, vehicle v0 has space s1 and vehicle v0 is present at location l0. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, then vehicle v0 moves from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, to location l1, where cargo c4 is loaded into vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. This sequence of actions is repeated for cargo c6 and c7, with vehicle v0 moving between locations l0 and l1, and finally, cargo c8 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, and then vehicle v0 moves to location l0, with fuel levels f2 and f1, where cargo c8 is unloaded from vehicle v0, which has spaces s0 and s1, resulting in the current state. \n\nIn this state, are the following properties valid and do not involve negations? \ncargo c0 is not located at l1, \ncargo c0 is not present at location l1, \ncargo c1 is present at location l0, \ncargo c10 is not located at l0, \ncargo c10 is not present at location l0, \ncargo c2 is present at location l0, \ncargo c3 is not situated at location l0, \ncargo c4 is present at location l0, \ncargo c5 is not located at l1, \ncargo c5 is not present at location l1, \ncargo c6 is present at location l0, \ncargo c7 is not located at l0, \ncargo c7 is not present at location l0, \ncargo c8 is located at l0, \ncargo c9 is located at l1, \nfuel level f0 is not adjacent to fuel level f1, \nfuel level f2 is adjacent to fuel level f3, \nfuel level f3 is not adjacent to fuel level f4, \nfuel level f5 is not adjacent to fuel level f6, \nfuel level f7 is not adjacent to fuel level f8, \nfuel levels f1 and f2 are not adjacent, \nfuel levels f4 and f5 are adjacent, \nfuel levels f6 and f7 are adjacent, \nlocations l0 and l1 are not connected, \nlocation l0 has fuel f3, \nlocation l1 has fuel f1, \nspace s0 is not adjacent to space s1, \nthere is no connection between locations l1 and l0, \nvehicle v0 has space s1, \nvehicle v0 is present at location l0.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "46d920db-d977-41dc-bf19-34eb31d74c26", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? cargo c0 is not in vehicle v0, cargo c0 is not situated at location l0, cargo c0 is present at location l1, cargo c1 is not in vehicle v0, cargo c1 is not situated at location l1, cargo c1 is present at location l0, cargo c10 is not located in vehicle v0, cargo c10 is not situated at location l1, cargo c10 is present at location l0, cargo c2 is at location l0, cargo c2 is not situated at location l1, cargo c3 is not in vehicle v0, cargo c3 is not situated at location l1, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c4 is not in vehicle v0, cargo c5 is at location l1, cargo c5 is not located in vehicle v0, cargo c5 is not situated at location l0, cargo c6 is at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c7 is at location l0, cargo c7 is not situated at location l1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not in vehicle v0, cargo c8 is situated at location l0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not located in vehicle v0, cargo c9 is situated at location l1, fuel f1 exists in location l1, fuel f3 does not exist in location l1, fuel f4 does not exist in location l0, fuel f5 does not exist in location l0, fuel f7 does not exist in location l0, fuel f7 does not exist in location l1, fuel f8 does not exist in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 neighbors fuel level f1, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f7, fuel level f1 does not neighbour fuel level f8, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f8, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f5, fuel level f7 neighbors fuel level f8, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f4 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f8, location l0 does not have fuel f1, location l0 does not have fuel f2, location l0 has fuel f3, location l1 and location l0 are connected, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f5, location l1 does not have fuel f0, location l1 does not have fuel f2, location l1 does not have fuel f6, space s1 does not neighbour space s0, spaces s0 and s1 are neighbors, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c7, vehicle v0 does not have space s0, vehicle v0 has space s1, vehicle v0 is at location l0 and vehicle v0 is not situated at location l1. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is performed: \n- Cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1.\n- Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0.\n- At location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1.\n- Vehicle v0 then moves from location l0, which has fuel levels f7 and f6, to location l1.\n- At location l1, cargo c4 is loaded into vehicle v0, which has spaces s1 and s0.\n- Vehicle v0 then moves from location l1, which has fuel levels f5 and f4, to location l0.\n- At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1.\n- Vehicle v0 then moves from location l0, which has fuel levels f6 and f5, to location l1.\n- At location l1, cargo c6 is loaded into vehicle v0, which has spaces s1 and s0.\n- Vehicle v0 then moves from location l1, which has fuel levels f4 and f3, to location l0.\n- At location l0, cargo c6 is unloaded from vehicle v0, which has spaces s0 and s1.\n- Vehicle v0 then moves from location l0, which has fuel levels f5 and f4, to location l1.\n- At location l1, cargo c7 is loaded into vehicle v0, which has spaces s1 and s0.\n- Vehicle v0 then moves from location l1, which has fuel levels f3 and f2, to location l0.\n- At location l0, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1.\n- Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1.\n- At location l1, cargo c8 is loaded into vehicle v0, which has spaces s1 and s0.\n- Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, to location l0.\n- At location l0, cargo c8 is unloaded from vehicle v0, which has spaces s0 and s1.\n\nIn this state, are the following properties of the state valid (both with and without negations)?\n- Cargo c0 is not in vehicle v0.\n- Cargo c0 is not at location l0.\n- Cargo c0 is at location l1.\n- Cargo c1 is not in vehicle v0.\n- Cargo c1 is not at location l1.\n- Cargo c1 is at location l0.\n- Cargo c10 is not in vehicle v0.\n- Cargo c10 is not at location l1.\n- Cargo c10 is at location l0.\n- Cargo c2 is at location l0.\n- Cargo c2 is not at location l1.\n- Cargo c3 is not in vehicle v0.\n- Cargo c3 is not at location l1.\n- Cargo c3 is at location l0.\n- Cargo c4 is at location l0.\n- Cargo c4 is not at location l1.\n- Cargo c4 is not in vehicle v0.\n- Cargo c5 is at location l1.\n- Cargo c5 is not in vehicle v0.\n- Cargo c5 is not at location l0.\n- Cargo c6 is at location l0.\n- Cargo c6 is not at location l1.\n- Cargo c7 is at location l0.\n- Cargo c7 is not at location l1.\n- Cargo c8 is not at location l1.\n- Cargo c8 is not in vehicle v0.\n- Cargo c8 is at location l0.\n- Cargo c9 is not at location l0.\n- Cargo c9 is not at location l0.\n- Cargo c9 is not in vehicle v0.\n- Cargo c9 is at location l1.\n- Fuel f1 is at location l1.\n- Fuel f3 is not at location l1.\n- Fuel f4 is not at location l0.\n- Fuel f5 is not at location l0.\n- Fuel f7 is not at location l0.\n- Fuel f7 is not at location l1.\n- Fuel f8 is not at location l1.\n- Fuel level f0 does not neighbor fuel level f2.\n- Fuel level f0 does not neighbor fuel level f3.\n- Fuel level f0 does not neighbor fuel level f5.\n- Fuel level f0 does not neighbor fuel level f6.\n- Fuel level f0 neighbors fuel level f1.\n- Fuel level f1 does not neighbor fuel level f4.\n- Fuel level f1 does not neighbor fuel level f5.\n- Fuel level f1 does not neighbor fuel level f7.\n- Fuel level f1 does not neighbor fuel level f8.\n- Fuel level f1 neighbors fuel level f2.\n- Fuel level f2 does not neighbor fuel level f7.\n- Fuel level f3 does not neighbor fuel level f0.\n- Fuel level f3 does not neighbor fuel level f1.\n- Fuel level f3 does not neighbor fuel level f5.\n- Fuel level f3 does not neighbor fuel level f6.\n- Fuel level f3 does not neighbor fuel level f8.\n- Fuel level f4 does not neighbor fuel level f1.\n- Fuel level f4 does not neighbor fuel level f3.\n- Fuel level f4 does not neighbor fuel level f6.\n- Fuel level f4 does not neighbor fuel level f7.\n- Fuel level f4 does not neighbor fuel level f8.\n- Fuel level f4 neighbors fuel level f5.\n- Fuel level f5 does not neighbor fuel level f0.\n- Fuel level f5 does not neighbor fuel level f3.\n- Fuel level f5 neighbors fuel level f6.\n- Fuel level f6 does not neighbor fuel level f1.\n- Fuel level f6 does not neighbor fuel level f2.\n- Fuel level f6 does not neighbor fuel level f8.\n- Fuel level f7 does not neighbor fuel level f1.\n- Fuel level f7 does not neighbor fuel level f5.\n- Fuel level f7 neighbors fuel level f8.\n- Fuel level f8 does not neighbor fuel level f0.\n- Fuel level f8 does not neighbor fuel level f1.\n- Fuel level f8 does not neighbor fuel level f2.\n- Fuel level f8 does not neighbor fuel level f3.\n- Fuel level f8 does not neighbor fuel level f5.\n- Fuel level f8 does not neighbor fuel level f6.\n- Fuel level f8 does not neighbor fuel level f7.\n- Fuel levels f0 and f4 are not neighbors.\n- Fuel levels f0 and f7 are not neighbors.\n- Fuel levels f0 and f8 are not neighbors.\n- Fuel levels f1 and f0 are not neighbors.\n- Fuel levels f1 and f3 are not neighbors.\n- Fuel levels f1 and f6 are not neighbors.\n- Fuel levels f2 and f0 are not neighbors.\n- Fuel levels f2 and f1 are not neighbors.\n- Fuel levels f2 and f3 are neighbors.\n- Fuel levels f2 and f4 are not neighbors.\n- Fuel levels f2 and f5 are not neighbors.\n- Fuel levels f2 and f6 are not neighbors.\n- Fuel levels f2 and f8 are not neighbors.\n- Fuel levels f3 and f2 are not neighbors.\n- Fuel levels f3 and f4 are neighbors.\n- Fuel levels f3 and f7 are not neighbors.\n- Fuel levels f4 and f0 are not neighbors.\n- Fuel levels f4 and f2 are not neighbors.\n- Fuel levels f5 and f1 are not neighbors.\n- Fuel levels f5 and f2 are not neighbors.\n- Fuel levels f5 and f4 are not neighbors.\n- Fuel levels f5 and f7 are not neighbors.\n- Fuel levels f5 and f8 are not neighbors.\n- Fuel levels f6 and f0 are not neighbors.\n- Fuel levels f6 and f3 are not neighbors.\n- Fuel levels f6 and f4 are not neighbors.\n- Fuel levels f6 and f5 are not neighbors.\n- Fuel levels f6 and f7 are neighbors.\n- Fuel levels f7 and f0 are not neighbors.\n- Fuel levels f7 and f2 are not neighbors.\n- Fuel levels f7 and f3 are not neighbors.\n- Fuel levels f7 and f4 are not neighbors.\n- Fuel levels f7 and f6 are not neighbors.\n- Fuel levels f8 and f4 are not neighbors.\n- Location l0 and location l1 are connected.\n- Location l0 does not have fuel level f0.\n- Location l0 does not have fuel level f6.\n- Location l0 does not have fuel level f8.\n- Location l0 does not have fuel f1.\n- Location l0 does not have fuel f2.\n- Location l0 has fuel f3.\n- Location l1 and location l0 are connected.\n- Location l1 does not have fuel level f4.\n- Location l1 does not have fuel level f5.\n- Location l1 does not have fuel f0.\n- Location l1 does not have fuel f2.\n- Location l1 does not have fuel f6.\n- Space s1 does not neighbor space s0.\n- Spaces s0 and s1 are neighbors.\n- Vehicle v0 does not contain cargo c2.\n- Vehicle v0 does not contain cargo c6.\n- Vehicle v0 does not contain cargo c7.\n- Vehicle v0 does not have space s0.\n- Vehicle v0 has space s1.\n- Vehicle v0 is at location l0.\n- Vehicle v0 is not at location l1.\n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "4cb8656b-3094-42ca-8219-827463f82976", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s2 and s1, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is present at location l1, cargo c1 is situated at location l1, cargo c10 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is present at location l0, cargo c6 is located in vehicle v0, cargo c7 is at location l0, cargo c8 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 has a fuel-level of f2, location l0 is connected to location l1, location l1 and location l0 are connected, location l1 has fuel f0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains cargo c9, vehicle v0 has space s0, vehicle v0 is at location l0, vehicle v1 has space s2 and vehicle v1 is present at location l1. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded into vehicle v0 occupying spaces s2 and s1, and cargo c2 is loaded into vehicle v0 occupying spaces s1 and s0. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c0 is unloaded from vehicle v0 occupying spaces s0 and s1, cargo c10 is loaded into vehicle v0 occupying spaces s1 and s0, and cargo c2 is unloaded from vehicle v0 occupying spaces s0 and s1. Next, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0, where cargo c10 is unloaded from vehicle v0 occupying spaces s1 and s2, cargo c3 is loaded into vehicle v0 occupying spaces s2 and s1, and cargo c4 is loaded into vehicle v0 occupying spaces s1 and s0. Then, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, where cargo c3 is unloaded from vehicle v0 occupying spaces s0 and s1, cargo c4 is unloaded from vehicle v0 occupying spaces s1 and s2, cargo c5 is loaded into vehicle v0 occupying spaces s2 and s1, and cargo c9 is loaded into vehicle v0 occupying spaces s1 and s0. Finally, vehicle v0 moves from location l1, which has fuel levels f1 and f0, to location l0, where cargo c5 is unloaded from vehicle v0 occupying spaces s0 and s1, and cargo c6 is loaded into vehicle v0 occupying spaces s1 and s0, resulting in the current state. In this state, are the following properties valid without involving negations? cargo c0 is at location l1, cargo c1 is at location l1, cargo c10 is at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is at location l0, cargo c6 is in vehicle v0, cargo c7 is at location l0, cargo c8 is at location l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, location l0 has a fuel level of f2, location l0 is connected to location l1, location l1 is connected to location l0, location l1 has fuel f0, space s0 is adjacent to space s1, spaces s1 and s2 are adjacent, vehicle v0 contains cargo c9, vehicle v0 has space s0, vehicle v0 is at location l0, vehicle v1 has space s2, and vehicle v1 is at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is located at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1, and fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. Location l0 is equipped with fuel f4, and it is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0.\n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Similarly, vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "14df2f79-71c6-406f-b507-6637c51c904f", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is situated at location l1, cargo c1 is at location l0, cargo c10 is at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is situated at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is present at location l0, cargo c8 is at location l0, cargo c9 is at location l1, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has fuel f3, location l0 is connected to location l1, location l1 has a fuel-level of f1, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is present at location l0. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Then, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1. This sequence of actions continues with cargo c6 and c7 being loaded and unloaded, and vehicle v0 moving between locations l0 and l1. The sequence ends with cargo c8 being loaded onto vehicle v0 at location l1, vehicle v0 moving to location l0, and cargo c8 being unloaded at location l0. \n\nIn the resulting state, are the following properties true without involving negations? cargo c0 is at location l1, cargo c1 is at location l0, cargo c10 is at location l0, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is at location l0, cargo c9 is at location l1, fuel level f1 is adjacent to fuel level f2, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, location l0 has fuel f3, location l0 is connected to location l1, location l1 has a fuel level of f1, location l1 is connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, and vehicle v0 is at location l0. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1."}
{"question_id": "34d3faeb-9f7e-47ea-a45b-913533897fe7", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is present at location l0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c2 is not located in vehicle v0, cargo c3 is present at location l1, cargo c4 is not situated at location l1, cargo c5 is at location l1, cargo c6 is not situated at location l0, cargo c7 is not situated at location l0, cargo c8 is present at location l1, cargo c9 is not at location l1cargo c9 is not present at location l1, fuel f8 exists in location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are not neighbors, fuel-levels f6 and f7 are not neighbors, fuel-levels f7 and f8 are not neighbors, location l0 does not have a fuel-level of f3, location l0 is connected to location l1, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 does not contain space s0, vehicle v0 is not situated at location l1, vehicle v1 does not have space s1 and vehicle v1 is situated at location l0. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c2 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, are all of the following properties of the state that do not involve negations valid? Cargo c0 is located at location l0, cargo c1 is absent from location l0, cargo c1 is not present at location l0, cargo c2 is not inside vehicle v0, cargo c3 is located at location l1, cargo c4 is not present at location l1, cargo c5 is situated at location l1, cargo c6 is not located at location l0, cargo c7 is not located at location l0, cargo c8 is present at location l1, cargo c9 is absent from location l1, cargo c9 is not present at location l1, fuel f8 is available at location l1, fuel level f2 is adjacent to fuel level f3, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f3 and f4 are not adjacent, fuel levels f6 and f7 are not adjacent, fuel levels f7 and f8 are not adjacent, location l0 does not have a fuel level of f3, location l0 is connected to location l1, location l1 and location l0 are connected, space s0 is adjacent to space s1, vehicle v0 does not contain space s0, vehicle v0 is not located at location l1, vehicle v1 does not have space s1, and vehicle v1 is located at location l0. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "baa82a7a-9d30-41fd-9c59-94fe7c2e8064", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is in vehicle v0, cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c1 is at location l1, cargo c1 is located in vehicle v0, cargo c1 is located in vehicle v1, cargo c2 is located in vehicle v1, cargo c2 is not situated at location l0, cargo c2 is situated at location l1, cargo c3 is located in vehicle v0, cargo c3 is not in vehicle v1, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c4 is not in vehicle v1, cargo c5 is located in vehicle v1, cargo c5 is not in vehicle v0, cargo c5 is present at location l0, cargo c6 is at location l1, cargo c6 is not in vehicle v0, cargo c6 is not located in vehicle v1, cargo c7 is not located in vehicle v0, cargo c7 is not located in vehicle v1, cargo c7 is present at location l1, cargo c8 is at location l0, cargo c9 is situated at location l0, fuel f0 does not exist in location l1, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel level f0 neighbors fuel level f7, fuel level f1 does not neighbour fuel level f3, fuel level f1 neighbors fuel level f4, fuel level f1 neighbors fuel level f5, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f8, fuel level f2 neighbors fuel level f0, fuel level f2 neighbors fuel level f4, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f8, fuel level f4 neighbors fuel level f0, fuel level f4 neighbors fuel level f1, fuel level f4 neighbors fuel level f7, fuel level f6 does not neighbour fuel level f3, fuel level f6 neighbors fuel level f1, fuel level f6 neighbors fuel level f2, fuel level f7 neighbors fuel level f2, fuel level f7 neighbors fuel level f5, fuel level f7 neighbors fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 neighbors fuel level f2, fuel level f8 neighbors fuel level f3, fuel level f8 neighbors fuel level f4, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f1 are neighbors, fuel-levels f3 and f5 are neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are neighbors, fuel-levels f5 and f2 are neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f7 are neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are neighbors, fuel-levels f7 and f1 are neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are neighbors, fuel-levels f8 and f7 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f2, location l0 has a fuel-level of f1, location l0 has a fuel-level of f5, location l0 has a fuel-level of f6, location l0 has a fuel-level of f7, location l0 has fuel f8, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f6, location l1 does not have a fuel-level of f7, location l1 has a fuel-level of f3, location l1 has fuel f1, location l1 has fuel f5, space s1 neighbors space s0, vehicle v0 contains cargo c4, vehicle v0 contains cargo c9, vehicle v0 does not contain cargo c8, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains cargo c8, vehicle v1 contains cargo c9, vehicle v1 does not contain cargo c0, vehicle v1 does not contain space s0 and vehicle v1 is at location l1. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? \n\ncargo c0 is in vehicle v0, \ncargo c0 is not located at location l1, \ncargo c0 is absent from location l1, \ncargo c1 is present at location l1, \ncargo c1 is inside vehicle v0, \ncargo c1 is inside vehicle v1, \ncargo c2 is inside vehicle v1, \ncargo c2 is not found at location l0, \ncargo c2 is situated at location l1, \ncargo c3 is inside vehicle v0, \ncargo c3 is not inside vehicle v1, \ncargo c3 is present at location l0, \ncargo c4 is at location l0, \ncargo c4 is not inside vehicle v1, \ncargo c5 is inside vehicle v1, \ncargo c5 is not inside vehicle v0, \ncargo c5 is present at location l0, \ncargo c6 is at location l1, \ncargo c6 is not inside vehicle v0, \ncargo c6 is not inside vehicle v1, \ncargo c7 is not inside vehicle v0, \ncargo c7 is not inside vehicle v1, \ncargo c7 is present at location l1, \ncargo c8 is at location l0, \ncargo c9 is situated at location l0, \nfuel f0 does not exist at location l1, \nfuel f4 does not exist at location l0, \nfuel f4 does not exist at location l1, \nfuel level f0 is adjacent to fuel level f7, \nfuel level f1 is not adjacent to fuel level f3, \nfuel level f1 is adjacent to fuel level f4, \nfuel level f1 is adjacent to fuel level f5, \nfuel level f2 is not adjacent to fuel level f1, \nfuel level f2 is not adjacent to fuel level f6, \nfuel level f2 is not adjacent to fuel level f7, \nfuel level f2 is not adjacent to fuel level f8, \nfuel level f2 is adjacent to fuel level f0, \nfuel level f2 is adjacent to fuel level f4, \nfuel level f3 is not adjacent to fuel level f0, \nfuel level f3 is not adjacent to fuel level f2, \nfuel level f3 is not adjacent to fuel level f6, \nfuel level f3 is not adjacent to fuel level f7, \nfuel level f3 is not adjacent to fuel level f8, \nfuel level f4 is not adjacent to fuel level f2, \nfuel level f4 is not adjacent to fuel level f8, \nfuel level f4 is adjacent to fuel level f0, \nfuel level f4 is adjacent to fuel level f1, \nfuel level f4 is adjacent to fuel level f7, \nfuel level f6 is not adjacent to fuel level f3, \nfuel level f6 is adjacent to fuel level f1, \nfuel level f6 is adjacent to fuel level f2, \nfuel level f7 is adjacent to fuel level f2, \nfuel level f7 is adjacent to fuel level f5, \nfuel level f7 is adjacent to fuel level f6, \nfuel level f8 is not adjacent to fuel level f0, \nfuel level f8 is not adjacent to fuel level f1, \nfuel level f8 is not adjacent to fuel level f5, \nfuel level f8 is not adjacent to fuel level f6, \nfuel level f8 is adjacent to fuel level f2, \nfuel level f8 is adjacent to fuel level f3, \nfuel level f8 is adjacent to fuel level f4, \nfuel-levels f0 and f2 are not adjacent, \nfuel-levels f0 and f3 are not adjacent, \nfuel-levels f0 and f4 are adjacent, \nfuel-levels f0 and f5 are not adjacent, \nfuel-levels f0 and f6 are not adjacent, \nfuel-levels f0 and f8 are not adjacent, \nfuel-levels f1 and f0 are adjacent, \nfuel-levels f1 and f6 are not adjacent, \nfuel-levels f1 and f7 are not adjacent, \nfuel-levels f1 and f8 are not adjacent, \nfuel-levels f2 and f5 are not adjacent, \nfuel-levels f3 and f1 are adjacent, \nfuel-levels f3 and f5 are adjacent, \nfuel-levels f4 and f3 are not adjacent, \nfuel-levels f4 and f6 are not adjacent, \nfuel-levels f5 and f0 are not adjacent, \nfuel-levels f5 and f1 are adjacent, \nfuel-levels f5 and f2 are adjacent, \nfuel-levels f5 and f3 are not adjacent, \nfuel-levels f5 and f4 are not adjacent, \nfuel-levels f5 and f7 are adjacent, \nfuel-levels f5 and f8 are not adjacent, \nfuel-levels f6 and f0 are not adjacent, \nfuel-levels f6 and f4 are not adjacent, \nfuel-levels f6 and f5 are not adjacent, \nfuel-levels f6 and f8 are not adjacent, \nfuel-levels f7 and f0 are adjacent, \nfuel-levels f7 and f1 are adjacent, \nfuel-levels f7 and f3 are not adjacent, \nfuel-levels f7 and f4 are adjacent, \nfuel-levels f8 and f7 are not adjacent, \nlocation l0 does not have a fuel-level of f0, \nlocation l0 does not have a fuel-level of f2, \nlocation l0 has a fuel-level of f1, \nlocation l0 has a fuel-level of f5, \nlocation l0 has a fuel-level of f6, \nlocation l0 has a fuel-level of f7, \nlocation l0 has fuel f8, \nlocation l1 does not have a fuel-level of f2, \nlocation l1 does not have a fuel-level of f6, \nlocation l1 does not have a fuel-level of f7, \nlocation l1 has a fuel-level of f3, \nlocation l1 has fuel f1, \nlocation l1 has fuel f5, \nspace s1 is adjacent to space s0, \nvehicle v0 contains cargo c4, \nvehicle v0 contains cargo c9, \nvehicle v0 does not contain cargo c8, \nvehicle v0 has space s1, \nvehicle v0 is present at location l0, \nvehicle v1 contains cargo c8, \nvehicle v1 contains cargo c9, \nvehicle v1 does not contain cargo c0, \nvehicle v1 does not contain space s0 and \nvehicle v1 is at location l1.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "da8f64cb-85b8-4021-afff-9f79fb466039", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not located in vehicle v0, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not in vehicle v0, cargo c1 is not in vehicle v1, cargo c2 is not situated at location l0, cargo c3 is not in vehicle v0, cargo c3 is not in vehicle v1, cargo c3 is not situated at location l1, cargo c4 is not located in vehicle v0, cargo c4 is not situated at location l0, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not in vehicle v0, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not located in vehicle v0, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not in vehicle v1, cargo c8 is not located in vehicle v0, fuel f1 does not exist in location l1, fuel f2 does not exist in location l1, fuel f3 does not exist in location l1, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f6, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f7, location l0 does not have fuel f1, location l0 does not have fuel f3, location l0 does not have fuel f6, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f6, location l1 does not have fuel f0, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c7, vehicle v0 does not have space s0, vehicle v0 is not situated at location l0, vehicle v1 does not contain cargo c0, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c6, vehicle v1 does not have space s0 and vehicle v1 is not present at location l0. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1. In this state, are all of the following valid properties of the state that involve negations? \n\ncargo c0 is not located at location l0, \ncargo c0 is not present at location l0, \ncargo c0 is not in vehicle v0, \ncargo c1 is not at location l0, \ncargo c1 is not present at location l0, \ncargo c1 is not in vehicle v0, \ncargo c1 is not in vehicle v1, \ncargo c2 is not at location l0, \ncargo c3 is not in vehicle v0, \ncargo c3 is not in vehicle v1, \ncargo c3 is not at location l1, \ncargo c4 is not in vehicle v0, \ncargo c4 is not at location l0, \ncargo c5 is not at location l1, \ncargo c5 is not present at location l1, \ncargo c5 is not in vehicle v0, \ncargo c6 is not at location l0, \ncargo c6 is not present at location l0, \ncargo c6 is not in vehicle v0, \ncargo c7 is not at location l0, \ncargo c7 is not present at location l0, \ncargo c7 is not in vehicle v1, \ncargo c8 is not at location l1, \ncargo c8 is not present at location l1, \ncargo c8 is not in vehicle v1, \ncargo c8 is not in vehicle v0, \nfuel f1 does not exist at location l1, \nfuel f2 does not exist at location l1, \nfuel f3 does not exist at location l1, \nfuel f5 does not exist at location l0, \nfuel levels f0 and f2 are not adjacent, \nfuel levels f0 and f4 are not adjacent, \nfuel levels f0 and f5 are not adjacent, \nfuel levels f0 and f6 are not adjacent, \nfuel levels f2 and f5 are not adjacent, \nfuel levels f2 and f7 are not adjacent, \nfuel levels f3 and f7 are not adjacent, \nfuel levels f4 and f3 are not adjacent, \nfuel levels f4 and f6 are not adjacent, \nfuel levels f5 and f0 are not adjacent, \nfuel levels f5 and f1 are not adjacent, \nfuel levels f5 and f2 are not adjacent, \nfuel levels f6 and f3 are not adjacent, \nfuel levels f6 and f4 are not adjacent, \nfuel levels f7 and f1 are not adjacent, \nfuel levels f7 and f2 are not adjacent, \nfuel levels f7 and f3 are not adjacent, \nfuel levels f7 and f6 are not adjacent, \nfuel levels f0 and f3 are not adjacent, \nfuel levels f0 and f7 are not adjacent, \nfuel levels f1 and f0 are not adjacent, \nfuel levels f1 and f3 are not adjacent, \nfuel levels f1 and f4 are not adjacent, \nfuel levels f1 and f5 are not adjacent, \nfuel levels f1 and f6 are not adjacent, \nfuel levels f1 and f7 are not adjacent, \nfuel levels f2 and f0 are not adjacent, \nfuel levels f2 and f1 are not adjacent, \nfuel levels f2 and f4 are not adjacent, \nfuel levels f2 and f6 are not adjacent, \nfuel levels f3 and f0 are not adjacent, \nfuel levels f3 and f1 are not adjacent, \nfuel levels f3 and f2 are not adjacent, \nfuel levels f3 and f5 are not adjacent, \nfuel levels f3 and f6 are not adjacent, \nfuel levels f4 and f0 are not adjacent, \nfuel levels f4 and f1 are not adjacent, \nfuel levels f4 and f2 are not adjacent, \nfuel levels f4 and f7 are not adjacent, \nfuel levels f5 and f3 are not adjacent, \nfuel levels f5 and f4 are not adjacent, \nfuel levels f5 and f7 are not adjacent, \nfuel levels f6 and f0 are not adjacent, \nfuel levels f6 and f1 are not adjacent, \nfuel levels f6 and f2 are not adjacent, \nfuel levels f6 and f5 are not adjacent, \nfuel levels f7 and f0 are not adjacent, \nfuel levels f7 and f4 are not adjacent, \nfuel levels f7 and f5 are not adjacent, \nlocation l0 does not have fuel level f0, \nlocation l0 does not have fuel level f2, \nlocation l0 does not have fuel level f7, \nlocation l0 does not have fuel f1, \nlocation l0 does not have fuel f3, \nlocation l0 does not have fuel f6, \nlocation l1 does not have fuel level f4, \nlocation l1 does not have fuel level f5, \nlocation l1 does not have fuel level f6, \nlocation l1 does not have fuel f0, \nspace s1 does not neighbour space s0, \nvehicle v0 does not contain cargo c2, \nvehicle v0 does not contain cargo c7, \nvehicle v0 does not have space s0, \nvehicle v0 is not at location l0, \nvehicle v1 does not contain cargo c0, \nvehicle v1 does not contain cargo c2, \nvehicle v1 does not contain cargo c4, \nvehicle v1 does not contain cargo c5, \nvehicle v1 does not contain cargo c6, \nvehicle v1 does not have space s0, \nvehicle v1 is not at location l0.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "b5159be6-e57f-4333-8dd4-72806590cb68", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is situated at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not situated at location l0, cargo c10 is not at location l1cargo c10 is not present at location l1, cargo c10 is not in vehicle v0, cargo c2 is not situated at location l1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not located in vehicle v0, cargo c4 is not located in vehicle v0, cargo c4 is present at location l0, cargo c5 is in vehicle v0, cargo c5 is situated at location l0, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l0, cargo c7 is not situated at location l0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v0, cargo c9 is present at location l0, fuel f2 exists in location l0, fuel f3 exists in location l1, fuel f5 does not exist in location l1, fuel f6 does not exist in location l0, fuel f8 exists in location l0, fuel level f0 neighbors fuel level f3, fuel level f0 neighbors fuel level f6, fuel level f0 neighbors fuel level f7, fuel level f1 does not neighbour fuel level f3, fuel level f1 neighbors fuel level f4, fuel level f1 neighbors fuel level f7, fuel level f1 neighbors fuel level f8, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f2 neighbors fuel level f8, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 neighbors fuel level f1, fuel level f3 neighbors fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 neighbors fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f8, fuel level f5 neighbors fuel level f4, fuel level f5 neighbors fuel level f7, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f6 neighbors fuel level f1, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel level f7 neighbors fuel level f0, fuel level f8 neighbors fuel level f0, fuel level f8 neighbors fuel level f2, fuel level f8 neighbors fuel level f5, fuel level f8 neighbors fuel level f6, fuel-levels f0 and f2 are neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f8 are neighbors, fuel-levels f1 and f0 are neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are neighbors, fuel-levels f2 and f1 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are neighbors, fuel-levels f4 and f0 are neighbors, fuel-levels f4 and f6 are neighbors, fuel-levels f4 and f8 are neighbors, fuel-levels f5 and f1 are neighbors, fuel-levels f5 and f2 are neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f8 are neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are neighbors, fuel-levels f7 and f3 are neighbors, fuel-levels f7 and f6 are neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f4 are neighbors, fuel-levels f8 and f7 are neighbors, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f5, location l0 does not have fuel f4, location l0 has a fuel-level of f0, location l0 has fuel f3, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f4, location l1 does not have fuel f7, location l1 has a fuel-level of f8, location l1 has fuel f1, location l1 has fuel f2, spaces s1 and s0 are neighbors, vehicle v0 contains cargo c2, vehicle v0 contains cargo c9, vehicle v0 contains space s1, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c7 and vehicle v0 is not at location l0. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? \n\n- Cargo c0 is located at location l0.\n- Cargo c1 is not located at location l1.\n- Cargo c1 is not present at location l1.\n- Cargo c1 is not situated at location l0.\n- Cargo c10 is not located at location l1.\n- Cargo c10 is not present at location l1.\n- Cargo c10 is not in vehicle v0.\n- Cargo c2 is not situated at location l1.\n- Cargo c3 is not located at location l1.\n- Cargo c3 is not present at location l1.\n- Cargo c3 is not located in vehicle v0.\n- Cargo c4 is not located in vehicle v0.\n- Cargo c4 is present at location l0.\n- Cargo c5 is in vehicle v0.\n- Cargo c5 is situated at location l0.\n- Cargo c6 is not located in vehicle v0.\n- Cargo c6 is not situated at location l0.\n- Cargo c7 is not situated at location l0.\n- Cargo c8 is not located at location l0.\n- Cargo c8 is not present at location l0.\n- Cargo c8 is not in vehicle v0.\n- Cargo c9 is present at location l0.\n- Fuel f2 exists at location l0.\n- Fuel f3 exists at location l1.\n- Fuel f5 does not exist at location l1.\n- Fuel f6 does not exist at location l0.\n- Fuel f8 exists at location l0.\n- Fuel level f0 is adjacent to fuel level f3.\n- Fuel level f0 is adjacent to fuel level f6.\n- Fuel level f0 is adjacent to fuel level f7.\n- Fuel level f1 is not adjacent to fuel level f3.\n- Fuel level f1 is adjacent to fuel level f4.\n- Fuel level f1 is adjacent to fuel level f7.\n- Fuel level f1 is adjacent to fuel level f8.\n- Fuel level f2 is not adjacent to fuel level f0.\n- Fuel level f2 is not adjacent to fuel level f4.\n- Fuel level f2 is not adjacent to fuel level f6.\n- Fuel level f2 is not adjacent to fuel level f7.\n- Fuel level f2 is adjacent to fuel level f8.\n- Fuel level f3 is not adjacent to fuel level f0.\n- Fuel level f3 is not adjacent to fuel level f6.\n- Fuel level f3 is not adjacent to fuel level f7.\n- Fuel level f3 is adjacent to fuel level f1.\n- Fuel level f3 is adjacent to fuel level f8.\n- Fuel level f4 is not adjacent to fuel level f1.\n- Fuel level f4 is not adjacent to fuel level f2.\n- Fuel level f4 is not adjacent to fuel level f3.\n- Fuel level f4 is adjacent to fuel level f7.\n- Fuel level f5 is not adjacent to fuel level f0.\n- Fuel level f5 is not adjacent to fuel level f3.\n- Fuel level f5 is not adjacent to fuel level f8.\n- Fuel level f5 is adjacent to fuel level f4.\n- Fuel level f5 is adjacent to fuel level f7.\n- Fuel level f6 is not adjacent to fuel level f2.\n- Fuel level f6 is not adjacent to fuel level f3.\n- Fuel level f6 is not adjacent to fuel level f5.\n- Fuel level f6 is adjacent to fuel level f1.\n- Fuel level f7 is not adjacent to fuel level f4.\n- Fuel level f7 is not adjacent to fuel level f5.\n- Fuel level f7 is adjacent to fuel level f0.\n- Fuel level f8 is adjacent to fuel level f0.\n- Fuel level f8 is adjacent to fuel level f2.\n- Fuel level f8 is adjacent to fuel level f5.\n- Fuel level f8 is adjacent to fuel level f6.\n- Fuel levels f0 and f2 are adjacent.\n- Fuel levels f0 and f4 are not adjacent.\n- Fuel levels f0 and f5 are not adjacent.\n- Fuel levels f0 and f8 are adjacent.\n- Fuel levels f1 and f0 are adjacent.\n- Fuel levels f1 and f5 are not adjacent.\n- Fuel levels f1 and f6 are adjacent.\n- Fuel levels f2 and f1 are adjacent.\n- Fuel levels f2 and f5 are not adjacent.\n- Fuel levels f3 and f2 are not adjacent.\n- Fuel levels f3 and f5 are adjacent.\n- Fuel levels f4 and f0 are adjacent.\n- Fuel levels f4 and f6 are adjacent.\n- Fuel levels f4 and f8 are adjacent.\n- Fuel levels f5 and f1 are adjacent.\n- Fuel levels f5 and f2 are adjacent.\n- Fuel levels f6 and f0 are not adjacent.\n- Fuel levels f6 and f4 are not adjacent.\n- Fuel levels f6 and f8 are adjacent.\n- Fuel levels f7 and f1 are not adjacent.\n- Fuel levels f7 and f2 are adjacent.\n- Fuel levels f7 and f3 are adjacent.\n- Fuel levels f7 and f6 are adjacent.\n- Fuel levels f8 and f1 are not adjacent.\n- Fuel levels f8 and f3 are not adjacent.\n- Fuel levels f8 and f4 are adjacent.\n- Fuel levels f8 and f7 are adjacent.\n- Location l0 does not have a fuel level of f1.\n- Location l0 does not have a fuel level of f5.\n- Location l0 does not have fuel f4.\n- Location l0 has a fuel level of f0.\n- Location l0 has fuel f3.\n- Location l1 does not have a fuel level of f0.\n- Location l1 does not have a fuel level of f4.\n- Location l1 does not have fuel f7.\n- Location l1 has a fuel level of f8.\n- Location l1 has fuel f1.\n- Location l1 has fuel f2.\n- Spaces s1 and s0 are adjacent.\n- Vehicle v0 contains cargo c2.\n- Vehicle v0 contains cargo c9.\n- Vehicle v0 contains space s1.\n- Vehicle v0 does not contain cargo c0.\n- Vehicle v0 does not contain cargo c7.\n- Vehicle v0 is not at location l0.\n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is positioned at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is located at l1, cargo c5 is also at l1, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is also at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is positioned at location l1."}
{"question_id": "b9e00be6-15e4-452a-9389-0dd42841cb15", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is present at location l1, cargo c1 is located in vehicle v0, cargo c10 is situated at location l0, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l1, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f7, location l0 is connected to location l1, location l1 has fuel f6, space s0 neighbors space s1, there is a connection between locations l1 and l0, vehicle v0 has space s0 and vehicle v0 is situated at location l1. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, are the following properties valid without involving negations? cargo c0 is at location l1, cargo c1 is in vehicle v0, cargo c10 is at location l0, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is at location l1, cargo c9 is at location l1, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 has a fuel level of f7, location l0 is connected to location l1, location l1 has fuel f6, spaces s0 and s1 are adjacent, locations l1 and l0 are connected, vehicle v0 has space s0, and vehicle v0 is at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "48a1cd8a-d35b-4525-8e4d-b1cbedd961f4", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? cargo c0 is not in vehicle v0, cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l0, cargo c0 is situated at location l1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is situated at location l1, cargo c2 is not in vehicle v0, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l0, cargo c2 is situated at location l1, cargo c3 is not located in vehicle v0, cargo c3 is not located in vehicle v1, cargo c3 is not situated at location l1, cargo c3 is present at location l0, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not located in vehicle v0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not located in vehicle v1, cargo c6 is at location l1, cargo c6 is not in vehicle v1, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l0, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not in vehicle v0, cargo c7 is situated at location l1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not in vehicle v0, cargo c8 is not in vehicle v1, cargo c8 is situated at location l0, fuel f0 does not exist in location l1, fuel f1 does not exist in location l0, fuel f1 does not exist in location l1, fuel f2 does not exist in location l0, fuel f6 does not exist in location l0, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f7, fuel level f0 neighbors fuel level f1, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f6, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f6 neighbors fuel level f7, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f6, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f5 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f0, location l0 does not have fuel f3, location l0 does not have fuel f5, location l0 has a fuel-level of f4, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f6, location l1 does not have fuel f2, location l1 does not have fuel f4, location l1 has fuel f7, location l1 is connected to location l0, space s1 does not neighbour space s0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c5, vehicle v0 does not contain space s0, vehicle v0 is not situated at location l0, vehicle v0 is present at location l1, vehicle v1 contains space s1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0, vehicle v1 is not situated at location l0 and vehicle v1 is situated at location l1. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1. In this state, are the following properties of the state (both with and without negations) valid? \n\nCargo c0 is not in vehicle v0, cargo c0 is not in vehicle v1, cargo c0 is not at location l0, cargo c0 is at location l1, cargo c1 is not at location l0, cargo c1 is not at location l0, cargo c1 is at location l1, cargo c2 is not in vehicle v0, cargo c2 is not in vehicle v1, cargo c2 is not at location l0, cargo c2 is at location l1, cargo c3 is not in vehicle v0, cargo c3 is not in vehicle v1, cargo c3 is not at location l1, cargo c3 is at location l0, cargo c4 is not at location l0, cargo c4 is not at location l0, cargo c4 is not in vehicle v0, cargo c4 is at location l1, cargo c5 is at location l0, cargo c5 is not at location l1, cargo c5 is not at location l1, cargo c5 is not in vehicle v1, cargo c6 is at location l1, cargo c6 is not in vehicle v1, cargo c6 is not in vehicle v0, cargo c6 is not at location l0, cargo c7 is not at location l0, cargo c7 is not at location l0, cargo c7 is not in vehicle v0, cargo c7 is at location l1, cargo c8 is not at location l1, cargo c8 is not at location l1, cargo c8 is not in vehicle v0, cargo c8 is not in vehicle v1, cargo c8 is at location l0, fuel f0 does not exist at location l1, fuel f1 does not exist at location l0, fuel f1 does not exist at location l1, fuel f2 does not exist at location l0, fuel f6 does not exist at location l0, fuel f7 does not exist at location l0, fuel level f0 is not adjacent to fuel level f4, fuel level f0 is not adjacent to fuel level f5, fuel level f0 is not adjacent to fuel level f6, fuel level f0 is not adjacent to fuel level f7, fuel level f0 is adjacent to fuel level f1, fuel level f1 is not adjacent to fuel level f4, fuel level f1 is not adjacent to fuel level f6, fuel level f1 is adjacent to fuel level f2, fuel level f2 is not adjacent to fuel level f4, fuel level f2 is not adjacent to fuel level f6, fuel level f2 is not adjacent to fuel level f7, fuel level f3 is not adjacent to fuel level f0, fuel level f3 is not adjacent to fuel level f1, fuel level f3 is not adjacent to fuel level f2, fuel level f3 is not adjacent to fuel level f5, fuel level f4 is not adjacent to fuel level f3, fuel level f4 is not adjacent to fuel level f7, fuel level f5 is not adjacent to fuel level f3, fuel level f5 is not adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is not adjacent to fuel level f3, fuel level f6 is not adjacent to fuel level f4, fuel level f6 is adjacent to fuel level f7, fuel level f7 is not adjacent to fuel level f1, fuel level f7 is not adjacent to fuel level f2, fuel level f7 is not adjacent to fuel level f3, fuel level f7 is not adjacent to fuel level f4, fuel level f7 is not adjacent to fuel level f6, fuel levels f0 and f2 are not adjacent, fuel levels f0 and f3 are not adjacent, fuel levels f1 and f0 are not adjacent, fuel levels f1 and f3 are not adjacent, fuel levels f1 and f5 are not adjacent, fuel levels f1 and f7 are not adjacent, fuel levels f2 and f0 are not adjacent, fuel levels f2 and f1 are not adjacent, fuel levels f2 and f3 are adjacent, fuel levels f2 and f5 are not adjacent, fuel levels f3 and f4 are adjacent, fuel levels f3 and f6 are not adjacent, fuel levels f3 and f7 are not adjacent, fuel levels f4 and f0 are not adjacent, fuel levels f4 and f1 are not adjacent, fuel levels f4 and f2 are not adjacent, fuel levels f4 and f5 are adjacent, fuel levels f4 and f6 are not adjacent, fuel levels f5 and f0 are not adjacent, fuel levels f5 and f1 are not adjacent, fuel levels f5 and f2 are not adjacent, fuel levels f5 and f7 are not adjacent, fuel levels f6 and f0 are not adjacent, fuel levels f6 and f1 are not adjacent, fuel levels f6 and f2 are not adjacent, fuel levels f6 and f5 are not adjacent, fuel levels f7 and f0 are not adjacent, fuel levels f7 and f5 are not adjacent, locations l0 and l1 are connected, location l0 does not have a fuel level of f0, location l0 does not have fuel f3, location l0 does not have fuel f5, location l0 has a fuel level of f4, location l1 does not have a fuel level of f3, location l1 does not have a fuel level of f5, location l1 does not have a fuel level of f6, location l1 does not have fuel f2, location l1 does not have fuel f4, location l1 has fuel f7, location l1 is connected to location l0, space s1 does not neighbor space s0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c5, vehicle v0 does not contain space s0, vehicle v0 is not at location l0, vehicle v0 is at location l1, vehicle v1 contains space s1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0, vehicle v1 is not at location l0 and vehicle v1 is at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "ae81c0fe-6dfd-4dae-8588-b6fd68f84950", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c1 is not located in vehicle v0, cargo c1 is not situated at location l1, cargo c10 is not situated at location l1, cargo c2 is not in vehicle v0, cargo c2 is not situated at location l1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c4 is not in vehicle v0, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c8 is not in vehicle v0, cargo c8 is not situated at location l0, cargo c9 is not at location l0cargo c9 is not present at location l0, fuel f1 does not exist in location l0, fuel f1 does not exist in location l1, fuel f2 does not exist in location l0, fuel f3 does not exist in location l0, fuel f5 does not exist in location l1, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f7, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f6 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f4, location l0 does not have fuel f6, location l0 does not have fuel f8, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f7, location l1 does not have a fuel-level of f8, location l1 does not have fuel f2, location l1 does not have fuel f6, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c5, vehicle v0 does not contain cargo c7, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s1 and vehicle v0 is not present at location l1. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f7 and f6, to location l1. At location l1, cargo c4 is loaded into vehicle v0, which has spaces s1 and s0. Then, vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f6 and f5, to location l1. At location l1, cargo c6 is loaded into vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f4 and f3, to reach the current state. \n\nIn this state, are all of the following valid properties of the state that involve negations? \ncargo c0 is not located at location l0, \ncargo c0 is not present at location l0, \ncargo c1 is not in vehicle v0, \ncargo c1 is not situated at location l1, \ncargo c10 is not situated at location l1, \ncargo c2 is not in vehicle v0, \ncargo c2 is not situated at location l1, \ncargo c3 is not at location l1, \ncargo c3 is not present at location l1, \ncargo c4 is not at location l1, \ncargo c4 is not present at location l1, \ncargo c4 is not in vehicle v0, \ncargo c5 is not at location l0, \ncargo c5 is not present at location l0, \ncargo c6 is not at location l0, \ncargo c6 is not present at location l0, \ncargo c6 is not at location l1, \ncargo c6 is not present at location l1, \ncargo c7 is not at location l0, \ncargo c7 is not present at location l0, \ncargo c8 is not in vehicle v0, \ncargo c8 is not situated at location l0, \ncargo c9 is not at location l0, \ncargo c9 is not present at location l0, \nfuel f1 does not exist at location l0, \nfuel f1 does not exist at location l1, \nfuel f2 does not exist at location l0, \nfuel f3 does not exist at location l0, \nfuel f5 does not exist at location l1, \nfuel f7 does not exist at location l0, \nfuel level f0 is not adjacent to fuel level f5, \nfuel level f0 is not adjacent to fuel level f6, \nfuel level f0 is not adjacent to fuel level f7, \nfuel level f0 is not adjacent to fuel level f8, \nfuel level f1 is not adjacent to fuel level f0, \nfuel level f1 is not adjacent to fuel level f3, \nfuel level f1 is not adjacent to fuel level f4, \nfuel level f1 is not adjacent to fuel level f5, \nfuel level f1 is not adjacent to fuel level f6, \nfuel level f2 is not adjacent to fuel level f0, \nfuel level f2 is not adjacent to fuel level f1, \nfuel level f2 is not adjacent to fuel level f5, \nfuel level f2 is not adjacent to fuel level f7, \nfuel level f2 is not adjacent to fuel level f8, \nfuel level f3 is not adjacent to fuel level f0, \nfuel level f3 is not adjacent to fuel level f2, \nfuel level f3 is not adjacent to fuel level f7, \nfuel level f3 is not adjacent to fuel level f8, \nfuel level f4 is not adjacent to fuel level f0, \nfuel level f4 is not adjacent to fuel level f1, \nfuel level f4 is not adjacent to fuel level f3, \nfuel level f5 is not adjacent to fuel level f2, \nfuel level f5 is not adjacent to fuel level f3, \nfuel level f5 is not adjacent to fuel level f7, \nfuel level f5 is not adjacent to fuel level f8, \nfuel level f6 is not adjacent to fuel level f1, \nfuel level f6 is not adjacent to fuel level f2, \nfuel level f6 is not adjacent to fuel level f4, \nfuel level f6 is not adjacent to fuel level f5, \nfuel level f8 is not adjacent to fuel level f2, \nfuel level f8 is not adjacent to fuel level f3, \nfuel level f8 is not adjacent to fuel level f4, \nfuel level f8 is not adjacent to fuel level f7, \nfuel levels f0 and f2 are not adjacent, \nfuel levels f0 and f3 are not adjacent, \nfuel levels f0 and f4 are not adjacent, \nfuel levels f1 and f7 are not adjacent, \nfuel levels f1 and f8 are not adjacent, \nfuel levels f2 and f4 are not adjacent, \nfuel levels f2 and f6 are not adjacent, \nfuel levels f3 and f1 are not adjacent, \nfuel levels f3 and f5 are not adjacent, \nfuel levels f3 and f6 are not adjacent, \nfuel levels f4 and f2 are not adjacent, \nfuel levels f4 and f6 are not adjacent, \nfuel levels f4 and f7 are not adjacent, \nfuel levels f4 and f8 are not adjacent, \nfuel levels f5 and f0 are not adjacent, \nfuel levels f5 and f1 are not adjacent, \nfuel levels f5 and f4 are not adjacent, \nfuel levels f6 and f0 are not adjacent, \nfuel levels f6 and f3 are not adjacent, \nfuel levels f6 and f8 are not adjacent, \nfuel levels f7 and f0 are not adjacent, \nfuel levels f7 and f1 are not adjacent, \nfuel levels f7 and f2 are not adjacent, \nfuel levels f7 and f3 are not adjacent, \nfuel levels f7 and f4 are not adjacent, \nfuel levels f7 and f5 are not adjacent, \nfuel levels f7 and f6 are not adjacent, \nfuel levels f8 and f0 are not adjacent, \nfuel levels f8 and f1 are not adjacent, \nfuel levels f8 and f5 are not adjacent, \nfuel levels f8 and f6 are not adjacent, \nlocation l0 does not have a fuel level of f0, \nlocation l0 does not have a fuel level of f4, \nlocation l0 does not have fuel f6, \nlocation l0 does not have fuel f8, \nlocation l1 does not have a fuel level of f0, \nlocation l1 does not have a fuel level of f4, \nlocation l1 does not have a fuel level of f7, \nlocation l1 does not have a fuel level of f8, \nlocation l1 does not have fuel f2, \nlocation l1 does not have fuel f6, \nspace s1 is not adjacent to space s0, \nvehicle v0 does not contain cargo c0, \nvehicle v0 does not contain cargo c10, \nvehicle v0 does not contain cargo c3, \nvehicle v0 does not contain cargo c5, \nvehicle v0 does not contain cargo c7, \nvehicle v0 does not contain cargo c9, \nvehicle v0 does not have space s1, and \nvehicle v0 is not present at location l1. \n\nRespond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "db1f9217-993f-4d96-9d5b-70f5aa006239", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is present at location l1, cargo c5 is at location l0, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is present at location l0, fuel f7 exists in location l1, fuel level f3 neighbors fuel level f4, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has fuel f4, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is at location l1. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, are all of the following properties of the state valid, excluding those that involve negations? Cargo c0 is located at l1, cargo c1 is present at l1, cargo c2 is present at l1, cargo c3 is present at l0, cargo c4 is present at l1, cargo c5 is located at l0, cargo c6 is located at l1, cargo c7 is located at l1, cargo c8 is present at l0, fuel f7 is present in location l1, fuel level f3 is adjacent to fuel level f4, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, location l0 contains fuel f4, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 contains space s1, vehicle v0 is present at location l1, vehicle v1 contains space s1, and vehicle v1 is at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "52d438aa-c001-4c2f-bc74-869dd005fd83", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not located in vehicle v1, cargo c0 is situated at location l0, cargo c1 is located in vehicle v1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not located in vehicle v0, cargo c2 is located in vehicle v0, cargo c2 is located in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is located in vehicle v1, cargo c3 is not located in vehicle v0, cargo c3 is present at location l1, cargo c4 is in vehicle v0, cargo c4 is located in vehicle v1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c5 is located in vehicle v0, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c6 is in vehicle v1, cargo c6 is located in vehicle v0, cargo c6 is not situated at location l1, cargo c7 is located in vehicle v0, cargo c7 is present at location l0, cargo c8 is in vehicle v0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c9 is at location l1, cargo c9 is located in vehicle v1, cargo c9 is not at location l0cargo c9 is not present at location l0, fuel f0 does not exist in location l1, fuel f3 exists in location l1, fuel f4 does not exist in location l1, fuel f5 does not exist in location l0, fuel f6 does not exist in location l1, fuel f7 exists in location l1, fuel f8 does not exist in location l1, fuel f8 exists in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f0 neighbors fuel level f8, fuel level f1 does not neighbour fuel level f4, fuel level f1 neighbors fuel level f6, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 neighbors fuel level f6, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f4, fuel level f5 neighbors fuel level f7, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f5, fuel level f7 neighbors fuel level f2, fuel level f7 neighbors fuel level f3, fuel level f7 neighbors fuel level f4, fuel level f7 neighbors fuel level f6, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f6, fuel level f8 neighbors fuel level f1, fuel level f8 neighbors fuel level f2, fuel-levels f0 and f2 are neighbors, fuel-levels f0 and f3 are neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are neighbors, fuel-levels f1 and f5 are neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are neighbors, fuel-levels f2 and f0 are neighbors, fuel-levels f2 and f4 are neighbors, fuel-levels f2 and f5 are neighbors, fuel-levels f2 and f6 are neighbors, fuel-levels f2 and f8 are neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f3 and f8 are neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f2 are neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f3 are neighbors, fuel-levels f6 and f4 are neighbors, fuel-levels f6 and f8 are neighbors, fuel-levels f7 and f1 are neighbors, fuel-levels f8 and f0 are neighbors, fuel-levels f8 and f5 are neighbors, fuel-levels f8 and f7 are not neighbors, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f4, location l0 does not have a fuel-level of f6, location l0 does not have fuel f3, location l0 does not have fuel f7, location l0 has a fuel-level of f1, location l1 does not have fuel f2, location l1 has fuel f1, spaces s1 and s0 are neighbors, vehicle v0 contains cargo c0, vehicle v0 has space s1, vehicle v0 is not situated at location l0, vehicle v1 contains cargo c8, vehicle v1 contains space s0, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c7 and vehicle v1 is present at location l1. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c2 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, and cargo c0 is loaded into vehicle v0 with spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, where cargo c0 is unloaded from vehicle v0 with spaces s0 and s1. At location l1, cargo c3 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f7 and f6, to location l0, where cargo c3 is unloaded from vehicle v0 with spaces s0 and s1. At location l0, cargo c1 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1, where cargo c1 is unloaded from vehicle v0 with spaces s0 and s1. Cargo c5 is then loaded into vehicle v0 with spaces s1 and s0 at location l1, and vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0, where cargo c5 is unloaded from vehicle v0 with spaces s0 and s1. At location l0, cargo c7 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 moves from location l0, which has fuel levels f1 and f0, to location l1, where cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, and cargo c9 is loaded into vehicle v0 with spaces s1 and s0 at location l1, resulting in the current state. In this state, are all of the following valid properties of the state that involve negations? cargo c0 is not in vehicle v1, cargo c0 is at location l0, cargo c1 is in vehicle v1, cargo c1 is not at location l0, cargo c1 is not in vehicle v0, cargo c2 is in vehicle v0, cargo c2 is in vehicle v1, cargo c2 is not at location l1, cargo c3 is in vehicle v1, cargo c3 is not in vehicle v0, cargo c3 is at location l1, cargo c4 is not in vehicle v0, cargo c4 is not in vehicle v1, cargo c4 is not at location l0, cargo c5 is in vehicle v0, cargo c5 is not at location l1, cargo c6 is not in vehicle v1, cargo c6 is not in vehicle v0, cargo c6 is not at location l1, cargo c7 is in vehicle v0, cargo c7 is at location l0, cargo c8 is not in vehicle v0, cargo c8 is not at location l0, cargo c9 is at location l1, cargo c9 is in vehicle v1, cargo c9 is not at location l0, fuel f0 does not exist at location l1, fuel f3 exists at location l1, fuel f4 does not exist at location l1, fuel f5 does not exist at location l0, fuel f6 does not exist at location l1, fuel f7 exists at location l1, fuel f8 exists at location l0, fuel level f0 does not neighbor fuel level f4, fuel level f0 does not neighbor fuel level f6, fuel level f0 neighbors fuel level f8, fuel level f1 does not neighbor fuel level f4, fuel level f1 neighbors fuel level f6, fuel level f2 does not neighbor fuel level f1, fuel level f2 does not neighbor fuel level f7, fuel level f3 does not neighbor fuel level f0, fuel level f3 neighbors fuel level f6, fuel level f4 does not neighbor fuel level f0, fuel level f4 does not neighbor fuel level f1, fuel level f4 does not neighbor fuel level f3, fuel level f5 does not neighbor fuel level f1, fuel level f5 does not neighbor fuel level f4, fuel level f5 neighbors fuel level f7, fuel level f6 does not neighbor fuel level f0, fuel level f6 does not neighbor fuel level f1, fuel level f6 does not neighbor fuel level f5, fuel level f7 does not neighbor fuel level f0, fuel level f7 does not neighbor fuel level f5, fuel level f7 neighbors fuel level f2, fuel level f7 neighbors fuel level f3, fuel level f7 neighbors fuel level f4, fuel level f7 neighbors fuel level f6, fuel level f8 does not neighbor fuel level f3, fuel level f8 does not neighbor fuel level f4, fuel level f8 does not neighbor fuel level f6, fuel level f8 neighbors fuel level f1, fuel level f8 neighbors fuel level f2, fuel levels f0 and f2 are neighbors, fuel levels f0 and f3 are not neighbors, fuel levels f0 and f5 are not neighbors, fuel levels f0 and f7 are neighbors, fuel levels f1 and f0 are not neighbors, fuel levels f1 and f3 are neighbors, fuel levels f1 and f5 are neighbors, fuel levels f1 and f7 are not neighbors, fuel levels f1 and f8 are neighbors, fuel levels f2 and f0 are neighbors, fuel levels f2 and f4 are not neighbors, fuel levels f2 and f5 are neighbors, fuel levels f2 and f6 are neighbors, fuel levels f2 and f8 are neighbors, fuel levels f3 and f1 are not neighbors, fuel levels f3 and f2 are neighbors, fuel levels f3 and f5 are not neighbors, fuel levels f3 and f7 are not neighbors, fuel levels f3 and f8 are neighbors, fuel levels f4 and f2 are not neighbors, fuel levels f4 and f6 are not neighbors, fuel levels f4 and f7 are not neighbors, fuel levels f4 and f8 are not neighbors, fuel levels f5 and f0 are not neighbors, fuel levels f5 and f2 are neighbors, fuel levels f5 and f3 are not neighbors, fuel levels f5 and f8 are not neighbors, fuel levels f6 and f2 are not neighbors, fuel levels f6 and f3 are neighbors, fuel levels f6 and f4 are neighbors, fuel levels f6 and f8 are neighbors, fuel levels f7 and f1 are neighbors, fuel levels f8 and f0 are neighbors, fuel levels f8 and f5 are not neighbors, fuel levels f8 and f7 are not neighbors, location l0 does not have a fuel level of f2, location l0 does not have a fuel level of f4, location l0 does not have a fuel level of f6, location l0 does not have fuel f3, location l0 does not have fuel f7, location l0 has a fuel level of f1, location l1 does not have fuel f2, location l1 has fuel f1, spaces s1 and s0 are neighbors, vehicle v0 contains cargo c0, vehicle v0 has space s1, vehicle v0 is not at location l0, vehicle v1 contains cargo c8, vehicle v1 contains space s0, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c7, and vehicle v1 is at location l1. Respond with True or False.\n\nAnswer: True", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "2ba52541-58aa-4bc6-826e-0b07303e3abe", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is located in vehicle v0, cargo c3 is present at location l0, cargo c4 is present at location l1, cargo c5 is at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is present at location l0, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f2, location l1 has fuel f5, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 has space s0, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l1. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing spaces s0 and s1. Next, vehicle v0 returns to location l1, which has fuel levels f4 and f3, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Subsequently, vehicle v0 moves back to location l0, which now has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, freeing spaces s0 and s1. Finally, vehicle v0 returns to location l1, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, are the following properties valid without involving negations? cargo c0 is located at location l0, cargo c1 is present at location l0, cargo c2 is inside vehicle v0, cargo c3 is present at location l0, cargo c4 is present at location l1, cargo c5 is at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is present at location l0, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, locations l0 and l1 are connected, location l0 has a fuel level of f2, location l1 has fuel f5, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 has space s0, vehicle v0 is located at l1, vehicle v1 contains space s1, and vehicle v1 is situated at location l1. Respond with True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at l0, vehicle v1 contains space s1 and vehicle v1 is positioned at l1."}
