{"id": -3302321995949387375, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_15, block_20, block_6, block_13, block_9, and block_10. The following block(s) are stacked on top of another block: block_2 is on block_14, block_17 is on block_12, block_1 is on block_17, block_19 is on block_16, block_16 is on block_11, block_5 is on block_1, block_11 is on block_8, block_18 is on block_5, block_3 is on block_13, block_7 is on block_10, block_12 is on block_7, block_14 is on block_20, block_4 is on block_6, and block_8 is on block_4. The goal is to reach a state where the following facts hold: The block block_10 is currently situated under the block block_7, The block block_19 is on top of block block_16, The block block_2 is on top of block block_14, The block block_5 is on top of block block_1, The block block_12 is on top of block block_7, The block block_8 is currently situated under the block block_11, The block block_17 is currently situated above the block block_12, The block block_14 is currently situated above the block block_20, The block block_4 is on top of block block_6, The block block_5 is currently situated under the block block_18, The block block_16 is on top of block block_11, The block block_8 is currently situated above the block block_4, The block block_2 is currently situated under the block block_9, The block block_9 is currently situated under the block block_15, The block block_17 is currently situated under the block block_1, and The block block_13 is currently situated under the block block_3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_8", "answer": "no"}
{"id": -7137910774579353729, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5, block_2, and block_4. The following block(s) are stacked on top of another block: block_1 is on block_4 and block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_2 is currently situated under the block block_4, The block block_5 is currently situated above the block block_1, and The block block_2 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_2", "answer": "yes"}
{"id": -7254553642265898425, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_5 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is currently situated above the block block_1, and The block block_2 is on top of block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_2 is on top of block block_3", "answer": "no"}
{"id": -5317635039746009446, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_2 and block_4. The following block(s) are stacked on top of another block: block_3 is on block_2 and block_5 is on block_4. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_4 is on top of block block_2, The block block_5 is currently situated above the block block_1, and The block block_2 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_5 is currently situated under the block block_3", "answer": "no"}
{"id": 6224798673497953201, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_2, block_20, block_16, block_6, block_18, block_13, block_14, and block_10. The following block(s) are stacked on top of another block: block_17 is on block_12, block_15 is on block_9, block_1 is on block_17, block_19 is on block_16, block_9 is on block_2, block_11 is on block_8, block_3 is on block_13, block_7 is on block_10, block_12 is on block_7, block_4 is on block_6, and block_8 is on block_4. The goal is to reach a state where the following facts hold: The block block_7 is currently situated above the block block_10, The block block_16 is currently situated under the block block_19, The block block_2 is on top of block block_14, The block block_1 is currently situated under the block block_5, The block block_12 is currently situated above the block block_7, The block block_8 is currently situated under the block block_11, The block block_17 is on top of block block_12, The block block_14 is currently situated above the block block_20, The block block_4 is currently situated above the block block_6, The block block_18 is on top of block block_5, The block block_16 is currently situated above the block block_11, The block block_8 is on top of block block_4, The block block_9 is currently situated above the block block_2, The block block_9 is currently situated under the block block_15, The block block_17 is currently situated under the block block_1, and The block block_3 is currently situated above the block block_13.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_16 is currently being held by the robotic arm", "answer": "yes"}
{"id": 7085451526155926061, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_10 and block_2. The following block(s) are stacked on top of another block: block_9 is on block_5, block_4 is on block_9, block_8 is on block_10, block_3 is on block_4, block_7 is on block_3, block_5 is on block_2, and block_6 is on block_7. The goal is to reach a state where the following facts hold: The block block_9 is currently situated above the block block_1, The block block_7 is on top of block block_10, The block block_4 is currently situated above the block block_2, The block block_8 is currently situated under the block block_1, The block block_5 is on top of block block_9, The block block_3 is currently situated under the block block_6, The block block_3 is on top of block block_7, and The block block_2 is on top of block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? No blocks are placed on top of block_5", "answer": "yes"}
{"id": 2682874459393992293, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5, block_1, and block_4. The following block(s) is stacked on top of another block: block_3 is on block_4. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_5, The block block_4 is currently situated above the block block_1, and The block block_3 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_5", "answer": "yes"}
{"id": 8261394128363890052, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3, block_5, block_1, and block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is on top of block block_1, and The block block_5 is currently situated under the block block_2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is not holding anything", "answer": "yes"}
{"id": 3496520136928626784, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_5 and block_4. The following block(s) are stacked on top of another block: block_1 is on block_2 and block_2 is on block_4. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is on top of block block_1, and The block block_2 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? block_4 is not obstructed by any other blocks", "answer": "yes"}
{"id": -389379424263951733, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_2 and block_1. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_1 is currently situated above the block block_1", "answer": "no"}
{"id": 8057917460877948722, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_3 is currently situated under the block block_1, and The block block_2 is on top of block block_4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_1 is currently being held by the robotic arm", "answer": "yes"}
{"id": 733749781673395898, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5, block_3 is on block_1, and block_2 is on block_4. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_1 is currently situated above the block block_3, and The block block_2 is currently situated above the block block_4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? No blocks are placed on top of block_1", "answer": "yes"}
{"id": -7600613827097934085, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_2 and block_4. The following block(s) are stacked on top of another block: block_3 is on block_5 and block_5 is on block_2. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_1 is currently situated above the block block_3, and The block block_2 is on top of block block_4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is empty", "answer": "yes"}
{"id": -622379711637640856, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_5 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_4 and block_4 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_5, The block block_3 is currently situated under the block block_1, and The block block_4 is currently situated under the block block_2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_1", "answer": "yes"}
{"id": -2242158988487479973, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) is stacked on top of another block: block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3 and The block block_1 is currently situated above the block block_2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_2 is currently situated under the block block_2", "answer": "no"}
