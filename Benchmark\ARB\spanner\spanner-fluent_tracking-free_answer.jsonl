{"question_id": "7490f7c1-0e4e-46d5-96e8-c83526a21359", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob picks up spanner4 from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks from location4 to location5, from location5 to location6, bob walks, spanner5 is picked up by bob from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for location9? Write None if there are none", "answer": "location8 is linked to location9 and location9 and gate are linked", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He proceeds to location3, then to location4, and subsequently to location5 and location6. At location6, Bob picks up spanner5 and spanner2, resulting in the current state. In this state, what are the valid properties that do not involve negations for location9? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, also loose, nut4, not secured, and nut5, also not secured. The shed is connected to location1. The following spanners are available for use: spanner1, which is located at location8, spanner2, which is usable and located at location6, spanner3, located at location2, spanner4, also at location2, and spanner5, which is functional and located at location6."}
{"question_id": "a8e82ae5-9056-4dad-a993-3efd274a1cc9", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob picks up spanner5 from location1, spanner4 is picked up by bob from location1, bob walks to location2 from location1, bob walks from location2 to location3, from location3 to location4, bob walks, spanner1 is picked up by bob from location4, bob walks to location5 from location4, from location5 to location6, bob walks and from location6, bob picks up spanner3 to reach the current state. In this state, what are the valid properties of the state that involve negations for bob? Write None if there are none", "answer": "bob is not at location4, bob is not at location7, bob is not at shed, bob is not currently at gate, bob is not currently at location2, bob is not currently at location8, bob is not located at location1, bob is not located at location3, bob is not located at location5, bob is not located at location9 and spanner2 is not carried by bob", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1, and then walks to location5 and location6. Finally, Bob collects spanner3 at location6, resulting in the current state. In this state, what are the valid properties that involve negations for Bob? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1 is available and located at location4. Spanner2 is functional and currently at location7, while spanner3 is functional and at location6. Spanner4 is functional and located at location1, and spanner5 is also available and located at location1."}
{"question_id": "2dbc7763-f9d5-48d3-b1bf-63ebb2daab8b", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_10", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, bob walks to location3 from location2, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks to location6 from location5, from location6, bob picks up spanner5 and bob picks up spanner2 from location6 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for location6? Write None if there are none", "answer": "a link between gate and location6 does not exist, a link between location6 and gate does not exist, a link between location6 and location3 does not exist, a link between location6 and location5 does not exist, bob is located at location6, location1 and location6 are not linked, location2 is not linked to location6, location3 and location6 are not linked, location4 and location6 are not linked, location5 is linked to location6, location6 and location1 are not linked, location6 and location2 are not linked, location6 and location7 are linked, location6 is not linked to location4, location6 is not linked to location8, location6 is not linked to location9, location6 is not linked to shed, location7 and location6 are not linked, location8 and location6 are not linked, location9 is not linked to location6, nut1 is not at location6, nut2 is not currently at location6, nut3 is not located at location6, nut4 is not currently at location6, nut5 is not at location6, shed is not linked to location6, spanner1 is not located at location6, spanner2 is not at location6, spanner3 is not at location6, spanner4 is not currently at location6 and spanner5 is not located at location6", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He then proceeds to location3, followed by location4, location5, and finally location6. Upon reaching location6, Bob picks up spanner5 and spanner2. Considering the current state, what are the valid properties (including both affirmative and negative statements) that can be inferred about location6? If none exist, please state 'None'.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "7eaa86bc-19e3-4eb1-a099-0438b04e06dd", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, from location2, bob picks up spanner4, from location2, bob picks up spanner3, from location2 to location3, bob walks, bob walks from location3 to location4, bob walks from location4 to location5, from location5 to location6, bob walks, spanner5 is picked up by bob from location6 and bob picks up spanner2 from location6 to reach the current state. In this state, what are the valid properties of the state that involve negations for location2? Write None if there are none", "answer": "a link between location2 and gate does not exist, a link between location2 and shed does not exist, a link between location3 and location2 does not exist, a link between location6 and location2 does not exist, a link between location8 and location2 does not exist, a link between shed and location2 does not exist, bob is not at location2, gate and location2 are not linked, location2 and location5 are not linked, location2 and location6 are not linked, location2 and location9 are not linked, location2 is not linked to location1, location2 is not linked to location4, location2 is not linked to location7, location2 is not linked to location8, location4 is not linked to location2, location5 is not linked to location2, location7 is not linked to location2, location9 and location2 are not linked, nut1 is not currently at location2, nut2 is not located at location2, nut3 is not currently at location2, nut4 is not located at location2, nut5 is not at location2, spanner1 is not located at location2, spanner2 is not currently at location2, spanner3 is not at location2, spanner4 is not at location2 and spanner5 is not located at location2", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up both spanner4 and spanner3. From location2, Bob proceeds to location3, then to location4, and subsequently to location5. He then walks from location5 to location6, where he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, what are the valid properties that involve negations for location2? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is connected to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, also loose, nut4, which is not secured, and nut5, also not secured. The shed is connected to location1. The following spanners are available for use: spanner1, which is located at location8, spanner2, which is usable and located at location6, spanner3, which is currently at location2, spanner4, also at location2, and spanner5, which is functional and located at location6."}
{"question_id": "b2bfed1c-698f-4cb4-8a1b-86d4b7c4ff39", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_11", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for location1? Write None if there are none", "answer": "a link between gate and location1 does not exist, a link between location1 and location2 exists, a link between location1 and location4 does not exist, a link between location1 and location5 does not exist, a link between location1 and location7 does not exist, a link between location1 and location9 does not exist, a link between location6 and location1 does not exist, a link between location7 and location1 does not exist, a link between location8 and location1 does not exist, a link between shed and location1 exists, bob is located at location1, location1 and gate are not linked, location1 and location6 are not linked, location1 and location8 are not linked, location1 is not linked to location3, location1 is not linked to shed, location2 and location1 are not linked, location3 is not linked to location1, location4 and location1 are not linked, location5 is not linked to location1, location9 and location1 are not linked, nut1 is not at location1, nut2 is not currently at location1, nut3 is not currently at location1, nut4 is not located at location1, nut5 is not currently at location1, spanner1 is not at location1, spanner2 is not currently at location1, spanner3 is not at location1, spanner4 is not located at location1 and spanner5 is not located at location1", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, what are the valid properties (including both affirmative and negated properties) that apply to location1? If there are no such properties, please state None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "23a8b09b-9183-495d-8590-da8b4c7a26a0", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, spanner3 is picked up by bob from location2, bob walks from location2 to location3, from location3, bob picks up spanner5, from location3, bob picks up spanner1, bob walks to location4 from location3, from location4 to location5, bob walks, bob picks up spanner2 from location5, bob walks from location5 to location6, from location6, bob picks up spanner4, from location6 to location7, bob walks, bob walks to location8 from location7, bob walks from location8 to location9, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut3? Write None if there are none", "answer": "nut3 is currently at gate and tightening of nut3 is complete", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. He proceeds to walk from location2 to location3, picks up spanner5 and spanner1 from location3, and then walks to location4. From location4, he walks to location5, picks up spanner2, and then walks to location6, where he picks up spanner4. He continues walking from location6 to location7, then to location8, and from location8 to location9, before finally walking to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, what are the valid properties that do not involve negations for nut3? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is unsecured, nut4, which is unsecured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at their respective locations: spanner1 at location3 and is in working condition, spanner2 at location5 and is usable, spanner3 at location2 and is functional, spanner4 at location6 and is usable, and spanner5 at location3 and is functional."}
{"question_id": "d8ca00d0-6063-4ed3-9c47-58536a1705a8", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob picks up spanner3 from location2, from location2 to location3, bob walks, spanner5 is picked up by bob from location3, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, bob walks from location4 to location5, spanner2 is picked up by bob from location5 and bob walks from location5 to location6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for gate? Write None if there are none", "answer": "location9 and gate are linked, nut1 is currently at gate, nut2 is located at gate, nut3 is at gate, nut4 is at gate and nut5 is at gate", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. He then walks from location2 to location3, and at location3, he picks up both spanner5 and spanner1. Next, Bob walks from location3 to location4, then from location4 to location5, where he picks up spanner2, and finally walks from location5 to location6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for gate? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "2bfd1bac-428d-484c-af67-4f51fed058d8", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, from location2, bob picks up spanner3, bob walks from location2 to location3, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, bob walks to location4 from location3, bob walks to location5 from location4, from location5, bob picks up spanner2 and bob walks from location5 to location6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for shed? Write None if there are none", "answer": "a link between shed and location1 exists", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. Next, he walks from location2 to location3, picks up spanner5 and spanner1 at location3, and then proceeds to location4 and subsequently to location5. At location5, he picks up spanner2 and finally walks to location6, resulting in the current state. In this state, what are the valid properties of the state that do not involve negations for the shed? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate and not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "67951372-e8cb-4fab-92da-570a65033512", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, what are the valid properties of the state that involve negations for shed? Write None if there are none", "answer": "a link between location2 and shed does not exist, a link between location4 and shed does not exist, a link between location6 and shed does not exist, a link between location8 and shed does not exist, a link between shed and location3 does not exist, a link between shed and location4 does not exist, a link between shed and location6 does not exist, bob is not located at shed, gate and shed are not linked, location1 and shed are not linked, location3 is not linked to shed, location5 is not linked to shed, location7 and shed are not linked, location9 is not linked to shed, nut1 is not at shed, nut2 is not located at shed, nut3 is not at shed, nut4 is not at shed, nut5 is not at shed, shed and location2 are not linked, shed and location7 are not linked, shed and location8 are not linked, shed is not linked to gate, shed is not linked to location5, shed is not linked to location9, spanner1 is not located at shed, spanner2 is not at shed, spanner3 is not at shed, spanner4 is not currently at shed and spanner5 is not at shed", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, what are the valid properties of the state that involve negations for shed? Write None if there are none.\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, what are the valid state properties involving negations related to shed? If none exist, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "25ffd674-03fd-4a2a-8614-8869583c2a57", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for bob? Write None if there are none", "answer": "bob is located at location1, bob is not at location2, bob is not at location6, bob is not at location8, bob is not at location9, bob is not carrying spanner2, bob is not carrying spanner4, bob is not carrying spanner5, bob is not currently at gate, bob is not currently at location3, bob is not currently at location4, bob is not currently at location5, bob is not currently at location7, bob is not located at shed, spanner1 is not carried by bob and spanner3 is not carried by bob", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for bob? Write None if there are none.\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state (both with and without negations) applicable to bob? If none exist, write None.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate and not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "482f3d5a-3efa-44b3-ae24-cbd971973398", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob walks from location2 to location3, bob walks from location3 to location4, bob walks from location4 to location5, bob picks up spanner4 from location5, bob walks from location5 to location6, spanner1 is picked up by bob from location6, bob walks to location7 from location6, spanner5 is picked up by bob from location7, bob walks to location8 from location7, from location8, bob picks up spanner3, spanner2 is picked up by bob from location8, bob walks to location9 from location8, bob walks from location9 to gate, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut5? Write None if there are none", "answer": "nut5 is at gate and nut5 is not secured", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then to location2, followed by location3, location4, and location5. At location5, Bob picks up spanner4, then proceeds to location6, where he picks up spanner1. He then walks to location7, picks up spanner5, and moves to location8. At location8, Bob picks up both spanner3 and spanner2. He then walks to location9 and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, what are the valid properties that do not involve negations for nut5? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "e38849a4-6dc9-4f21-af2b-d21b3993bf32", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for location4? Write None if there are none", "answer": "a link between location2 and location4 does not exist, a link between location4 and gate does not exist, a link between location4 and location1 does not exist, a link between location4 and location6 does not exist, a link between location5 and location4 does not exist, a link between location7 and location4 does not exist, bob is not at location4, gate is not linked to location4, location1 and location4 are not linked, location3 is linked to location4, location4 and location5 are linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 is not linked to location2, location4 is not linked to location3, location4 is not linked to location8, location4 is not linked to shed, location6 and location4 are not linked, location8 is not linked to location4, location9 is not linked to location4, nut1 is not located at location4, nut2 is not currently at location4, nut3 is not at location4, nut4 is not currently at location4, nut5 is not located at location4, shed is not linked to location4, spanner1 is not at location4, spanner2 is not at location4, spanner3 is not currently at location4, spanner4 is not at location4 and spanner5 is not located at location4", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties (including both affirmative and negated properties) for location4? If there are no valid properties, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "26cbd228-19b2-48b7-9910-6feaae6518f9", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob picks up spanner3 from location2, from location2 to location3, bob walks, from location3, bob picks up spanner5, from location3, bob picks up spanner1, from location3 to location4, bob walks, bob walks from location4 to location5, bob picks up spanner2 from location5, from location5 to location6, bob walks, from location6, bob picks up spanner4, bob walks from location6 to location7, from location7 to location8, bob walks, from location8 to location9, bob walks, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, what are the valid properties of the state that involve negations for spanner5? Write None if there are none", "answer": "spanner5 can't be used, spanner5 is not at gate, spanner5 is not at location1, spanner5 is not at location4, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not located at location5, spanner5 is not located at location6, spanner5 is not located at location7, spanner5 is not located at location8, spanner5 is not located at location9 and spanner5 is not located at shed", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he walks and picks up spanner3. He proceeds to location3, walks, and collects spanner5 and spanner1. From location3, Bob walks to location4, then to location5, where he picks up spanner2. He continues to location6, walks, and collects spanner4. Bob then moves to location7, then to location8, and finally to location9, before walking to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, while also using spanner2 to tighten nut4, resulting in the current state. In this state, what are the valid properties that involve negations for spanner5? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and is functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "bef6deba-dd90-4fcc-8f58-25bd44ab28be", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what are the valid properties of the state that involve negations for nut1? Write None if there are none", "answer": "nut1 is not at location7, nut1 is not at location9, nut1 is not at shed, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location4, nut1 is not currently at location5, nut1 is not currently at location8, nut1 is not located at location3, nut1 is not located at location6 and tightening of nut1 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, what are the valid properties of the state that involve negations for nut1? If there are no such properties, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "37a75e08-2fe1-4b8a-a7b2-26f4e9727ab1", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, from location1, bob picks up spanner4, bob walks to location2 from location1, from location2 to location3, bob walks, bob walks to location4 from location3, from location4, bob picks up spanner1, bob walks to location5 from location4, bob walks to location6 from location5 and from location6, bob picks up spanner3 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut1? Write None if there are none", "answer": "nut1 is at gate and nut1 is not secured", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to walk from location1 to location2, then to location3, and subsequently to location4. At location4, he picks up spanner1, and then walks to location5 and then to location6, where he collects spanner3, ultimately reaching the current state. In this state, what are the valid properties of the state that do not involve negations for nut1? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "54bb0d54-de35-4a14-a08e-89a1686e2d5f", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut4? Write None if there are none", "answer": "nut4 is at gate and nut4 is not secured", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut4? Write None if there are none.\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut4? Write None if there are none.\n\nSince the original text is already clear and concise, the paraphrased text remains the same.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, location8 and location9 are connected, and location9 is connected to the gate. At the gate, nut1 is located and is not secured. Also at the gate, nut2 is currently present and is loose, nut3 is located and is loose, nut4 is currently present and is not secured, and nut5 is currently present and is not secured. The shed and location1 are interconnected. Spanner1 is functional and is located at location8. Spanner2 is located at location6 and is usable. Spanner3 is functional and is currently at location2. Spanner4 is functional and is located at location2. Spanner5 is functional and is located at location6."}
{"question_id": "ce93928b-9cb5-47bc-af9b-d871b3cd56c1", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, spanner4 is picked up by bob from location1, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks to location4 from location3, bob picks up spanner1 from location4, bob walks to location5 from location4, bob walks to location6 from location5 and spanner3 is picked up by bob from location6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for location7? Write None if there are none", "answer": "a link between location7 and location8 exists, location6 and location7 are linked and spanner2 is located at location7", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1, proceeds to location2, then to location3, and then walks to location4. From location4, Bob picks up spanner1, moves to location5, then to location6, and finally collects spanner3 from location6, resulting in the current state. In this state, what are the valid properties that do not involve negations for location7? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "3d6e2686-b824-488d-9ddf-058151c8a897", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks from location3 to location4, bob walks to location5 from location4, bob picks up spanner4 from location5, bob walks from location5 to location6, spanner1 is picked up by bob from location6, bob walks from location6 to location7, from location7, bob picks up spanner5, bob walks to location8 from location7, bob picks up spanner3 from location8, spanner2 is picked up by bob from location8, bob walks to location9 from location8, bob walks from location9 to gate, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, what are the valid properties of the state that involve negations for bob? Write None if there are none", "answer": "bob is not at location1, bob is not at location5, bob is not at location7, bob is not currently at location6, bob is not currently at location9, bob is not located at location2, bob is not located at location3, bob is not located at location4, bob is not located at location8 and bob is not located at shed", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and location5. From location5, Bob picks up spanner4 and proceeds to location6, where he picks up spanner1. He then moves to location7, picks up spanner5, and walks to location8, where he collects spanner3 and spanner2. From location8, Bob heads to location9 and then to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, what are the valid properties of the state that involve negations for Bob? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is located at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "a91128f6-db7c-4dae-a3f7-7e6bc9fa26fe", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1, bob picks up spanner5, spanner4 is picked up by bob from location1, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, bob picks up spanner1 from location4, bob walks to location5 from location4, bob walks to location6 from location5 and bob picks up spanner3 from location6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for location3? Write None if there are none", "answer": "location2 is linked to location3 and location3 and location4 are linked", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1, then picks up spanner5 and spanner4 from location1, proceeds to location2, then to location3, and then to location4, where he picks up spanner1, continues to location5, then to location6, and finally picks up spanner3 to reach the current state. In this state, what are the valid properties that do not involve negations for location3? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1 is available and located at location4. Spanner2 is functional and currently at location7, while spanner3 is functional and at location6. Spanner4 is functional and located at location1, and spanner5 is also available and located at location1."}
{"question_id": "f116d577-9014-4b8a-bcea-1e83ee18b4af", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, bob walks from location4 to location5, bob picks up spanner2 from location5, bob walks to location6 from location5, bob picks up spanner4 from location6, bob walks to location7 from location6, from location7 to location8, bob walks, bob walks to location9 from location8, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, what are the valid properties of the state that involve negations for shed? Write None if there are none", "answer": "a link between location4 and shed does not exist, a link between location5 and shed does not exist, a link between location7 and shed does not exist, a link between shed and gate does not exist, a link between shed and location2 does not exist, a link between shed and location5 does not exist, bob is not currently at shed, gate is not linked to shed, location1 is not linked to shed, location2 is not linked to shed, location3 is not linked to shed, location6 and shed are not linked, location8 and shed are not linked, location9 is not linked to shed, nut1 is not currently at shed, nut2 is not at shed, nut3 is not currently at shed, nut4 is not located at shed, nut5 is not currently at shed, shed and location4 are not linked, shed and location6 are not linked, shed and location7 are not linked, shed and location9 are not linked, shed is not linked to location3, shed is not linked to location8, spanner1 is not located at shed, spanner2 is not at shed, spanner3 is not currently at shed, spanner4 is not located at shed and spanner5 is not located at shed", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner3. From location2, Bob heads to location3, picks up spanner5 and spanner1, and then walks to location4. He then moves to location5, where he collects spanner2, and proceeds to location6 to pick up spanner4. Bob then walks to location7, followed by location8, and then location9. Finally, he moves to the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, what are the valid properties of the state that involve negations for the shed? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and in working condition."}
{"question_id": "49b9171a-b28b-4698-894c-fac2336825eb", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut4? Write None if there are none", "answer": "nut4 is located at gate and nut4 is loose", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut4? Write None if there are none.\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, what are the valid properties of the state that do not involve negations for nut4? Write None if there are none.\n\nSince the original text is already clear and concise, the paraphrased text remains the same.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are interconnected, and location2 is also linked to location3, which in turn is connected to location4. Furthermore, location5 is linked to location6, which is connected to location7, and location7 is linked to location8, which is then connected to location9. At the gate, there are several items: nut1 is present but not secured, nut2 is loose, nut3 is not secured, nut4 is currently unsecured, and nut5 is loose. The shed is connected to location1. Several spanners are located at various points: spanner1 is functional and situated at location3, spanner2 is usable and located at location5, spanner3 is functional and can be found at location2, spanner4 is usable and located at location6, and spanner5 is functional and also situated at location3."}
{"question_id": "ddabac1c-1862-46c8-a1c8-893b84aaac3b", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_12", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, spanner5 is picked up by bob from location1, spanner4 is picked up by bob from location1, from location1 to location2, bob walks, from location2 to location3, bob walks, bob walks from location3 to location4, spanner1 is picked up by bob from location4, bob walks to location5 from location4, bob walks to location6 from location5 and spanner3 is picked up by bob from location6 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for location8? Write None if there are none", "answer": "a link between gate and location8 does not exist, a link between location1 and location8 does not exist, a link between location3 and location8 does not exist, a link between location8 and location3 does not exist, a link between location8 and location4 does not exist, a link between location8 and location9 exists, a link between location9 and location8 does not exist, a link between shed and location8 does not exist, bob is not located at location8, location2 is not linked to location8, location4 and location8 are not linked, location5 is not linked to location8, location6 is not linked to location8, location7 is linked to location8, location8 and location1 are not linked, location8 and location2 are not linked, location8 and location5 are not linked, location8 and shed are not linked, location8 is not linked to gate, location8 is not linked to location6, location8 is not linked to location7, nut1 is not at location8, nut2 is not located at location8, nut3 is not located at location8, nut4 is not currently at location8, nut5 is not currently at location8, spanner1 is not at location8, spanner2 is not at location8, spanner3 is not currently at location8, spanner4 is not currently at location8 and spanner5 is not at location8", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to walk from location1 to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1 and continues walking to location5 and then to location6, where he collects spanner3, ultimately reaching the current state. In this state, what are the valid properties (including both affirmative and negated properties) for location8? If there are none, please state 'None'.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
