{"question_id": "504ed621-7f07-4e60-af1f-48498ef22b69", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, at location l1, cargo c5 is loaded in vehicle v0 with spaces s2 and s1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f1 and f0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, is the number of executable actions equal to 9? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded onto vehicle v0, utilizing spaces s2 and s1. Subsequently, cargo c2 is loaded onto vehicle v0 at location l0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. Upon arrival, cargo c0 is unloaded from vehicle v0 at location l1, freeing up spaces s0 and s1. Cargo c10 is then loaded onto vehicle v0 at location l1, occupying spaces s1 and s0. At location l1, cargo c2 is unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, to location l0. At location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2. Subsequently, cargo c3 and cargo c4 are loaded onto vehicle v0 at location l0, occupying spaces s2 and s1, and spaces s1 and s0, respectively. Vehicle v0 then moves from location l0 to location l1, which has fuel levels f3 and f2. At location l1, cargo c3 and cargo c4 are unloaded from vehicle v0, releasing spaces s0 and s1, and spaces s1 and s2, respectively. Cargo c5 and cargo c9 are then loaded onto vehicle v0 at location l1, occupying spaces s2 and s1, and spaces s1 and s0, respectively. Vehicle v0 then moves from location l1, which has fuel levels f1 and f0, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0. This sequence of actions results in the current state. In this state, is the number of executable actions equal to 9? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are interconnected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Similarly, vehicle v1 contains space s2 and is present at location l1."}
{"question_id": "67a959c2-7c8b-438f-9aac-b0073d7b22a4", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 25? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location l1, cargo c2 is loaded into vehicle v0 with spaces s1 and s0, resulting in the current state. In this state, does the number of valid properties that do not involve negations equal 25? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which also contains space s1, is located at l0."}
{"question_id": "cee9c033-17c2-4369-a635-145978054cb5", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 29? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, does the count of valid properties that do not involve negations equal 29? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. It's also noted that l1 is connected to l0. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "4f125616-1f4e-4845-9d24-1a850212c469", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 25? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l0, which has fuel levels f5 and f4, to location l1, resulting in the current state. In this state, does the count of valid properties that do not involve negations equal 25? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at l1."}
{"question_id": "38e5d0b5-0e5d-4225-a167-3498a678ef03", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 115? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, then vehicle v0 travels from location l1, where the fuel levels are f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. At location l0, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1, which has fuel levels f7 and f6, to location l0, where cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1. At location l0, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1. Cargo c1 is then unloaded from vehicle v0, which has spaces s0 and s1, at location l1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels to location l0 from location l1, which has fuel levels f6 and f5, and at location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1. Cargo c7 is then loaded onto vehicle v0, which has spaces s1 and s0, at location l0, and vehicle v0 moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 115? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which contains space s1, is located at l0."}
{"question_id": "ef17a1e1-0419-4949-b512-e4b389e3aa66", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 24? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded into vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, back to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Subsequently, cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0, and cargo c2 is unloaded from vehicle v0, vacating spaces s0 and s1. Vehicle v0 then proceeds to move from location l1, which has fuel levels f2 and f1, back to location l0. Upon arrival, cargo c10 is unloaded from vehicle v0, releasing spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, ultimately reaching the current state. In this state, is the number of valid properties that do not involve negations equal to 24? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. It's also noted that l1 is connected to l0. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "74d896b3-20fc-42a7-a47a-1d3798023959", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 105? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and then vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. Next, at location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1, and then vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1. This process continues with cargo c6 being loaded and unloaded, followed by cargo c7, and finally cargo c8. After these actions, vehicle v0 moves from location l1, with fuel levels f2 and f1, to location l0, where cargo c8 is unloaded from vehicle v0, which has spaces s0 and s1, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 105? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is available at l1, fuel f7 is available at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, spaces s0 and s1 are adjacent, vehicle v0 is equipped with space s1 and vehicle v0 is currently at location l1."}
{"question_id": "1bd0f39a-e350-4743-850a-56d5af03223b", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 22?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Then, vehicle v0 moves from location l1, which has fuel levels f7 and f6, back to location l0, where cargo c0 is unloaded from vehicle v0, occupying spaces s0 and s1. This pattern continues, with vehicle v0 moving between locations l0 and l1, loading and unloading cargo c1, c2, and c4, while traversing through various fuel levels. The sequence concludes with vehicle v0 relocating from location l0, which has fuel levels f1 and f0, to location l1, where cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, and finally moving from location l1, which has fuel levels f3 and f2, to location l0, reaching the current state. Is it True or False that the total number of actions in this sequence that led to the current state is 22?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is located at l1."}
{"question_id": "a79f896a-37bd-43f9-811e-f6bb886486ff", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 23? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing spaces s0 and s1. Next, vehicle v0 travels from location l0, with fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0. Subsequently, vehicle v0 returns to location l0, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. Finally, vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1, where cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 23? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at location l1, cargo c1 is also found at location l1, cargo c2 is situated at location l1, cargo c3 is situated at location l0, cargo c4 is positioned at location l1, cargo c5 is located at location l0, cargo c6 is present at location l1, cargo c7 is also present at location l1, cargo c8 is situated at location l0, fuel f7 is available at location l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at location l1."}
{"question_id": "04f1422f-d888-48c1-9d44-f73ab7a84ca6", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 131? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which now has available spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. This process is repeated for cargo c4, c6, c7, and c8, with vehicle v0 traveling between locations l0 and l1, loading and unloading cargo, and updating its available spaces and fuel levels accordingly. The sequence of actions results in the current state. The question is, does this state have a total of 131 valid properties, including both affirmative and negated properties? True or False.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is available in l1, fuel f7 is available in l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is connected to location l1, location l1 is connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 occupies space s1 and vehicle v0 is currently at location l1."}
{"question_id": "1590b643-1664-4e69-b0e4-49475bced7a1", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is placed in vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 travels from location l1, where the fuel levels are f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is removed from vehicle v0, which has spaces s0 and s1. Next, cargo c0 is loaded into vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, where the fuel levels are f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded into vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then travels from location l1, where the fuel levels are f7 and f6, to location l0. At location l0, cargo c3 is removed from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 27? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which also contains space s1, is located at l0."}
{"question_id": "22e12ee3-a645-4c39-9aa8-9843895e54e7", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, is the number of executable actions equal to 13? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. Upon arrival at location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0. Subsequently, cargo c2 is unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then moves back to location l0 from location l1, which has fuel levels f2 and f1. At location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, resulting in the current state. In this state, is the number of executable actions equal to 13? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected to each other. Location l0 has a fuel level of f4, whereas location l1 has a fuel level of f3. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "5b6ff0d7-4407-4e36-8e22-a25539972e5b", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1. Then, vehicle v0 moves from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded into vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1. Finally, cargo c6 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, and vehicle v0 moves from location l1, with fuel levels f4 and f3, to location l0, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is present in l1, fuel f7 is present in l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, spaces s0 and s1 are adjacent, vehicle v0 is equipped with space s1 and vehicle v0 is currently at location l1."}
{"question_id": "ff90af1c-b28e-46e6-a978-1f191d862ade", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 142? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is present at location l0, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l1, cargo c6 is at location l1, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f6 exists in location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, spaces s0 and s1 are neighbors, vehicle v0 has space s1 and vehicle v0 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following steps are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0, resulting in the current state. In this state, does the total count of valid properties (including both affirmative and negated properties) equal 142? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c10 is situated at l0, cargo c2 is located at l0, cargo c3 is found at l0, cargo c4 is situated at l1, cargo c5 is located at l1, cargo c6 is found at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is found at l1, fuel f6 is available at l1, fuel f7 is available at l0, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are linked, locations l1 and l0 are linked, spaces s0 and s1 are adjacent, vehicle v0 occupies space s1 and vehicle v0 is currently at location l1."}
{"question_id": "fd785b49-9c5d-4767-a7d9-841bc64735d3", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 95? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l0, which has fuel levels of f5 and f4, to location l1, resulting in the current state. In this state, does the count of valid properties involving negations equal 95? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at l1."}
{"question_id": "8798a545-d42b-46c0-98e4-32d63319f5d7", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 32? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 travels from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, at location l0, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f3 and f2, back to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then travels from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 32? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which also contains space s1, is located at l0."}
{"question_id": "a8043e7b-8b46-4e37-ac95-dc6f0cea33ef", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 25? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, then cargo c0 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Next, vehicle v0 moves from location l1, which has fuel levels f7 and f6, back to location l0, where cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Subsequently, vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. Finally, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, and cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 25? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at l1."}
{"question_id": "62201ea5-c34c-4361-9c20-ee7b4a36fa86", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is the number of executable actions equal to 6? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, and cargo c0 is then unloaded from vehicle v0, which has spaces s0 and s1, at location l1. Subsequently, at location l1, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 moves from location l1, with fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, is the number of executable actions equal to 6? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which contains space s1, is located at l0."}
{"question_id": "0c95baa8-b655-436d-b89b-75fae4047107", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s2 and s1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c0 is loaded onto vehicle v0, occupying spaces s2 and s1. Additionally, at location l0, cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Subsequently, cargo c10 is loaded onto vehicle v0 at location l1, occupying spaces s1 and s0. Furthermore, at location l1, cargo c2 is unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then proceeds to move from location l1, which has fuel levels f2 and f1, to location l0. Upon arrival, cargo c10 is unloaded from vehicle v0 at location l0, vacating spaces s1 and s2. At location l0, cargo c3 is loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c3 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c4 is unloaded from vehicle v0, releasing spaces s1 and s2. Additionally, at location l1, cargo c5 is loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c9 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then relocates from location l1, which has fuel levels f1 and f0, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, vacating spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, ultimately reaching the current state. In this state, is the number of valid properties that do not involve negations equal to 27? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are interconnected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. Space s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "c0cf5b51-816b-4bd0-b9c5-b960e965e1e4", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, at location l1, cargo c5 is loaded in vehicle v0 with spaces s2 and s1, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 79? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded onto vehicle v0, utilizing spaces s2 and s1. Subsequently, at location l0, cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. Upon arrival at location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Cargo c10 is then loaded onto vehicle v0 at location l1, utilizing spaces s1 and s0. Additionally, cargo c2 is unloaded from vehicle v0 at location l1, releasing spaces s0 and s1. Vehicle v0 then moves from location l1, which has fuel levels f2 and f1, back to location l0. At location l0, cargo c10 is unloaded from vehicle v0, vacating spaces s1 and s2. Cargo c3 is then loaded onto vehicle v0 at location l0, occupying spaces s2 and s1. Furthermore, cargo c4 is loaded onto vehicle v0 at location l0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c3 is unloaded from vehicle v0, freeing up spaces s0 and s1. Cargo c4 is also unloaded from vehicle v0 at location l1, releasing spaces s1 and s2. Subsequently, cargo c5 is loaded onto vehicle v0 at location l1, utilizing spaces s2 and s1. Cargo c9 is also loaded onto vehicle v0 at location l1, occupying spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f1 and f0, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, vacating spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0. This sequence of actions results in the current state. In this state, is the number of valid properties that involve negations equal to 79? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. It's also noted that l1 is connected to l0. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Furthermore, vehicle v1 contains space s2 and is present at location l1."}
{"question_id": "d62243bd-691d-4fe0-8672-f6944cff69d6", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 7?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 travels from location l0, which has fuel levels of f5 and f4, to location l1. Upon arrival at l1, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from l1, which now has fuel levels of f7 and f6, back to location l0. At l0, cargo c0 is then unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then travels from l0, which has fuel levels of f4 and f3, back to l1. At l1, cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l1, which now has fuel levels of f6 and f5, back to l0, where cargo c1 is unloaded, releasing spaces s0 and s1. Finally, vehicle v0 travels from l0, which has fuel levels of f3 and f2, to l1, and at l1, cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. Is it True or False that the number of actions in the sequence that led to the current state is equal to 7?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and is located at l1."}
{"question_id": "0e025913-0e90-445c-b018-e358d1493fbb", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 30? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Next, vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 30? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected. Location l0 contains fuel f3, and location l1 is also connected to l0. Space s0 is adjacent to space s1, which is part of vehicle v0. Vehicle v0 is currently at location l1, while vehicle v1, which also contains space s1, is situated at location l0."}
{"question_id": "fc422a1f-a7b4-4a0e-9b7d-b7235b16122f", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 29? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is present at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l0, cargo c6 is present at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has a fuel-level of f5, location l1 and location l0 are connected, space s0 neighbors space s1, there is a connection between locations l0 and l1, vehicle v0 has space s1, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l0, which has fuel levels of f5 and f4, to location l1, resulting in the current state. In this state, does the count of valid properties that do not involve negations equal 29? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is present at l1, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is present at l1, cargo c7 is also at l1, cargo c8 is situated at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, location l0 has a fuel level of f5, locations l0 and l1 are connected, space s0 is adjacent to space s1, a connection exists between locations l0 and l1, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l0, vehicle v1 contains space s1 and vehicle v1 is positioned at location l1."}
{"question_id": "0a410fea-8d92-4470-917c-82c2294e02fa", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 78? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 transitions from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, does the count of valid state properties involving negations equal 78? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected to each other. Location l0 has a fuel level of f4, whereas location l1 has a fuel level of f3. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "6d4eb0af-a3d2-477c-815e-00fee6a705ef", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 78? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0. At location l0, cargo c0 is loaded into vehicle v0, utilizing spaces s2 and s1. Subsequently, at location l0, cargo c2 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. Upon arrival at location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, cargo c10 is loaded into vehicle v0 at location l1, occupying spaces s1 and s0. Additionally, cargo c2 is unloaded from vehicle v0 at location l1, releasing spaces s0 and s1. Vehicle v0 then proceeds to move from location l1, which has fuel levels f2 and f1, to location l0. At location l0, cargo c10 is unloaded from vehicle v0, vacating spaces s1 and s2. Furthermore, at location l0, cargo c3 is loaded into vehicle v0, utilizing spaces s2 and s1, and cargo c4 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c3 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c4 is unloaded from vehicle v0, releasing spaces s1 and s2. Subsequently, cargo c5 is loaded into vehicle v0 at location l1, occupying spaces s2 and s1, and cargo c9 is loaded into vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f1 and f0, to location l0. Finally, at location l0, cargo c5 is unloaded from vehicle v0, vacating spaces s0 and s1, and cargo c6 is loaded into vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 78? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are interconnected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. Space s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Similarly, vehicle v1 contains space s2 and is present at location l1."}
{"question_id": "518b90e0-01a5-4ba7-8d8a-4a7d354bb5d0", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 78? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded into vehicle v0 occupying spaces s2 and s1, and cargo c2 is also loaded into vehicle v0 occupying spaces s1 and s0. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c0 is unloaded from vehicle v0 occupying spaces s0 and s1, and cargo c10 is loaded into vehicle v0 occupying spaces s1 and s0. At location l1, cargo c2 is also unloaded from vehicle v0 occupying spaces s0 and s1. Next, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0, where cargo c10 is unloaded from vehicle v0 occupying spaces s1 and s2, and cargo c3 is loaded into vehicle v0 occupying spaces s2 and s1, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 78? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are interconnected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. Space s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
{"question_id": "8bd9570e-0b49-4d4f-b608-9c49c32591c0", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l0, cargo c2 is at location l1, cargo c3 is at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is present at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f3, location l1 and location l0 are connected, space s0 neighbors space s1, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is situated at location l0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, does the count of valid properties that do not involve negations equal 27? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is also found at l0. In contrast, cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1. Meanwhile, cargo c6 and c7 are located at l0. Additionally, fuel f8 is present at location l1. The fuel levels are arranged in a sequence where f0 is adjacent to f1, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Furthermore, f1 and f2 are neighboring fuel levels, as are f2 and f3, and f7 and f8. Locations l0 and l1 are interconnected, with l0 having fuel f3. Space s0 is adjacent to space s1, which is part of vehicle v0, currently situated at location l1. Vehicle v1, which contains space s1, is located at l0."}
{"question_id": "16296955-9037-4c91-8599-5d567224fc3e", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c4 is unloaded from vehicle v0 with spaces s1 and s2, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, is the number of inexecutable actions equal to 639? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is present at location l0, cargo c5 is present at location l1, cargo c6 is at location l0, cargo c7 is situated at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has a fuel-level of f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 has space s2, vehicle v0 is situated at location l1, vehicle v1 contains space s2 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial state, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded onto vehicle v0 occupying spaces s2 and s1, and cargo c2 is loaded onto vehicle v0 occupying spaces s1 and s0. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c0 is unloaded from vehicle v0 occupying spaces s0 and s1, and cargo c10 is loaded onto vehicle v0 occupying spaces s1 and s0. At location l1, cargo c2 is also unloaded from vehicle v0 occupying spaces s0 and s1. Next, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0, where cargo c10 is unloaded from vehicle v0 occupying spaces s1 and s2, and cargo c3 and c4 are loaded onto vehicle v0 occupying spaces s2 and s1, and spaces s1 and s0, respectively. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1, where cargo c3 and c4 are unloaded from vehicle v0 occupying spaces s0 and s1, and spaces s1 and s2, respectively. At location l1, cargo c5 and c9 are loaded onto vehicle v0 occupying spaces s2 and s1, and spaces s1 and s0, respectively. Finally, vehicle v0 moves from location l1, which has fuel levels f1 and f0, to location l0, where cargo c5 is unloaded from vehicle v0 occupying spaces s0 and s1, and cargo c6 is loaded onto vehicle v0 occupying spaces s1 and s0, resulting in the current state. In this state, is the number of inexecutable actions equal to 639? True or False", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, c4, c6, and c7 are all located at l0. Meanwhile, cargo c5, c8, and c9 are present at l1. \n\nFuel level f2 is adjacent to fuel level f3, and fuel level f4 is adjacent to fuel level f5. Additionally, fuel levels f0 and f1 are neighboring, as are fuel levels f1 and f2, and fuel levels f3 and f4. \n\nLocations l0 and l1 are connected, with l0 having a fuel level of f4 and l1 having a fuel level of f3. It's also noted that l1 is connected to l0. \n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are neighboring. Vehicle v0 is equipped with space s2 and is situated at location l1. Vehicle v1, which contains space s2, is also present at location l1."}
