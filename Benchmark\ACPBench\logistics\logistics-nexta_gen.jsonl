{"id": -1871705618513331550, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1. Currently, t1, a0, and p2 are at l1-0, p1 and p3 are at l0-2, t0 is at l0-1, p0 is in t0. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-1, p3 is at l0-1, and p0 is at l0-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck which is in location ?loc-from in city ?city to another location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(LOAD-AIRPLANE p2 a0 l1-0)", "(DRIVE-TRUCK t0 l0-1 l0-0 c0)", "(DRIVE-TRUCK t0 l0-1 l0-2 c0)", "(LOAD-TRUCK p2 t1 l1-0)", "(DRIVE-TRUCK t0 l0-1 l0-1 c0)", "(FLY-AIRPLANE a0 l1-0 l0-0)", "(DRIVE-TRUCK t1 l1-0 l1-2 c1)"], "no": ["(DRIVE-TRUCK t1 l1-0 l1-1 c1)", "(DRIVE-TRUCK t1 l1-0 l1-0 c1)", "(FLY-AIRPLANE a0 l1-0 l1-0)"], "opt": "13", "yes": ["(UNLOAD-TRUCK p0 t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p1 l0-2) (at p2 l1-0) (at p3 l0-2) (at t0 l0-1) (at t1 l1-0) (in p0 t0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-1) (at p3 l0-1)))\n)"}
{"id": -4521433977823638683, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l3-6, l3-7, l3-9, l3-2, l3-8, l3-1, l3-0, l3-4, l3-3, and l3-5 are in c3; l4-1, l4-6, l4-0, l4-4, l4-3, l4-2, l4-9, l4-7, l4-8, and l4-5 are in c4; l1-9, l1-0, l1-4, l1-2, l1-1, l1-3, l1-7, l1-5, l1-6, and l1-8 are in c1; l0-8, l0-9, l0-5, l0-4, l0-1, l0-0, l0-3, l0-2, l0-7, and l0-6 are in c0; l2-4, l2-2, l2-3, l2-0, l2-7, l2-6, l2-9, l2-8, l2-1, and l2-5 are in c2. Currently, t0 is at l0-2, t1 is at l1-0, t4 and p2 are at l4-8, a0 and p1 are at l2-0, t2 is at l2-4, t3 is at l3-0, p3 and p0 are in t2. The goal is to reach a state where the following facts hold: p3 is at l1-1, p0 is at l1-5, p2 is at l4-8, and p1 is at l2-7. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(DRIVE-TRUCK t3 l3-0 l3-9 c3)", "(DRIVE-TRUCK t4 l4-8 l4-4 c4)", "(DRIVE-TRUCK t4 l4-8 l4-7 c4)", "(DRIVE-TRUCK t0 l0-2 l0-5 c0)", "(DRIVE-TRUCK t4 l4-8 l4-2 c4)", "(DRIVE-TRUCK t3 l3-0 l3-0 c3)", "(DRIVE-TRUCK t0 l0-2 l0-8 c0)", "(DRIVE-TRUCK t3 l3-0 l3-4 c3)", "(DRIVE-TRUCK t3 l3-0 l3-5 c3)", "(DRIVE-TRUCK t4 l4-8 l4-0 c4)", "(DRIVE-TRUCK t4 l4-8 l4-1 c4)", "(DRIVE-TRUCK t2 l2-4 l2-2 c2)", "(DRIVE-TRUCK t4 l4-8 l4-5 c4)", "(DRIVE-TRUCK t4 l4-8 l4-9 c4)", "(DRIVE-TRUCK t0 l0-2 l0-4 c0)", "(DRIVE-TRUCK t4 l4-8 l4-8 c4)", "(DRIVE-TRUCK t1 l1-0 l1-3 c1)", "(DRIVE-TRUCK t4 l4-8 l4-3 c4)", "(DRIVE-TRUCK t1 l1-0 l1-8 c1)", "(DRIVE-TRUCK t3 l3-0 l3-8 c3)", "(FLY-AIRPLANE a0 l2-0 l0-0)", "(FLY-AIRPLANE a0 l2-0 l4-0)", "(DRIVE-TRUCK t2 l2-4 l2-8 c2)", "(DRIVE-TRUCK t0 l0-2 l0-2 c0)", "(DRIVE-TRUCK t1 l1-0 l1-4 c1)", "(DRIVE-TRUCK t1 l1-0 l1-6 c1)", "(DRIVE-TRUCK t2 l2-4 l2-9 c2)", "(DRIVE-TRUCK t2 l2-4 l2-3 c2)", "(DRIVE-TRUCK t3 l3-0 l3-1 c3)", "(DRIVE-TRUCK t3 l3-0 l3-6 c3)", "(DRIVE-TRUCK t0 l0-2 l0-0 c0)", "(DRIVE-TRUCK t4 l4-8 l4-6 c4)", "(DRIVE-TRUCK t2 l2-4 l2-1 c2)", "(DRIVE-TRUCK t0 l0-2 l0-3 c0)", "(LOAD-AIRPLANE p1 a0 l2-0)", "(FLY-AIRPLANE a0 l2-0 l2-0)", "(DRIVE-TRUCK t2 l2-4 l2-6 c2)", "(FLY-AIRPLANE a0 l2-0 l1-0)", "(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "(DRIVE-TRUCK t1 l1-0 l1-0 c1)", "(DRIVE-TRUCK t0 l0-2 l0-7 c0)", "(DRIVE-TRUCK t1 l1-0 l1-9 c1)", "(FLY-AIRPLANE a0 l2-0 l3-0)", "(LOAD-TRUCK p2 t4 l4-8)", "(DRIVE-TRUCK t1 l1-0 l1-5 c1)", "(DRIVE-TRUCK t0 l0-2 l0-6 c0)", "(DRIVE-TRUCK t2 l2-4 l2-7 c2)", "(DRIVE-TRUCK t3 l3-0 l3-3 c3)", "(DRIVE-TRUCK t1 l1-0 l1-7 c1)", "(DRIVE-TRUCK t2 l2-4 l2-4 c2)", "(UNLOAD-TRUCK p0 t2 l2-4)", "(DRIVE-TRUCK t0 l0-2 l0-9 c0)", "(UNLOAD-TRUCK p3 t2 l2-4)", "(DRIVE-TRUCK t2 l2-4 l2-5 c2)", "(DRIVE-TRUCK t1 l1-0 l1-2 c1)"], "no": ["(DRIVE-TRUCK t1 l1-0 l1-1 c1)", "(DRIVE-TRUCK t3 l3-0 l3-7 c3)", "(DRIVE-TRUCK t3 l3-0 l3-2 c3)"], "opt": "17", "yes": ["(DRIVE-TRUCK t2 l2-4 l2-0 c2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s10-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l0-3 l0-4 l0-5 l0-6 l0-7 l0-8 l0-9 l1-1 l1-2 l1-3 l1-4 l1-5 l1-6 l1-7 l1-8 l1-9 l2-1 l2-2 l2-3 l2-4 l2-5 l2-6 l2-7 l2-8 l2-9 l3-1 l3-2 l3-3 l3-4 l3-5 l3-6 l3-7 l3-8 l3-9 l4-1 l4-2 l4-3 l4-4 l4-5 l4-6 l4-7 l4-8 l4-9 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p1 l2-0) (at p2 l4-8) (at t0 l0-2) (at t1 l1-0) (at t2 l2-4) (at t3 l3-0) (at t4 l4-8) (in p0 t2) (in p3 t2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l0-3 c0) (in-city l0-4 c0) (in-city l0-5 c0) (in-city l0-6 c0) (in-city l0-7 c0) (in-city l0-8 c0) (in-city l0-9 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l1-3 c1) (in-city l1-4 c1) (in-city l1-5 c1) (in-city l1-6 c1) (in-city l1-7 c1) (in-city l1-8 c1) (in-city l1-9 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l2-3 c2) (in-city l2-4 c2) (in-city l2-5 c2) (in-city l2-6 c2) (in-city l2-7 c2) (in-city l2-8 c2) (in-city l2-9 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l3-3 c3) (in-city l3-4 c3) (in-city l3-5 c3) (in-city l3-6 c3) (in-city l3-7 c3) (in-city l3-8 c3) (in-city l3-9 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4) (in-city l4-3 c4) (in-city l4-4 c4) (in-city l4-5 c4) (in-city l4-6 c4) (in-city l4-7 c4) (in-city l4-8 c4) (in-city l4-9 c4))\n    (:goal (and (at p0 l1-5) (at p1 l2-7) (at p2 l4-8) (at p3 l1-1)))\n)"}
{"id": -6813552456374777893, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l3-6, l3-7, l3-9, l3-2, l3-8, l3-1, l3-0, l3-4, l3-3, and l3-5 are in c3; l4-1, l4-6, l4-0, l4-4, l4-3, l4-2, l4-9, l4-7, l4-8, and l4-5 are in c4; l1-9, l1-0, l1-4, l1-2, l1-1, l1-3, l1-7, l1-5, l1-6, and l1-8 are in c1; l0-8, l0-9, l0-5, l0-4, l0-1, l0-0, l0-3, l0-2, l0-7, and l0-6 are in c0; l2-4, l2-2, l2-3, l2-0, l2-7, l2-6, l2-9, l2-8, l2-1, and l2-5 are in c2. Currently, t0 is at l0-2, t1 and p1 are at l1-0, t4 and p2 are at l4-8, a0 and t2 are at l2-0, t3 is at l3-0, p3 and p0 are in t2. The goal is to reach a state where the following facts hold: p3 is at l1-1, p0 is at l1-5, p2 is at l4-8, and p1 is at l2-7. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(LOAD-TRUCK p1 t1 l1-0)", "(DRIVE-TRUCK t4 l4-8 l4-4 c4)", "(DRIVE-TRUCK t4 l4-8 l4-7 c4)", "(DRIVE-TRUCK t0 l0-2 l0-5 c0)", "(DRIVE-TRUCK t4 l4-8 l4-2 c4)", "(DRIVE-TRUCK t3 l3-0 l3-0 c3)", "(DRIVE-TRUCK t2 l2-0 l2-4 c2)", "(DRIVE-TRUCK t0 l0-2 l0-8 c0)", "(DRIVE-TRUCK t3 l3-0 l3-7 c3)", "(DRIVE-TRUCK t3 l3-0 l3-4 c3)", "(DRIVE-TRUCK t3 l3-0 l3-5 c3)", "(DRIVE-TRUCK t3 l3-0 l3-2 c3)", "(DRIVE-TRUCK t4 l4-8 l4-0 c4)", "(DRIVE-TRUCK t2 l2-0 l2-2 c2)", "(DRIVE-TRUCK t1 l1-0 l1-1 c1)", "(DRIVE-TRUCK t4 l4-8 l4-1 c4)", "(DRIVE-TRUCK t2 l2-0 l2-7 c2)", "(DRIVE-TRUCK t4 l4-8 l4-9 c4)", "(DRIVE-TRUCK t0 l0-2 l0-4 c0)", "(DRIVE-TRUCK t4 l4-8 l4-8 c4)", "(DRIVE-TRUCK t1 l1-0 l1-3 c1)", "(DRIVE-TRUCK t2 l2-0 l2-8 c2)", "(DRIVE-TRUCK t4 l4-8 l4-3 c4)", "(DRIVE-TRUCK t1 l1-0 l1-8 c1)", "(DRIVE-TRUCK t3 l3-0 l3-8 c3)", "(FLY-AIRPLANE a0 l2-0 l0-0)", "(FLY-AIRPLANE a0 l2-0 l4-0)", "(DRIVE-TRUCK t2 l2-0 l2-9 c2)", "(DRIVE-TRUCK t0 l0-2 l0-2 c0)", "(DRIVE-TRUCK t1 l1-0 l1-4 c1)", "(DRIVE-TRUCK t1 l1-0 l1-6 c1)", "(DRIVE-TRUCK t3 l3-0 l3-1 c3)", "(DRIVE-TRUCK t3 l3-0 l3-6 c3)", "(DRIVE-TRUCK t0 l0-2 l0-0 c0)", "(DRIVE-TRUCK t2 l2-0 l2-0 c2)", "(DRIVE-TRUCK t4 l4-8 l4-6 c4)", "(DRIVE-TRUCK t0 l0-2 l0-3 c0)", "(FLY-AIRPLANE a0 l2-0 l2-0)", "(DRIVE-TRUCK t2 l2-0 l2-1 c2)", "(DRIVE-TRUCK t2 l2-0 l2-3 c2)", "(FLY-AIRPLANE a0 l2-0 l1-0)", "(DRIVE-TRUCK t1 l1-0 l1-0 c1)", "(DRIVE-TRUCK t0 l0-2 l0-7 c0)", "(DRIVE-TRUCK t1 l1-0 l1-9 c1)", "(LOAD-TRUCK p2 t4 l4-8)", "(FLY-AIRPLANE a0 l2-0 l3-0)", "(DRIVE-TRUCK t1 l1-0 l1-5 c1)", "(DRIVE-TRUCK t0 l0-2 l0-6 c0)", "(DRIVE-TRUCK t3 l3-0 l3-3 c3)", "(DRIVE-TRUCK t1 l1-0 l1-7 c1)", "(DRIVE-TRUCK t0 l0-2 l0-9 c0)", "(DRIVE-TRUCK t2 l2-0 l2-5 c2)", "(DRIVE-TRUCK t2 l2-0 l2-6 c2)", "(DRIVE-TRUCK t1 l1-0 l1-2 c1)"], "no": ["(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "(DRIVE-TRUCK t3 l3-0 l3-9 c3)", "(DRIVE-TRUCK t4 l4-8 l4-5 c4)"], "opt": "19", "yes": ["(UNLOAD-TRUCK p3 t2 l2-0)", "(UNLOAD-TRUCK p0 t2 l2-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s10-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l0-3 l0-4 l0-5 l0-6 l0-7 l0-8 l0-9 l1-1 l1-2 l1-3 l1-4 l1-5 l1-6 l1-7 l1-8 l1-9 l2-1 l2-2 l2-3 l2-4 l2-5 l2-6 l2-7 l2-8 l2-9 l3-1 l3-2 l3-3 l3-4 l3-5 l3-6 l3-7 l3-8 l3-9 l4-1 l4-2 l4-3 l4-4 l4-5 l4-6 l4-7 l4-8 l4-9 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p1 l1-0) (at p2 l4-8) (at t0 l0-2) (at t1 l1-0) (at t2 l2-0) (at t3 l3-0) (at t4 l4-8) (in p0 t2) (in p3 t2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l0-3 c0) (in-city l0-4 c0) (in-city l0-5 c0) (in-city l0-6 c0) (in-city l0-7 c0) (in-city l0-8 c0) (in-city l0-9 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l1-3 c1) (in-city l1-4 c1) (in-city l1-5 c1) (in-city l1-6 c1) (in-city l1-7 c1) (in-city l1-8 c1) (in-city l1-9 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l2-3 c2) (in-city l2-4 c2) (in-city l2-5 c2) (in-city l2-6 c2) (in-city l2-7 c2) (in-city l2-8 c2) (in-city l2-9 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l3-3 c3) (in-city l3-4 c3) (in-city l3-5 c3) (in-city l3-6 c3) (in-city l3-7 c3) (in-city l3-8 c3) (in-city l3-9 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4) (in-city l4-3 c4) (in-city l4-4 c4) (in-city l4-5 c4) (in-city l4-6 c4) (in-city l4-7 c4) (in-city l4-8 c4) (in-city l4-9 c4))\n    (:goal (and (at p0 l1-5) (at p1 l2-7) (at p2 l4-8) (at p3 l1-1)))\n)"}
{"id": -3523002357953760965, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-1 and l1-0 are in c1; l0-0 and l0-1 are in c0. Currently, a0 and t0 are at l0-0, p1, p3, and t1 are at l1-1, p2 and p0 are in a0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck which is in location ?loc-from in city ?city to another location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(UNLOAD-AIRPLANE p2 a0 l0-0)", "(FLY-AIRPLANE a0 l0-0 l1-0)", "(DRIVE-TRUCK t1 l1-1 l1-1 c1)", "(FLY-AIRPLANE a0 l0-0 l0-0)", "(DRIVE-TRUCK t0 l0-0 l0-0 c0)", "(UNLOAD-AIRPLANE p0 a0 l0-0)"], "no": ["(LOAD-TRUCK p3 t1 l1-1)", "(LOAD-TRUCK p1 t1 l1-1)", "(DRIVE-TRUCK t0 l0-0 l0-1 c0)"], "opt": "7", "yes": ["(DRIVE-TRUCK t1 l1-1 l1-0 c1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l1-1) (at p3 l1-1) (at t0 l0-0) (at t1 l1-1) (in p0 a0) (in p2 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 3653578614408809387, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1; l4-1, l4-0, and l4-2 are in c4; l3-2, l3-1, and l3-0 are in c3; l2-2, l2-0, and l2-1 are in c2. Currently, p1 is at l3-1, p3, a0, and t4 are at l4-0, t0 is at l0-2, t3 is at l3-0, p2 and t2 are at l2-2, t1 is at l1-2, p0 is in a0. The goal is to reach a state where the following facts hold: p3 is at l3-2, p0 is at l3-0, p1 is at l3-2, and p2 is at l2-2. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(DRIVE-TRUCK t0 l0-2 l0-0 c0)", "(LOAD-TRUCK p3 t4 l4-0)", "(DRIVE-TRUCK t4 l4-0 l4-0 c4)", "(DRIVE-TRUCK t1 l1-2 l1-2 c1)", "(DRIVE-TRUCK t3 l3-0 l3-0 c3)", "(FLY-AIRPLANE a0 l4-0 l0-0)", "(DRIVE-TRUCK t3 l3-0 l3-2 c3)", "(FLY-AIRPLANE a0 l4-0 l3-0)", "(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "(FLY-AIRPLANE a0 l4-0 l2-0)", "(FLY-AIRPLANE a0 l4-0 l4-0)", "(DRIVE-TRUCK t1 l1-2 l1-0 c1)", "(UNLOAD-AIRPLANE p0 a0 l4-0)", "(DRIVE-TRUCK t4 l4-0 l4-2 c4)", "(DRIVE-TRUCK t2 l2-2 l2-0 c2)", "(DRIVE-TRUCK t2 l2-2 l2-1 c2)", "(DRIVE-TRUCK t0 l0-2 l0-2 c0)", "(DRIVE-TRUCK t4 l4-0 l4-1 c4)", "(DRIVE-TRUCK t1 l1-2 l1-1 c1)", "(DRIVE-TRUCK t3 l3-0 l3-1 c3)"], "no": ["(LOAD-TRUCK p2 t2 l2-2)", "(DRIVE-TRUCK t2 l2-2 l2-2 c2)", "(FLY-AIRPLANE a0 l4-0 l1-0)"], "opt": "10", "yes": ["(LOAD-AIRPLANE p3 a0 l4-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l4-0) (at p1 l3-1) (at p2 l2-2) (at p3 l4-0) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-0) (at t4 l4-0) (in p0 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": 1929803378312429850, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1; l4-1, l4-0, and l4-2 are in c4; l3-2, l3-1, and l3-0 are in c3; l2-2, l2-0, and l2-1 are in c2. Currently, t0 is at l0-2, p2 and t2 are at l2-2, a0 and p0 are at l3-0, t4 is at l4-0, t1 is at l1-2, t3 is at l3-2, p3 and p1 are in t3. The goal is to reach a state where the following facts hold: p3 is at l3-2, p0 is at l3-0, p1 is at l3-2, and p2 is at l2-2. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(DRIVE-TRUCK t0 l0-2 l0-0 c0)", "(DRIVE-TRUCK t2 l2-2 l2-2 c2)", "(DRIVE-TRUCK t4 l4-0 l4-0 c4)", "(FLY-AIRPLANE a0 l3-0 l3-0)", "(DRIVE-TRUCK t1 l1-2 l1-2 c1)", "(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "(LOAD-AIRPLANE p0 a0 l3-0)", "(DRIVE-TRUCK t1 l1-2 l1-0 c1)", "(DRIVE-TRUCK t4 l4-0 l4-2 c4)", "(FLY-AIRPLANE a0 l3-0 l1-0)", "(DRIVE-TRUCK t3 l3-2 l3-1 c3)", "(DRIVE-TRUCK t2 l2-2 l2-1 c2)", "(UNLOAD-TRUCK p1 t3 l3-2)", "(LOAD-TRUCK p2 t2 l2-2)", "(FLY-AIRPLANE a0 l3-0 l0-0)", "(DRIVE-TRUCK t0 l0-2 l0-2 c0)", "(DRIVE-TRUCK t4 l4-0 l4-1 c4)", "(FLY-AIRPLANE a0 l3-0 l4-0)", "(DRIVE-TRUCK t1 l1-2 l1-1 c1)", "(FLY-AIRPLANE a0 l3-0 l2-0)"], "no": ["(DRIVE-TRUCK t2 l2-2 l2-0 c2)", "(DRIVE-TRUCK t3 l3-2 l3-0 c3)", "(DRIVE-TRUCK t3 l3-2 l3-2 c3)"], "opt": "2", "yes": ["(UNLOAD-TRUCK p3 t3 l3-2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l3-0) (at p0 l3-0) (at p2 l2-2) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in p1 t3) (in p3 t3) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": 977136274744167794, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-1 and l1-0 are in c1; l0-0 and l0-1 are in c0. Currently, a0 is at l0-0, p1 and t1 are at l1-1, t0 is at l0-1, p3, p2, and p0 are in a0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(UNLOAD-AIRPLANE p2 a0 l0-0)", "(FLY-AIRPLANE a0 l0-0 l1-0)", "(FLY-AIRPLANE a0 l0-0 l0-0)", "(DRIVE-TRUCK t0 l0-1 l0-1 c0)", "(UNLOAD-AIRPLANE p0 a0 l0-0)", "(DRIVE-TRUCK t0 l0-1 l0-0 c0)"], "no": ["(UNLOAD-AIRPLANE p3 a0 l0-0)", "(LOAD-TRUCK p1 t1 l1-1)", "(DRIVE-TRUCK t1 l1-1 l1-1 c1)"], "opt": "10", "yes": ["(DRIVE-TRUCK t1 l1-1 l1-0 c1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l1-1) (at t0 l0-1) (at t1 l1-1) (in p0 a0) (in p2 a0) (in p3 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 4195562601084254884, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-1 and l1-0 are in c1; l0-0 and l0-1 are in c0. Currently, a0 and t0 are at l0-0, p3, t1, and p2 are at l1-0, p1 is at l1-1, p0 is in t1. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(UNLOAD-TRUCK p0 t1 l1-0)", "(DRIVE-TRUCK t0 l0-0 l0-1 c0)", "(LOAD-TRUCK p2 t1 l1-0)", "(DRIVE-TRUCK t1 l1-0 l1-1 c1)", "(DRIVE-TRUCK t1 l1-0 l1-0 c1)"], "no": ["(DRIVE-TRUCK t0 l0-0 l0-0 c0)", "(FLY-AIRPLANE a0 l0-0 l1-0)", "(FLY-AIRPLANE a0 l0-0 l0-0)"], "opt": "4", "yes": ["(LOAD-TRUCK p3 t1 l1-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l1-1) (at p2 l1-0) (at p3 l1-0) (at t0 l0-0) (at t1 l1-0) (in p0 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 1361760087439729272, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-1 and l1-0 are in c1; l0-0 and l0-1 are in c0. Currently, p1 and p3 are at l1-1, t1 and a0 are at l1-0, t0 is at l0-0, p2 is in a0, p0 is in t1. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(UNLOAD-TRUCK p0 t1 l1-0)", "(DRIVE-TRUCK t1 l1-0 l1-1 c1)", "(DRIVE-TRUCK t0 l0-0 l0-0 c0)", "(DRIVE-TRUCK t1 l1-0 l1-0 c1)"], "no": ["(FLY-AIRPLANE a0 l1-0 l0-0)", "(DRIVE-TRUCK t0 l0-0 l0-1 c0)", "(FLY-AIRPLANE a0 l1-0 l1-0)"], "opt": "3", "yes": ["(UNLOAD-AIRPLANE p2 a0 l1-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p1 l1-1) (at p3 l1-1) (at t0 l0-0) (at t1 l1-0) (in p0 t1) (in p2 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 7431923526505070145, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1; l2-2, l2-0, and l2-1 are in c2. Currently, a0, p1, and p2 are at l1-0, p4 and t0 are at l0-0, t2 is at l2-1, t1 and p3 are at l1-2, p0 is in t1. The goal is to reach a state where the following facts hold: p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, p1 is at l1-0, and p3 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(LOAD-AIRPLANE p2 a0 l1-0)", "(DRIVE-TRUCK t1 l1-2 l1-2 c1)", "(DRIVE-TRUCK t2 l2-1 l2-2 c2)", "(DRIVE-TRUCK t0 l0-0 l0-2 c0)", "(DRIVE-TRUCK t1 l1-2 l1-0 c1)", "(LOAD-TRUCK p4 t0 l0-0)", "(DRIVE-TRUCK t0 l0-0 l0-1 c0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "(DRIVE-TRUCK t2 l2-1 l2-0 c2)", "(FLY-AIRPLANE a0 l1-0 l1-0)", "(DRIVE-TRUCK t0 l0-0 l0-0 c0)", "(FLY-AIRPLANE a0 l1-0 l0-0)", "(LOAD-TRUCK p3 t1 l1-2)"], "no": ["(DRIVE-TRUCK t2 l2-1 l2-1 c2)", "(FLY-AIRPLANE a0 l1-0 l2-0)", "(DRIVE-TRUCK t1 l1-2 l1-1 c1)"], "opt": "5", "yes": ["(UNLOAD-TRUCK p0 t1 l1-2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p5-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 p4 - package t0 t1 t2 - truck)\n    (:init (at a0 l1-0) (at p1 l1-0) (at p2 l1-0) (at p3 l1-2) (at p4 l0-0) (at t0 l0-0) (at t1 l1-2) (at t2 l2-1) (in p0 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l1-2) (at p1 l1-0) (at p2 l1-2) (at p3 l1-2) (at p4 l0-0)))\n)"}
{"id": 6017629270997064125, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. \nThe locations are in cities as follows: l1-1 and l1-0 are in c1; l0-0 and l0-1 are in c0. \nCurrently, p1 and t0 are at l0-1, p3 is at l0-0, t1 is at l1-1, a0 is at l1-0, p0 is in t0, p2 is in t1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-0, p0 is at l0-1, and p3 is at l0-0. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(FLY-AIRPLANE a0 l1-0 l1-0)", "(DRIVE-TRUCK t1 l1-1 l1-0 c1)", "(DRIVE-TRUCK t0 l0-1 l0-1 c0)", "(UNLOAD-TRUCK p2 t1 l1-1)", "(DRIVE-TRUCK t0 l0-1 l0-0 c0)"], "no": ["(LOAD-TRUCK p1 t0 l0-1)", "(FLY-AIRPLANE a0 l1-0 l0-0)", "(DRIVE-TRUCK t1 l1-1 l1-1 c1)"], "opt": "6", "yes": ["(UNLOAD-TRUCK p0 t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p1 l0-1) (at p3 l0-0) (at t0 l0-1) (at t1 l1-1) (in p0 t0) (in p2 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-0) (at p3 l0-0)))\n)"}
{"id": 3451799420544961128, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1. \nCurrently, a0 is at l0-0, t0 is at l0-2, p2 and t1 are at l1-2, p3 is at l0-1, p0 is in t0, p1 is in t1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-2, p3 is at l0-1, and p0 is at l0-2. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(FLY-AIRPLANE a0 l0-0 l1-0)", "(DRIVE-TRUCK t1 l1-2 l1-2 c1)", "(DRIVE-TRUCK t0 l0-2 l0-2 c0)", "(DRIVE-TRUCK t1 l1-2 l1-1 c1)", "(FLY-AIRPLANE a0 l0-0 l0-0)", "(DRIVE-TRUCK t1 l1-2 l1-0 c1)"], "no": ["(LOAD-TRUCK p2 t1 l1-2)", "(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "(DRIVE-TRUCK t0 l0-2 l0-0 c0)"], "opt": "2", "yes": ["(UNLOAD-TRUCK p0 t0 l0-2)", "(UNLOAD-TRUCK p1 t1 l1-2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p2 l1-2) (at p3 l0-1) (at t0 l0-2) (at t1 l1-2) (in p0 t0) (in p1 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": 9122084414558642831, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1. \nCurrently, a0 is at l0-0, p1 and t1 are at l1-1, t0 is at l0-2, p3, p2, and p0 are in a0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-2, and p2 is at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(UNLOAD-AIRPLANE p2 a0 l0-0)", "(FLY-AIRPLANE a0 l0-0 l1-0)", "(DRIVE-TRUCK t1 l1-1 l1-1 c1)", "(UNLOAD-AIRPLANE p3 a0 l0-0)", "(DRIVE-TRUCK t0 l0-2 l0-2 c0)", "(FLY-AIRPLANE a0 l0-0 l0-0)", "(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "(LOAD-TRUCK p1 t1 l1-1)"], "no": ["(DRIVE-TRUCK t1 l1-1 l1-2 c1)", "(UNLOAD-AIRPLANE p0 a0 l0-0)", "(DRIVE-TRUCK t0 l0-2 l0-0 c0)"], "opt": "11", "yes": ["(DRIVE-TRUCK t1 l1-1 l1-0 c1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l1-1) (at t0 l0-2) (at t1 l1-1) (in p0 a0) (in p2 a0) (in p3 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 5547486219358455775, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. \nThe locations are in cities as follows: l1-1 and l1-0 are in c1; l0-0 and l0-1 are in c0. \nCurrently, p1, t0, and p0 are at l0-1, a0 and p3 are at l0-0, t1 is at l1-1, p2 is in t1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-0, p0 is at l0-1, and p3 is at l0-0. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(LOAD-TRUCK p1 t0 l0-1)", "(LOAD-TRUCK p0 t0 l0-1)", "(DRIVE-TRUCK t1 l1-1 l1-0 c1)", "(DRIVE-TRUCK t1 l1-1 l1-1 c1)", "(UNLOAD-TRUCK p2 t1 l1-1)", "(DRIVE-TRUCK t0 l0-1 l0-0 c0)"], "no": ["(DRIVE-TRUCK t0 l0-1 l0-1 c0)", "(LOAD-AIRPLANE p3 a0 l0-0)", "(FLY-AIRPLANE a0 l0-0 l0-0)"], "opt": "6", "yes": ["(FLY-AIRPLANE a0 l0-0 l1-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l0-1) (at p3 l0-0) (at t0 l0-1) (at t1 l1-1) (in p2 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-0) (at p3 l0-0)))\n)"}
{"id": -8564985257498037210, "group": "goal_closer_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-1, and l1-0 are in c1. \nCurrently, p1 is at l1-1, p0 and a0 are at l1-0, t0 is at l0-1, t1 is at l1-2, p3 and p2 are in a0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-2, and p2 is at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(DRIVE-TRUCK t1 l1-2 l1-2 c1)", "(UNLOAD-AIRPLANE p2 a0 l1-0)", "(LOAD-AIRPLANE p0 a0 l1-0)", "(DRIVE-TRUCK t1 l1-2 l1-1 c1)", "(UNLOAD-AIRPLANE p3 a0 l1-0)", "(FLY-AIRPLANE a0 l1-0 l0-0)", "(DRIVE-TRUCK t0 l0-1 l0-0 c0)"], "no": ["(DRIVE-TRUCK t0 l0-1 l0-1 c0)", "(FLY-AIRPLANE a0 l1-0 l1-0)", "(DRIVE-TRUCK t0 l0-1 l0-2 c0)"], "opt": "9", "yes": ["(DRIVE-TRUCK t1 l1-2 l1-0 c1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l1-0) (at p1 l1-1) (at t0 l0-1) (at t1 l1-2) (in p2 a0) (in p3 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
