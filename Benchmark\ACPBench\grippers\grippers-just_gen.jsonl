{"id": -8805827649085697431, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 is at room2, ball2, ball4, and ball1 are at room1. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball4 is in room room2, Ball ball2 is at room2 location, and Ball ball1 is at room2 location.", "question": "Simplify the plan \"(move robot1 room2 room1) (pick robot1 ball1 room1 right1) (drop robot1 ball1 room1 right1) (pick robot1 ball1 room1 left1) (pick robot1 ball4 room1 right1) (move robot1 room1 room2) (drop robot1 ball4 room2 right1) (move robot1 room2 room1) (pick robot1 ball2 room1 right1) (move robot1 room1 room2) (drop robot1 ball2 room2 right1) (drop robot1 ball1 room2 left1) (move robot1 room2 room1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room2 room1)", "3"], ["(pick robot1 ball1 room1 right1)", "(drop robot1 ball1 room1 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 430577470843354446, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 is at room2, ball2, ball4, and ball1 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball4 is at room2 location, Ball ball2 is at room2 location, and Ball ball1 is in room room2.", "question": "Simplify the plan \"(move robot1 room2 room1) (pick robot1 ball1 room1 right1) (drop robot1 ball1 room1 right1) (pick robot1 ball4 room1 left1) (move robot1 room1 room2) (drop robot1 ball4 room2 left1) (move robot1 room2 room1) (pick robot1 ball1 room1 right1) (pick robot1 ball2 room1 left1) (move robot1 room1 room2) (drop robot1 ball1 room2 right1) (drop robot1 ball2 room2 left1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick robot1 ball1 room1 right1)", "(drop robot1 ball1 room1 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -2174743514233604542, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 is at room2, ball2, ball4, and ball1 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball4 is in room room2, Ball ball2 is at room2 location, and Ball ball1 is at room2 location.", "question": "Simplify the plan \"(move robot1 room2 room1) (move robot1 room1 room2) (move robot1 room2 room1) (pick robot1 ball2 room1 right1) (pick robot1 ball1 room1 left1) (move robot1 room1 room2) (drop robot1 ball1 room2 left1) (drop robot1 ball2 room2 right1) (move robot1 room2 room1) (pick robot1 ball4 room1 left1) (move robot1 room1 room2) (drop robot1 ball4 room2 left1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room2 room1)", "(move robot1 room1 room2)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 3607000784502824460, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 is at room2, ball2, ball4, and ball1 are at room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball4 is at room2 location, Ball ball2 is at room2 location, and Ball ball1 is at room2 location.", "question": "Simplify the plan \"(move robot1 room2 room1) (pick robot1 ball1 room1 right1) (pick robot1 ball4 room1 left1) (move robot1 room1 room2) (drop robot1 ball4 room2 left1) (drop robot1 ball1 room2 right1) (move robot1 room2 room1) (pick robot1 ball2 room1 right1) (move robot1 room1 room2) (drop robot1 ball2 room2 right1) (move robot1 room2 room1) (move robot1 room1 room2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room1 room2)", "3"], ["(move robot1 room2 room1)", "(move robot1 room1 room2)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 1034046769448917270, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 is at room2, ball2, ball4, and ball1 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball4 is in room room2, Ball ball2 is in room room2, and Ball ball1 is in room room2.", "question": "Simplify the plan \"(move robot1 room2 room1) (pick robot1 ball1 room1 left1) (pick robot1 ball2 room1 right1) (move robot1 room1 room2) (drop robot1 ball2 room2 right1) (move robot1 room2 room1) (pick robot1 ball4 room1 right1) (drop robot1 ball4 room1 right1) (pick robot1 ball4 room1 right1) (move robot1 room1 room2) (drop robot1 ball1 room2 left1) (drop robot1 ball4 room2 right1) (move robot1 room2 room1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room2 room1)", "3"], ["(pick robot1 ball4 room1 right1)", "(drop robot1 ball4 room1 right1)", "-1"], ["(drop robot1 ball4 room1 right1)", "(pick robot1 ball4 room1 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -2476745569509929703, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room4 and both grippers are free. Additionally, ball2 and ball4 are at room1, ball3 is at room5, ball1 is at room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r. The goal is to reach a state where the following facts hold: Ball ball1 is in room room4, Ball ball4 is in room room5, Ball ball3 is at room4 location, and Ball ball2 is at room3 location.", "question": "Simplify the plan \"(move robot1 room4 room3) (move robot1 room3 room4) (move robot1 room4 room1) (pick robot1 ball4 room1 left1) (pick robot1 ball2 room1 right1) (move robot1 room1 room3) (drop robot1 ball2 room3 right1) (move robot1 room3 room2) (pick robot1 ball1 room2 right1) (move robot1 room2 room1) (move robot1 room1 room5) (drop robot1 ball4 room5 left1) (pick robot1 ball3 room5 left1) (move robot1 room5 room4) (drop robot1 ball3 room4 left1) (drop robot1 ball1 room4 right1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room4 room3)", "(move robot1 room3 room4)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball3 room5) (at ball4 room1) (at-robby robot1 room4) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 2617187573479978795, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball8, ball5, ball7, and ball3 are at room3, ball10, ball2, ball11, ball14, ball9, ball4, and ball1 are at room1, ball13, ball12, ball15, and ball6 are at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball1 is at room2 location, Ball ball13 is at room2 location, Ball ball6 is at room1 location, Ball ball12 is at room2 location, Ball ball2 is at room1 location, Ball ball10 is at room3 location, Ball ball7 is at room3 location, Ball ball8 is in room room1, Ball ball14 is in room room3, Ball ball11 is at room3 location, Ball ball15 is at room1 location, Ball ball9 is at room3 location, Ball ball4 is in room room1, and Ball ball5 is in room room1.", "question": "Simplify the plan \"(pick robot1 ball5 room3 right1) (drop robot1 ball5 room3 right1) (pick robot1 ball3 room3 left1) (move robot1 room3 room1) (move robot1 room1 room2) (drop robot1 ball3 room2 left1) (pick robot1 ball6 room2 left1) (pick robot1 ball15 room2 right1) (move robot1 room2 room1) (drop robot1 ball6 room1 left1) (drop robot1 ball15 room1 right1) (pick robot1 ball1 room1 left1) (pick robot1 ball10 room1 right1) (move robot1 room1 room2) (drop robot1 ball1 room2 left1) (move robot1 room2 room1) (pick robot1 ball14 room1 left1) (move robot1 room1 room3) (drop robot1 ball14 room3 left1) (drop robot1 ball10 room3 right1) (pick robot1 ball5 room3 left1) (move robot1 room3 room1) (drop robot1 ball5 room1 left1) (pick robot1 ball9 room1 left1) (pick robot1 ball11 room1 right1) (move robot1 room1 room3) (drop robot1 ball9 room3 left1) (pick robot1 ball8 room3 left1) (drop robot1 ball11 room3 right1) (move robot1 room3 room1) (drop robot1 ball8 room1 left1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick robot1 ball5 room3 right1)", "(drop robot1 ball5 room3 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": -2726856982932954081, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 is at room2, ball2, ball4, and ball1 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball4 is in room room2, Ball ball2 is in room room2, and Ball ball1 is at room2 location.", "question": "Simplify the plan \"(move robot1 room2 room1) (pick robot1 ball4 room1 left1) (pick robot1 ball1 room1 right1) (move robot1 room1 room2) (drop robot1 ball1 room2 right1) (drop robot1 ball4 room2 left1) (move robot1 room2 room1) (pick robot1 ball2 room1 right1) (move robot1 room1 room2) (drop robot1 ball2 room2 right1) (pick robot1 ball4 room2 right1) (drop robot1 ball4 room2 right1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick robot1 ball4 room2 right1)", "(drop robot1 ball4 room2 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -7064736536543481337, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball8, ball5, ball7, and ball3 are at room3, ball10, ball2, ball11, ball14, ball9, ball4, and ball1 are at room1, ball13, ball12, ball15, and ball6 are at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball1 is at room2 location, Ball ball13 is at room2 location, Ball ball6 is at room1 location, Ball ball12 is at room2 location, Ball ball2 is in room room1, Ball ball10 is in room room3, Ball ball7 is in room room3, Ball ball8 is at room1 location, Ball ball14 is in room room3, Ball ball11 is at room3 location, Ball ball15 is in room room1, Ball ball9 is at room3 location, Ball ball4 is at room1 location, and Ball ball5 is in room room1.", "question": "Simplify the plan \"(pick robot1 ball5 room3 right1) (drop robot1 ball5 room3 right1) (pick robot1 ball3 room3 right1) (move robot1 room3 room2) (drop robot1 ball3 room2 right1) (pick robot1 ball15 room2 right1) (move robot1 room2 room1) (drop robot1 ball15 room1 right1) (pick robot1 ball1 room1 right1) (pick robot1 ball11 room1 left1) (move robot1 room1 room2) (drop robot1 ball1 room2 right1) (pick robot1 ball6 room2 right1) (move robot1 room2 room1) (drop robot1 ball6 room1 right1) (pick robot1 ball14 room1 right1) (move robot1 room1 room3) (drop robot1 ball14 room3 right1) (pick robot1 ball5 room3 right1) (drop robot1 ball11 room3 left1) (drop robot1 ball5 room3 right1) (pick robot1 ball8 room3 right1) (pick robot1 ball5 room3 left1) (move robot1 room3 room1) (drop robot1 ball8 room1 right1) (drop robot1 ball5 room1 left1) (pick robot1 ball10 room1 right1) (pick robot1 ball9 room1 left1) (move robot1 room1 room3) (drop robot1 ball10 room3 right1) (drop robot1 ball9 room3 left1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick robot1 ball5 room3 right1)", "(drop robot1 ball5 room3 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 7666233745450460101, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball5, ball7, and ball3 are at room3, ball2, ball4, and ball1 are at room1, ball6 is at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball5 is in room room2, Ball ball3 is at room1 location, Ball ball1 is in room room3, Ball ball2 is at room1 location, Ball ball6 is in room room2, Ball ball7 is at room1 location, and Ball ball4 is at room1 location.", "question": "Simplify the plan \"(pick robot1 ball3 room3 right1) (pick robot1 ball7 room3 left1) (move robot1 room3 room1) (drop robot1 ball7 room1 left1) (drop robot1 ball3 room1 right1) (pick robot1 ball1 room1 left1) (move robot1 room1 room3) (drop robot1 ball1 room3 left1) (pick robot1 ball1 room3 right1) (drop robot1 ball1 room3 right1) (pick robot1 ball5 room3 right1) (move robot1 room3 room2) (drop robot1 ball5 room2 right1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick robot1 ball1 room3 right1)", "(drop robot1 ball1 room3 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 5654483433824749790, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 2 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1 and both grippers are free. Additionally, ball3 and ball4 are at room2, ball2 and ball1 are at room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball4 is at room1 location, Ball ball3 is at room1 location, Ball ball2 is at room1 location, and Ball ball1 is in room room1.", "question": "Simplify the plan \"(move robot1 room1 room2) (move robot1 room2 room1) (move robot1 room1 room2) (pick robot1 ball3 room2 right1) (pick robot1 ball4 room2 left1) (move robot1 room2 room1) (drop robot1 ball4 room1 left1) (drop robot1 ball3 room1 right1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room1 room2)", "(move robot1 room2 room1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room2) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room1) (at ball4 room1)))\n)"}
{"id": 8786151350007727441, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball2, ball4, and ball1 are at room1, ball3 is at room3. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper. The goal is to reach a state where the following facts hold: Ball ball1 is at room3 location, Ball ball3 is in room room3, Ball ball2 is at room2 location, and Ball ball4 is in room room3.", "question": "Simplify the plan \"(move robot1 room3 room1) (pick robot1 ball4 room1 left1) (pick robot1 ball1 room1 right1) (move robot1 room1 room3) (drop robot1 ball1 room3 right1) (pick robot1 ball1 room3 right1) (drop robot1 ball4 room3 left1) (drop robot1 ball1 room3 right1) (move robot1 room3 room1) (pick robot1 ball2 room1 left1) (move robot1 room1 room2) (drop robot1 ball2 room2 left1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drop robot1 ball1 room3 right1)", "(pick robot1 ball1 room3 right1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": -6584878200179340380, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1 and ball2 are at room3, ball4 is at room6, ball3 is at room5. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r. The goal is to reach a state where the following facts hold: Ball ball4 is at room1 location, Ball ball3 is in room room3, Ball ball2 is in room room2, and Ball ball1 is in room room1.", "question": "Simplify the plan \"(pick robot1 ball1 room3 left1) (drop robot1 ball1 room3 left1) (pick robot1 ball1 room3 right1) (move robot1 room3 room6) (pick robot1 ball4 room6 left1) (move robot1 room6 room1) (drop robot1 ball1 room1 right1) (drop robot1 ball4 room1 left1) (move robot1 room1 room5) (pick robot1 ball3 room5 right1) (move robot1 room5 room3) (drop robot1 ball3 room3 right1) (pick robot1 ball2 room3 right1) (move robot1 room3 room6) (move robot1 room6 room2) (drop robot1 ball2 room2 right1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick robot1 ball1 room3 left1)", "(drop robot1 ball1 room3 left1)", "-1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room3) (at ball2 room3) (at ball3 room5) (at ball4 room6) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -8285462760965129352, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2 and both grippers are free. Additionally, ball3 and ball1 are at room2, ball2 is at room1, ball4 is at room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball4 is at room1 location, Ball ball2 is in room room1, and Ball ball1 is in room room1.", "question": "Simplify the plan \"(pick robot1 ball1 room2 left1) (move robot1 room2 room3) (move robot1 room3 room1) (drop robot1 ball1 room1 left1) (move robot1 room1 room3) (pick robot1 ball4 room3 left1) (move robot1 room3 room1) (drop robot1 ball4 room1 left1) (move robot1 room1 room2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room1 room2)", "1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room3) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 6627623340339066738, "group": "action_justification_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball2 and ball1 are at room2, ball4 is at room4, ball3 is at room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball3 is in room room3, Ball ball2 is at room2 location, and Ball ball1 is in room room1.", "question": "Simplify the plan \"(move robot1 room3 room2) (pick robot1 ball1 room2 left1) (move robot1 room2 room4) (pick robot1 ball4 room4 right1) (move robot1 room4 room5) (move robot1 room5 room1) (drop robot1 ball1 room1 left1) (drop robot1 ball4 room1 right1) (move robot1 room1 room4)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move robot1 room1 room4)", "1"]], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball2 room2) (at ball3 room3) (at ball4 room4) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
