{"question_id": "b0892f2a-5fe1-400b-af7b-9c0b891343ab", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob picks up spanner5 from location1, bob picks up spanner4 from location1, bob walks to location2 from location1, bob walks from location2 to location3, from location3 to location4, bob walks, spanner1 is picked up by bob from location4, bob walks to location5 from location4, bob walks from location5 to location6 and from location6, bob picks up spanner3 to reach the current state. In this state, if bob walks from location6 to location7, is it True or False that nut3 is at gate and spanner2 is not currently at location1?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then collects spanner5 and spanner4 from location1, proceeds to location2, and subsequently walks to location3 and then location4. At location4, bob picks up spanner1, then walks to location5 and from there to location6, where he collects spanner3, resulting in the current state. In this state, if bob walks from location6 to location7, is it True or False that nut3 is at the gate and spanner2 is no longer at location1?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, with all of them being functional."}
{"question_id": "02bf2025-cab8-4797-9b3e-40352c02f9d2", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, from location2, bob picks up spanner3, bob walks from location2 to location3, spanner5 is picked up by bob from location3, bob picks up spanner1 from location3, bob walks to location4 from location3, bob walks from location4 to location5, spanner2 is picked up by bob from location5 and bob walks to location6 from location5 to reach the current state. In this state, if spanner4 is picked up by bob from location6, is it True or False that spanner4 is carried by bob?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. He then walks from location2 to location3, picks up spanner5 and spanner1 from location3, and proceeds to walk from location3 to location4 and then from location4 to location5. At location5, Bob picks up spanner2 and walks to location6, resulting in the current state. In this state, if Bob picks up spanner4 from location6, is it True or False that Bob is carrying spanner4?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6, a connection between location9 and the gate, and a connection between the shed and location1. Bob is currently situated at the shed. Location2 is connected to location3, location3 is connected to location4, location4 is connected to location5, location6 is connected to location7, and locations 7 and 8 are connected, as are locations 8 and 9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "dbada969-b108-4a09-bc0c-3b9dd427f571", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if bob picks up spanner5 from location1, is it True or False that nut5 is not currently at location9?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if bob picks up spanner5 from location1, is it True or False that nut5 is not at location9 at this point?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, all of which are functional."}
{"question_id": "e82aef92-c01e-4b7a-8449-6f0a3132bb24", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1 to location2, bob walks, bob picks up spanner3 from location2, bob walks to location3 from location2, spanner5 is picked up by bob from location3, spanner1 is picked up by bob from location3, bob walks from location3 to location4, from location4 to location5, bob walks, from location5, bob picks up spanner2 and bob walks from location5 to location6 to reach the current state. In this state, if bob picks up spanner4 from location6, is it True or False that nut4 is not currently at location2?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then to location2, then walks, picks up spanner3 at location2, proceeds to location3, collects spanner5 and spanner1 from location3, walks to location4 and then to location5, walks again, picks up spanner2 at location5, and finally moves to location6, resulting in the current state. In this state, if bob picks up spanner4 from location6, is it True or False that nut4 is not currently located at location2?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "c957bb88-21a2-4cef-af35-eb1497f30fd4", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if bob walks to location2 from location1, is it True or False that nut5 is not located at location9?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the shed to location1, bob walks to reach the current state. In this state, if bob walks to location2 from location1, is it True or False that nut5 is not at location9?", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked to location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, and location9 is connected to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "6394a668-f7cd-434a-a9b5-bef3bc7617b7", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1 to location2, bob walks, bob picks up spanner3 from location2, bob walks from location2 to location3, bob picks up spanner5 from location3, bob picks up spanner1 from location3, from location3 to location4, bob walks, bob walks to location5 from location4, bob picks up spanner2 from location5 and bob walks to location6 from location5 to reach the current state. In this state, if bob picks up spanner4 from location6, is it True or False that spanner4 is not currently at location6?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then to location2, then walks, picks up spanner3 at location2, proceeds to location3, collects spanner5 and spanner1 at location3, moves to location4, walks, then heads to location5 from location4, picks up spanner2 at location5, and finally walks to location6 from location5, resulting in the current state. In this state, if bob picks up spanner4 from location6, is it True or False that spanner4 is not currently at location6?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is situated at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "1f054bf9-6c8b-4d82-9af1-550eba62a2de", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob picks up spanner3 from location2, bob walks to location3 from location2, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, bob walks to location4 from location3, bob walks from location4 to location5, bob picks up spanner2 from location5, from location5 to location6, bob walks, spanner4 is picked up by bob from location6, bob walks to location7 from location6, bob walks from location7 to location8, bob walks from location8 to location9, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, if at gate, bob uses spanner1 to tighten nut5, is it True or False that nut5 is not loose and spanner1 can't be used?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, and walks. At location2, Bob picks up spanner3, proceeds to location3, and from there, he collects spanner5 and spanner1. Next, Bob walks to location4 and then to location5, where he picks up spanner2. He then moves to location6, picks up spanner4, and walks to location7, followed by location8, location9, and finally the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, is it True or False that nut5 is not loose and spanner1 can't be used?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "c2dad4d0-c34b-4595-a8b6-54037e292445", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, from location2 to location3, bob walks, bob walks from location3 to location4, bob walks to location5 from location4, spanner4 is picked up by bob from location5, from location5 to location6, bob walks, spanner1 is picked up by bob from location6, from location6 to location7, bob walks, spanner5 is picked up by bob from location7, from location7 to location8, bob walks, spanner3 is picked up by bob from location8, bob picks up spanner2 from location8, from location8 to location9, bob walks, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if at gate, bob uses spanner1 to tighten nut5, is it True or False that spanner1 is usable and spanner2 is not functional?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then to location2, followed by location3, and then to location4. From location4, Bob proceeds to location5, where he picks up spanner4. He then walks to location6, picks up spanner1, and continues to location7, where he collects spanner5. Next, Bob walks to location8, where he picks up both spanner3 and spanner2. After that, he moves to location9 and then to the gate. Upon arriving at the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and finally uses spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, is it True or False that spanner1 is usable and spanner2 is not functional?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is functional and is currently at location7."}
{"question_id": "fc2955da-4211-4ba3-89a7-161ccf527d28", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob picks up spanner5 from location1, from location1, bob picks up spanner4, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks to location4 from location3, spanner1 is picked up by bob from location4, from location4 to location5, bob walks, bob walks to location6 from location5, from location6, bob picks up spanner3, from location6 to location7, bob walks, bob picks up spanner2 from location7, from location7 to location8, bob walks, bob walks to location9 from location8, bob walks from location9 to gate, nut1 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, is it True or False that nut1 is not secured and spanner5 is not usable?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: bob moves from the shed to location1, then collects spanner5 from location1, followed by picking up spanner4 from the same location. Next, bob proceeds from location1 to location2, then to location3, and subsequently walks to location4 from location3. At location4, bob picks up spanner1 and then moves to location5, followed by location6, where bob collects spanner3. From location6, bob walks to location7, picks up spanner2, and then moves to location8, followed by location9, and finally walks to the gate. Upon reaching the gate, bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if bob uses spanner1 to tighten nut5 at the gate, is it True or False that nut1 remains unsecured and spanner5 becomes unusable?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, all of which are functional."}
{"question_id": "656450f6-af12-482a-8ab8-c4b43bfd26f7", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, if from location1 to location2, bob walks, is it True or False that bob is located at location2 and bob is not located at location1?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, if bob then walks from location1 to location2, is it True or False that bob is now at location2 and no longer at location1?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1, which is functional, is situated at location6. Spanner2, also functional, is located at location8. Spanner3, which is usable, is at location8, while spanner4, also usable, is at location5. Spanner5 is functional and is currently located at location7."}
{"question_id": "cb39d15d-ca53-44cb-a6b5-3cc0ba42160d", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, spanner5 is picked up by bob from location1, spanner4 is picked up by bob from location1, bob walks from location1 to location2, bob walks to location3 from location2, bob walks from location3 to location4, spanner1 is picked up by bob from location4, bob walks from location4 to location5, from location5 to location6, bob walks, spanner3 is picked up by bob from location6, bob walks from location6 to location7, spanner2 is picked up by bob from location7, bob walks from location7 to location8, bob walks from location8 to location9, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if nut5 is tightened by bob using spanner1 at gate, is it True or False that bob is carrying spanner2?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, picks up spanner5 and spanner4 from location1, then proceeds to location2, followed by location3, and then location4, where he picks up spanner1. From location4, Bob walks to location5, then to location6, where he picks up spanner3. He then moves to location7, picks up spanner2, and continues to location8, then location9, and finally reaches the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and also uses spanner2 to tighten nut4. In this state, if Bob uses spanner1 to tighten nut5 at the gate, is it True or False that Bob is carrying spanner2?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is located at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, all of which are functional."}
{"question_id": "b7d08ec1-f00f-4d1a-8cc3-23809bf2296a", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, if spanner5 is picked up by bob from location1, is it True or False that nut2 is tightened?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, if bob picks up spanner5 from location1, is it True or False that nut2 is tightened?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, all of which are functional."}
{"question_id": "be0faaf6-1edf-4aba-bbc9-d480738a24e4", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1 to location2, bob walks, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks from location4 to location5, spanner4 is picked up by bob from location5, bob walks to location6 from location5, from location6, bob picks up spanner1, from location6 to location7, bob walks, bob picks up spanner5 from location7, bob walks to location8 from location7, from location8, bob picks up spanner3, from location8, bob picks up spanner2, bob walks to location9 from location8, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if at gate, bob uses spanner1 to tighten nut5, is it True or False that tightening of nut5 is complete?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then to location2, and continues walking. He proceeds from location2 to location3, then to location4, and walks again. From location4, Bob moves to location5, where he picks up spanner4, and then walks to location6. At location6, he picks up spanner1, and then moves to location7, where he walks and picks up spanner5. Bob then walks to location8, where he picks up spanner3 and spanner2. He proceeds to location9 and then to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, if Bob is at the gate and uses spanner1 to tighten nut5, is the tightening of nut5 complete?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "65754afb-89c4-41d7-90e0-f5d7f9fae308", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob picks up spanner4 from location2, bob picks up spanner3 from location2, bob walks from location2 to location3, bob walks to location4 from location3, bob walks to location5 from location4, bob walks from location5 to location6, spanner5 is picked up by bob from location6, spanner2 is picked up by bob from location6, bob walks to location7 from location6, bob walks from location7 to location8, from location8, bob picks up spanner1, bob walks from location8 to location9, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if nut5 is tightened by bob using spanner1 at gate, is it True or False that nut4 is tightened?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: starting from the shed, bob proceeds to location1, then to location2, where he walks around, picks up spanner4, and then spanner3. From location2, bob moves to location3, then to location4, followed by location5, and then location6, where he collects spanner5 and spanner2. He then walks to location7, then to location8, where he picks up spanner1, and proceeds to location9, before finally reaching the gate. At the gate, bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if bob uses spanner1 to tighten nut5 at the gate, is it True or False that nut4 is tightened?", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is connected to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "b8a24307-5dbd-4a2f-a5e2-6eaa71e9ffb1", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks from location3 to location4, bob walks to location5 from location4, bob picks up spanner4 from location5, bob walks from location5 to location6, spanner1 is picked up by bob from location6, from location6 to location7, bob walks and bob picks up spanner5 from location7 to reach the current state. In this state, if bob walks to location8 from location7, is it True or False that nut5 is not currently at shed?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then to location2, then to location3, then to location4, and then to location5. From location5, Bob picks up spanner4 and proceeds to location6, where he picks up spanner1. He then walks to location7 and picks up spanner5, resulting in the current state. In this state, if Bob walks from location7 to location8, is it True or False that nut5 is not currently at the shed?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "ff950b0d-f420-4ef4-9a49-b3d9d9ca9a59", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, spanner4 is picked up by bob from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, bob walks from location3 to location4, bob walks to location5 from location4, bob walks to location6 from location5, spanner5 is picked up by bob from location6, bob picks up spanner2 from location6, bob walks to location7 from location6, from location7 to location8, bob walks, from location8, bob picks up spanner1, bob walks from location8 to location9, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if nut5 is tightened by bob using spanner1 at gate, is it True or False that nut5 is not loose and spanner1 can't be used?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner4 and spanner3. He then walks to location3, followed by location4, location5, and location6, where he picks up spanner5 and spanner2. Bob continues to location7, then location8, where he collects spanner1, and subsequently walks to location9 before arriving at the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, is it True or False that nut5 is not loose and spanner1 can no longer be used?", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is linked to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "b7591f50-e769-4b73-ad9b-9381bffa4764", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if spanner5 is picked up by bob from location1, is it True or False that bob is carrying spanner5?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the shed to location1, bob walks to reach the current state. In this state, if bob picks up spanner5 from location1, is it True or False that spanner5 is being carried by bob?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, with all of them being functional."}
{"question_id": "e605e31e-564b-4277-ba01-a74458667bad", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, if bob walks from location1 to location2, is it True or False that bob is currently at location2 and bob is not currently at location1?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, if bob proceeds from location1 to location2, is it True or False that bob is now at location2 and no longer at location1?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is usable and located at location5, and spanner3 is at location2 and functional. Spanner4 is also functional and located at location6, and spanner5 is both functional and situated at location3."}
{"question_id": "b298ee36-a28b-428d-85ae-1a3060868cab", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1 to location2, bob walks, spanner3 is picked up by bob from location2, bob walks to location3 from location2, spanner5 is picked up by bob from location3, bob picks up spanner1 from location3, from location3 to location4, bob walks, bob walks from location4 to location5, bob picks up spanner2 from location5 and bob walks to location6 from location5 to reach the current state. In this state, if from location6, bob picks up spanner4, is it True or False that spanner4 is carried by bob?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, then walks, picks up spanner3 at location2, proceeds to location3 from location2, collects spanner5 and spanner1 at location3, walks to location4, then to location5, picks up spanner2 at location5, and finally walks to location6 from location5, resulting in the current state. In this state, if Bob picks up spanner4 from location6, is it True or False that Bob is carrying spanner4?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "ef59720d-40cf-4502-bf18-6589790b74ae", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, bob picks up spanner3 from location2, from location2 to location3, bob walks, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, bob walks from location4 to location5, from location5, bob picks up spanner2, bob walks to location6 from location5, spanner4 is picked up by bob from location6, from location6 to location7, bob walks, bob walks to location8 from location7, from location8 to location9, bob walks, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, is it True or False that nut5 is not loose?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he collects spanner3. He proceeds from location2 to location3, picks up spanner5, and then spanner1. From location3, Bob walks to location4, then to location5, where he collects spanner2. He then moves from location5 to location6, picks up spanner4, and continues from location6 to location7, then to location8, and from location8 to location9, before finally reaching the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and also uses spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, is it True or False that nut5 is not loose?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "05a3f75c-5bb8-46f4-86a9-1deb6640b6e4", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1 to location2, bob walks, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob picks up spanner5 from location3, from location3, bob picks up spanner1, from location3 to location4, bob walks, bob walks to location5 from location4, bob picks up spanner2 from location5, bob walks from location5 to location6, spanner4 is picked up by bob from location6, bob walks from location6 to location7, from location7 to location8, bob walks, from location8 to location9, bob walks, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if at gate, bob uses spanner1 to tighten nut5, is it True or False that spanner3 is carried by bob?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then to location2, where he picks up spanner3, and proceeds to location3. At location3, bob collects spanner5 and spanner1. He then walks to location4, followed by location5, where he picks up spanner2, and then to location6, where he collects spanner4. From location6, bob moves to location7, then to location8, and subsequently to location9, before finally reaching the gate. Upon arriving at the gate, bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and also uses spanner2 to tighten nut4. In this state, if at the gate, bob uses spanner1 to tighten nut5, is it True or False that spanner3 is carried by bob?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "11c916b6-f092-45b9-9a45-3169c724e848", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, spanner4 is picked up by bob from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, bob walks to location4 from location3, from location4 to location5, bob walks, from location5 to location6, bob walks, spanner5 is picked up by bob from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, if bob walks from location6 to location7, is it True or False that bob is at location7 and bob is not located at location6?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3, before proceeding to location3, then location4, location5, and location6, where he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, if bob moves from location6 to location7, is it True or False that bob is now at location7 and no longer at location6?", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is linked to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "0857f537-9b66-414c-b01f-fdd09f049874", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, if bob walks from location1 to location2, is it True or False that spanner4 is not currently at location6?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, if bob proceeds from location1 to location2, is it True or False that spanner4 is not presently at location6?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "e13b5a5c-85fd-4bd8-add1-d63e8ba7a85d", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1, bob picks up spanner5, from location1, bob picks up spanner4, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks from location3 to location4, bob picks up spanner1 from location4, bob walks to location5 from location4, bob walks to location6 from location5, spanner3 is picked up by bob from location6, bob walks from location6 to location7, from location7, bob picks up spanner2, from location7 to location8, bob walks, from location8 to location9, bob walks, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if nut5 is tightened by bob using spanner1 at gate, is it True or False that nut5 is secured and spanner1 is not usable?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from the shed, bob proceeds to location1, where he collects spanner5 and spanner4, then walks to location2, followed by location3, and then location4, where he picks up spanner1, after which he moves to location5, then location6, where he collects spanner3, and then location7, where he picks up spanner2, and subsequently walks to location8, then location9, and finally reaches the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if bob uses spanner1 to tighten nut5 at the gate, is it True or False that nut5 is secured and spanner1 is no longer usable?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, with all of them being functional."}
{"question_id": "9f185404-44d7-4d21-8b64-20647bdc9426", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob picks up spanner4 from location2, spanner3 is picked up by bob from location2, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks to location5 from location4, bob walks to location6 from location5, from location6, bob picks up spanner5 and bob picks up spanner2 from location6 to reach the current state. In this state, if bob walks to location7 from location6, is it True or False that nut3 is located at location9?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He then proceeds to location3, followed by location4, and subsequently walks to location5 and then location6. At location6, Bob picks up spanner5 and spanner2, resulting in the current state. In this state, if Bob walks from location6 to location7, is it True or False that nut3 is located at location9?", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked to location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is connected to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "743595c4-60f6-4a93-80c4-a386a28ae099", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, spanner5 is picked up by bob from location1, bob picks up spanner4 from location1, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, from location4, bob picks up spanner1, bob walks from location4 to location5, bob walks to location6 from location5, spanner3 is picked up by bob from location6, bob walks to location7 from location6, spanner2 is picked up by bob from location7, bob walks from location7 to location8, from location8 to location9, bob walks, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, is it True or False that tightening of nut5 is complete?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then collects spanner5 from location1, followed by picking up spanner4 from the same location. He then proceeds to location2 from location1, then to location3 from location2, and subsequently to location4 from location3. At location4, Bob picks up spanner1, then walks to location5, followed by location6, where he collects spanner3. He then moves to location7, where he picks up spanner2, and then walks to location8, then to location9, and finally to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and also uses spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, is it True or False that the tightening of nut5 is complete?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, with all of them being functional."}
{"question_id": "c0cb2583-9191-46bf-9def-90b1e6e4f088", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, spanner5 is picked up by bob from location1, from location1, bob picks up spanner4, bob walks to location2 from location1, bob walks to location3 from location2, bob walks from location3 to location4, bob picks up spanner1 from location4, bob walks from location4 to location5, from location5 to location6, bob walks and from location6, bob picks up spanner3 to reach the current state. In this state, if bob walks from location6 to location7, is it True or False that bob is currently at location7 and bob is not at location6?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then collects spanner5 from location1, followed by picking up spanner4 from the same location. Next, bob proceeds to location2 from location1, then to location3 from location2, and subsequently to location4 from location3. At location4, bob picks up spanner1, then walks to location5, and from there to location6. Finally, bob collects spanner3 at location6, resulting in the current state. In this state, if bob walks from location6 to location7, is it True or False that bob is currently at location7 and no longer at location6?", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and spanners 4 and 5 are both at location1, with all of them being functional."}
