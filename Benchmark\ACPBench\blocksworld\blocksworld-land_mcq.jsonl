{"id": 7118131213557277978, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_3. The goal is to reach a state where the following facts hold: The block block_3 is currently situated above the block block_1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robotic arm is holding block_3. B. The block block_3 is on top of block block_3. C. The block block_1 is on top of block block_1. D. The block block_1 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_3", "The block block_3 is on top of block block_3", "The block block_1 is on top of block block_1", "The block block_1 is currently being held by the robotic arm"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -6685431312027226529, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_1 and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_5 is currently situated under the block block_3, and The block block_5 is on top of block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. No blocks are placed on top of block_5. B. The block block_1 is on top of block block_2. C. The block block_2 is currently situated above the block block_4. D. The robotic arm is holding block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["No blocks are placed on top of block_5", "The block block_1 is on top of block block_2", "The block block_2 is currently situated above the block block_4", "The robotic arm is holding block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 4678240301829034066, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_1, block_2 is on block_4, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_5 is currently situated under the block block_3, and The block block_4 is currently situated under the block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Block block_4 is clear. B. The block block_5 is currently situated above the block block_2. C. The block block_2 is currently situated under the block block_2. D. The block block_4 is on top of block block_4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_4 is clear", "The block block_5 is currently situated above the block block_2", "The block block_2 is currently situated under the block block_2", "The block block_4 is on top of block block_4"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 7598263982846238383, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_5, and block_3. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_1 is on block_4. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_3 is currently situated above the block block_5, and The block block_4 is currently situated under the block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robotic arm is holding block_3. B. The block block_4 is on top of block block_3. C. The robotic arm is holding block_2. D. The block block_2 is currently situated under the block block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_3", "The block block_4 is on top of block block_3", "The robotic arm is holding block_2", "The block block_2 is currently situated under the block block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 3315024090946547688, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_4 and block_2. The following block(s) are stacked on top of another block: block_5 is on block_2 and block_3 is on block_4. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_1, The block block_5 is currently situated under the block block_3, and The block block_5 is currently situated above the block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Block block_5 is located on the table. B. The block block_1 is currently situated above the block block_3. C. The block block_3 is on top of block block_3. D. No blocks are placed on top of block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_5 is located on the table", "The block block_1 is currently situated above the block block_3", "The block block_3 is on top of block block_3", "No blocks are placed on top of block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -6768951488006466563, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_2, block_8, and block_10. The following block(s) are stacked on top of another block: block_5 is on block_2, block_3 is on block_7, block_6 is on block_3, block_9 is on block_5, block_7 is on block_10, and block_4 is on block_9. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_5, The block block_4 is currently situated above the block block_2, The block block_1 is currently situated under the block block_9, The block block_7 is currently situated under the block block_3, The block block_5 is currently situated above the block block_9, The block block_8 is currently situated under the block block_1, The block block_6 is on top of block block_3, and The block block_10 is currently situated under the block block_7.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. No blocks are placed on top of block_1. B. The block block_7 is on top of block block_7. C. Block block_3 is placed on the table. D. The block block_9 is currently situated under the block block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["No blocks are placed on top of block_1", "The block block_7 is on top of block block_7", "Block block_3 is placed on the table", "The block block_9 is currently situated under the block block_3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -4986561784387295667, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_3, block_2 is on block_1, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_4 is on top of block block_2, The block block_5 is on top of block block_1, and The block block_5 is currently situated under the block block_2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated under the block block_1. B. The block block_2 is currently situated above the block block_2. C. The block block_1 is currently being held by the robotic arm. D. The block block_4 is currently situated above the block block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_1", "The block block_2 is currently situated above the block block_2", "The block block_1 is currently being held by the robotic arm", "The block block_4 is currently situated above the block block_5"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 633469100244057525, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_3. The following block(s) are stacked on top of another block: block_1 is on block_2 and block_2 is on block_3. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated under the block block_2. B. The block block_2 is currently situated under the block block_3. C. The block block_3 is currently being held by the robotic arm. D. The block block_3 is currently situated under the block block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_2", "The block block_2 is currently situated under the block block_3", "The block block_3 is currently being held by the robotic arm", "The block block_3 is currently situated under the block block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -6965764745176315240, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_2, block_5, and block_1. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_1, The block block_5 is currently situated under the block block_3, and The block block_5 is currently situated above the block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_5 is currently situated under the block block_1. B. The block block_2 is on top of block block_5. C. The robotic arm is not holding anything. D. The block block_3 is currently situated under the block block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_5 is currently situated under the block block_1", "The block block_2 is on top of block block_5", "The robotic arm is not holding anything", "The block block_3 is currently situated under the block block_5"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 2289501855374731759, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_4, block_2, and block_3. The following block(s) is stacked on top of another block: block_1 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is on top of block block_1, and The block block_5 is currently situated under the block block_2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated above the block block_5. B. The block block_4 is currently situated above the block block_4. C. The block block_2 is currently situated above the block block_1. D. The block block_4 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated above the block block_5", "The block block_4 is currently situated above the block block_4", "The block block_2 is currently situated above the block block_1", "The block block_4 is currently being held by the robotic arm"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -6319126809807475294, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_3. The following block(s) are stacked on top of another block: block_4 is on block_2, block_5 is on block_3, and block_1 is on block_4. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated above the block block_4, and The block block_4 is currently situated above the block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_5 is on top of block block_2. B. The block block_3 is currently situated above the block block_2. C. The block block_5 is currently situated under the block block_3. D. The robotic arm is holding block_4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_5 is on top of block block_2", "The block block_3 is currently situated above the block block_2", "The block block_5 is currently situated under the block block_3", "The robotic arm is holding block_4"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2469729868887267898, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_5. The following block(s) are stacked on top of another block: block_4 is on block_5, block_1 is on block_4, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_2 is on top of block block_4, and The block block_4 is on top of block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is currently situated under the block block_5. B. Block block_2 is clear. C. The block block_2 is on top of block block_1. D. The block block_5 is currently situated under the block block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_5", "Block block_2 is clear", "The block block_2 is on top of block block_1", "The block block_5 is currently situated under the block block_3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -6285380303636201548, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_3 is on block_1 and block_2 is on block_3. The goal is to reach a state where the following facts hold: The block block_3 is on top of block block_1 and The block block_1 is on top of block block_2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Block block_1 is clear. B. The block block_2 is on top of block block_2. C. The block block_1 is currently situated under the block block_1. D. The block block_3 is on top of block block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_1 is clear", "The block block_2 is on top of block block_2", "The block block_1 is currently situated under the block block_1", "The block block_3 is on top of block block_3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 5790727990109838271, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_1, block_5 is on block_3, and block_3 is on block_4. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is on top of block block_4, and The block block_5 is currently situated under the block block_4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is on top of block block_2. B. The block block_3 is on top of block block_3. C. The block block_2 is currently situated under the block block_5. D. The robotic arm is holding block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is on top of block block_2", "The block block_3 is on top of block block_3", "The block block_2 is currently situated under the block block_5", "The robotic arm is holding block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8705558055030751129, "group": "landmarks_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_4 is on block_1, block_5 is on block_4, block_3 is on block_5, and block_2 is on block_3. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_3, The block block_2 is currently situated above the block block_4, and The block block_4 is on top of block block_5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The block block_1 is on top of block block_4. B. The block block_5 is on top of block block_2. C. The robotic arm is holding block_1. D. The block block_3 is on top of block block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is on top of block block_4", "The block block_5 is on top of block block_2", "The robotic arm is holding block_1", "The block block_3 is on top of block block_1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
