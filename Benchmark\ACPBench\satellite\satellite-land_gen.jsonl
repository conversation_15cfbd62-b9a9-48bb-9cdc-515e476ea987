{"id": -2409802757353088814, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, star0, groundstation4, groundstation3, planet5, star2, phenomenon6. There are 3 image mode(s): spectrograph0, infrared1, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0, instrument1, instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation3. Satellite satellite2 is pointing to groundstation3. Satellite satellite0 is pointing to groundstation4. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A infrared1 mode image of target groundstation3 is available. The goal is to reach a state where the following facts hold: Satellite satellite1 is pointing to phenomenon6, A spectrograph0 mode image of target phenomenon6 is available, and A thermograph2 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(have_image phenomenon6 thermograph2)", "(pointing satellite2 groundstation4)", "(pointing satellite0 star2)", "(pointing satellite1 planet5)", "(have_image star0 spectrograph0)", "(pointing satellite1 groundstation4)", "(have_image groundstation3 thermograph2)", "(pointing satellite0 planet5)", "(pointing satellite0 star0)", "(calibrated instrument1)", "(pointing satellite1 groundstation1)", "(pointing satellite2 planet5)", "(have_image phenomenon6 infrared1)", "(pointing satellite0 groundstation3)", "(calibrated instrument2)", "(have_image star0 thermograph2)", "(pointing satellite2 phenomenon6)", "(pointing satellite1 star0)", "(have_image groundstation3 spectrograph0)", "(power_on instrument0)", "(pointing satellite0 phenomenon6)", "(have_image planet5 spectrograph0)", "(power_on instrument2)", "(have_image groundstation4 thermograph2)", "(have_image groundstation4 infrared1)", "(pointing satellite2 groundstation1)", "(have_image star2 infrared1)", "(power_on instrument1)", "(have_image groundstation4 spectrograph0)", "(have_image groundstation1 infrared1)", "(pointing satellite1 star2)", "(have_image groundstation1 spectrograph0)", "(have_image star2 thermograph2)", "(calibrated instrument0)", "(have_image planet5 infrared1)", "(power_avail satellite2)", "(power_on instrument3)", "(have_image groundstation1 thermograph2)", "(have_image star2 spectrograph0)", "(calibrated instrument3)", "(pointing satellite0 groundstation1)", "(have_image star0 infrared1)", "(pointing satellite2 star0)", "(pointing satellite2 star2)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (have_image groundstation3 infrared1) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation4) (pointing satellite1 groundstation3) (pointing satellite2 groundstation3) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": -2622969714904081828, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, planet6, groundstation0, groundstation3, star4, planet5, star2. There are 3 image mode(s): infrared2, thermograph0, image1. There are 16 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite1 has following instruments onboard: instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument12 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode image1 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument14 supports image of mode image1 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet5. Satellite satellite7 is pointing to star2. Satellite satellite8 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation3. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite5 is pointing to groundstation1. Power is available on the following satellite(s): satellite1, satellite5, satellite4, satellite7, satellite0, satellite8, satellite3, satellite9, satellite2. Following instruments are powered on: instrument10. Following instruments are calibrated: instrument10. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet6 is available. The goal is to reach a state where the following facts hold: Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, A infrared2 mode image of target planet6 is available, and Satellite satellite6 is pointing to star4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(have_image groundstation0 image1)", "(have_image groundstation1 infrared2)", "(have_image planet6 image1)", "(pointing satellite4 star4)", "(calibrated instrument8)", "(power_on instrument12)", "(pointing satellite6 planet5)", "(power_on instrument8)", "(power_on instrument14)", "(power_on instrument9)", "(calibrated instrument2)", "(pointing satellite7 groundstation1)", "(pointing satellite9 star2)", "(power_avail satellite6)", "(pointing satellite3 planet6)", "(have_image star4 image1)", "(pointing satellite2 groundstation3)", "(pointing satellite7 groundstation0)", "(power_on instrument13)", "(have_image groundstation0 infrared2)", "(power_on instrument7)", "(pointing satellite1 groundstation3)", "(have_image planet5 image1)", "(pointing satellite1 star2)", "(pointing satellite3 groundstation0)", "(pointing satellite4 planet6)", "(pointing satellite7 star4)", "(pointing satellite8 star4)", "(pointing satellite9 groundstation0)", "(pointing satellite7 groundstation3)", "(pointing satellite1 planet5)", "(pointing satellite8 groundstation3)", "(pointing satellite6 planet6)", "(calibrated instrument4)", "(have_image star4 infrared2)", "(pointing satellite2 planet5)", "(calibrated instrument9)", "(have_image planet5 infrared2)", "(pointing satellite5 groundstation0)", "(pointing satellite6 groundstation1)", "(pointing satellite3 groundstation3)", "(pointing satellite0 star4)", "(pointing satellite6 star2)", "(pointing satellite5 planet6)", "(pointing satellite3 planet5)", "(pointing satellite5 planet5)", "(pointing satellite9 planet5)", "(calibrated instrument0)", "(power_on instrument3)", "(pointing satellite6 groundstation0)", "(pointing satellite7 planet5)", "(pointing satellite4 groundstation3)", "(pointing satellite2 star4)", "(pointing satellite8 planet5)", "(pointing satellite5 groundstation3)", "(have_image star2 image1)", "(have_image groundstation3 infrared2)", "(power_on instrument6)", "(pointing satellite8 groundstation1)", "(pointing satellite0 planet5)", "(calibrated instrument1)", "(have_image star2 thermograph0)", "(pointing satellite1 planet6)", "(calibrated instrument12)", "(power_on instrument15)", "(have_image groundstation1 thermograph0)", "(pointing satellite4 groundstation0)", "(pointing satellite1 star4)", "(pointing satellite9 planet6)", "(have_image groundstation1 image1)", "(pointing satellite8 star2)", "(pointing satellite2 groundstation0)", "(calibrated instrument13)", "(calibrated instrument15)", "(have_image groundstation3 thermograph0)", "(pointing satellite5 star4)", "(pointing satellite0 planet6)", "(have_image star4 thermograph0)", "(pointing satellite3 star2)", "(calibrated instrument11)", "(pointing satellite4 star2)", "(pointing satellite2 star2)", "(pointing satellite0 groundstation0)", "(pointing satellite9 groundstation1)", "(pointing satellite0 star2)", "(have_image groundstation0 thermograph0)", "(power_on instrument5)", "(pointing satellite1 groundstation1)", "(power_on instrument11)", "(pointing satellite4 groundstation1)", "(have_image star2 infrared2)", "(pointing satellite0 groundstation3)", "(calibrated instrument5)", "(power_on instrument0)", "(calibrated instrument7)", "(power_on instrument2)", "(pointing satellite2 groundstation1)", "(power_on instrument4)", "(power_on instrument1)", "(pointing satellite7 planet6)", "(calibrated instrument14)", "(pointing satellite9 groundstation3)", "(calibrated instrument6)", "(pointing satellite5 star2)", "(calibrated instrument3)", "(have_image planet6 thermograph0)", "(have_image groundstation3 image1)", "(pointing satellite3 star4)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-10-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image1 infrared2 thermograph0 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 satellite7 satellite8 satellite9 - satellite)\n    (:init (calibrated instrument10) (calibration_target instrument0 star4) (calibration_target instrument1 groundstation3) (calibration_target instrument10 groundstation1) (calibration_target instrument11 groundstation3) (calibration_target instrument12 groundstation3) (calibration_target instrument13 star4) (calibration_target instrument14 star2) (calibration_target instrument15 groundstation3) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation0) (calibration_target instrument4 groundstation1) (calibration_target instrument5 groundstation1) (calibration_target instrument6 groundstation0) (calibration_target instrument7 star2) (calibration_target instrument8 star2) (calibration_target instrument9 star4) (have_image planet5 thermograph0) (have_image planet6 infrared2) (on_board instrument0 satellite0) (on_board instrument1 satellite1) (on_board instrument10 satellite6) (on_board instrument11 satellite6) (on_board instrument12 satellite7) (on_board instrument13 satellite8) (on_board instrument14 satellite8) (on_board instrument15 satellite9) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite3) (on_board instrument5 satellite3) (on_board instrument6 satellite4) (on_board instrument7 satellite4) (on_board instrument8 satellite5) (on_board instrument9 satellite6) (pointing satellite0 groundstation1) (pointing satellite1 groundstation0) (pointing satellite2 planet6) (pointing satellite3 groundstation1) (pointing satellite4 planet5) (pointing satellite5 groundstation1) (pointing satellite6 groundstation3) (pointing satellite7 star2) (pointing satellite8 groundstation0) (pointing satellite9 star4) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite7) (power_avail satellite8) (power_avail satellite9) (power_on instrument10) (supports instrument0 image1) (supports instrument0 thermograph0) (supports instrument1 thermograph0) (supports instrument10 infrared2) (supports instrument10 thermograph0) (supports instrument11 infrared2) (supports instrument12 image1) (supports instrument12 infrared2) (supports instrument12 thermograph0) (supports instrument13 thermograph0) (supports instrument14 image1) (supports instrument15 image1) (supports instrument2 image1) (supports instrument2 infrared2) (supports instrument2 thermograph0) (supports instrument3 image1) (supports instrument4 image1) (supports instrument4 infrared2) (supports instrument4 thermograph0) (supports instrument5 infrared2) (supports instrument5 thermograph0) (supports instrument6 image1) (supports instrument6 infrared2) (supports instrument6 thermograph0) (supports instrument7 image1) (supports instrument7 infrared2) (supports instrument7 thermograph0) (supports instrument8 image1) (supports instrument8 thermograph0) (supports instrument9 thermograph0))\n    (:goal (and (pointing satellite6 star4) (pointing satellite8 planet6) (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -919392881045413254, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, star0, groundstation4, groundstation3, planet5, star2, phenomenon6. There are 3 image mode(s): spectrograph0, infrared1, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0, instrument1, instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite1 is pointing to phenomenon6. Satellite satellite2 is pointing to star2. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. The goal is to reach a state where the following facts hold: Satellite satellite1 is pointing to phenomenon6, A spectrograph0 mode image of target phenomenon6 is available, and A thermograph2 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(pointing satellite2 groundstation4)", "(pointing satellite0 star2)", "(have_image phenomenon6 thermograph2)", "(pointing satellite1 planet5)", "(have_image star0 spectrograph0)", "(pointing satellite1 groundstation4)", "(have_image groundstation3 thermograph2)", "(pointing satellite0 planet5)", "(calibrated instrument4)", "(pointing satellite0 star0)", "(calibrated instrument1)", "(pointing satellite1 groundstation1)", "(have_image phenomenon6 infrared1)", "(pointing satellite2 planet5)", "(calibrated instrument2)", "(have_image star0 thermograph2)", "(pointing satellite2 phenomenon6)", "(pointing satellite1 star0)", "(pointing satellite2 groundstation3)", "(pointing satellite0 groundstation4)", "(have_image groundstation3 spectrograph0)", "(power_on instrument0)", "(pointing satellite0 phenomenon6)", "(have_image planet5 spectrograph0)", "(power_on instrument2)", "(have_image groundstation4 thermograph2)", "(have_image groundstation4 infrared1)", "(pointing satellite2 groundstation1)", "(have_image star2 infrared1)", "(power_on instrument1)", "(have_image groundstation4 spectrograph0)", "(pointing satellite1 groundstation3)", "(have_image groundstation1 infrared1)", "(have_image groundstation3 infrared1)", "(pointing satellite1 star2)", "(have_image groundstation1 spectrograph0)", "(have_image star2 thermograph2)", "(calibrated instrument0)", "(have_image planet5 infrared1)", "(power_avail satellite2)", "(power_on instrument3)", "(have_image groundstation1 thermograph2)", "(have_image star2 spectrograph0)", "(calibrated instrument3)", "(pointing satellite0 groundstation1)", "(have_image star0 infrared1)", "(pointing satellite2 star0)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation3) (pointing satellite1 phenomenon6) (pointing satellite2 star2) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 3699908803455813421, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, planet6, groundstation0, groundstation3, star4, planet5, star2. There are 3 image mode(s): infrared2, thermograph0, image1. There are 16 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite1 has following instruments onboard: instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument12 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode image1 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument14 supports image of mode image1 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation0. Satellite satellite6 is pointing to star4. Satellite satellite9 is pointing to star4. Satellite satellite2 is pointing to planet6. Satellite satellite3 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite8 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite0 is pointing to groundstation1. Satellite satellite5 is pointing to groundstation3. Power is available on the following satellite(s): satellite1, satellite5, satellite4, satellite7, satellite6, satellite0, satellite9, satellite2. Following instruments are powered on: instrument5, instrument14. Following instruments are calibrated: instrument5. The goal is to reach a state where the following facts hold: Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, A infrared2 mode image of target planet6 is available, and Satellite satellite6 is pointing to star4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(have_image groundstation0 image1)", "(have_image groundstation1 infrared2)", "(have_image planet6 image1)", "(pointing satellite4 star4)", "(calibrated instrument8)", "(power_on instrument12)", "(pointing satellite6 planet5)", "(power_on instrument8)", "(power_on instrument9)", "(calibrated instrument2)", "(pointing satellite7 groundstation1)", "(pointing satellite9 star2)", "(have_image star4 image1)", "(pointing satellite2 groundstation3)", "(pointing satellite7 groundstation0)", "(power_on instrument13)", "(have_image groundstation0 infrared2)", "(power_on instrument7)", "(pointing satellite8 groundstation0)", "(pointing satellite1 groundstation3)", "(have_image planet5 image1)", "(pointing satellite1 star2)", "(pointing satellite3 groundstation0)", "(pointing satellite7 star4)", "(pointing satellite8 star4)", "(pointing satellite5 groundstation1)", "(pointing satellite9 groundstation0)", "(pointing satellite7 groundstation3)", "(pointing satellite1 planet5)", "(pointing satellite8 groundstation3)", "(pointing satellite6 planet6)", "(calibrated instrument4)", "(have_image star4 infrared2)", "(pointing satellite2 planet5)", "(calibrated instrument9)", "(have_image planet5 infrared2)", "(pointing satellite5 groundstation0)", "(pointing satellite6 groundstation1)", "(pointing satellite3 groundstation3)", "(pointing satellite4 planet5)", "(pointing satellite0 star4)", "(pointing satellite6 star2)", "(pointing satellite5 planet6)", "(pointing satellite3 planet5)", "(pointing satellite5 planet5)", "(power_avail satellite3)", "(pointing satellite9 planet5)", "(calibrated instrument0)", "(power_on instrument3)", "(pointing satellite3 groundstation1)", "(pointing satellite6 groundstation0)", "(pointing satellite7 planet5)", "(pointing satellite4 groundstation3)", "(pointing satellite2 star4)", "(pointing satellite8 planet5)", "(have_image star2 image1)", "(have_image groundstation3 infrared2)", "(power_on instrument6)", "(pointing satellite8 groundstation1)", "(pointing satellite0 planet5)", "(calibrated instrument1)", "(have_image star2 thermograph0)", "(pointing satellite1 planet6)", "(calibrated instrument12)", "(power_on instrument15)", "(have_image groundstation1 thermograph0)", "(pointing satellite4 groundstation0)", "(pointing satellite1 star4)", "(pointing satellite9 planet6)", "(have_image groundstation1 image1)", "(pointing satellite8 star2)", "(pointing satellite2 groundstation0)", "(calibrated instrument13)", "(calibrated instrument15)", "(have_image groundstation3 thermograph0)", "(pointing satellite5 star4)", "(pointing satellite0 planet6)", "(have_image star4 thermograph0)", "(pointing satellite3 star2)", "(pointing satellite6 groundstation3)", "(calibrated instrument11)", "(pointing satellite4 star2)", "(pointing satellite2 star2)", "(pointing satellite0 groundstation0)", "(pointing satellite9 groundstation1)", "(pointing satellite0 star2)", "(have_image groundstation0 thermograph0)", "(pointing satellite1 groundstation1)", "(power_on instrument11)", "(pointing satellite4 groundstation1)", "(have_image star2 infrared2)", "(pointing satellite0 groundstation3)", "(power_on instrument0)", "(power_avail satellite8)", "(calibrated instrument7)", "(power_on instrument2)", "(pointing satellite2 groundstation1)", "(power_on instrument4)", "(power_on instrument1)", "(pointing satellite7 planet6)", "(calibrated instrument14)", "(pointing satellite9 groundstation3)", "(calibrated instrument6)", "(pointing satellite5 star2)", "(calibrated instrument3)", "(power_on instrument10)", "(calibrated instrument10)", "(have_image planet6 thermograph0)", "(have_image groundstation3 image1)", "(pointing satellite3 star4)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-10-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image1 infrared2 thermograph0 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 satellite7 satellite8 satellite9 - satellite)\n    (:init (calibrated instrument5) (calibration_target instrument0 star4) (calibration_target instrument1 groundstation3) (calibration_target instrument10 groundstation1) (calibration_target instrument11 groundstation3) (calibration_target instrument12 groundstation3) (calibration_target instrument13 star4) (calibration_target instrument14 star2) (calibration_target instrument15 groundstation3) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation0) (calibration_target instrument4 groundstation1) (calibration_target instrument5 groundstation1) (calibration_target instrument6 groundstation0) (calibration_target instrument7 star2) (calibration_target instrument8 star2) (calibration_target instrument9 star4) (on_board instrument0 satellite0) (on_board instrument1 satellite1) (on_board instrument10 satellite6) (on_board instrument11 satellite6) (on_board instrument12 satellite7) (on_board instrument13 satellite8) (on_board instrument14 satellite8) (on_board instrument15 satellite9) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite3) (on_board instrument5 satellite3) (on_board instrument6 satellite4) (on_board instrument7 satellite4) (on_board instrument8 satellite5) (on_board instrument9 satellite6) (pointing satellite0 groundstation1) (pointing satellite1 groundstation0) (pointing satellite2 planet6) (pointing satellite3 planet6) (pointing satellite4 planet6) (pointing satellite5 groundstation3) (pointing satellite6 star4) (pointing satellite7 star2) (pointing satellite8 planet6) (pointing satellite9 star4) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (power_avail satellite7) (power_avail satellite9) (power_on instrument14) (power_on instrument5) (supports instrument0 image1) (supports instrument0 thermograph0) (supports instrument1 thermograph0) (supports instrument10 infrared2) (supports instrument10 thermograph0) (supports instrument11 infrared2) (supports instrument12 image1) (supports instrument12 infrared2) (supports instrument12 thermograph0) (supports instrument13 thermograph0) (supports instrument14 image1) (supports instrument15 image1) (supports instrument2 image1) (supports instrument2 infrared2) (supports instrument2 thermograph0) (supports instrument3 image1) (supports instrument4 image1) (supports instrument4 infrared2) (supports instrument4 thermograph0) (supports instrument5 infrared2) (supports instrument5 thermograph0) (supports instrument6 image1) (supports instrument6 infrared2) (supports instrument6 thermograph0) (supports instrument7 image1) (supports instrument7 infrared2) (supports instrument7 thermograph0) (supports instrument8 image1) (supports instrument8 thermograph0) (supports instrument9 thermograph0))\n    (:goal (and (pointing satellite6 star4) (pointing satellite8 planet6) (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -4361428816887892889, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation5, groundstation6, planet8, groundstation1, planet7, star0, star10, groundstation3, groundstation4, star9, star2. There are 3 image mode(s): spectrograph0, infrared1, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite0 is pointing to star2. Satellite satellite1 is pointing to planet7. Satellite satellite2 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite2, satellite0, satellite5, satellite3, satellite4. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. The goal is to reach a state where the following facts hold: A infrared1 mode image of target star9 is available, A infrared1 mode image of target planet8 is available, A infrared1 mode image of target star10 is available, A thermograph2 mode image of target planet7 is available, Satellite satellite5 is pointing to star2, Satellite satellite4 is pointing to star10, and Satellite satellite1 is pointing to star9. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(calibrated instrument8)", "(pointing satellite3 groundstation5)", "(have_image groundstation5 infrared1)", "(pointing satellite3 planet7)", "(pointing satellite1 groundstation4)", "(have_image star0 spectrograph0)", "(power_on instrument6)", "(have_image groundstation3 thermograph2)", "(pointing satellite0 star9)", "(pointing satellite3 star0)", "(pointing satellite4 star0)", "(calibrated instrument1)", "(have_image star10 spectrograph0)", "(pointing satellite0 planet7)", "(power_on instrument8)", "(power_on instrument9)", "(pointing satellite4 planet8)", "(pointing satellite4 planet7)", "(have_image planet7 spectrograph0)", "(pointing satellite2 groundstation3)", "(pointing satellite1 star0)", "(have_image groundstation3 spectrograph0)", "(pointing satellite5 planet7)", "(power_on instrument7)", "(have_image groundstation4 thermograph2)", "(have_image groundstation4 infrared1)", "(pointing satellite5 star0)", "(have_image groundstation4 spectrograph0)", "(pointing satellite1 groundstation3)", "(have_image planet8 spectrograph0)", "(have_image groundstation3 infrared1)", "(have_image groundstation5 spectrograph0)", "(pointing satellite1 star2)", "(pointing satellite3 star2)", "(have_image star9 thermograph2)", "(have_image star2 thermograph2)", "(have_image groundstation6 thermograph2)", "(have_image groundstation1 thermograph2)", "(have_image star2 spectrograph0)", "(pointing satellite0 groundstation1)", "(pointing satellite4 star2)", "(pointing satellite4 groundstation5)", "(pointing satellite2 groundstation6)", "(have_image planet7 infrared1)", "(pointing satellite5 groundstation1)", "(have_image star0 infrared1)", "(pointing satellite3 star10)", "(pointing satellite1 groundstation6)", "(pointing satellite2 star0)", "(pointing satellite5 star9)", "(pointing satellite2 star2)", "(pointing satellite2 groundstation4)", "(pointing satellite1 planet8)", "(pointing satellite0 groundstation6)", "(pointing satellite0 star10)", "(power_avail satellite1)", "(pointing satellite1 star10)", "(calibrated instrument4)", "(pointing satellite0 star0)", "(pointing satellite5 planet8)", "(power_on instrument5)", "(pointing satellite1 groundstation1)", "(pointing satellite4 groundstation1)", "(pointing satellite0 groundstation3)", "(pointing satellite2 planet7)", "(calibrated instrument9)", "(pointing satellite5 star10)", "(have_image star9 spectrograph0)", "(pointing satellite3 groundstation3)", "(have_image star0 thermograph2)", "(pointing satellite5 groundstation5)", "(pointing satellite0 groundstation4)", "(pointing satellite2 planet8)", "(pointing satellite3 planet8)", "(calibrated instrument5)", "(power_on instrument0)", "(calibrated instrument7)", "(pointing satellite0 groundstation5)", "(pointing satellite2 groundstation1)", "(have_image planet8 thermograph2)", "(power_on instrument1)", "(have_image star2 infrared1)", "(pointing satellite1 groundstation5)", "(power_on instrument4)", "(have_image groundstation1 infrared1)", "(have_image groundstation6 spectrograph0)", "(calibrated instrument6)", "(have_image groundstation1 spectrograph0)", "(pointing satellite4 star9)", "(pointing satellite5 groundstation4)", "(calibrated instrument0)", "(have_image groundstation5 thermograph2)", "(power_on instrument3)", "(pointing satellite4 groundstation4)", "(pointing satellite2 star9)", "(pointing satellite3 groundstation1)", "(calibrated instrument3)", "(pointing satellite5 groundstation6)", "(have_image groundstation6 infrared1)", "(pointing satellite0 planet8)", "(have_image star10 thermograph2)", "(pointing satellite4 groundstation3)", "(pointing satellite3 groundstation6)", "(calibrated instrument10)", "(power_on instrument10)", "(pointing satellite3 groundstation4)", "(pointing satellite2 groundstation5)", "(pointing satellite4 groundstation6)", "(pointing satellite5 groundstation3)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-6-3-3-7-4)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 groundstation5 groundstation6 planet7 planet8 star0 star10 star2 star9 - direction instrument0 instrument1 instrument10 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 - satellite)\n    (:init (calibrated instrument2) (calibration_target instrument0 star0) (calibration_target instrument1 groundstation5) (calibration_target instrument10 groundstation4) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation1) (calibration_target instrument3 groundstation3) (calibration_target instrument4 groundstation5) (calibration_target instrument5 star2) (calibration_target instrument6 groundstation4) (calibration_target instrument6 star0) (calibration_target instrument7 groundstation3) (calibration_target instrument8 groundstation3) (calibration_target instrument8 star2) (calibration_target instrument9 groundstation1) (calibration_target instrument9 groundstation6) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite5) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite2) (on_board instrument5 satellite2) (on_board instrument6 satellite3) (on_board instrument7 satellite4) (on_board instrument8 satellite4) (on_board instrument9 satellite5) (pointing satellite0 star2) (pointing satellite1 planet7) (pointing satellite2 star10) (pointing satellite3 star9) (pointing satellite4 star10) (pointing satellite5 star2) (power_avail satellite0) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_on instrument2) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 infrared1) (supports instrument1 spectrograph0) (supports instrument1 thermograph2) (supports instrument10 thermograph2) (supports instrument2 infrared1) (supports instrument2 thermograph2) (supports instrument3 infrared1) (supports instrument3 spectrograph0) (supports instrument3 thermograph2) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2) (supports instrument5 infrared1) (supports instrument6 infrared1) (supports instrument6 spectrograph0) (supports instrument6 thermograph2) (supports instrument7 spectrograph0) (supports instrument8 spectrograph0) (supports instrument9 infrared1) (supports instrument9 spectrograph0) (supports instrument9 thermograph2))\n    (:goal (and (pointing satellite1 star9) (pointing satellite4 star10) (pointing satellite5 star2) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star9 infrared1) (have_image star10 infrared1)))\n)"}
{"id": -6638859757713280359, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, planet6, groundstation0, star4, groundstation3, planet5, star2. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode image1 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to star2. Following instruments are powered on: instrument0. The goal is to reach a state where the following facts hold: A thermograph0 mode image of target planet5 is available and A infrared2 mode image of target planet6 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(have_image groundstation1 infrared2)", "(have_image groundstation0 image1)", "(have_image planet6 image1)", "(have_image star2 image1)", "(have_image groundstation0 thermograph0)", "(have_image groundstation3 infrared2)", "(have_image star4 infrared2)", "(have_image star2 infrared2)", "(pointing satellite0 groundstation3)", "(have_image planet5 infrared2)", "(have_image star2 thermograph0)", "(have_image star4 image1)", "(power_avail satellite0)", "(have_image groundstation1 thermograph0)", "(have_image groundstation1 image1)", "(have_image groundstation0 infrared2)", "(have_image groundstation3 thermograph0)", "(have_image planet5 image1)", "(have_image star4 thermograph0)", "(pointing satellite0 groundstation1)", "(have_image planet6 thermograph0)", "(have_image groundstation3 image1)", "(pointing satellite0 groundstation0)"], "yes": ["(pointing satellite0 planet6)", "(calibrated instrument0)", "(pointing satellite0 planet5)", "(pointing satellite0 star4)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibration_target instrument0 star4) (on_board instrument0 satellite0) (pointing satellite0 star2) (power_on instrument0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -951513705827996427, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): phenomenon5, star6, star1, groundstation0, groundstation2, groundstation4, star3. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument16, instrument15, instrument17. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite5 has following instruments onboard: instrument14, instrument12, instrument13. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite3 is pointing to star6. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation0. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to groundstation2. Satellite satellite2 is pointing to star6. Power is available on the following satellite(s): satellite1, satellite6, satellite0, satellite4. Following instruments are powered on: instrument8, instrument14, instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. A image1 mode image of target star6 is available. The goal is to reach a state where the following facts hold: A image0 mode image of target phenomenon5 is available, A image1 mode image of target star6 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite5 is pointing to groundstation0, and Satellite satellite4 is pointing to star1. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(pointing satellite4 star6)", "(have_image groundstation0 image1)", "(calibrated instrument8)", "(calibrated instrument17)", "(pointing satellite6 star1)", "(pointing satellite1 groundstation0)", "(have_image star6 image0)", "(pointing satellite1 groundstation4)", "(pointing satellite5 phenomenon5)", "(pointing satellite6 groundstation2)", "(power_avail satellite5)", "(pointing satellite0 phenomenon5)", "(pointing satellite4 star3)", "(calibrated instrument1)", "(have_image groundstation4 image1)", "(have_image star1 image0)", "(power_on instrument12)", "(have_image star3 image0)", "(power_on instrument9)", "(calibrated instrument2)", "(calibrated instrument12)", "(pointing satellite1 star3)", "(power_on instrument15)", "(pointing satellite4 groundstation0)", "(power_on instrument13)", "(power_on instrument7)", "(pointing satellite2 star1)", "(pointing satellite2 groundstation0)", "(calibrated instrument13)", "(calibrated instrument15)", "(pointing satellite6 groundstation4)", "(have_image groundstation4 image0)", "(pointing satellite6 star3)", "(pointing satellite2 phenomenon5)", "(have_image star1 image2)", "(have_image star3 image1)", "(pointing satellite1 star1)", "(have_image groundstation0 image2)", "(pointing satellite5 groundstation2)", "(pointing satellite3 groundstation0)", "(have_image groundstation4 image2)", "(calibrated instrument11)", "(pointing satellite3 groundstation2)", "(pointing satellite0 star3)", "(pointing satellite3 star1)", "(calibrated instrument16)", "(pointing satellite0 groundstation0)", "(pointing satellite2 groundstation4)", "(pointing satellite3 star3)", "(pointing satellite5 star6)", "(have_image phenomenon5 image2)", "(have_image star3 image2)", "(calibrated instrument4)", "(pointing satellite1 star6)", "(power_on instrument5)", "(power_on instrument11)", "(have_image groundstation2 image1)", "(pointing satellite2 groundstation2)", "(calibrated instrument9)", "(pointing satellite0 star6)", "(pointing satellite0 groundstation4)", "(calibrated instrument5)", "(have_image phenomenon5 image1)", "(power_on instrument0)", "(pointing satellite6 star6)", "(calibrated instrument7)", "(power_on instrument2)", "(pointing satellite0 star1)", "(pointing satellite2 star3)", "(power_on instrument4)", "(power_on instrument1)", "(power_on instrument17)", "(calibrated instrument14)", "(have_image groundstation2 image0)", "(have_image star6 image2)", "(pointing satellite1 groundstation2)", "(power_avail satellite3)", "(pointing satellite5 groundstation4)", "(calibrated instrument0)", "(power_avail satellite2)", "(power_on instrument3)", "(pointing satellite4 groundstation4)", "(have_image groundstation0 image0)", "(calibrated instrument3)", "(have_image groundstation2 image2)", "(pointing satellite5 star3)", "(power_on instrument10)", "(calibrated instrument10)", "(pointing satellite6 phenomenon5)", "(have_image star1 image1)", "(pointing satellite3 groundstation4)", "(power_on instrument16)", "(pointing satellite4 phenomenon5)", "(pointing satellite3 phenomenon5)", "(pointing satellite5 star1)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-7-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation2 groundstation4 phenomenon5 star1 star3 star6 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument16 instrument17 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image0 image1 image2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 - satellite)\n    (:init (calibrated instrument6) (calibration_target instrument0 groundstation0) (calibration_target instrument1 groundstation4) (calibration_target instrument10 groundstation4) (calibration_target instrument11 star3) (calibration_target instrument12 groundstation0) (calibration_target instrument13 groundstation0) (calibration_target instrument14 groundstation4) (calibration_target instrument15 groundstation2) (calibration_target instrument16 groundstation2) (calibration_target instrument17 groundstation0) (calibration_target instrument2 star1) (calibration_target instrument3 star3) (calibration_target instrument4 groundstation0) (calibration_target instrument5 star3) (calibration_target instrument6 groundstation2) (calibration_target instrument7 star3) (calibration_target instrument8 groundstation0) (calibration_target instrument9 star3) (have_image phenomenon5 image0) (have_image star6 image1) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite4) (on_board instrument11 satellite4) (on_board instrument12 satellite5) (on_board instrument13 satellite5) (on_board instrument14 satellite5) (on_board instrument15 satellite6) (on_board instrument16 satellite6) (on_board instrument17 satellite6) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite1) (on_board instrument5 satellite1) (on_board instrument6 satellite2) (on_board instrument7 satellite3) (on_board instrument8 satellite3) (on_board instrument9 satellite4) (pointing satellite0 groundstation2) (pointing satellite1 phenomenon5) (pointing satellite2 star6) (pointing satellite3 star6) (pointing satellite4 groundstation2) (pointing satellite5 groundstation0) (pointing satellite6 groundstation0) (power_avail satellite0) (power_avail satellite1) (power_avail satellite4) (power_avail satellite6) (power_on instrument14) (power_on instrument6) (power_on instrument8) (supports instrument0 image2) (supports instrument1 image0) (supports instrument10 image0) (supports instrument10 image2) (supports instrument11 image1) (supports instrument11 image2) (supports instrument12 image0) (supports instrument13 image0) (supports instrument13 image2) (supports instrument14 image0) (supports instrument14 image1) (supports instrument14 image2) (supports instrument15 image0) (supports instrument15 image1) (supports instrument15 image2) (supports instrument16 image0) (supports instrument16 image1) (supports instrument17 image1) (supports instrument2 image0) (supports instrument2 image1) (supports instrument2 image2) (supports instrument3 image0) (supports instrument3 image1) (supports instrument3 image2) (supports instrument4 image2) (supports instrument5 image0) (supports instrument5 image1) (supports instrument5 image2) (supports instrument6 image0) (supports instrument6 image1) (supports instrument6 image2) (supports instrument7 image0) (supports instrument7 image1) (supports instrument8 image0) (supports instrument8 image1) (supports instrument8 image2) (supports instrument9 image1))\n    (:goal (and (pointing satellite1 phenomenon5) (pointing satellite4 star1) (pointing satellite5 groundstation0) (have_image phenomenon5 image0) (have_image star6 image1)))\n)"}
{"id": 3240593405062459386, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, star0, groundstation4, groundstation3, planet5, star2, phenomenon6. There are 3 image mode(s): spectrograph0, infrared1, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0, instrument1, instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite1 is pointing to groundstation3. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. The goal is to reach a state where the following facts hold: Satellite satellite1 is pointing to phenomenon6, A spectrograph0 mode image of target phenomenon6 is available, and A thermograph2 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(pointing satellite2 groundstation4)", "(pointing satellite0 star2)", "(have_image phenomenon6 thermograph2)", "(pointing satellite1 planet5)", "(have_image star0 spectrograph0)", "(pointing satellite1 groundstation4)", "(have_image groundstation3 thermograph2)", "(pointing satellite0 planet5)", "(pointing satellite0 star0)", "(calibrated instrument1)", "(pointing satellite1 groundstation1)", "(have_image phenomenon6 infrared1)", "(calibrated instrument2)", "(have_image star0 thermograph2)", "(pointing satellite2 phenomenon6)", "(pointing satellite1 star0)", "(pointing satellite2 groundstation3)", "(pointing satellite0 groundstation4)", "(have_image groundstation3 spectrograph0)", "(power_on instrument0)", "(pointing satellite0 phenomenon6)", "(have_image planet5 spectrograph0)", "(power_on instrument2)", "(have_image groundstation4 thermograph2)", "(have_image groundstation4 infrared1)", "(pointing satellite2 groundstation1)", "(have_image star2 infrared1)", "(power_on instrument1)", "(have_image groundstation4 spectrograph0)", "(have_image groundstation1 infrared1)", "(have_image groundstation3 infrared1)", "(pointing satellite1 star2)", "(have_image groundstation1 spectrograph0)", "(have_image star2 thermograph2)", "(calibrated instrument0)", "(have_image planet5 infrared1)", "(power_avail satellite2)", "(power_on instrument3)", "(have_image groundstation1 thermograph2)", "(have_image star2 spectrograph0)", "(calibrated instrument3)", "(pointing satellite0 groundstation1)", "(have_image star0 infrared1)", "(pointing satellite2 star0)", "(pointing satellite2 star2)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (have_image planet5 thermograph2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation3) (pointing satellite1 groundstation3) (pointing satellite2 planet5) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": -2545569758200091479, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): phenomenon5, star6, star1, groundstation0, groundstation2, groundstation4, star3. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument16, instrument15, instrument17. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite5 has following instruments onboard: instrument14, instrument12, instrument13. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite3 is pointing to star6. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star1. Satellite satellite5 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation0. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite1, satellite6, satellite5, satellite3, satellite4. Following instruments are powered on: instrument6, instrument0. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. The goal is to reach a state where the following facts hold: A image0 mode image of target phenomenon5 is available, A image1 mode image of target star6 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite5 is pointing to groundstation0, and Satellite satellite4 is pointing to star1. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(pointing satellite4 star6)", "(have_image groundstation0 image1)", "(calibrated instrument8)", "(calibrated instrument17)", "(pointing satellite6 star1)", "(pointing satellite1 groundstation0)", "(have_image star6 image0)", "(pointing satellite1 groundstation4)", "(pointing satellite5 phenomenon5)", "(pointing satellite6 groundstation2)", "(pointing satellite0 phenomenon5)", "(pointing satellite4 star3)", "(calibrated instrument1)", "(have_image groundstation4 image1)", "(have_image star1 image0)", "(power_on instrument12)", "(power_on instrument8)", "(power_on instrument14)", "(have_image star3 image0)", "(power_on instrument9)", "(calibrated instrument2)", "(calibrated instrument12)", "(pointing satellite1 star3)", "(power_avail satellite0)", "(power_on instrument15)", "(pointing satellite4 groundstation0)", "(power_on instrument13)", "(power_on instrument7)", "(pointing satellite2 groundstation0)", "(calibrated instrument13)", "(calibrated instrument15)", "(pointing satellite6 groundstation4)", "(have_image groundstation4 image0)", "(pointing satellite6 star3)", "(pointing satellite2 phenomenon5)", "(have_image star1 image2)", "(have_image star3 image1)", "(pointing satellite1 star1)", "(have_image groundstation0 image2)", "(pointing satellite5 groundstation2)", "(pointing satellite3 groundstation0)", "(have_image groundstation4 image2)", "(calibrated instrument11)", "(pointing satellite3 groundstation2)", "(pointing satellite0 star3)", "(pointing satellite3 star1)", "(pointing satellite4 groundstation2)", "(calibrated instrument16)", "(pointing satellite0 groundstation0)", "(pointing satellite2 groundstation4)", "(pointing satellite3 star3)", "(pointing satellite5 star6)", "(have_image phenomenon5 image2)", "(have_image star3 image2)", "(calibrated instrument4)", "(pointing satellite1 star6)", "(power_on instrument5)", "(power_on instrument11)", "(pointing satellite2 star6)", "(have_image groundstation2 image1)", "(pointing satellite2 groundstation2)", "(calibrated instrument9)", "(pointing satellite0 star6)", "(pointing satellite0 groundstation4)", "(calibrated instrument5)", "(have_image phenomenon5 image1)", "(pointing satellite6 star6)", "(calibrated instrument7)", "(power_on instrument2)", "(pointing satellite0 star1)", "(pointing satellite2 star3)", "(power_on instrument4)", "(power_on instrument1)", "(power_on instrument17)", "(calibrated instrument14)", "(have_image groundstation2 image0)", "(have_image star6 image2)", "(pointing satellite1 groundstation2)", "(pointing satellite5 groundstation4)", "(calibrated instrument0)", "(power_avail satellite2)", "(power_on instrument3)", "(pointing satellite4 groundstation4)", "(have_image groundstation0 image0)", "(calibrated instrument3)", "(have_image groundstation2 image2)", "(pointing satellite5 star3)", "(power_on instrument10)", "(calibrated instrument10)", "(pointing satellite6 phenomenon5)", "(have_image star1 image1)", "(pointing satellite3 groundstation4)", "(power_on instrument16)", "(pointing satellite4 phenomenon5)", "(pointing satellite3 phenomenon5)", "(pointing satellite5 star1)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-7-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation2 groundstation4 phenomenon5 star1 star3 star6 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument16 instrument17 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image0 image1 image2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 - satellite)\n    (:init (calibrated instrument6) (calibration_target instrument0 groundstation0) (calibration_target instrument1 groundstation4) (calibration_target instrument10 groundstation4) (calibration_target instrument11 star3) (calibration_target instrument12 groundstation0) (calibration_target instrument13 groundstation0) (calibration_target instrument14 groundstation4) (calibration_target instrument15 groundstation2) (calibration_target instrument16 groundstation2) (calibration_target instrument17 groundstation0) (calibration_target instrument2 star1) (calibration_target instrument3 star3) (calibration_target instrument4 groundstation0) (calibration_target instrument5 star3) (calibration_target instrument6 groundstation2) (calibration_target instrument7 star3) (calibration_target instrument8 groundstation0) (calibration_target instrument9 star3) (have_image phenomenon5 image0) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite4) (on_board instrument11 satellite4) (on_board instrument12 satellite5) (on_board instrument13 satellite5) (on_board instrument14 satellite5) (on_board instrument15 satellite6) (on_board instrument16 satellite6) (on_board instrument17 satellite6) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite1) (on_board instrument5 satellite1) (on_board instrument6 satellite2) (on_board instrument7 satellite3) (on_board instrument8 satellite3) (on_board instrument9 satellite4) (pointing satellite0 groundstation2) (pointing satellite1 phenomenon5) (pointing satellite2 star1) (pointing satellite3 star6) (pointing satellite4 star1) (pointing satellite5 groundstation0) (pointing satellite6 groundstation0) (power_avail satellite1) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (power_on instrument0) (power_on instrument6) (supports instrument0 image2) (supports instrument1 image0) (supports instrument10 image0) (supports instrument10 image2) (supports instrument11 image1) (supports instrument11 image2) (supports instrument12 image0) (supports instrument13 image0) (supports instrument13 image2) (supports instrument14 image0) (supports instrument14 image1) (supports instrument14 image2) (supports instrument15 image0) (supports instrument15 image1) (supports instrument15 image2) (supports instrument16 image0) (supports instrument16 image1) (supports instrument17 image1) (supports instrument2 image0) (supports instrument2 image1) (supports instrument2 image2) (supports instrument3 image0) (supports instrument3 image1) (supports instrument3 image2) (supports instrument4 image2) (supports instrument5 image0) (supports instrument5 image1) (supports instrument5 image2) (supports instrument6 image0) (supports instrument6 image1) (supports instrument6 image2) (supports instrument7 image0) (supports instrument7 image1) (supports instrument8 image0) (supports instrument8 image1) (supports instrument8 image2) (supports instrument9 image1))\n    (:goal (and (pointing satellite1 phenomenon5) (pointing satellite4 star1) (pointing satellite5 groundstation0) (have_image phenomenon5 image0) (have_image star6 image1)))\n)"}
{"id": -8549638704888957449, "group": "landmarks_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation5, groundstation6, planet8, groundstation1, planet7, star0, star10, groundstation3, groundstation4, star9, star2. There are 3 image mode(s): spectrograph0, infrared1, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite0 is pointing to groundstation6. Satellite satellite4 is pointing to star2. Satellite satellite2 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite1 is pointing to star9. Power is available on the following satellite(s): satellite1, satellite4, satellite0, satellite5, satellite3. Following instruments are powered on: instrument3. Following instruments are calibrated: instrument3. A infrared1 mode image of target star9 is available. A infrared1 mode image of target planet8 is available. The goal is to reach a state where the following facts hold: A infrared1 mode image of target star9 is available, A infrared1 mode image of target planet8 is available, A infrared1 mode image of target star10 is available, A thermograph2 mode image of target planet7 is available, Satellite satellite5 is pointing to star2, Satellite satellite4 is pointing to star10, and Satellite satellite1 is pointing to star9. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(calibrated instrument8)", "(pointing satellite3 groundstation5)", "(have_image groundstation5 infrared1)", "(pointing satellite3 planet7)", "(pointing satellite1 groundstation4)", "(have_image star0 spectrograph0)", "(power_on instrument6)", "(have_image groundstation3 thermograph2)", "(pointing satellite0 star9)", "(pointing satellite3 star0)", "(pointing satellite4 star0)", "(calibrated instrument1)", "(have_image star10 spectrograph0)", "(pointing satellite0 planet7)", "(power_on instrument8)", "(power_on instrument9)", "(calibrated instrument2)", "(pointing satellite4 planet8)", "(pointing satellite4 planet7)", "(have_image planet7 spectrograph0)", "(pointing satellite2 groundstation3)", "(pointing satellite1 star0)", "(have_image groundstation3 spectrograph0)", "(pointing satellite5 planet7)", "(power_on instrument7)", "(have_image groundstation4 thermograph2)", "(have_image groundstation4 infrared1)", "(pointing satellite5 star0)", "(have_image groundstation4 spectrograph0)", "(pointing satellite1 groundstation3)", "(have_image planet8 spectrograph0)", "(have_image groundstation3 infrared1)", "(have_image groundstation5 spectrograph0)", "(pointing satellite1 star2)", "(pointing satellite3 star2)", "(have_image star9 thermograph2)", "(have_image star2 thermograph2)", "(have_image groundstation6 thermograph2)", "(have_image groundstation1 thermograph2)", "(have_image star2 spectrograph0)", "(pointing satellite0 groundstation1)", "(pointing satellite4 groundstation5)", "(pointing satellite2 groundstation6)", "(have_image planet7 infrared1)", "(pointing satellite5 groundstation1)", "(have_image star0 infrared1)", "(pointing satellite3 star10)", "(pointing satellite1 groundstation6)", "(pointing satellite2 star0)", "(pointing satellite5 star9)", "(pointing satellite2 star2)", "(pointing satellite2 groundstation4)", "(pointing satellite1 planet8)", "(pointing satellite0 star2)", "(pointing satellite0 star10)", "(pointing satellite1 star10)", "(calibrated instrument4)", "(pointing satellite0 star0)", "(pointing satellite5 planet8)", "(power_on instrument5)", "(pointing satellite1 groundstation1)", "(pointing satellite4 groundstation1)", "(pointing satellite0 groundstation3)", "(pointing satellite2 planet7)", "(calibrated instrument9)", "(pointing satellite1 planet7)", "(pointing satellite5 star10)", "(have_image star9 spectrograph0)", "(pointing satellite3 groundstation3)", "(have_image star0 thermograph2)", "(pointing satellite5 groundstation5)", "(pointing satellite0 groundstation4)", "(pointing satellite2 planet8)", "(pointing satellite3 planet8)", "(calibrated instrument5)", "(power_on instrument0)", "(calibrated instrument7)", "(power_on instrument2)", "(pointing satellite0 groundstation5)", "(pointing satellite2 groundstation1)", "(have_image planet8 thermograph2)", "(power_on instrument1)", "(have_image star2 infrared1)", "(pointing satellite1 groundstation5)", "(power_on instrument4)", "(have_image groundstation1 infrared1)", "(have_image groundstation6 spectrograph0)", "(calibrated instrument6)", "(have_image groundstation1 spectrograph0)", "(pointing satellite4 star9)", "(pointing satellite5 groundstation4)", "(calibrated instrument0)", "(power_avail satellite2)", "(have_image groundstation5 thermograph2)", "(pointing satellite4 groundstation4)", "(pointing satellite2 star9)", "(pointing satellite3 groundstation1)", "(pointing satellite5 groundstation6)", "(have_image groundstation6 infrared1)", "(pointing satellite0 planet8)", "(have_image star10 thermograph2)", "(pointing satellite4 groundstation3)", "(pointing satellite3 groundstation6)", "(calibrated instrument10)", "(power_on instrument10)", "(pointing satellite3 groundstation4)", "(pointing satellite2 groundstation5)", "(pointing satellite4 groundstation6)", "(pointing satellite5 groundstation3)"], "yes": []}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-6-3-3-7-4)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 groundstation5 groundstation6 planet7 planet8 star0 star10 star2 star9 - direction instrument0 instrument1 instrument10 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 - satellite)\n    (:init (calibrated instrument3) (calibration_target instrument0 star0) (calibration_target instrument1 groundstation5) (calibration_target instrument10 groundstation4) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation1) (calibration_target instrument3 groundstation3) (calibration_target instrument4 groundstation5) (calibration_target instrument5 star2) (calibration_target instrument6 groundstation4) (calibration_target instrument6 star0) (calibration_target instrument7 groundstation3) (calibration_target instrument8 groundstation3) (calibration_target instrument8 star2) (calibration_target instrument9 groundstation1) (calibration_target instrument9 groundstation6) (have_image planet8 infrared1) (have_image star9 infrared1) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite5) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite2) (on_board instrument5 satellite2) (on_board instrument6 satellite3) (on_board instrument7 satellite4) (on_board instrument8 satellite4) (on_board instrument9 satellite5) (pointing satellite0 groundstation6) (pointing satellite1 star9) (pointing satellite2 star10) (pointing satellite3 star9) (pointing satellite4 star2) (pointing satellite5 star2) (power_avail satellite0) (power_avail satellite1) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_on instrument3) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 infrared1) (supports instrument1 spectrograph0) (supports instrument1 thermograph2) (supports instrument10 thermograph2) (supports instrument2 infrared1) (supports instrument2 thermograph2) (supports instrument3 infrared1) (supports instrument3 spectrograph0) (supports instrument3 thermograph2) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2) (supports instrument5 infrared1) (supports instrument6 infrared1) (supports instrument6 spectrograph0) (supports instrument6 thermograph2) (supports instrument7 spectrograph0) (supports instrument8 spectrograph0) (supports instrument9 infrared1) (supports instrument9 spectrograph0) (supports instrument9 thermograph2))\n    (:goal (and (pointing satellite1 star9) (pointing satellite4 star10) (pointing satellite5 star2) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star9 infrared1) (have_image star10 infrared1)))\n)"}
