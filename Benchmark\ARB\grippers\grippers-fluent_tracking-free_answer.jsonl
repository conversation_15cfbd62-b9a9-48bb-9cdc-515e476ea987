{"question_id": "3cebb817-d2ab-4e1c-8255-de95f5d3b904", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for ball2? Write None if there are none", "answer": "ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not located at room3, ball2 is not located at room4, ball2 is not located at room5, ball2 is present at room2 and rgripper1 of robot1 is not carrying ball2", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, what are the valid properties (including both affirmative and negated properties) of ball2, or state None if there are no valid properties.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "d280237c-4b96-4adf-8c8a-81a97f3081ba", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, what are the valid properties of the state that involve negations for rgripper1? Write None if there are none", "answer": "ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's rgripper1, ball3 is not being carried by robot2's rgripper1, ball4 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's rgripper1, ball7 is not being carried by robot1's rgripper1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot2 is not free, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with rgripper1 and robot2 is not carrying ball7 with rgripper1", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 transitions from room2 to room3 to achieve the current state. In this state, what are the valid state properties involving negations for rgripper1? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is positioned in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "64d46c6b-77f5-4b83-a4dd-1954fb79c6d2", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves from room5 to room2 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for rgripper1? Write None if there are none", "answer": "robot1's rgripper1 is available", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and picks up ball3 with rgripper1, both in room1. Then, robot1 moves from room1 to room5, drops ball1 using lgripper1, and drops ball3 using rgripper1, all in room5. Finally, robot1 moves from room5 to room2, reaching the current state. In this state, what are the valid properties of the state that do not involve negations for rgripper1? Write None if there are none.", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "41cba015-940d-41ba-952d-9e01098c560e", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, what are the valid properties of the state that involve negations for ball6? Write None if there are none", "answer": "ball6 is not at room1, ball6 is not at room3, ball6 is not at room4, ball6 is not present at room2, ball6 is not present at room5 and robot1 is not carrying ball6 with lgripper1", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, the left gripper of robot1 (lgripper1) grasps ball1 in room1, subsequently, robot1's right gripper (rgripper1) picks up ball3 in room1, robot1 then moves from room1 to room5, and in room5, robot1's rgripper1 releases ball3, after which robot1 proceeds to room2 from room5, in room2, robot1's rgripper1 picks up ball4, robot1 then moves back to room1 from room2, and in room1, robot1's rgripper1 drops ball4, and finally, robot1's rgripper1 picks up ball6 in room1 to attain the current state. In this state, what are the valid properties of the state that involve negations for ball6? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "c42b47c2-177b-4d56-a1b9-e7e9077574e5", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, in room5, robot1's rgripper1 drops ball3 and robot1 moves to room2 from room5 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for ball1? Write None if there are none", "answer": "ball1 is at room5", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, where it drops ball7 using lgripper1, and then proceeds to room1. In room1, robot1's lgripper1 picks up ball1 and its rgripper1 picks up ball3. Next, robot1 moves to room5 from room1, and in room5, it drops ball1 using lgripper1 and ball3 using rgripper1, before finally moving to room2 from room5 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for ball1? Write None if there are none.", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "9e405a65-8b2a-4830-8cf1-465d661312d2", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball7 in room3, robot1 moves to room4 from room3, ball1 is dropped in room4 with lgripper1 by robot1, in room4, robot1's rgripper1 drops ball7, robot1 moves to room2 from room4, ball2 is picked from room2 with lgripper1 by robot1 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for rgripper1? Write None if there are none", "answer": "ball2 is not being carried by robot1's rgripper1, ball5 is being carried by robot1's rgripper1, ball7 is not being carried by robot1's rgripper1, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not free, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1 and robot1 is not carrying ball6 with rgripper1", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, robot1 uses lgripper1 to pick up ball1 in room1, robot1's rgripper1 then picks up ball3 in room1, robot1 proceeds to move from room1 to room5, where it drops ball3 using rgripper1, robot1 then moves from room5 to room2, picks up ball4 with rgripper1 in room2, moves from room2 to room1, drops ball4 in room1 using rgripper1, picks up ball6 with rgripper1 in room1, moves from room1 to room3, drops ball6 in room3 using rgripper1, picks up ball7 with rgripper1 in room3, moves from room3 to room4, drops ball1 in room4 using lgripper1, drops ball7 in room4 using rgripper1, moves from room4 to room2, picks up ball2 with lgripper1 in room2, and finally picks up ball5 with rgripper1 in room2 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for rgripper1? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "28e45f5c-e9c3-4c08-abd9-ee67e7256650", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves from room4 to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for ball4? Write None if there are none", "answer": "ball4 is not at room3, ball4 is not at room4, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room2, ball4 is not present at room1, ball4 is not present at room5 and robot1 is carrying ball4 with lgripper1", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, moves to room2, and uses rgripper1 to pick up ball2 in room2. Next, robot1 proceeds to room3 from room2, drops ball1 in room3 using lgripper1, and then moves to room4 from room3. In room4, robot1 drops ball2 using rgripper1, and subsequently moves from room4 to room2. Finally, in room2, robot1's lgripper1 picks up ball4, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for ball4? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "a0881fbb-3c29-4acb-aacb-0a7b47955c7c", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves to room1 from room2, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, rgripper2 of robot2 picks up ball6 in room1, from room1, robot2 moves to room2 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for robot2? Write None if there are none", "answer": "ball3 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's lgripper1, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper1, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball4, lgripper2 of robot2 is not carrying ball1, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball4, rgripper1 of robot2 is not carrying ball6, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not free, robot2 is carrying ball6 with rgripper2, robot2 is not at room1, robot2 is not at room3, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball1 with rgripper2, robot2 is not carrying ball2 with lgripper1, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball7 with lgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not carrying ball7 with rgripper1, robot2 is not carrying ball7 with rgripper2, robot2 is present in room2, robot2's lgripper1 is not free, robot2's lgripper2 is available and robot2's rgripper1 is not free", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then uses its left gripper (lgripper2) to pick up ball1 and its right gripper (rgripper2) to pick up ball2 in room3. Next, robot2 returns to room2 from room3, drops ball1 with lgripper2, and drops ball2 with rgripper2 in room2. Then, robot2 moves back to room3, picks up ball4 with lgripper2, and picks up ball7 with rgripper2. After that, robot2 returns to room2, drops ball7 with rgripper2, picks up ball3 with rgripper2, and moves to room1. In room1, robot2 drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves back to room2 and drops ball5 with lgripper2, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for robot2? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "50213453-a24a-4a1e-a9f4-9f7e6be1c8c4", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for ball7? Write None if there are none", "answer": "ball7 is present at room3", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 transitions from room2 to room3 to achieve the current state. In this state, what are the valid properties of the state that do not involve negations for ball7? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "f050c717-b047-45fb-ab9a-f42afb05b486", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, ball5 is picked from room3 with rgripper1 by robot1 and from room3, robot1 moves to room6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for ball3? Write None if there are none", "answer": "ball3 is located at room5", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, where it drops ball7 using lgripper1. Next, robot1 moves to room1, picks up ball1 with lgripper1, and simultaneously picks up ball3 with its right gripper (rgripper1). From room1, robot1 proceeds to room5, where it drops ball1 using lgripper1 and ball3 using rgripper1. Then, robot1 moves to room2, picks up ball2 with lgripper1, and also picks up ball4 with rgripper1. After that, robot1 moves to room1, drops ball4 using rgripper1, picks up ball6 with rgripper1, and then moves to room3. In room3, robot1 drops ball6 using rgripper1 and picks up ball5 with rgripper1, before finally moving to room6 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for ball3? Write None if there are none.", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "aff53973-fb07-4577-88ce-f1c162e572e7", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, what are the valid properties of the state that involve negations for room1? Write None if there are none", "answer": "ball2 is not located at room1, ball4 is not located at room1, ball5 is not at room1 and ball7 is not at room1", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 transitions from room4 to room1 to achieve the current state. In this state, what are the valid state properties related to room1 that include negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "d3ac99b3-3d82-4968-86f3-64028a8b3626", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for room1? Write None if there are none", "answer": "ball5 is at room1 and ball6 is at room1", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot2 relocates from room2 to room3, robot2 uses lgripper2 to pick up ball1 in room3, robot2's rgripper2 picks up ball2 in room3, robot2 moves back from room3 to room2, robot2 drops ball1 in room2 using lgripper2, and then robot2's rgripper2 drops ball2 in room2, after which robot2 moves from room2 back to room3, where robot2's lgripper2 picks up ball4 and rgripper2 picks up ball7, and finally robot2 moves from room3 to room2 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for room1? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is positioned in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "62df8f00-59c9-4ceb-a257-92a115336a15", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, what are the valid properties of the state that involve negations for ball1? Write None if there are none", "answer": "ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not present at room1, ball1 is not present at room4 and ball1 is not present at room5", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 moves to room5 from room1, drops ball3 in room5 using rgripper1, and then moves to room2. In room2, rgripper1 of robot1 picks up ball4, and robot1 moves back to room1, where it drops ball4 using rgripper1. Finally, from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, what are the valid properties of the state that involve negations for ball1? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "da606016-07ec-49b5-a003-4d8140a7a777", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves from room3 to room4, ball1 is dropped in room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball7 in room4, from room4, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for robot1? Write None if there are none", "answer": "ball5 is being carried by robot1's rgripper1, robot1 is at room2 and robot1 is carrying ball2 with lgripper1", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to grasp ball1 in room1, and subsequently uses its right gripper (rgripper1) to pick up ball3 in room1. From room1, robot1 proceeds to room5, where it releases ball3 using rgripper1. Next, robot1 moves from room5 to room2, picks up ball4 with rgripper1, and then returns to room1, where it drops ball4 using rgripper1. Robot1 then picks up ball6 with rgripper1 in room1 and moves to room3, where it releases ball6 using rgripper1. In room3, robot1 picks up ball7 with rgripper1, moves to room4, and drops ball1 using lgripper1. Additionally, robot1 releases ball7 using rgripper1 in room4. From room4, robot1 moves to room2, where it uses lgripper1 to pick up ball2 and rgripper1 to pick up ball5, ultimately reaching the current state. In this state, what are the valid properties of the state that do not involve negations for robot1? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
