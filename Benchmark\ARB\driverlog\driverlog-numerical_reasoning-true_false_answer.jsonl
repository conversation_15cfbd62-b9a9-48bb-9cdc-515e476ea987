{"question_id": "a13278ab-dc94-4cda-ba6c-c959e72e9956", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, package3 is loaded in truck1 at location s0, truck1 is loaded with package1 at location s0, truck1 is driven from location s0 to s3 by driver3, truck1 is unloaded with package1 at location s3, driver3 drives truck1 to location s1 from location s3, at location s1, driver3 disembarks from truck1, package3 is unloaded from truck1 at location s1, package2 is loaded in truck2 at location s2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1254? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, package3 is placed in truck1 at location s0, and package1 is also loaded into truck1 at location s0. Then, driver3 drives truck1 from location s0 to s3, unloads package1 at location s3, and proceeds to drive truck1 to location s1 from s3. Upon arrival at location s1, driver3 gets off truck1, and package3 is unloaded from truck1. Meanwhile, package2 is loaded into truck2 at location s2, and driver1 walks from location s3 to location p3_0, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 1254? True or False", "initial_state_nl_paraphrased": "Driver1 is currently situated at location s3, while driver2 is also at location s3. Meanwhile, driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, s3 and p3_0, s3 and s1, and s3 and s2. Package1 is at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and located at s0, while truck2 is also empty and situated at location s2."}
{"question_id": "219b9b0c-71a1-4249-87b6-5c8cf9ab412c", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks from location p4_3 to location s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to location s1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, package4 is loaded in truck1 at location s0, driver1 drives truck1 to location s2 from location s0, at location s2, package2 is loaded in truck1 and at location s2, package1 is loaded in truck1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 283? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and finally from p4_1 to s1. At location s1, driver1 boards truck1, then drives it from s1 to s0. At s0, package4 is loaded into truck1. Driver1 then drives truck1 from s0 to s2, where package2 and package1 are loaded into truck1, resulting in the current state. In this state, is the number of valid properties involving negations equal to 283? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link is present between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "4eafcc6c-79d7-4254-9eef-889ec0b62ef3", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks from location p4_3 to location s4, driver1 walks from location s4 to location p4_1, driver1 walks from location p4_1 to location s1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, package4 is loaded in truck1 at location s0, truck1 is driven from location s0 to s2 by driver1, at location s2, package2 is loaded in truck1, package1 is loaded in truck1 at location s2, driver1 drives truck1 from location s2 to location s3, truck1 is loaded with package3 at location s3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s4 by driver1, at location s4, package4 is unloaded in truck1, package3 is unloaded from truck1 at location s4, package2 is unloaded from truck1 at location s4, truck1 is driven from location s4 to s1 by driver1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 282? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and p4_1 to s1. At s1, driver1 boards truck1, then drives it from s1 to s0. At s0, package4 is loaded into truck1. Next, truck1 is driven from s0 to s2 by driver1, where package2 and package1 are loaded. Driver1 then drives truck1 from s2 to s3, where package3 is loaded. At s3, package1 is unloaded from truck1. Subsequently, truck1 is driven from s3 to s4, where package4, package3, and package2 are unloaded. Finally, truck1 is driven from s4 to s1, and driver1 disembarks from truck1 at s1, reaching the current state. In this state, is the number of valid properties of the state that involve negations equal to 282? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to both s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. A link exists between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. A path is present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "835495f1-7be7-47c7-80c9-a2400eacdd6a", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, package3 is loaded in truck1 at location s0, truck1 is loaded with package1 at location s0, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, driver3 drives truck1 from location s3 to location s1, at location s1, driver3 disembarks from truck1, package3 is unloaded from truck1 at location s1, package2 is loaded in truck2 at location s2, driver1 walks from location s3 to location p3_0, driver1 walks from location p3_0 to s0, driver2 walks from location s3 to p1_3, driver2 walks from location p1_3 to s1, driver2 walks from location s1 to p1_2, driver2 walks from location p1_2 to location s2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to location s2, at location s2, driver3 boards truck2 and driver3 drives truck2 from location s2 to location s3 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1254? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, package3 is placed in truck1 at location s0, and package1 is also loaded into truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1. Next, driver3 drives truck1 from location s3 to location s1, where driver3 gets off truck1 and package3 is unloaded from truck1. Meanwhile, package2 is loaded into truck2 at location s2. Additionally, driver1 walks from location s3 to p3_0 and then to s0, while driver2 walks from location s3 to p1_3, then to s1, followed by p1_2, and finally to location s2. Driver3 also walks from location s1 to p1_2 and then to location s2, where driver3 boards truck2 and drives it from location s2 to location s3, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 1254? True or False", "initial_state_nl_paraphrased": "Driver1 is currently situated at location s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s2."}
{"question_id": "cc27e5b1-e36d-480f-b391-222c1cbc249e", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, package3 is loaded in truck1 at location s0, at location s0, package1 is loaded in truck1, driver3 drives truck1 to location s3 from location s0, package1 is unloaded from truck1 at location s3, driver3 drives truck1 from location s3 to location s1, driver3 disembarks from truck1 at location s1, truck1 is unloaded with package3 at location s1, at location s2, package2 is loaded in truck2 and driver1 walks to location p3_0 from location s3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 278? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, package3 is placed in truck1 at location s0, and package1 is also loaded into truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1. Next, driver3 drives truck1 from location s3 to location s1, disembarks from truck1 at location s1, and unloads package3 from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2, and driver1 moves from location s3 to location p3_0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 278? True or False", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also currently at location s3. Meanwhile, driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and present at location s2."}
{"question_id": "d1c1a0f9-99e5-4e10-8954-43c3049d9675", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, is the number of executable actions equal to 10? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location s0, driver3 gets on truck1 to reach the current state. In this state, is the number of executable actions equal to 10? True or False", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also currently at location s3. Meanwhile, driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, s3 and p3_0, s3 and s1, and s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, locations s1 and s2, s1 and s3, and s2 and s3 are all connected by links. Truck1 is empty and currently at location s0, while truck2 is also empty and present at location s2."}
{"question_id": "96ccbda9-b6a5-48ef-aece-082063eb022d", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to s1, at location s1, driver1 boards truck1, driver1 drives truck1 to location s0 from location s1, at location s0, package4 is loaded in truck1, truck1 is driven from location s0 to s2 by driver1, truck1 is loaded with package2 at location s2 and truck1 is loaded with package1 at location s2 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1362? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and p4_1 to s1. Upon reaching s1, driver1 boards truck1, then drives it from s1 to s0. At s0, package4 is loaded into truck1. Driver1 then drives truck1 from s0 to s2, where it is loaded with package2 and package1, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 1362? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "1d62fb10-b0e1-4aa9-9d40-1abe3d539b0b", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks from location p4_3 to location s4, driver1 walks from location s4 to location p4_1, driver1 walks to location s1 from location p4_1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, truck1 is loaded with package4 at location s0, driver1 drives truck1 from location s0 to location s2, at location s2, package2 is loaded in truck1, at location s2, package1 is loaded in truck1, truck1 is driven from location s2 to s3 by driver1, package3 is loaded in truck1 at location s3, package1 is unloaded from truck1 at location s3, driver1 drives truck1 to location s4 from location s3, at location s4, package4 is unloaded in truck1, at location s4, package3 is unloaded in truck1, truck1 is unloaded with package2 at location s4, truck1 is driven from location s4 to s1 by driver1 and at location s1, driver1 disembarks from truck1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 292? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and then from p4_1 to s1. At s1, driver1 boards truck1, and then drives it from s1 to s0. At s0, truck1 is loaded with package4, and driver1 drives it from s0 to s2. Upon arrival at s2, truck1 is loaded with package2 and package1. Driver1 then drives truck1 from s2 to s3, where package3 is loaded. However, package1 is unloaded at s3. Driver1 then drives truck1 from s3 to s4, where package4 and package3 are unloaded, and package2 is also unloaded from truck1. Finally, driver1 drives truck1 from s4 to s1 and disembarks at s1, reaching the current state. In this state, is the number of valid properties of the state that involve negations equal to 292? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link is present between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. A link exists between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. A path is present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "fef510d7-0eb3-42ad-9ecd-4d87efc03851", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to p0_2, driver1 walks to location s0 from location p0_2, driver1 boards truck1 at location s0, truck1 is driven from location s0 to s3 by driver1, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks from location s3 to location p0_3, driver1 walks to location s0 from location p0_3 and driver2 walks from location s2 to location p0_2 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 9?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to p0_2, then proceeds to location s0 from p0_2, boards truck1 at location s0, drives truck1 from location s0 to s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, walks from location s3 to p0_3, and then returns to location s0 from p0_3, while driver2 moves from location s2 to p0_2, resulting in the current state. Is it True or False that the total number of actions in this sequence that led to the current state is 9?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is established between s2 and s1. Package1 is currently situated at location s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link exists between s1 and s2, and between s3 and s0. Paths are present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are established between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "83bdffd4-27ae-4500-8cd3-9a49cf84916e", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, at location s0, package1 is loaded in truck1, driver3 drives truck1 to location s3 from location s0, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, package2 is loaded in truck2 at location s2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 40? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at location s0, driver3 gets into truck1, at location s0, package3 is placed in truck1, at location s0, package1 is also placed in truck1, driver3 then drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 from location s3 to location s1, driver3 gets out of truck1 at location s1, package3 is unloaded from truck1 at location s1, package2 is loaded into truck2 at location s2, and driver1 moves from location s3 to location p3_0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 40? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s2."}
{"question_id": "39fffdcc-7612-4c31-be84-3f7313b7993f", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 261? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver2 moves from location s3 to location p3_0 to achieve the current state. In this state, does the number of valid properties involving negations equal 261? True or False", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are also linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3. Moreover, links exist between s0 and s2, s1 and s0. Paths are also present between p0_1 and s0, p3_0 and s3, s0 and p2_0, s0 and p3_0, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, while truck2 is empty and at s3. Truck3 is also empty and present at s0."}
{"question_id": "70ab81cf-2eb3-43e8-9f30-be6d13538230", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to s0, driver2 boards truck1 at location s0, driver2 drives truck1 to location s2 from location s0, at location s2, package3 is loaded in truck1, at location s2, package2 is loaded in truck1, truck1 is driven from location s2 to s0 by driver2, truck1 is unloaded with package2 at location s0, driver2 drives truck1 to location s3 from location s0 and truck1 is loaded with package1 at location s3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 224? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 boards truck1. Driver2 then drives truck1 from s0 to s2, where package3 and package2 are loaded into truck1. Subsequently, driver2 drives truck1 from s2 back to s0, unloading package2, and then drives truck1 from s0 to s3, where package1 is loaded into truck1, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 224? True or False", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, s0 is linked to s2, and s1 is linked to s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, while truck2, which is also empty, is at s3. Truck3, which contains nothing, is present at s0."}
{"question_id": "d0a91682-cc84-48b0-bcf9-a2a4d747114e", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks to location s0 from location p3_0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 to location s2 from location s0, truck1 is loaded with package3 at location s2, package2 is loaded in truck1 at location s2, truck1 is driven from location s2 to s0 by driver2, package2 is unloaded from truck1 at location s0, driver2 drives truck1 from location s0 to location s3, at location s3, package1 is loaded in truck1, truck1 is driven from location s3 to s1 by driver2, driver2 disembarks from truck1 at location s1, driver2 walks to location p0_1 from location s1, driver2 walks from location p0_1 to location s0, at location s0, driver2 boards truck3, truck3 is driven from location s0 to s2 by driver2, package3 is unloaded from truck1 at location s1, truck1 is unloaded with package1 at location s1 and driver3 walks to location p3_0 from location s3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 262? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads truck1 with package3 and package2 at s2, drives truck1 back to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, loads package1 into truck1 at s3, drives truck1 from s3 to s1, disembarks from truck1 at s1, walks to p0_1 and then to s0, boards truck3 at s0, drives truck3 from s0 to s2, unloads package3 from truck1 at s1, and unloads package1 from truck1 at s1, while driver3 walks from s3 to p3_0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 262? True or False", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, s0 is linked to s2, and s1 is linked to s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "bd897fba-4a98-47c9-8b6a-c6eb0b4d35cb", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks from location p0_2 to s0, at location s0, driver1 boards truck1, truck1 is driven from location s0 to s3 by driver1, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, is the number of executable actions equal to 7? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 is loaded with package3 at location s0, driver1 moves from location s2 to p0_2, then proceeds from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3, and then from p0_3 back to s0, while driver2 moves from s2 to p0_2 to reach the current state. In this state, is the number of executable actions equal to 7? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1. Package1 is currently situated at location s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link exists between locations s1 and s2, and between s3 and s0. Paths are present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links exist between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "2bb83b35-30f9-4c62-b395-c247bec8101a", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, at location s0, driver1 boards truck1, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, package3 is unloaded from truck1 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0, driver2 walks to location p0_2 from location s2, driver2 walks from location p0_2 to s0, truck2 is boarded by driver2 at location s0, truck2 is driven from location s0 to s1 by driver2, package1 is loaded in truck2 at location s1, driver2 drives truck2 to location s2 from location s1, truck2 is loaded with package2 at location s2, truck2 is unloaded with package1 at location s2, driver2 drives truck2 from location s2 to location s1 and from truck2, driver2 disembarks at location s1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 42? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, truck1 is loaded with package3, driver1 moves from location s2 to p0_2 and then to s0, where driver1 gets into truck1, then drives it from s0 to s3, disembarks at s3, and unloads package3 from truck1 at s3, after which driver1 walks to p0_3 and then back to s0. Meanwhile, driver2 moves from s2 to p0_2 and then to s0, boards truck2 at s0, drives it to s1, loads package1 into truck2 at s1, drives to s2, loads package2 into truck2, unloads package1 at s2, drives back to s1, and disembarks from truck2 at s1, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 42? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1, and package1 is currently situated at s1, while package2 and package4 are at s2, and package3 is at s0. \n\nThere are links between s1 and s2, s3 and s0, and paths between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Moreover, links exist between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. \n\nPaths are also present between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Currently, truck1 is empty and located at s0, and truck2 is also empty and situated at s0."}
{"question_id": "3073e05f-65ed-4268-b6f8-a77f628baf89", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to location s0, at location s0, driver2 boards truck1, driver2 drives truck1 from location s0 to location s2, at location s2, package3 is loaded in truck1, at location s2, package2 is loaded in truck1, driver2 drives truck1 to location s0 from location s2, at location s0, package2 is unloaded in truck1, truck1 is driven from location s0 to s3 by driver2 and package1 is loaded in truck1 at location s3 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1500? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 gets into truck1. Driver2 then drives truck1 from s0 to s2, where package3 and package2 are loaded into truck1. Next, driver2 drives truck1 back to s0, unloads package2, and then drives truck1 from s0 to s3, where package1 is loaded into truck1, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 1500? True or False", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, s0 is linked to s2, and s1 is linked to s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "a4958be9-dbe2-407c-8bb2-34fecc1b3df2", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3 to reach the current state. In this state, is the number of inexecutable actions equal to 1411? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver1 moves from location s3 to location p4_3 to achieve the current state. In this state, is the number of inexecutable actions equal to 1411? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to both s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "edae88ae-a668-4329-b872-11abe38e648e", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 232? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: package3 is loaded onto truck1 at location s0 to achieve the current state. In this state, does the number of valid properties involving negations equal 232? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1, and package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is at location s2. There is a link between locations s1 and s2, and between s3 and s0. A path exists between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Moreover, a link exists between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. There is also a path between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Currently, truck1 is empty and located at s0, and truck2 is also empty and at location s0."}
{"question_id": "7e960e0d-0992-4157-8286-a2167c23ad72", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 53? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver1 moves from location s3 to p4_3 to attain the current state. In this state, does the count of valid properties that do not include negations equal 53? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link is present between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "c9a4764b-34a1-4afd-8147-6f944c6ff34f", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, truck1 is boarded by driver1 at location s0, truck1 is driven from location s0 to s3 by driver1, driver1 disembarks from truck1 at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to p0_3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to location p0_2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 50? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: package3 is placed in truck1 at location s0, driver1 moves from location s2 to p0_2, then proceeds to location s0, boards truck1 at location s0, drives truck1 from location s0 to s3, gets off truck1 at location s3, unloads package3 from truck1 at location s3, walks from location s3 to p0_3, and then to s0, while driver2 moves from location s2 to p0_2, resulting in the current state. In this state, is the count of valid properties that do not involve negations equal to 50? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1, and package1 is currently at location s1, while package2 and package4 are at location s2, and package3 is at location s0. \n\nMoreover, a link exists between locations s1 and s2, and between s3 and s0. Paths are also present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are present between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. \n\nAdditionally, paths exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Currently, truck1 is empty and located at s0, and truck2 is also empty and at location s0."}
{"question_id": "c0e73495-b531-4d31-a9c6-d7857741071f", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks from location s3 to p0_3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 223? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: truck1 is loaded with package3 at location s0, driver1 moves from location s2 to p0_2, then proceeds to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, walks to p0_3 from location s3, and then returns to location s0, while driver2 moves from location s2 to p0_2, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 223? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is established between s2 and s1. Package1 is currently situated at s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link is present between s1 and s2, and between s3 and s0. Paths exist between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are present between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "e36b9f81-18a8-4a3f-8378-92426ffacfd7", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to location s0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 from location s0 to location s2, truck1 is loaded with package3 at location s2, at location s2, package2 is loaded in truck1, driver2 drives truck1 from location s2 to location s0, at location s0, package2 is unloaded in truck1, driver2 drives truck1 to location s3 from location s0, at location s3, package1 is loaded in truck1, driver2 drives truck1 to location s1 from location s3, driver2 disembarks from truck1 at location s1, driver2 walks to location p0_1 from location s1, driver2 walks to location s0 from location p0_1, driver2 boards truck3 at location s0, driver2 drives truck3 from location s0 to location s2, package3 is unloaded from truck1 at location s1, truck1 is unloaded with package1 at location s1 and driver3 walks from location s3 to p3_0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 43? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to location p3_0, then from location p3_0 to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s2, loads truck1 with package3 at location s2, also loads package2 into truck1 at location s2, drives truck1 from location s2 back to location s0, unloads package2 from truck1 at location s0, drives truck1 from location s0 to location s3, loads package1 into truck1 at location s3, drives truck1 from location s3 to location s1, disembarks from truck1 at location s1, walks to location p0_1 from location s1, then to location s0 from location p0_1, boards truck3 at location s0, drives truck3 from location s0 to location s2, unloads package3 from truck1 at location s1, unloads package1 from truck1 at location s1, and driver3 moves from location s3 to p3_0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 43? True or False", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, s0 is linked to s2, and s1 is linked to s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "2adc4ed8-c2b3-40cc-9c96-3f60d2eb74ac", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 283? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver3 boards truck1 at location s0 to achieve the current state. In this state, does the number of valid properties involving negations equal 283? True or False", "initial_state_nl_paraphrased": "Driver1 is currently situated at location s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s2."}
{"question_id": "eda6879a-8b82-414a-af55-e6bbde4cfbe6", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks from location p4_3 to s4, driver1 walks from location s4 to p4_1, driver1 walks to location s1 from location p4_1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, package4 is loaded in truck1 at location s0, driver1 drives truck1 to location s2 from location s0, at location s2, package2 is loaded in truck1 and package1 is loaded in truck1 at location s2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 56? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, then from p4_1 to s1, where driver1 boards truck1. Driver1 then drives truck1 from s1 to s0, loads package4 into truck1 at s0, and drives truck1 from s0 to s2. At location s2, both package2 and package1 are loaded into truck1, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 56? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "515ff271-f19d-4b55-aaa6-631e8d67cb58", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks to location s4 from location p4_3, driver1 walks to location p4_1 from location s4, driver1 walks from location p4_1 to location s1, at location s1, driver1 boards truck1, driver1 drives truck1 to location s0 from location s1, at location s0, package4 is loaded in truck1, truck1 is driven from location s0 to s2 by driver1, truck1 is loaded with package2 at location s2 and truck1 is loaded with package1 at location s2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 244? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by a move from s4 to p4_1, and then from p4_1 to s1. Upon arriving at s1, driver1 boards truck1, and then drives it from s1 to s0. At s0, package4 is loaded onto truck1. Subsequently, driver1 drives truck1 from s0 to s2, where it is loaded with package2 and package1, resulting in the current state. In this state, is the number of valid properties involving negations equal to 244? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to both s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. A link exists between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. A path is present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "4e9cb82e-8226-4cf6-ad1f-d771436c81d8", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0 to reach the current state. In this state, is the number of executable actions equal to 10? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver2 moves from location s3 to location p3_0 to attain the current state. In this state, is the number of executable actions equal to 10? True or False", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, s0 is linked to s2, and s1 is linked to s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, while truck2 is empty and at s3. Truck3 is also empty and present at s0."}
{"question_id": "42ac9720-45a7-40ef-8b66-6b306b113b52", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to location s1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, at location s0, package4 is loaded in truck1, driver1 drives truck1 from location s0 to location s2, package2 is loaded in truck1 at location s2, at location s2, package1 is loaded in truck1, driver1 drives truck1 to location s3 from location s2, package3 is loaded in truck1 at location s3, at location s3, package1 is unloaded in truck1, truck1 is driven from location s3 to s4 by driver1, truck1 is unloaded with package4 at location s4, at location s4, package3 is unloaded in truck1, truck1 is unloaded with package2 at location s4, truck1 is driven from location s4 to s1 by driver1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 53? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then to s4, followed by p4_1, and then to s1, where driver1 boards truck1. Driver1 then drives truck1 from s1 to s0, where package4 is loaded onto truck1. Next, driver1 drives truck1 from s0 to s2, where package2 and package1 are loaded onto truck1. Driver1 then drives truck1 from s2 to s3, where package3 is loaded onto truck1. At location s3, package1 is unloaded from truck1. Driver1 then drives truck1 from s3 to s4, where package4 and package3 are unloaded, and package2 is also unloaded at location s4. Finally, driver1 drives truck1 from s4 to s1 and disembarks from truck1 at location s1, reaching the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 53? True or False", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4 and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link is present between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to both s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "3a06309b-60b7-45a5-9a6f-15c2779c9032", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1254? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: at location s0, driver3 gets on truck1 to attain the current state. In this state, is the number of executable and inexecutable actions equal to 1254? True or False", "initial_state_nl_paraphrased": "Driver1 is currently situated at location s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s2."}
{"question_id": "8f2a1b81-d1ce-4b22-9c1b-7fa7eaa33692", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, truck1 is driven from location s0 to s3 by driver1, driver1 disembarks from truck1 at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to p0_3, driver1 walks to location s0 from location p0_3, driver2 walks from location s2 to location p0_2, driver2 walks to location s0 from location p0_2, driver2 boards truck2 at location s0, driver2 drives truck2 from location s0 to location s1, truck2 is loaded with package1 at location s1, driver2 drives truck2 to location s2 from location s1, package2 is loaded in truck2 at location s2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 from location s2 to location s1 and at location s1, driver2 disembarks from truck2 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 740? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2, then proceeds from p0_2 to s0, boards truck1 at s0, and drives it from s0 to s3, where driver1 gets off the truck, and package3 is unloaded from truck1 at s3. Subsequently, driver1 walks from s3 to p0_3 and then back to s0. Meanwhile, driver2 moves from s2 to p0_2, then to s0, boards truck2, and drives it from s0 to s1, where package1 is loaded onto truck2. Driver2 then drives truck2 to s2, loads package2, unloads package1, and drives back to s1, where driver2 disembarks from truck2, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 740? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1. Package1 is currently situated at location s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link exists between locations s1 and s2, and between s3 and s0. Paths are present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links exist between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "71ea1637-9a82-4e1c-8fa2-e4d074d2fead", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 190? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: package3 is loaded onto truck1 at location s0 to achieve the current state. In this state, does the count of valid state properties involving negations equal 190? True or False", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is established between s2 and s1. Package1 is currently situated at s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link is present between s1 and s2, and between s3 and s0. Paths exist between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are present between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
