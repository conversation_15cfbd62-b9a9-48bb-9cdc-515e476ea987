{"question_id": "d9f288db-6871-4608-be0a-0a6408599ee7", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, at depot1, hoist1 drops crate2 on pallet1, at distributor0, hoist3 unloads crate0 from truck0, at distributor2, hoist5 lifts crate0 off pallet5, at distributor2, hoist5 loads crate1 into truck0, crate1 is lifted from crate0 at distributor2 by hoist5, crate2 is unloaded by hoist1 from truck1 at depot1, crate3 is loaded by hoist2 into truck2 at depot2, from depot0, truck1 is driven to depot1, from depot2, truck2 is driven to distributor3, hoist0 loads crate2 into truck1 at depot0, hoist2 lifts crate3 from pallet2 at depot2, hoist3 drops crate0 on pallet3 at distributor0, hoist5 loads crate0 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, hoist6 drops crate3 on pallet6 at distributor3, hoist6 unloads crate3 from truck2 at distributor3, truck0 is driven to distributor0 from distributor2 and truck1 is driven to depot0 from depot1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at depot0, hoist0 is set to lift crate2 from pallet0, at depot1, hoist1 is set to place crate2 on pallet1, at distributor0, hoist3 is set to unload crate0 from truck0, at distributor2, hoist5 is set to lift crate0 from pallet5, at distributor2, hoist5 is set to load crate1 into truck0, hoist5 at distributor2 is set to lift crate1 from crate0, at depot1, hoist1 is set to unload crate2 from truck1, at depot2, hoist2 is set to load crate3 into truck2, truck1 is set to travel from depot0 to depot1, truck2 is set to travel from depot2 to distributor3, at depot0, hoist0 is set to load crate2 into truck1, at depot2, hoist2 is set to lift crate3 from pallet2, at distributor0, hoist3 is set to place crate0 on pallet3, at distributor2, hoist5 is set to load crate0 into truck0, hoist5 at distributor2 is set to unload crate1 from truck0, at distributor3, hoist6 is set to place crate3 on pallet6, at distributor3, hoist6 is set to unload crate3 from truck2, truck0 is set to travel from distributor2 to distributor0 and truck1 is set to travel from depot1 to depot0. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 has no crates on it, crate1 is situated at distributor2, crate1 is placed on top of crate0, crate2 has no crates on it, crate3 is empty, crate3 is located at depot2, depot0 is the location of crate2, depot1 is the location of hoist1, depot1 is the location of pallet1, depot1 is the location of truck1, depot2 is the location of pallet2, depot2 is the location of truck2, distributor0 is the location of pallet3, distributor1 is the location of pallet4, hoist0 is situated at depot0, hoist0 is available for use, hoist1 is available to work, hoist2 is located at depot2, hoist2 is available, hoist3 is accessible and located at distributor0, hoist4 is accessible and located at distributor1, hoist5 is located at distributor2, hoist5 is available, hoist6 is situated at distributor3, hoist6 is available for work, pallet0 is located at depot0, pallet0 has crate2 on it, pallet1 is empty, pallet2 has crate3 on it, pallet3 is empty, pallet4 has no crates on it, pallet5 has crate0 on it, pallet5 is situated at distributor2, pallet6 is located at distributor3, pallet6 is empty and truck0 is located at distributor2."}
{"question_id": "09babb81-63d6-4747-9361-e622d6286ac5", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, hoist3 unloads crate1 from truck0 at distributor0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1 and at distributor1, hoist4 loads crate0 into truck2. Is the action: hoist4 loads crate0 into truck2 at distributor1 executable at step 10, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: at depot0, hoist0 lifts crate2 from pallet0 and loads it into truck2, then truck2 is driven from depot0 to distributor1. At depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. Meanwhile, at distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. Is the action: hoist4 loading crate0 into truck2 at distributor1 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 and hoist4 are both accessible, with hoist4 located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1, clear of any crates, is situated at depot1. Pallet2 is located at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is at distributor1. Pallet5 is located at distributor2. Truck0 is situated at depot2, and truck2 is at depot0."}
{"question_id": "7ba79b86-df55-4b01-880e-11fae709bdd8", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: truck2 is driven to depot0 from depot1. Is the action: truck2 is driven from depot1 to depot0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include moving truck2 from depot1 to depot0. Is the action of driving truck2 from depot1 to depot0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, is clear of any crates, and is placed on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2. Pallet5 is situated at distributor1, while pallet6 is located at distributor2. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available for use. Hoist2 is available and situated at depot2, while hoist3 is available and located at depot3. Hoist4 is available and situated at distributor0, and hoist5 is available and located at distributor1. Hoist6 is available for work and situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is clear and located at depot2. Pallet3 is situated at depot3. Pallet4 is clear and located at distributor0. Pallet5 has crate3 on it. Truck0 is situated at depot1, and truck1 is located at distributor0."}
{"question_id": "83bc257f-af09-4a39-8ede-59d492f44841", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist3 lifts crate2 off pallet3, from depot2, truck0 is driven to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, at distributor2, hoist5 lifts crate3 off pallet5, crate3 is loaded by hoist5 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, at distributor1, hoist4 unloads crate3 from truck1, hoist4 drops crate3 on pallet4 at distributor1, hoist4 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to depot1, crate0 is unloaded by hoist1 from truck2 at depot1 and hoist1 drops crate0 on pallet1 at depot1. Is the action: crate2 is lifted from pallet3 at depot2 by hoist3 executable at step 5, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at depot0, hoist0 lifts crate2 from pallet0 and loads it into truck2, then truck2 is driven to distributor1. At depot2, hoist2 lifts crate1 off pallet2 and hoist3 lifts crate2 off pallet3. Meanwhile, truck0 is driven to distributor0, where crate1 is unloaded by hoist3 from truck0 and placed on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. At distributor2, hoist5 lifts crate3 off pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4, then unloads crate2 from truck2. Truck2 is then driven to depot1, where crate0 is unloaded by hoist1 from truck2 and placed on pallet1. Is the action: crate2 is lifted from pallet3 at depot2 by hoist3 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is home to hoist3, while distributor2 is where truck1 is located. Hoist0 is available and situated at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is located at depot2. Hoist3 and hoist4 are both accessible, with hoist4 situated at distributor1. Hoist5 is available and located at distributor2. Pallet0 is situated at depot0. Pallet1, clear of any crates, is located at depot1. Pallet2 is situated at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2, and truck2 is at depot0."}
{"question_id": "d095e765-ffaa-4e29-b83e-3e59975f884f", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: truck2 is driven to depot2 from depot0. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: truck2 will be driven from depot0 to depot2. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work. Hoist5 is available and is located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and is located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and is situated at distributor2. \n\nLastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "9fc0c9aa-2ea7-45f9-a9af-c86db5c2f792", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven to distributor0 from depot2, crate1 is unloaded by hoist3 from truck0 at distributor0, at distributor0, hoist3 drops crate1 on pallet3, crate0 is lifted from pallet4 at distributor1 by hoist4, at distributor1, hoist4 loads crate0 into truck2, hoist5 lifts crate3 from pallet5 at distributor2, at distributor2, hoist5 loads crate3 into truck1, from distributor2, truck1 is driven to distributor1, hoist4 unloads crate3 from truck1 at distributor1, crate3 is dropped on pallet4 at distributor1 by hoist4, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, hoist1 unloads crate0 from truck2 at depot1 and crate0 is dropped on pallet1 at depot1 by hoist1. Is the action: truck1 is driven from distributor2 to distributor1 executable at step 13, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, after which truck2 is driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven from distributor2 to distributor1. Upon arrival, hoist4 unloads crate3 from truck1 and places it on pallet4 at distributor1. Additionally, hoist4 unloads crate2 from truck2 at distributor1, and truck2 is then driven from distributor1 to depot1. Finally, at depot1, hoist1 unloads crate0 from truck2 and places it on pallet1. Is the action: truck1 is driven from distributor2 to distributor1 executable at step 13, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 and hoist4 are both accessible, with hoist4 located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1, clear of any crates, is situated at depot1. Pallet2 is located at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2, and truck2 is at depot0."}
{"question_id": "429d7caa-6417-49fb-b295-1f3b7a3e83d7", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate2 into truck2, crate0 is lifted from pallet3 at distributor0 by hoist3, crate0 is loaded by hoist3 into truck2 at distributor0, crate3 is lifted from crate2 at depot2 by hoist2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate1 into truck2 at depot2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot0 to depot2 and truck2 is driven from depot2 to distributor0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at depot2, hoist2 will first remove crate1 from pallet2, then load crate2 into truck2, meanwhile at distributor0, hoist3 will lift crate0 from pallet3 and load it into truck2, at depot2, hoist2 will then lift crate3 from crate2, followed by lifting crate2 from crate1, then load crate1 into truck2, and finally load crate3 into truck2, truck2 will travel from depot0 to depot2 and then from depot2 to distributor0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work at distributor1. Hoist5 is available and located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates and is situated at distributor1. Pallet5 is also clear and located at distributor2. Lastly, truck0 is at distributor0, and truck2 is situated at depot0."}
{"question_id": "b11b7cbe-dcf3-4c9b-a3a0-f7d7be7bbc23", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: truck2 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, at depot3, hoist3 lifts crate1 off pallet3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1 and at distributor1, hoist5 drops crate2 on pallet5. Is the action: from depot0, truck2 is driven to distributor1 executable at step 4, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: truck2 will travel from depot1 to depot0, hoist0 will lift crate2 from pallet0 at depot0, hoist0 will then load crate2 into truck2 at depot0, truck2 will be driven from depot0 to distributor1, at depot3, hoist3 will lift crate1 off pallet3, hoist5 will lift crate3 from pallet5 at distributor1, hoist5 will load crate3 into truck2 at distributor1, hoist5 will unload crate2 from truck2 at distributor1, truck2 will travel from distributor1 to distributor2, and at distributor1, hoist5 will place crate2 on pallet5. Is the action of driving truck2 from depot0 to distributor1 executable at step 4, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, is clear of any crates, and is placed on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2. Pallet5 is situated at distributor1, while pallet6 is located at distributor2. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available. Hoist2 is available and is at depot2. Hoist3 is available and is located at depot3. Hoist4 is available and situated at distributor0. Hoist5 is available and located at distributor1. Hoist6 is available for work and is at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and is located at depot1. Pallet2 is clear and is located at depot2. Pallet3 is at depot3. Pallet4 is clear and is located at distributor0. Pallet5 has crate3 on it. Truck0 is at depot1, and truck1 is located at distributor0."}
{"question_id": "1ef0b4a3-9878-4d26-94cd-b353e863e239", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist3 lifts crate1 from pallet3 at depot3, crate3 is lifted from pallet5 at distributor1 by hoist5, at distributor1, hoist5 loads crate3 into truck2, at distributor1, hoist5 unloads crate2 from truck2, from distributor1, truck2 is driven to distributor2, crate2 is dropped on pallet5 at distributor1 by hoist5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, from distributor2, truck2 is driven to depot3, crate1 is loaded by hoist3 into truck2 at depot3, crate0 is unloaded by hoist3 from truck2 at depot3, truck2 is driven to distributor0 from depot3, hoist4 lifts crate3 from pallet3 at depot3, crate0 is dropped on pallet3 at depot3 by hoist3 and crate3 is dropped on pallet4 at distributor0 by hoist4. Is the action: hoist4 lifts crate3 from pallet3 at depot3 executable at step 17, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 then travels from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 then travels from distributor1 to distributor2, hoist5 drops crate2 on pallet5 at distributor1, hoist6 lifts crate0 from pallet6 at distributor2, hoist6 loads crate0 into truck2 at distributor2, truck2 then travels from distributor2 to depot3, hoist3 loads crate1 into truck2 at depot3, hoist3 unloads crate0 from truck2 at depot3, truck2 then travels from depot3 to distributor0, hoist4 lifts crate3 from pallet3 at depot3, hoist3 drops crate0 on pallet3 at depot3, and hoist4 drops crate3 on pallet4 at distributor0. Is the action: hoist4 lifts crate3 from pallet3 at depot3 executable at step 17, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear. Additionally, crate0 is positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is placed on top of pallet3. Crate2 can be found at depot0, is clear of any other crates, and is positioned on pallet0. Crate3 is located at distributor1 and is clear of any other crates. Depot1 is the location of truck2. Pallet5 is situated at distributor1, while pallet6 is located at distributor2. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available for use. Hoist2 is available and situated at depot2, while hoist3 is available and located at depot3. Hoist4 is available and situated at distributor0, and hoist5 is available and located at distributor1. Hoist6 is available for work and situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is clear and located at depot2. Pallet3 is situated at depot3. Pallet4 is clear and located at distributor0. Pallet5 has crate3 on it. Truck0 is situated at depot1, and truck1 is located at distributor0."}
{"question_id": "49f8d10c-59ef-41bc-87c5-d7ed964a18ff", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at distributor0, hoist2 unloads crate2 from truck1. Is the action: hoist2 unloads crate2 from truck1 at distributor0 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: at distributor0, hoist2 is set to unload crate2 from truck1. Is the action: hoist2 unloading crate2 from truck1 at distributor0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, is clear of any crates, and is positioned on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2, while distributor1 is where pallet5 is situated, and distributor2 is where pallet6 is located. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and is available. Hoist3 is available and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available and located at distributor1, and hoist6 is at distributor2 and available for work. Pallet0 is at depot0, pallet1 is clear and located at depot1, pallet2 is clear and situated at depot2, pallet3 is at depot3, pallet4 is clear and located at distributor0, and pallet5 has crate3 on it. Additionally, truck0 is at depot1, and truck1 is located at distributor0."}
{"question_id": "6fc2bdeb-970a-479d-b627-dca93d998de6", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 loads crate2 into truck2, crate1 is lifted from pallet3 at depot3 by hoist3, crate2 is dropped on pallet5 at distributor1 by hoist5, crate2 is unloaded by hoist5 from truck2 at distributor1, crate3 is lifted from pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, from distributor1, truck2 is driven to distributor2, hoist0 drops crate0 on crate2 at depot3, hoist0 lifts crate2 from pallet0 at depot0 and truck2 is driven from depot1 to depot0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at depot0, hoist0 is set to load crate2 onto truck2, meanwhile at depot3, hoist3 will lift crate1 from pallet3, then at distributor1, hoist5 will drop crate2 onto pallet5 and unload it from truck2, subsequently, hoist5 will lift crate3 from pallet5 and load it into truck2, after which truck2 will be driven from distributor1 to distributor2, in the meantime, hoist0 will drop crate0 onto crate2 at depot3 and lift crate2 from pallet0 at depot0, and truck2 will also be driven from depot1 to depot0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is placed on top of pallet3. Crate2 can be found at depot0, and it is clear of any other crates, resting on pallet0. Crate3 is located at distributor1 and is clear of any other crates. Depot1 is the location of truck2. Pallet5 is situated at distributor1, while pallet6 is located at distributor2. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available for use. Hoist2 is available and situated at depot2, while hoist3 is available and located at depot3. Hoist4 is available and situated at distributor0, and hoist5 is available and located at distributor1. Hoist6 is available for work and situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is clear and located at depot2. Pallet3 is situated at depot3. Pallet4 is clear and located at distributor0. Pallet5 has crate3 on it. Truck0 is situated at depot1, and truck1 is located at distributor0."}
{"question_id": "f86ee8e3-730b-4b40-b34a-35e94e741487", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at distributor0, hoist3 lifts crate0 off pallet3, at distributor0, hoist3 loads crate0 into truck2, at distributor2, hoist5 loads crate3 into truck1, crate1 is lifted from pallet2 at depot2 by hoist2, crate2 is lifted from crate1 at depot2 by hoist2, hoist2 lifts crate3 from crate2 at depot2, hoist2 loads crate1 into truck2 at depot2, hoist2 loads crate2 into truck2 at depot2, truck2 is driven from depot0 to depot2 and truck2 is driven from depot2 to distributor0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at distributor0, hoist3 will first remove crate0 from pallet3 and then load it into truck2, at distributor2, hoist5 will load crate3 into truck1, at depot2, hoist2 will lift crate1 from pallet2, then lift crate2 from crate1, and lift crate3 from crate2, followed by loading crate1 and crate2 into truck2, and finally, truck2 will travel from depot0 to depot2 and then from depot2 to distributor0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 is the location of both crate1 and crate2. Distributor0 is where hoist3 is located, and distributor1 is home to both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work. Hoist5 is available and is located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and is located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and is situated at distributor2. \n\nLastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "0721023e-9d7d-45b6-9d60-f4c68f1fa499", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at distributor0, hoist3 lifts crate0 off pallet3, at distributor1, hoist4 drops crate2 on pallet4, at distributor2, hoist5 unloads crate0 from truck2, crate0 is loaded by hoist3 into truck2 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate1 is loaded by hoist2 into truck2 at depot2, crate3 is lifted from crate2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, crate3 is unloaded by hoist5 from truck2 at distributor2, hoist2 lifts crate1 from pallet2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist3 unloads crate1 from truck2 at distributor0, hoist4 unloads crate2 from truck2 at distributor1, hoist5 drops crate3 on pallet5 at distributor2, truck2 is driven to depot2 from depot0, truck2 is driven to distributor0 from depot2, truck2 is driven to distributor1 from distributor0 and truck2 is driven to distributor2 from distributor1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at distributor0, hoist3 will lift crate0 from pallet3, at distributor1, hoist4 will place crate2 on pallet4, and at distributor2, hoist5 will unload crate0 from truck2. Additionally, at distributor0, hoist3 will load crate0 into truck2, and hoist3 will also place crate1 on pallet3. At depot2, hoist2 will load crate1 into truck2, and hoist2 will lift crate3 from crate2. Hoist2 will then load crate3 into truck2 at depot2, and hoist5 will unload crate3 from truck2 at distributor2. Furthermore, hoist2 will lift crate1 from pallet2 at depot2, lift crate2 from crate1 at depot2, and load crate2 into truck2 at depot2. Hoist3 will unload crate1 from truck2 at distributor0, hoist4 will unload crate2 from truck2 at distributor1, and hoist5 will place crate3 on pallet5 at distributor2. The truck2 will be driven from depot0 to depot2, then from depot2 to distributor0, from distributor0 to distributor1, and finally from distributor1 to distributor2. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work. Hoist5 is available and is located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and is located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and is situated at distributor2. \n\nLastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "556597a3-7a6e-44ba-99e1-088e1e5aac1e", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, at depot1, hoist1 drops crate2 on pallet1, at depot2, hoist2 lifts crate3 off pallet2, at distributor0, hoist3 drops crate0 on pallet3, at distributor2, hoist5 lifts crate0 off pallet5, crate0 is loaded by hoist5 into truck0 at distributor2, crate0 is unloaded by hoist3 from truck0 at distributor0, crate1 is lifted from crate0 at distributor2 by hoist5, crate1 is unloaded by hoist5 from truck0 at distributor2, crate2 is loaded by hoist0 into truck1 at depot0, crate3 is loaded by hoist2 into truck2 at depot2, from depot0, truck1 is driven to depot1, from depot1, truck1 is driven to depot0, from distributor2, truck0 is driven to distributor0, hoist0 loads crate2 into truck0 at depot0, hoist1 unloads crate2 from truck1 at depot1, hoist6 drops crate3 on pallet6 at distributor3, hoist6 unloads crate3 from truck2 at distributor3 and truck2 is driven to distributor3 from depot2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at depot0, hoist0 is set to lift crate2 from pallet0, at depot1, hoist1 is set to drop crate2 onto pallet1, at depot2, hoist2 is set to lift crate3 from pallet2, at distributor0, hoist3 is set to drop crate0 onto pallet3, at distributor2, hoist5 is set to lift crate0 from pallet5, then hoist5 loads crate0 into truck0 at distributor2, and hoist3 unloads crate0 from truck0 at distributor0, next, hoist5 lifts crate1 from crate0 at distributor2, and hoist5 unloads crate1 from truck0 at distributor2, hoist0 loads crate2 into truck1 at depot0, and hoist2 loads crate3 into truck2 at depot2, subsequently, truck1 is driven from depot0 to depot1, then from depot1 to depot0, truck0 is driven from distributor2 to distributor0, hoist0 loads crate2 into truck0 at depot0, hoist1 unloads crate2 from truck1 at depot1, hoist6 drops crate3 onto pallet6 at distributor3, hoist6 unloads crate3 from truck2 at distributor3, and truck2 is driven from depot2 to distributor3. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 is not on top of any other crate but is itself on crate0 and located at distributor2, crate2 is not on any other crate, crate3 is empty and situated at depot2. Depot0 is the location of crate2, depot1 houses hoist1, pallet1, and truck1, while depot2 is home to pallet2 and truck2. Distributor0 is where pallet3 can be found, distributor1 has pallet4, and hoist0 is available at depot0. Hoist1 is available for work at depot1, hoist2 is accessible at depot2, hoist3 is available at distributor0, and hoist4 is accessible at distributor1. Hoist5 is available at distributor2, and hoist6 is available for work at distributor3. Pallet0, which has crate2 on it, is at depot0, pallet1 is empty, pallet2 has crate3 on it, pallet3 is empty, pallet4 is clear of crates, pallet5 has crate0 on it and is at distributor2, pallet6 is empty and at distributor3, and truck0 is located at distributor2."}
{"question_id": "16759b80-0f16-425a-86e1-4d72565cda2e", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, at distributor0, hoist3 drops crate1 on pallet3, at distributor0, hoist3 unloads crate1 from truck0, at distributor1, hoist4 loads crate0 into truck2, crate0 is lifted from pallet4 at distributor1 by hoist4, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck0 at depot2, from depot0, truck2 is driven to distributor1 and truck0 is driven from depot2 to distributor0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at depot0, hoist0 will first remove crate2 from pallet0 and then load it into truck2, at distributor0, hoist3 will unload crate1 from truck0 and place it on pallet3, at distributor1, hoist4 will load crate0 into truck2 after lifting it from pallet4, meanwhile, at depot2, hoist2 will lift crate1 from pallet2 and load it into truck0, and finally, truck2 will travel from depot0 to distributor1 and truck0 will travel from depot2 to distributor0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1 is empty and positioned on pallet2. Crate2 is located at depot0, clear, and on pallet0. Crate3 is empty and situated at distributor2, on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated. Distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1 is available and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is accessible and located at distributor0. Hoist4 is accessible and situated at distributor1. Hoist5 is available and located at distributor2. Pallet0 is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2. Pallet3 is clear and located at distributor0. Pallet4 has crate0 on it and is situated at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2 and truck2 is at depot0."}
{"question_id": "a22d316b-37e1-461f-bc67-c105c393676d", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at distributor0, hoist2 unloads crate0 from truck2. Is the action: crate0 is unloaded by hoist2 from truck2 at distributor0 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled for steps 1 through 1: at distributor0, hoist2 is set to unload crate0 from truck2. Is the action: crate0 being unloaded by hoist2 from truck2 at distributor0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 is the location of both crate1 and crate2. Distributor0 is where hoist3 is located, and distributor1 is home to both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, hoist4 is available for work, and hoist5 is available and located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and situated at distributor2. Lastly, truck0 is at distributor0, and truck2 is located at depot0."}
{"question_id": "11fba00b-f45c-44c7-b8fd-5b05332f2f52", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: truck2 is driven to depot2 from depot0, hoist2 lifts crate3 from crate2 at depot2, hoist0 drops crate1 on crate2 at distributor2, at depot2, hoist2 lifts crate2 off crate1, at depot2, hoist2 loads crate2 into truck2, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3 and at distributor0, hoist3 loads crate0 into truck2. Is the action: hoist0 drops crate1 on crate2 at distributor2 executable at step 3, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled for steps 1 through 10: truck2 will travel from depot0 to depot2, hoist2 will lift crate3 off crate2 at depot2, hoist0 will place crate1 on top of crate2 at distributor2, at depot2, hoist2 will remove crate2 from crate1, at depot2, hoist2 will load crate2 onto truck2, at depot2, hoist2 will lift crate1 off pallet2, hoist2 will load crate1 into truck2 at depot2, truck2 will travel from depot2 to distributor0, at distributor0, hoist3 will lift crate0 off pallet3, and at distributor0, hoist3 will load crate0 into truck2. Is the action: hoist0 drops crate1 on crate2 at distributor2 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work. Hoist5 is available and is located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and is located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and is situated at distributor2. \n\nLastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "7748946d-b1d6-475d-9f3b-d4a4af2e06e5", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot3, hoist3 loads crate1 into truck2, at depot3, hoist3 unloads crate0 from truck2, at distributor0, hoist4 drops crate3 on pallet4, at distributor1, hoist5 drops crate2 on pallet5, at distributor1, hoist5 lifts crate3 off pallet5, at distributor1, hoist5 unloads crate2 from truck2, at distributor2, hoist6 loads crate0 into truck2, crate0 is lifted from pallet6 at distributor2 by hoist6, crate2 is lifted from pallet0 at depot0 by hoist0, crate3 is loaded by hoist5 into truck2 at distributor1, from depot1, truck2 is driven to depot0, from distributor2, truck2 is driven to depot3, hoist2 lifts crate0 from crate1 at depot1, hoist3 drops crate0 on pallet3 at depot3, hoist3 lifts crate1 from pallet3 at depot3, hoist4 unloads crate3 from truck2 at distributor0, truck2 is driven from depot3 to distributor0, truck2 is driven from distributor1 to distributor2 and truck2 is driven to distributor1 from depot0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at depot3, hoist3 is set to load crate1 onto truck2, then unload crate0 from truck2 at the same location. At distributor0, hoist4 is scheduled to place crate3 on pallet4. At distributor1, hoist5 will first drop crate2 onto pallet5, then lift crate3 off pallet5, and finally unload crate2 from truck2. At distributor2, hoist6 will load crate0 into truck2, and also lift crate0 from pallet6. Meanwhile, at depot0, hoist0 will lift crate2 from pallet0. At distributor1, hoist5 will load crate3 into truck2. The truck2 will be driven from depot1 to depot0, from distributor2 to depot3, from depot3 to distributor0, from distributor1 to distributor2, and from depot0 to distributor1. Additionally, at depot1, hoist2 will lift crate0 from crate1, and at depot3, hoist3 will first drop crate0 onto pallet3 and then lift crate1 from pallet3. At distributor0, hoist4 will also unload crate3 from truck2. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, is clear of any crates, and is positioned on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2, while distributor1 is where pallet5 is situated, and distributor2 is where pallet6 is located. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and is available. Hoist3 is available and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available and located at distributor1, and hoist6 is at distributor2 and available for work. Pallet0 is at depot0, pallet1 is clear of any crates and located at depot1, pallet2 is clear and situated at depot2, pallet3 is at depot3, pallet4 is clear and located at distributor0, and pallet5 has crate3 on it. Additionally, truck0 is at depot1, and truck1 is located at distributor0."}
{"question_id": "a0a49b00-fcdb-4e17-9612-7e0e54f4fbad", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, crate2 is dropped on pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, from depot0, truck2 is driven to distributor1, from depot1, truck2 is driven to depot0, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 unloads crate2 from truck2 at distributor1 and truck2 is driven from distributor1 to distributor2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at depot0, hoist0 will first lift crate2 from pallet0 and then load it into truck2, at depot0. Subsequently, at distributor1, hoist5 will unload crate2 from truck2 and place it on pallet5, and then load crate3 into truck2. The plan also involves driving truck2 from depot0 to distributor1 and then from distributor1 back to depot0. Additionally, hoist3 will lift crate1 from pallet3 at depot3, and hoist5 will lift crate3 from pallet5 at distributor1. Finally, truck2 will be driven from distributor1 to distributor2. Is the execution of this plan feasible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, and it is clear of any crates, resting on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2, while distributor1 is where pallet5 is situated, and distributor2 is where pallet6 is located. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and is available. Hoist3 is available and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available and located at distributor1, and hoist6 is at distributor2 and available for work. Pallet0 is at depot0, pallet1 is clear and located at depot1, pallet2 is clear and situated at depot2, pallet3 is at depot3, pallet4 is clear and located at distributor0, and pallet5 has crate3 on it. Additionally, truck0 is at depot1, and truck1 is located at distributor0."}
{"question_id": "bdd3ca8a-efd5-4b28-8077-9b50b5783558", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from depot0, truck2 is driven to depot2. Is the action: from depot0, truck2 is driven to depot2 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled for steps 1 through 1: truck2 is to be driven from depot0 to depot2. Is the action of driving truck2 from depot0 to depot2 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, hoist4 is available for work, and hoist5 is available and located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and situated at distributor2. Lastly, truck0 is at distributor0, and truck2 is located at depot0."}
{"question_id": "f4feaf17-49cd-4b45-970a-75bd320ea385", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: truck1 is driven from depot1 to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, crate1 is loaded by hoist6 into truck0 at depot1 and at distributor3, hoist6 unloads crate3 from truck2. Is the action: at depot1, hoist6 loads crate1 into truck0 executable at step 9, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck1 at depot0, truck1 proceeds from depot0 to depot1, upon arrival at depot1, hoist1 unloads crate2 from truck1 and places it on pallet1, meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, also, hoist6 loads crate1 into truck0 at depot1 and unloads crate3 from truck2 at distributor3. Is the action of hoist6 loading crate1 into truck0 at depot1 executable at step 9, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 is not on top of any other crate but is itself on crate0 and located at distributor2, crate2 is not on any other crate, crate3 is empty and situated at depot2. Depot0 is the location of crate2, depot1 houses hoist1, pallet1, and truck1, while depot2 is home to pallet2 and truck2. Distributor0 is where pallet3 can be found, distributor1 has pallet4, hoist0 is available and located at depot0, hoist1 is available for work and situated at depot1, hoist2 is available and at depot2, hoist3 is accessible and at distributor0, hoist4 is accessible and at distributor1, hoist5 is available and at distributor2, and hoist6 is available for work and at distributor3. Pallet0 is at depot0 with crate2 on it, pallet1 is empty, pallet2 has crate3 on it and is at depot2, pallet3 is empty, pallet4 is empty, pallet5 has crate0 on it and is at distributor2, pallet6 is empty and at distributor3, and truck0 is at distributor2."}
{"question_id": "26485c60-e06b-4b19-817c-446355cba049", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3, hoist6 unloads crate3 from truck2 at distributor3, hoist6 drops crate3 on pallet6 at distributor3, hoist5 lifts crate1 from crate0 at distributor2, hoist5 loads crate1 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, crate0 is loaded by hoist5 into truck0 at distributor2, at distributor2, hoist5 unloads crate1 from truck0, truck0 is driven from distributor2 to distributor0, crate0 is unloaded by hoist3 from truck0 at distributor0 and at distributor0, hoist3 drops crate0 on pallet3. Is the action: hoist1 unloads crate2 from truck1 at depot1 executable at step 5, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: truck1 is driven from depot1 to depot0, where hoist0 unloads crate2 from pallet0 and loads it into truck1, then truck1 is driven back to depot1. At depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, loads it into truck2, and truck2 is driven to distributor3. At distributor3, hoist6 unloads crate3 from truck2 and places it on pallet6. At distributor2, hoist5 lifts crate1 from crate0, loads it into truck0, lifts crate0 from pallet5, and loads it into truck0. Then, hoist5 unloads crate1 from truck0 at distributor2, truck0 is driven to distributor0, and hoist3 unloads crate0 from truck0 and places it on pallet3 at distributor0. Is the action: hoist1 unloads crate2 from truck1 at depot1 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 is not on top of any other crate but is itself on crate0 and located at distributor2, crate2 is not on any other crate, crate3 is empty and situated at depot2. Depot0 is the location of crate2, depot1 houses hoist1, pallet1, and truck1, while depot2 is home to pallet2 and truck2. Distributor0 contains pallet3, distributor1 has pallet4, hoist0 is available and located at depot0, hoist1 is available for work and situated at depot1, hoist2 is available and at depot2, hoist3 is accessible and at distributor0, hoist4 is accessible and at distributor1, hoist5 is available and at distributor2, and hoist6 is available for work and at distributor3. Pallet0 is at depot0 with crate2 on it, pallet1 is empty and at depot1, pallet2 has crate3 on it and is at depot2, pallet3 is empty and at distributor0, pallet4 is empty and at distributor1, pallet5 has crate0 on it and is at distributor2, pallet6 is empty and at distributor3, and truck0 is at distributor2."}
{"question_id": "42fd4288-a21e-4dd3-8468-4f026822bd87", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: hoist2 drops crate2 on pallet3 at distributor2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: hoist2 will place crate2 on pallet3 at distributor2. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, and it is clear of any crates, resting on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2, while distributor1 is where pallet5 is situated, and distributor2 is where pallet6 is located. Hoist0 is available for work and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and is available. Hoist3 is available and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available and located at distributor1, and hoist6 is at distributor2 and available for work. Pallet0 is at depot0, pallet1 is clear and located at depot1, pallet2 is clear and situated at depot2, pallet3 is at depot3, pallet4 is clear and located at distributor0, and pallet5 has crate3 on it. Truck0 is at depot1, and truck1 is located at distributor0."}
{"question_id": "9fca7e80-e714-4db9-94ec-3ea0ed83a1bc", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at distributor0, hoist5 loads crate0 into truck1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: at distributor0, hoist5 is to load crate0 onto truck1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, whereas distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work at distributor1. Hoist5 is available and located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates and is situated at distributor1. Pallet5 is also clear and located at distributor2. Lastly, truck0 is at distributor0, and truck2 is situated at depot0."}
{"question_id": "f8441920-021d-453f-bfec-bd81d7adf1ab", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot1, hoist1 drops crate0 on pallet1, at depot2, hoist2 lifts crate1 off pallet2, at distributor0, hoist3 drops crate1 on pallet3, at distributor0, hoist3 unloads crate1 from truck0, at distributor1, hoist4 unloads crate3 from truck1, at distributor2, hoist5 loads crate3 into truck1, crate1 is loaded by hoist2 into truck0 at depot2, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is unloaded by hoist4 from truck2 at distributor1, from distributor2, truck1 is driven to distributor1, hoist0 loads crate2 into truck2 at depot0, hoist1 unloads crate0 from truck2 at depot1, hoist4 drops crate3 on pallet4 at distributor1, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, hoist5 lifts crate3 from pallet5 at distributor2, truck0 is driven from depot2 to distributor0, truck2 is driven from depot0 to distributor1 and truck2 is driven from distributor1 to depot1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: at depot1, hoist1 will place crate0 onto pallet1, at depot2, hoist2 will pick up crate1 from pallet2, at distributor0, hoist3 will place crate1 onto pallet3 and then unload crate1 from truck0, at distributor1, hoist4 will unload crate3 from truck1, at distributor2, hoist5 will load crate3 into truck1, crate1 will be loaded by hoist2 into truck0 at depot2, crate2 will be lifted from pallet0 at depot0 by hoist0, crate2 will be unloaded by hoist4 from truck2 at distributor1, truck1 will be driven from distributor2 to distributor1, hoist0 will load crate2 into truck2 at depot0, hoist1 will unload crate0 from truck2 at depot1, hoist4 will place crate3 onto pallet4 at distributor1, hoist4 will lift crate0 from pallet4 at distributor1, hoist4 will load crate0 into truck2 at distributor1, hoist5 will lift crate3 from pallet5 at distributor2, truck0 will be driven from depot2 to distributor0, truck2 will be driven from depot0 to distributor1 and then from distributor1 to depot1. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 and hoist4 are both accessible, with hoist4 located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1, clear of any crates, is situated at depot1. Pallet2 is located at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2, and truck2 is at depot0."}
{"question_id": "912900d5-a957-45aa-b00e-898fb35045b1", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, at depot3, hoist3 lifts crate1 off pallet3, at distributor0, hoist4 unloads crate3 from truck2, at distributor1, hoist5 loads crate3 into truck2, at distributor2, hoist6 lifts crate0 off pallet6, at distributor2, hoist6 loads crate0 into truck2, crate0 is dropped on pallet3 at depot3 by hoist3, crate0 is unloaded by hoist3 from truck2 at depot3, crate2 is loaded by hoist0 into truck2 at depot0, crate2 is unloaded by hoist5 from truck2 at distributor1, crate3 is dropped on pallet4 at distributor0 by hoist4, crate3 is lifted from pallet5 at distributor1 by hoist5, from distributor2, truck2 is driven to depot3, hoist3 loads crate1 into truck2 at depot3, hoist5 drops crate2 on pallet5 at distributor1, truck2 is driven from depot0 to distributor1, truck2 is driven from distributor1 to distributor2, truck2 is driven to depot0 from depot1 and truck2 is driven to distributor0 from depot3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at depot0, hoist0 will remove crate2 from pallet0, at depot3, hoist3 will remove crate1 from pallet3, at distributor0, hoist4 will unload crate3 from truck2, at distributor1, hoist5 will load crate3 onto truck2, at distributor2, hoist6 will remove crate0 from pallet6, at distributor2, hoist6 will load crate0 onto truck2, hoist3 will place crate0 on pallet3 at depot3, hoist3 will unload crate0 from truck2 at depot3, hoist0 will load crate2 onto truck2 at depot0, hoist5 will unload crate2 from truck2 at distributor1, hoist4 will place crate3 on pallet4 at distributor0, hoist5 will remove crate3 from pallet5 at distributor1, truck2 will travel from distributor2 to depot3, hoist3 will load crate1 onto truck2 at depot3, hoist5 will place crate2 on pallet5 at distributor1, truck2 will travel from depot0 to distributor1, from distributor1 to distributor2, from depot0 to depot1, and from depot3 to distributor0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, is clear of any crates, and is placed on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2. Pallet5 is situated at distributor1, while pallet6 is located at distributor2. Hoist0 is available for work and is situated at depot0. Hoist1 is available and can be found at depot1. Hoist2 is located at depot2 and is available, while hoist3 is available and situated at depot3. Hoist4 is available and located at distributor0, and hoist5 is available and situated at distributor1. Hoist6 is available for work and is located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and is located at depot1. Pallet2 is clear and situated at depot2. Pallet3 is located at depot3. Pallet4 is clear and situated at distributor0. Pallet5 has crate3 on it. Truck0 is situated at depot1, and truck1 is located at distributor0."}
{"question_id": "1f529cca-aba4-4b90-b8ed-22ea78bec9f8", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: crate2 is dropped on crate0 at distributor1 by hoist4, at depot2, hoist2 lifts crate3 off crate2, at depot2, hoist2 loads crate3 into truck2, crate2 is lifted from crate1 at depot2 by hoist2, hoist2 loads crate2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven to distributor0 from depot2, hoist3 lifts crate0 from pallet3 at distributor0, crate0 is loaded by hoist3 into truck2 at distributor0, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven to distributor1 from distributor0, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, at distributor2, hoist5 unloads crate3 from truck2, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, hoist5 drops crate3 on pallet5 at distributor2 and hoist5 unloads crate0 from truck2 at distributor2. Is the action: at distributor1, hoist4 drops crate2 on crate0 executable at step 1, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at distributor1, hoist4 is set to drop crate2 onto crate0, at depot2, hoist2 is scheduled to lift crate3 off crate2, then at depot2, hoist2 will load crate3 into truck2, hoist2 will lift crate2 from crate1 at depot2, hoist2 will load crate2 into truck2 at depot2, at depot2, hoist2 will lift crate1 off pallet2, then at depot2, hoist2 will load crate1 into truck2, truck2 will be driven from depot2 to distributor0, hoist3 will lift crate0 from pallet3 at distributor0, hoist3 will load crate0 into truck2 at distributor0, at distributor0, hoist3 will unload crate1 from truck2, truck2 will be driven from distributor0 to distributor1, crate2 will be unloaded by hoist4 from truck2 at distributor1, truck2 will be driven from distributor1 to distributor2, at distributor2, hoist5 will unload crate3 from truck2, hoist3 will drop crate1 onto pallet3 at distributor0, hoist4 will drop crate2 onto pallet4 at distributor1, hoist5 will drop crate3 onto pallet5 at distributor2, and hoist5 will unload crate0 from truck2 at distributor2. Is the action: at distributor1, hoist4 drops crate2 on crate0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, whereas distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work. Hoist5 is available and is located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and is located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and is situated at distributor2. \n\nLastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "2b565958-af99-4f94-ae27-46a926f3d122", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: truck2 is driven to depot0 from depot1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on top of pallet6, crate1 is clear of any crates, crate1 is located at depot3, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on pallet0, crate3 is at distributor1, crate3 is clear of any crates, depot1 is where truck2 is located, distributor1 is where pallet5 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available, hoist3 is available, hoist3 is located at depot3, hoist4 is available, hoist4 is located at distributor0, hoist5 is available, hoist5 is located at distributor1, hoist6 is at distributor2, hoist6 is available for work, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is clear, pallet2 is located at depot2, pallet3 is at depot3, pallet4 is clear, pallet4 is located at distributor0, pallet5 has crate3 on it, truck0 is at depot1 and truck1 can be found located at distributor0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: truck2 will be moved from depot1 to depot0. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, and it is clear, with crate0 positioned on top of pallet6. Crate1, which is clear of any other crates, is located at depot3 and is on top of pallet3. Crate2 can be found at depot0, is clear of any crates, and is placed on pallet0. Crate3 is located at distributor1 and is clear of any crates. Depot1 is the location of truck2. Pallet5 is situated at distributor1, while pallet6 is located at distributor2. Hoist0 is available for work and is situated at depot0. Hoist1 is available and can be found at depot1. Hoist2 is located at depot2 and is available, while hoist3 is available and situated at depot3. Hoist4 is available and located at distributor0, and hoist5 is available and situated at distributor1. Hoist6 is available for work and is located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and is located at depot1. Pallet2 is clear and situated at depot2. Pallet3 is located at depot3. Pallet4 is clear and situated at distributor0. Pallet5 has crate3 on it. Truck0 is located at depot1, and truck1 can be found at distributor0."}
{"question_id": "ff52e00c-9f0e-46fe-8283-cd675f33d37a", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0. Is the action: hoist0 lifts crate2 from pallet0 at depot0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled for steps 1 through 1: at depot0, hoist0 is set to lift crate2 off pallet0. Is the action: hoist0 lifting crate2 from pallet0 at depot0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 and hoist4 are both accessible, with hoist4 located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1, clear of any crates, is situated at depot1. Pallet2 is located at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2, and truck2 is at depot0."}
{"question_id": "78c16190-57f2-4ddc-8cea-1d8e2c4ed9df", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: from depot0, truck2 is driven to depot2, at depot2, hoist2 lifts crate3 off crate2, crate3 is loaded by hoist2 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck2 at depot2, truck2 is driven to distributor0 from depot2, at distributor0, hoist3 lifts crate0 off pallet3, at distributor0, hoist3 loads crate0 into truck2, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven to distributor1 from distributor0, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1, at distributor2, hoist5 unloads crate3 from truck2, hoist3 drops crate1 on pallet3 at distributor0, crate2 is dropped on pallet4 at distributor1 by hoist4, hoist5 drops crate3 on pallet5 at distributor2 and hoist5 unloads crate0 from truck2 at distributor2. Is the action: crate2 is unloaded by hoist4 from truck2 at distributor1 executable at step 13, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: starting from depot0, truck2 is driven to depot2, where hoist2 performs the following tasks: it lifts crate3 off crate2, loads crate3 into truck2, lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2. Then, truck2 is driven to distributor0, where hoist3 performs the following tasks: it lifts crate0 off pallet3 and loads crate0 into truck2, and unloads crate1 from truck2. Next, truck2 is driven to distributor1, where hoist4 unloads crate2 from truck2, and then to distributor2, where hoist5 unloads crate3 from truck2. Additionally, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, and hoist5 drops crate3 on pallet5 and unloads crate0 from truck2 at distributor2. Is the action: crate2 is unloaded by hoist4 from truck2 at distributor1 executable at step 13, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, and hoist4 is available for work. Hoist5 is available and is located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and is located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and is situated at distributor2. \n\nLastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "f15d4088-8f40-4e97-8e7e-cdcd945a33d6", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: truck1 is driven to depot0 from depot1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: truck1 will move from depot1 to depot0. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 is not on top of any other crate but is itself on crate0 and located at distributor2, crate2 is not on any other crate, crate3 is empty and situated at depot2. Depot0 is the location of crate2, depot1 houses hoist1, pallet1, and truck1, while depot2 is home to pallet2 and truck2. Distributor0 contains pallet3, distributor1 has pallet4, and hoist0 is available at depot0. Hoist1 is available for work at depot1, hoist2 is available and located at depot2, hoist3 is accessible at distributor0, and hoist4 is accessible at distributor1. Hoist5 is available at distributor2, and hoist6 is available for work at distributor3. Pallet0, which has crate2 on it, is at depot0, pallet1 is empty, pallet2 has crate3 on it, pallet3 is empty, pallet4 is clear of any crates, pallet5 has crate0 on it and is at distributor2, pallet6 is empty and at distributor3, and truck0 is located at distributor2."}
{"question_id": "3980e777-5b14-4462-9af1-cdf22f19fe1f", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, crate1 is dropped on crate1 at depot2 by hoist4, crate2 is dropped on pallet1 at depot1 by hoist1, from depot0, truck1 is driven to depot1, from depot1, truck1 is driven to depot0, from depot2, truck2 is driven to distributor3, hoist0 loads crate2 into truck1 at depot0, hoist2 lifts crate3 from pallet2 at depot2, hoist2 loads crate3 into truck2 at depot2 and hoist6 unloads crate3 from truck2 at distributor3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: at depot0, hoist0 will pick up crate2 from pallet0, hoist4 will place crate1 on crate1 at depot2, hoist1 will place crate2 on pallet1 at depot1, truck1 will travel from depot0 to depot1 and then back to depot0, truck2 will travel from depot2 to distributor3, hoist0 will load crate2 into truck1 at depot0, hoist2 will lift crate3 from pallet2 at depot2 and load it into truck2, and finally, hoist6 will unload crate3 from truck2 at distributor3. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 is not on top of any other crate but is itself on crate0 and located at distributor2, crate2 is not on any other crate, crate3 is empty and situated at depot2. Depot0 is the location of crate2, depot1 houses hoist1, pallet1, and truck1, while depot2 is home to pallet2 and truck2. Distributor0 contains pallet3, distributor1 has pallet4, and hoist0 is available at depot0. Hoist1 is available for work at depot1, hoist2 is available at depot2, hoist3 is accessible at distributor0, and hoist4 is accessible at distributor1. Hoist5 is available at distributor2, and hoist6 is available for work at distributor3. Pallet0, which has crate2 on it, is at depot0, pallet1 is empty, pallet2 has crate3 on it, pallet3 is empty, pallet4 is clear of any crates, pallet5 has crate0 on it and is at distributor2, pallet6 is empty and at distributor3, and truck0 is located at distributor2."}
{"question_id": "a3b59fcc-b3ed-4582-885d-d258a2be4da3", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: crate1 is lifted from pallet3 at distributor0 by hoist2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: hoist2 will lift crate1 from pallet3 at distributor0. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, and it is clear. Crate1 is also clear of any crates and is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3 is clear of any crates, situated at distributor2, and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1 is available and can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 is accessible, and hoist4 is also accessible, with the latter located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and is situated at depot1. Pallet2 is located at depot2. Pallet3 is at distributor0 and is clear. Pallet4 has crate0 on it and is situated at distributor1. Pallet5 is located at distributor2. Truck0 is situated at depot2, and truck2 is at depot0."}
{"question_id": "8fa30e66-34e0-455b-8e5f-562af555fba1", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet2 at depot2 by hoist2, at distributor2, hoist1 lifts crate0 off pallet4, truck0 is driven to distributor0 from depot2, hoist3 unloads crate1 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, hoist4 lifts crate0 from pallet4 at distributor1 and crate0 is loaded by hoist4 into truck2 at distributor1. Is the action: crate0 is lifted from pallet4 at distributor2 by hoist1 executable at step 5, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled for steps 1 through 10: at depot0, hoist0 first lifts crate2 off pallet0 and then loads it into truck2, after which truck2 travels from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 off pallet2, and at distributor2, hoist1 lifts crate0 off pallet4. Additionally, truck0 is driven from depot2 to distributor0, where hoist3 unloads crate1 from truck0 and places it on pallet3. Furthermore, at distributor1, hoist4 lifts crate0 off pallet4 and loads it into truck2. Is the action: crate0 being lifted from pallet4 at distributor2 by hoist1 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 and hoist4 are both accessible, with hoist4 located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1, clear of any crates, is situated at depot1. Pallet2 is located at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2, and truck2 is at depot0."}
{"question_id": "eb238c77-8778-4682-966a-da3be0fb3b9e", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: truck1 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven from depot0 to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor3 and crate3 is unloaded by hoist6 from truck2 at distributor3. Is the action: from depot0, truck1 is driven to depot1 executable at step 4, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled for steps 1 through 10: truck1 travels from depot1 to depot0, at depot0, hoist0 unloads crate2 from pallet0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven from depot0 back to depot1, at depot1, hoist1 unloads crate2 from truck1, hoist1 places crate2 on pallet1 at depot1, meanwhile, hoist2 lifts crate3 from pallet2 at depot2, crate3 is then loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor3, and finally, crate3 is unloaded by hoist6 from truck2 at distributor3. Is the action: driving truck1 from depot0 to depot1 executable at step 4, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 has no crates on it, crate1 is situated at distributor2, crate1 is placed on crate0, crate2 has no crates on it, crate3 is empty, crate3 is situated at depot2, depot0 is the location of crate2, depot1 is the location of hoist1, depot1 is the location of pallet1, depot1 is the location of truck1, depot2 is the location of pallet2, depot2 is the location of truck2, distributor0 is the location of pallet3, distributor1 is the location of pallet4, hoist0 is situated at depot0, hoist0 is available for use, hoist1 is available for work, hoist2 is located at depot2, hoist2 is available for use, hoist3 is accessible and located at distributor0, hoist4 is accessible and located at distributor1, hoist5 is located at distributor2, hoist5 is available for use, hoist6 is situated at distributor3, hoist6 is available for work, pallet0 is situated at depot0, pallet0 has crate2 on it, pallet1 is empty, pallet2 has crate3 on it, pallet3 is empty, pallet4 has no crates on it, pallet5 has crate0 on it, pallet5 is situated at distributor2, pallet6 is situated at distributor3, pallet6 is empty and truck0 is situated at distributor2."}
{"question_id": "507661d5-260e-4005-8272-37b2a8fd07f7", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: hoist1 lifts crate0 from pallet2 at depot0. Is the action: hoist1 lifts crate0 from pallet2 at depot0 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is at distributor2, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 is clear of any crates, crate3 is clear, crate3 is located at depot2, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck1 is located, depot2 is where pallet2 is located, depot2 is where truck2 is located, distributor0 is where pallet3 is located, distributor1 is where pallet4 is located, hoist0 is at depot0, hoist0 is available, hoist1 is available for work, hoist2 can be found located at depot2, hoist2 is available, hoist3 is accessible, hoist3 is located at distributor0, hoist4 is accessible, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, hoist6 is at distributor3, hoist6 is available for work, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is clear, pallet2 has crate3 on it, pallet3 is clear, pallet4 is clear of any crates, pallet5 has crate0 on it, pallet5 is at distributor2, pallet6 is at distributor3, pallet6 is clear and truck0 can be found located at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include: hoist1 lifts crate0 from pallet2 at depot0. Is the action of hoist1 lifting crate0 from pallet2 at depot0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate1 is not on top of any other crate but is itself on crate0 and located at distributor2, crate2 is not on any other crate, crate3 is empty and situated at depot2. Depot0 is the location of crate2, depot1 houses hoist1, pallet1, and truck1, while depot2 is home to pallet2 and truck2. Distributor0 contains pallet3, distributor1 has pallet4, hoist0 is available and located at depot0, hoist1 is available for work and situated at depot1, hoist2 is available and found at depot2, hoist3 is accessible and located at distributor0, hoist4 is accessible and situated at distributor1, hoist5 is available and located at distributor2, and hoist6 is available for work and at distributor3. Pallet0 is at depot0 with crate2 on it, pallet1 is empty and at depot1, pallet2 has crate3 on it and is at depot2, pallet3 is empty and at distributor0, pallet4 is empty and at distributor1, pallet5 has crate0 on it and is at distributor2, pallet6 is empty and at distributor3, and truck0 is located at distributor2."}
{"question_id": "91b58386-137a-4b14-a374-9cf3cb509aeb", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at depot2, hoist1 drops crate2 on pallet3, at depot2, hoist2 lifts crate2 off crate1, crate0 is loaded by hoist3 into truck2 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate1 is lifted from pallet2 at depot2 by hoist2, crate2 is unloaded by hoist4 from truck2 at distributor1, crate3 is loaded by hoist2 into truck2 at depot2, crate3 is unloaded by hoist5 from truck2 at distributor2, from depot0, truck2 is driven to depot2, hoist2 lifts crate3 from crate2 at depot2, hoist2 loads crate1 into truck2 at depot2, hoist3 lifts crate0 from pallet3 at distributor0, hoist3 unloads crate1 from truck2 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, hoist5 drops crate3 on pallet5 at distributor2, hoist5 unloads crate0 from truck2 at distributor2, truck2 is driven from depot2 to distributor0, truck2 is driven from distributor1 to distributor2 and truck2 is driven to distributor1 from distributor0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 has crate2 on it, crate1 is on top of pallet2, crate3 is at depot2, crate3 is clear, crate3 is on crate2, depot0 is where hoist0 is located, depot1 is where truck1 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, distributor1 is where hoist4 is located, distributor1 is where pallet4 is located, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, pallet0 is clear, pallet0 is located at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet5 is clear of any crates, pallet5 is located at distributor2, truck0 is at distributor0 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: at depot2, hoist1 will place crate2 onto pallet3, at depot2, hoist2 will pick up crate2 from crate1, hoist3 will load crate0 into truck2 at distributor0, hoist3 will place crate1 onto pallet3 at distributor0, hoist2 will lift crate1 from pallet2 at depot2, hoist4 will unload crate2 from truck2 at distributor1, hoist2 will load crate3 into truck2 at depot2, hoist5 will unload crate3 from truck2 at distributor2, truck2 will be driven from depot0 to depot2, hoist2 will lift crate3 from crate2 at depot2, hoist2 will load crate1 into truck2 at depot2, hoist3 will lift crate0 from pallet3 at distributor0, hoist3 will unload crate1 from truck2 at distributor0, hoist4 will place crate2 onto pallet4 at distributor1, hoist5 will place crate3 onto pallet5 at distributor2, hoist5 will unload crate0 from truck2 at distributor2, truck2 will be driven from depot2 to distributor0, truck2 will be driven from distributor1 to distributor2, and truck2 will be driven from distributor0 to distributor1. Is it possible to execute this plan, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, and it is not obstructed by any other crates. Additionally, crate0 is positioned on pallet3. Crate1, on the other hand, has crate2 placed on top of it, and crate1 is situated on pallet2. Crate3 is located at depot2, is clear of any obstructions, and is placed on crate2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Depot2 serves as the location for both crate1 and crate2. Distributor0 is home to hoist3, and distributor1 houses both hoist4 and pallet4. \n\nHoist0 is available for work and is located at depot0. Hoist1 is situated at depot1 and is available for use. Hoist2 is also available and can be found at depot2. Hoist3 is accessible, hoist4 is available for work, and hoist5 is available and located at distributor2.\n\nPallet0 is clear of any obstructions and is situated at depot0. Pallet1 is also clear and located at depot1. Pallet2 is situated at depot2, while pallet3 is located at distributor0. Pallet4 is clear of any crates, and pallet5 is also clear and situated at distributor2. Lastly, truck0 is located at distributor0, and truck2 is situated at depot0."}
{"question_id": "775dc80d-2b55-4669-b392-7ca8e05aa2c9", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: crate1 is dropped on crate2 at depot1 by hoist5. Is the action: hoist5 drops crate1 on crate2 at depot1 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear, crate1 is clear of any crates, crate1 is on pallet2, crate2 is at depot0, crate2 is clear, crate2 is on pallet0, crate3 is clear of any crates, crate3 is located at distributor2, crate3 is on top of pallet5, depot2 is where crate1 is located, distributor0 is where hoist3 is located, distributor2 is where truck1 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available for work, hoist2 is located at depot2, hoist3 is accessible, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is at distributor0, pallet3 is clear, pallet4 has crate0 on it, pallet4 is at distributor1, pallet5 can be found located at distributor2, truck0 can be found located at depot2 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: hoist5 will place crate1 on top of crate2 at depot1. Is the action: hoist5 drops crate1 on crate2 at depot1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1 and is clear. Crate1, which is also clear of any crates, is positioned on pallet2. Meanwhile, crate2 is located at depot0, is clear, and rests on pallet0. Crate3, clear of any crates, is situated at distributor2 and is placed on top of pallet5. Depot2 is the location of crate1. Distributor0 is where hoist3 is situated, while distributor2 is where truck1 is located. Hoist0 is available and located at depot0. Hoist1, also available, can be found at depot1. Hoist2 is available for work and is situated at depot2. Hoist3 and hoist4 are both accessible, with hoist4 located at distributor1. Hoist5 is available and situated at distributor2. Pallet0 is located at depot0. Pallet1, clear of any crates, is situated at depot1. Pallet2 is located at depot2. Pallet3, clear of any crates, is at distributor0. Pallet4, which has crate0 on it, is at distributor1. Pallet5 is located at distributor2. Truck0 is situated at depot2, and truck2 is at depot0."}
