{"question_id": "37e587fe-bf44-4aca-94be-48e41ae5970d", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves from room1 to room5, lgripper1 of robot1 drops ball1 in room5, rgripper1 of robot1 drops ball3 in room5 and robot1 moves from room5 to room2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not at room1, ball1 is not at room3, ball1 is not at room4, ball1 is not located at room2, ball1 is not present at room6, ball2 is not at room4, ball2 is not located at room1, ball2 is not located at room6, ball2 is not present at room3, ball2 is not present at room5, ball3 is not at room1, ball3 is not at room2, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room3, ball3 is not located at room6, ball3 is not present at room4, ball4 is not at room3, ball4 is not located at room1, ball4 is not located at room4, ball4 is not located at room5, ball4 is not present at room6, ball5 is not at room1, ball5 is not at room6, ball5 is not located at room2, ball5 is not located at room4, ball5 is not present at room5, ball6 is not at room5, ball6 is not at room6, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room2, ball6 is not located at room3, ball6 is not located at room4, ball7 is not at room2, ball7 is not at room3, ball7 is not located at room6, ball7 is not present at room1, ball7 is not present at room4, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball4, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room4, robot1 is not present in room1, robot1 is not present in room5 and robot1 is not present in room6. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1's lgripper1 picks up ball7 in room4, then robot1 moves to room5 from room4, drops ball7 in room5 using lgripper1, and moves to room1 from room5. In room1, robot1's lgripper1 picks up ball1 and rgripper1 of robot1 picks up ball3. Then, robot1 moves from room1 to room5, drops ball1 using lgripper1 and ball3 using rgripper1 in room5, and finally moves from room5 to room2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not in room1, ball1 is not in room3, ball1 is not in room4, ball1 is not in room2, ball1 is not in room6, ball2 is not in room4, ball2 is not in room1, ball2 is not in room6, ball2 is not in room3, ball2 is not in room5, ball3 is not in room1, ball3 is not in room2, ball3 is not being held by robot1's lgripper1, ball3 is not in room3, ball3 is not in room6, ball3 is not in room4, ball4 is not in room3, ball4 is not in room1, ball4 is not in room4, ball4 is not in room5, ball4 is not in room6, ball5 is not in room1, ball5 is not in room6, ball5 is not in room2, ball5 is not in room4, ball5 is not in room5, ball6 is not in room5, ball6 is not in room6, ball6 is not being held by robot1's rgripper1, ball6 is not in room2, ball6 is not in room3, ball6 is not in room4, ball7 is not in room2, ball7 is not in room3, ball7 is not in room6, ball7 is not in room1, ball7 is not in room4, robot1's lgripper1 is not holding ball2, robot1's lgripper1 is not holding ball5, robot1's rgripper1 is not holding ball4, robot1 is not in room3, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball5 with rgripper1, robot1 is not holding ball6 with lgripper1, robot1 is not holding ball7 with lgripper1, robot1 is not holding ball7 with rgripper1, robot1 is not in room4, robot1 is not in room1, robot1 is not in room5, and robot1 is not in room6. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "7845a87a-d244-4c46-9036-18d8fa93afe0", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is at room1, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not present at room2, ball1 is not present at room5, ball1 is present at room4, ball2 is at room3, ball2 is at room5, ball2 is located at room1, ball2 is not being carried by robot1's rgripper1, ball2 is present at room2, ball3 is at room3, ball3 is at room4, ball3 is being carried by robot1's rgripper1, ball3 is located at room2, ball3 is not located at room5, ball4 is being carried by robot1's rgripper1, ball4 is not at room2, ball4 is not at room5, ball4 is not located at room1, ball4 is not located at room4, ball4 is present at room3, ball5 is being carried by robot1's rgripper1, ball5 is located at room5, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not present at room1, ball5 is not present at room4, ball6 is not at room2, ball6 is not present at room4, ball6 is not present at room5, ball6 is present at room3, rgripper1 of robot1 is not carrying ball6, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball6 with lgripper1, robot1 is located at room3, robot1 is located at room4, robot1 is located at room5, robot1 is not carrying ball3 with lgripper1, robot1 is not located at room1 and robot1's lgripper1 is not free. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1, robot1 uses lgripper1 to pick up ball1 from room1, then moves from room1 to room2, uses rgripper1 to pick up ball2 from room2, moves from room2 to room3, drops ball1 in room3 using lgripper1, moves from room3 to room4, drops ball2 in room4 using rgripper1, moves from room4 to room2, and picks up ball4 from room2 using lgripper1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is in room1, ball1 is not held by robot1's lgripper1, ball1 is not held by robot1's rgripper1, ball1 is not in room2, ball1 is not in room5, ball1 is in room4, ball2 is in room3, ball2 is in room5, ball2 is in room1, ball2 is not held by robot1's rgripper1, ball2 is in room2, ball3 is in room3, ball3 is in room4, ball3 is held by robot1's rgripper1, ball3 is in room2, ball3 is not in room5, ball4 is held by robot1's rgripper1, ball4 is not in room2, ball4 is not in room5, ball4 is not in room1, ball4 is not in room4, ball4 is in room3, ball5 is held by robot1's rgripper1, ball5 is in room5, ball5 is not in room3, ball5 is not held by robot1's lgripper1, ball5 is not in room1, ball5 is not in room4, ball6 is not in room2, ball6 is not in room4, ball6 is not in room5, ball6 is in room3, robot1's rgripper1 is not holding ball6, robot1 is holding ball2 with lgripper1, robot1 is holding ball6 with lgripper1, robot1 is in room3, robot1 is in room4, robot1 is in room5, robot1 is not holding ball3 with lgripper1, robot1 is not in room1, and robot1's lgripper1 is not free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "699e655f-c688-4b9c-bd4b-60a9c7548d38", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is not located at room1, ball2 is located at room2, ball3 is not located at room1, ball4 is located at room2, ball5 is not located at room2, ball6 is at room1, ball7 is not at room3, robot1 is located at room1, robot1's lgripper1 is not free and robot1's rgripper1 is free. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, are all of the following properties of the state that do not involve negations valid? ball1 is not in room1, ball2 is in room2, ball3 is not in room1, ball4 is in room2, ball5 is not in room2, ball6 is in room1, ball7 is not in room3, robot1 is in room1, robot1's left gripper is not free and robot1's right gripper is free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "58d4d412-96a7-4c38-9c2d-7e5ad6a3b2b0", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, in room5, robot1's lgripper1 drops ball1, in room5, robot1's rgripper1 drops ball3 and robot1 moves from room5 to room2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is not at room2, ball1 is not at room3, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room4, ball1 is not located at room6, ball1 is not present at room1, ball1 is present at room5, ball2 is at room2, ball2 is not at room1, ball2 is not at room6, ball2 is not present at room3, ball2 is not present at room4, ball2 is not present at room5, ball3 is at room5, ball3 is not at room1, ball3 is not at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room4, ball3 is not located at room6, ball4 is located at room2, ball4 is not at room1, ball4 is not at room5, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room6, ball4 is not present at room3, ball4 is not present at room4, ball5 is not at room1, ball5 is not at room2, ball5 is not at room4, ball5 is not located at room6, ball5 is not present at room5, ball5 is present at room3, ball6 is not at room2, ball6 is not at room3, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room4, ball6 is not located at room6, ball6 is not present at room5, ball6 is present at room1, ball7 is at room5, ball7 is not at room3, ball7 is not at room6, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room1, ball7 is not located at room2, ball7 is not located at room4, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball5, robot1 is not at room4, robot1 is not at room6, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room3, robot1 is not located at room5, robot1 is not present in room1, robot1 is present in room2 and robot1's lgripper1 is free. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves to room5, where it drops ball7, and subsequently moves to room1. In room1, robot1's lgripper1 picks up ball1 and its right gripper (rgripper1) picks up ball3. Then, robot1 moves to room5, where it drops ball1 with its lgripper1 and ball3 with its rgripper1, and finally moves to room2 to reach the current state. In this state, are all of the following properties of the state (both with and without negations) valid? ball1 is not in room2, ball1 is not in room3, ball1 is not being held by robot1's lgripper1, ball1 is not in room4, ball1 is not in room6, ball1 is not in room1, ball1 is in room5, ball2 is in room2, ball2 is not in room1, ball2 is not in room6, ball2 is not in room3, ball2 is not in room4, ball2 is not in room5, ball3 is in room5, ball3 is not in room1, ball3 is not in room2, ball3 is not in room3, ball3 is not being held by robot1's lgripper1, ball3 is not in room4, ball3 is not in room6, ball4 is in room2, ball4 is not in room1, ball4 is not in room5, ball4 is not being held by robot1's lgripper1, ball4 is not being held by robot1's rgripper1, ball4 is not in room6, ball4 is not in room3, ball4 is not in room4, ball5 is not in room1, ball5 is not in room2, ball5 is not in room4, ball5 is not in room6, ball5 is not in room5, ball5 is in room3, ball6 is not in room2, ball6 is not in room3, ball6 is not being held by robot1's lgripper1, ball6 is not in room4, ball6 is not in room6, ball6 is not in room5, ball6 is in room1, ball7 is in room5, ball7 is not in room3, ball7 is not in room6, ball7 is not being held by robot1's lgripper1, ball7 is not in room1, ball7 is not in room2, ball7 is not in room4, robot1's lgripper1 is not holding ball5, robot1's rgripper1 is free, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball5, robot1 is not in room4, robot1 is not in room6, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball6 with rgripper1, robot1 is not holding ball7 with rgripper1, robot1 is not in room3, robot1 is not in room5, robot1 is not in room1, robot1 is in room2 and robot1's lgripper1 is free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "394b8003-a31f-4aa9-9449-285adb08f288", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is not at room5, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room3, ball1 is not located at room6, ball1 is not present at room2, ball1 is not present at room4, ball1 is present at room1, ball2 is at room2, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room3, ball2 is not present at room1, ball2 is not present at room4, ball2 is not present at room6, ball3 is at room1, ball3 is not at room5, ball3 is not located at room2, ball3 is not located at room3, ball3 is not present at room4, ball3 is not present at room6, ball4 is not at room3, ball4 is not at room4, ball4 is not located at room5, ball4 is not located at room6, ball4 is not present at room1, ball4 is present at room2, ball5 is at room3, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room2, ball5 is not present at room1, ball5 is not present at room4, ball5 is not present at room6, ball6 is located at room1, ball6 is not at room2, ball6 is not located at room3, ball6 is not located at room4, ball6 is not located at room5, ball6 is not present at room6, ball7 is not at room5, ball7 is not located at room3, ball7 is not located at room6, ball7 is not present at room1, ball7 is not present at room2, ball7 is not present at room4, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, robot1 is at room4, robot1 is carrying ball7 with lgripper1, robot1 is not at room3, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not located at room1, robot1 is not located at room2, robot1 is not present in room5, robot1 is not present in room6, robot1's lgripper1 is not free and robot1's rgripper1 is available. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1's lgripper1 in room4 picks up ball7 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is not in room5, ball1 is not being held by robot1's lgripper1, ball1 is not in room3, ball1 is not in room6, ball1 is not in room2, ball1 is not in room4, ball1 is in room1, ball2 is in room2, ball2 is not in room5, ball2 is not being held by robot1's lgripper1, ball2 is not being held by robot1's rgripper1, ball2 is not in room3, ball2 is not in room1, ball2 is not in room4, ball2 is not in room6, ball3 is in room1, ball3 is not in room5, ball3 is not in room2, ball3 is not in room3, ball3 is not in room4, ball3 is not in room6, ball4 is not in room3, ball4 is not in room4, ball4 is not in room5, ball4 is not in room6, ball4 is not in room1, ball4 is in room2, ball5 is in room3, ball5 is not in room5, ball5 is not being held by robot1's lgripper1, ball5 is not in room2, ball5 is not in room1, ball5 is not in room4, ball5 is not in room6, ball6 is in room1, ball6 is not in room2, ball6 is not in room3, ball6 is not in room4, ball6 is not in room5, ball6 is not in room6, ball7 is not in room5, ball7 is not in room3, ball7 is not in room6, ball7 is not in room1, ball7 is not in room2, ball7 is not in room4, robot1's lgripper1 is not holding ball3, robot1's rgripper1 is not holding ball5, robot1's rgripper1 is not holding ball6, robot1's rgripper1 is not holding ball7, robot1 is in room4, robot1 is holding ball7 with lgripper1, robot1 is not in room3, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball6 with lgripper1, robot1 is not in room1, robot1 is not in room2, robot1 is not in room5, robot1 is not in room6, robot1's lgripper1 is occupied and robot1's rgripper1 is available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "05a2f37f-283d-4f0e-af96-aee65f8d4996", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, ball4 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball7, from room3, robot1 moves to room4, in room4, robot1's lgripper1 drops ball1, in room4, robot1's rgripper1 drops ball7, robot1 moves from room4 to room2, ball2 is picked from room2 with lgripper1 by robot1 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not at room5, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room1, ball1 is not located at room2, ball1 is not located at room3, ball2 is not at room2, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room3, ball2 is not located at room4, ball2 is not located at room5, ball2 is not present at room1, ball3 is not at room1, ball3 is not at room3, ball3 is not present at room2, ball3 is not present at room4, ball4 is not at room2, ball4 is not at room3, ball4 is not present at room4, ball4 is not present at room5, ball5 is not at room1, ball5 is not at room3, ball5 is not at room4, ball5 is not present at room2, ball5 is not present at room5, ball6 is not at room1, ball6 is not at room2, ball6 is not at room4, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room5, ball7 is not at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not present at room1, ball7 is not present at room2, ball7 is not present at room5, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not free, robot1 is not at room4, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room3, robot1 is not present in room1, robot1 is not present in room5 and robot1's lgripper1 is not available. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1, robot1 uses lgripper1 to pick up ball1 from room1, then robot1's rgripper1 picks up ball3 from room1, robot1 moves from room1 to room5, robot1's rgripper1 drops ball3 in room5, robot1 moves from room5 to room2, robot1 uses rgripper1 to pick up ball4 from room2, robot1 moves from room2 to room1, robot1 drops ball4 in room1 using rgripper1, robot1 uses rgripper1 to pick up ball6 from room1, robot1 moves from room1 to room3, robot1's rgripper1 drops ball6 in room3, robot1's rgripper1 picks up ball7 from room3, robot1 moves from room3 to room4, robot1's lgripper1 drops ball1 in room4, robot1's rgripper1 drops ball7 in room4, robot1 moves from room4 to room2, robot1 uses lgripper1 to pick up ball2 from room2 and robot1's rgripper1 picks up ball5 from room2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not in room5, ball1 is not being held by robot1's rgripper1, ball1 is not in room1, ball1 is not in room2, ball1 is not in room3, ball2 is not in room2, ball2 is not being held by robot1's rgripper1, ball2 is not in room3, ball2 is not in room4, ball2 is not in room5, ball2 is not in room1, ball3 is not in room1, ball3 is not in room3, ball3 is not in room2, ball3 is not in room4, ball4 is not in room2, ball4 is not in room3, ball4 is not in room4, ball4 is not in room5, ball5 is not in room1, ball5 is not in room3, ball5 is not in room4, ball5 is not in room2, ball5 is not in room5, ball6 is not in room1, ball6 is not in room2, ball6 is not in room4, ball6 is not being held by robot1's lgripper1, ball6 is not being held by robot1's rgripper1, ball6 is not in room5, ball7 is not in room3, ball7 is not being held by robot1's lgripper1, ball7 is not in room1, ball7 is not in room2, ball7 is not in room5, robot1's lgripper1 is not holding ball3, robot1's lgripper1 is not holding ball4, robot1's rgripper1 is not free, robot1 is not in room4, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball5 with lgripper1, robot1 is not holding ball7 with rgripper1, robot1 is not in room3, robot1 is not in room1, robot1 is not in room5 and robot1's lgripper1 is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "718bd898-4e37-410f-919f-c36dcda0bf2c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is present at room3, ball2 is at room4, ball3 is at room1, ball4 is being carried by robot1's lgripper1, ball5 is not present at room2, ball6 is not at room1, robot1 is not present in room2 and robot1's rgripper1 is not free. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then uses lgripper1 to pick up ball1 in room1, moves to room2, and uses rgripper1 to pick up ball2 in room2. Next, robot1 moves to room3, drops ball1 using lgripper1, and then moves back to room4, where it drops ball2 using rgripper1. Finally, robot1 moves to room2 and picks up ball4 with lgripper1, reaching the current state. In this state, are the following properties true without involving negations? ball1 is in room3, ball2 is in room4, ball3 is in room1, ball4 is being held by robot1's lgripper1, ball5 is not in room2, ball6 is not in room1, robot1 is not in room2, and robot1's rgripper1 is not free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "7591fc37-312a-4ccf-a36f-f09878715880", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is at room1, ball7 is located at room3, robot1 is present in room1, robot1's lgripper1 is free and robot1's rgripper1 is available. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, are all of the following properties of the state that do not involve negations valid? ball1 is in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is in room2, ball6 is in room1, ball7 is in room3, robot1 is in room1, robot1's lgripper1 is available and robot1's rgripper1 is free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "ac3c2d8c-46b2-4767-8908-4636dd009979", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, rgripper1 of robot1 drops ball4 in room1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, from room3, robot1's rgripper1 picks up ball5 and robot1 moves from room3 to room6 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is being carried by robot1's lgripper1, ball1 is being carried by robot1's rgripper1, ball1 is located at room6, ball1 is not at room1, ball1 is not at room2, ball1 is not located at room4, ball1 is present at room3, ball2 is at room3, ball2 is at room4, ball2 is not at room5, ball2 is not located at room1, ball2 is not located at room2, ball2 is not located at room6, ball3 is located at room3, ball3 is not at room2, ball3 is not present at room1, ball3 is not present at room6, ball3 is present at room4, ball4 is located at room5, ball4 is not at room6, ball4 is not located at room4, ball4 is not present at room3, ball4 is present at room2, ball5 is at room1, ball5 is at room4, ball5 is at room5, ball5 is being carried by robot1's lgripper1, ball5 is located at room2, ball5 is not at room6, ball5 is not located at room3, ball6 is located at room5, ball6 is not at room6, ball6 is not located at room4, ball6 is not present at room2, ball6 is present at room1, ball7 is located at room3, ball7 is not at room6, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room1, ball7 is not located at room4, ball7 is present at room2, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is free, rgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is carrying ball3, rgripper1 of robot1 is carrying ball7, robot1 is at room3, robot1 is carrying ball4 with rgripper1, robot1 is located at room4, robot1 is not at room1, robot1 is not at room2, robot1 is not at room5, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1 and robot1's rgripper1 is available. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1's lgripper1 picks up ball7 in room4, then robot1 moves from room4 to room5 and drops ball7 in room5 using lgripper1, after which robot1 moves to room1, picks up ball1 with lgripper1 and ball3 with rgripper1, then moves to room5, drops ball1 and ball3 in room5, and proceeds to room2, where it picks up ball2 with lgripper1 and ball4 with rgripper1, moves to room1, drops ball4 with rgripper1, picks up ball6 with rgripper1, moves to room3, drops ball6, picks up ball5 with rgripper1, and finally moves to room6 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is being carried by robot1's lgripper1, ball1 is being carried by robot1's rgripper1, ball1 is located at room6, ball1 is not at room1, ball1 is not at room2, ball1 is not located at room4, ball1 is present at room3, ball2 is at room3, ball2 is at room4, ball2 is not at room5, ball2 is not located at room1, ball2 is not located at room2, ball2 is not located at room6, ball3 is located at room3, ball3 is not at room2, ball3 is not present at room1, ball3 is not present at room6, ball3 is present at room4, ball4 is located at room5, ball4 is not at room6, ball4 is not located at room4, ball4 is not present at room3, ball4 is present at room2, ball5 is at room1, ball5 is at room4, ball5 is at room5, ball5 is being carried by robot1's lgripper1, ball5 is located at room2, ball5 is not at room6, ball5 is not located at room3, ball6 is located at room5, ball6 is not at room6, ball6 is not located at room4, ball6 is not present at room2, ball6 is present at room1, ball7 is located at room3, ball7 is not at room6, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room1, ball7 is not located at room4, ball7 is present at room2, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is free, rgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is carrying ball3, rgripper1 of robot1 is carrying ball7, robot1 is at room3, robot1 is carrying ball4 with rgripper1, robot1 is located at room4, robot1 is not at room1, robot1 is not at room2, robot1 is not at room5, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1 and robot1's rgripper1 is available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "f2f1e405-d90c-4448-bac7-f7327b57b2a7", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves from room4 to room2, ball4 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room2, robot1 moves from room2 to room5, lgripper1 of robot1 drops ball4 in room5, from room5, robot1 moves to room1, ball3 is picked from room1 with lgripper1 by robot1, in room1, robot1's rgripper1 drops ball5, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is located at room3, ball2 is present at room4, ball3 is located at room5, ball4 is located at room5, ball5 is located at room1, lgripper1 of robot1 is free, robot1 is carrying ball6 with rgripper1 and robot1 is located at room5. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then robot1's left gripper (lgripper1) grasps ball1 in room1, robot1 proceeds to room2 from room1, and robot1's right gripper (rgripper1) picks up ball2 in room2. From room2, robot1 moves to room3, where ball1 is released by lgripper1, and then robot1 moves to room4, where ball2 is dropped by rgripper1. Next, robot1 moves from room4 back to room2, picks up ball4 with lgripper1, and rgripper1 also picks up ball5 in room2. Robot1 then moves to room5, where lgripper1 drops ball4, and from room5, robot1 returns to room1. In room1, robot1's lgripper1 picks up ball3, and rgripper1 drops ball5. Subsequently, rgripper1 picks up ball6 in room1, and robot1 moves to room5, where lgripper1 drops ball3, resulting in the current state. In this state, are the following properties true without involving negations? ball1 is in room3, ball2 is in room4, ball3 is in room5, ball4 is in room5, ball5 is in room1, lgripper1 of robot1 is empty, robot1 is holding ball6 with rgripper1, and robot1 is in room5. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "fa311a5e-f66a-4165-921e-ba80c791a097", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not at room3, ball1 is not at room5, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room4, ball1 is not present at room2, ball2 is not at room1, ball2 is not at room5, ball2 is not being carried by robot1's rgripper1, ball2 is not present at room3, ball2 is not present at room4, ball3 is not at room5, ball3 is not located at room4, ball3 is not present at room2, ball3 is not present at room3, ball4 is not at room5, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room1, ball4 is not located at room3, ball4 is not present at room4, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not located at room3, ball5 is not present at room4, ball6 is not at room2, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room3, ball6 is not located at room4, ball6 is not present at room5, ball7 is not located at room4, ball7 is not present at room1, ball7 is not present at room2, ball7 is not present at room5, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball7, robot1 is not at room2, robot1 is not at room3, robot1 is not at room4, robot1 is not at room5, robot1 is not carrying ball3 with lgripper1 and robot1 is not carrying ball3 with rgripper1. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 transitions from room4 to room1 to achieve the current state. In this state, are all of the following properties that involve negations valid? ball1 is not in room3, ball1 is not in room5, ball1 is not held by robot1's left gripper (lgripper1), ball1 is not held by robot1's right gripper (rgripper1), ball1 is not at room4, ball1 is not found in room2, ball2 is not in room1, ball2 is not in room5, ball2 is not held by robot1's right gripper (rgripper1), ball2 is not in room3, ball2 is not in room4, ball3 is not in room5, ball3 is not at room4, ball3 is not in room2, ball3 is not in room3, ball4 is not in room5, ball4 is not held by robot1's left gripper (lgripper1), ball4 is not in room1, ball4 is not in room3, ball4 is not at room4, ball5 is not in room5, ball5 is not held by robot1's left gripper (lgripper1), ball5 is not in room1, ball5 is not in room3, ball5 is not at room4, ball6 is not in room2, ball6 is not held by robot1's right gripper (rgripper1), ball6 is not in room3, ball6 is not at room4, ball6 is not in room5, ball7 is not at room4, ball7 is not in room1, ball7 is not in room2, ball7 is not in room5, robot1's left gripper (lgripper1) is not holding ball2, robot1's left gripper (lgripper1) is not holding ball6, robot1's left gripper (lgripper1) is not holding ball7, robot1's right gripper (rgripper1) is not holding ball4, robot1's right gripper (rgripper1) is not holding ball5, robot1's right gripper (rgripper1) is not holding ball7, robot1 is not in room2, robot1 is not in room3, robot1 is not in room4, robot1 is not in room5, robot1 is not holding ball3 with its left gripper (lgripper1) and robot1 is not holding ball3 with its right gripper (rgripper1). Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "31fa84a7-1632-4b86-9e23-9e850998062c", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7 and robot2 moves to room2 from room3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's rgripper2, ball1 is not located at room1, ball1 is not located at room3, ball2 is not being carried by robot1's rgripper2, ball2 is not located at room1, ball2 is not present at room3, ball3 is not at room3, ball3 is not present at room1, ball4 is not at room1, ball4 is not at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper2, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot2's rgripper1, ball4 is not present at room2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper2, ball5 is not located at room2, ball5 is not located at room3, ball6 is not at room3, ball6 is not being carried by robot1's lgripper2, ball6 is not located at room2, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper2, ball7 is not being carried by robot2's lgripper2, ball7 is not located at room2, ball7 is not located at room3, ball7 is not present at room1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is not carrying ball3, lgripper1 of robot2 is not carrying ball6, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot2 is not carrying ball1, lgripper2 of robot2 is not carrying ball2, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not carrying ball6, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot1 is not carrying ball5, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not carrying ball6, rgripper2 of robot2 is not free, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not present in room1, robot1 is not present in room3, robot1's lgripper2 is not free, robot1's rgripper2 is not available, robot2 is not at room1, robot2 is not at room3, robot2 is not carrying ball1 with lgripper1, robot2 is not carrying ball2 with lgripper1, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with lgripper1, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball7 with lgripper1, robot2 is not carrying ball7 with rgripper1, robot2's lgripper2 is not free and robot2's rgripper1 is not available. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, then from room3, robot2's left gripper (lgripper2) picks up ball1 and its right gripper (rgripper2) picks up ball2, after which robot2 moves back to room2, drops ball1 and ball2 in room2 using lgripper2 and rgripper2 respectively, then moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and finally moves to room2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations: ball1 is not held by robot1's left gripper (lgripper1), ball1 is not held by robot1's right gripper (rgripper1), ball1 is not held by robot1's right gripper (rgripper2), ball1 is not held by robot2's right gripper (rgripper2), ball1 is not in room1, ball1 is not in room3, ball2 is not held by robot1's right gripper (rgripper2), ball2 is not in room1, ball2 is not in room3, ball3 is not in room3, ball3 is not in room1, ball4 is not in room1, ball4 is not in room3, ball4 is not held by robot1's left gripper (lgripper1), ball4 is not held by robot1's left gripper (lgripper2), ball4 is not held by robot1's right gripper (rgripper1), ball4 is not held by robot2's left gripper (lgripper1), ball4 is not in room2, ball5 is not held by robot2's left gripper (lgripper2), ball5 is not held by robot2's right gripper (rgripper2), ball5 is not in room2, ball5 is not in room3, ball6 is not in room3, ball6 is not held by robot1's left gripper (lgripper2), ball6 is not in room2, ball7 is not held by robot1's left gripper (lgripper1), ball7 is not held by robot1's left gripper (lgripper2), ball7 is not held by robot1's right gripper (rgripper2), ball7 is not held by robot2's left gripper (lgripper2), ball7 is not in room2, ball7 is not in room3, ball7 is not in room1, robot1's left gripper (lgripper1) is not holding ball2, robot1's left gripper (lgripper1) is not holding ball3, robot1's left gripper (lgripper1) is not holding ball6, robot2's left gripper (lgripper1) is not holding ball3, robot2's left gripper (lgripper1) is not holding ball6, robot2's left gripper (lgripper1) is not free, robot1's right gripper (lgripper2) is not holding ball2, robot1's right gripper (lgripper2) is not holding ball3, robot1's right gripper (lgripper2) is not holding ball5, robot2's right gripper (lgripper2) is not holding ball1, robot2's right gripper (lgripper2) is not holding ball2, robot1's right gripper (rgripper1) is not holding ball2, robot2's right gripper (rgripper1) is not holding ball1, robot2's right gripper (rgripper1) is not holding ball3, robot2's right gripper (rgripper1) is not holding ball5, robot2's right gripper (rgripper1) is not holding ball6, robot1's right gripper (rgripper2) is not holding ball1, robot1's right gripper (rgripper2) is not holding ball5, robot1's right gripper (rgripper2) is not holding ball6, robot2's right gripper (rgripper2) is not holding ball2, robot2's right gripper (rgripper2) is not holding ball4, robot2's right gripper (rgripper2) is not holding ball6, robot2's right gripper (rgripper2) is not free, robot1 is not holding ball3 with its right gripper (rgripper1), robot1 is not holding ball3 with its right gripper (rgripper2), robot1 is not holding ball4 with its right gripper (rgripper2), robot1 is not holding ball5 with its left gripper (lgripper1), robot1 is not holding ball5 with its right gripper (rgripper1), robot1 is not holding ball6 with its right gripper (rgripper1), robot1 is not holding ball7 with its right gripper (rgripper1), robot1 is not in room1, robot1 is not in room3, robot1's right gripper (rgripper2) is not available, robot2 is not in room1, robot2 is not in room3, robot2 is not holding ball1 with its left gripper (lgripper1), robot2 is not holding ball2 with its left gripper (lgripper1), robot2 is not holding ball2 with its right gripper (rgripper1), robot2 is not holding ball3 with its left gripper (lgripper2), robot2 is not holding ball3 with its right gripper (rgripper2), robot2 is not holding ball4 with its left gripper (lgripper1), robot2 is not holding ball5 with its left gripper (lgripper1), robot2 is not holding ball6 with its left gripper (lgripper2), robot2 is not holding ball7 with its left gripper (lgripper1), robot2 is not holding ball7 with its right gripper (rgripper1), robot2's left gripper (lgripper2) is not free and robot2's right gripper (rgripper1) is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "e840c145-e677-44be-ba45-ff8dfbf5548a", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is located at room3, ball1 is not at room2, ball1 is not located at room4, ball1 is not located at room5, ball2 is at room1, ball2 is at room3, ball2 is at room5, ball2 is not present at room4, ball3 is being carried by robot1's rgripper1, ball3 is present at room2, ball3 is present at room3, ball3 is present at room4, ball3 is present at room5, ball4 is being carried by robot1's rgripper1, ball4 is located at room1, ball4 is not being carried by robot1's lgripper1, ball4 is present at room3, ball4 is present at room4, ball4 is present at room5, ball5 is at room3, ball5 is not at room5, ball5 is not located at room4, ball5 is not present at room1, ball6 is located at room3, ball6 is not present at room2, ball6 is not present at room4, ball6 is not present at room5, ball7 is not at room2, ball7 is not being carried by robot1's rgripper1, ball7 is not present at room1, ball7 is not present at room4, ball7 is not present at room5, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5, robot1 is at room4, robot1 is at room5, robot1 is carrying ball6 with lgripper1, robot1 is carrying ball6 with rgripper1, robot1 is carrying ball7 with lgripper1, robot1 is not at room2 and robot1 is not located at room3. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is situated in room3, ball1 is not situated in room2, ball1 is not found in room4, ball1 is not found in room5, ball2 is situated in room1, ball2 is situated in room3, ball2 is situated in room5, ball2 is not present in room4, ball3 is being held by robot1's right gripper, ball3 is present in room2, ball3 is present in room3, ball3 is present in room4, ball3 is present in room5, ball4 is being held by robot1's right gripper, ball4 is situated in room1, ball4 is not being held by robot1's left gripper, ball4 is present in room3, ball4 is present in room4, ball4 is present in room5, ball5 is situated in room3, ball5 is not situated in room5, ball5 is not found in room4, ball5 is not present in room1, ball6 is situated in room3, ball6 is not present in room2, ball6 is not present in room4, ball6 is not present in room5, ball7 is not situated in room2, ball7 is not being held by robot1's right gripper, ball7 is not present in room1, ball7 is not present in room4, ball7 is not present in room5, the left gripper of robot1 is holding ball2, the left gripper of robot1 is not holding ball1, the left gripper of robot1 is not holding ball3, the left gripper of robot1 is not holding ball5, the right gripper of robot1 is holding ball1, the right gripper of robot1 is not holding ball2, the right gripper of robot1 is not holding ball5, robot1 is situated in room4, robot1 is situated in room5, robot1 is holding ball6 with its left gripper, robot1 is holding ball6 with its right gripper, robot1 is holding ball7 with its left gripper, robot1 is not situated in room2 and robot1 is not situated in room3. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "5663b5f5-2f0d-4e3c-a2ff-328f28dd397a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves from room2 to room1, in room1, robot2's lgripper2 drops ball4, ball5 is picked from room1 with lgripper2 by robot2, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, robot2 moves from room1 to room2 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is at room2, ball2 is located at room2, ball3 is not present at room1, ball4 is located at room1, ball5 is at room2, ball7 is not located at room2, lgripper2 of robot2 is free, rgripper1 of robot1 is not free, robot1 is present in room2, robot1's lgripper1 is not available, robot2 is not carrying ball6 with rgripper2 and robot2 is not present in room2. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 transitions from room2 to room3, where it uses its left gripper (lgripper2) to grasp ball1 and its right gripper (rgripper2) to grasp ball2, then moves back to room2. In room2, robot2 releases ball1 with lgripper2 and ball2 with rgripper2. Next, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and then moves back to room2, where it releases ball7 with rgripper2 and picks up ball3 with rgripper2. Robot2 then proceeds to room1, where it releases ball4 with lgripper2, picks up ball5 with lgripper2, releases ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves to room2 and releases ball5 with lgripper2, reaching the current state. In this state, are the following properties true without involving negations? ball1 is in room2, ball2 is in room2, ball3 is not in room1, ball4 is in room1, ball5 is in room2, ball7 is not in room2, robot2's lgripper2 is empty, robot1's rgripper1 is occupied, robot1 is in room2, robot1's lgripper1 is unavailable, robot2 is not holding ball6 with rgripper2, and robot2 is not in room2. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "bc2d58e2-b844-4c8f-bd2c-371ab78277a3", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball7 is dropped in room2 with rgripper2 by robot2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is located at room2, ball1 is not at room1, ball1 is not at room3, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's lgripper1, ball1 is not being carried by robot2's rgripper2, ball2 is not at room1, ball2 is not at room3, ball2 is not being carried by robot1's rgripper1, ball2 is not being carried by robot2's lgripper1, ball2 is not being carried by robot2's rgripper2, ball2 is present at room2, ball3 is located at room1, ball3 is not at room3, ball3 is not present at room2, ball4 is located at room1, ball4 is not at room2, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot2's rgripper2, ball4 is not located at room3, ball5 is at room2, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not located at room1, ball5 is not located at room3, ball6 is not at room2, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper1, ball6 is not located at room1, ball6 is not present at room3, ball7 is located at room2, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's rgripper2, ball7 is not present at room1, ball7 is not present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not carrying ball5, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is free, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball3, lgripper2 of robot2 is not carrying ball7, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball7, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball2, rgripper1 of robot2 is not carrying ball7, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot2 is not carrying ball5, rgripper2 of robot2 is not free, robot1 is located at room2, robot1 is not at room1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with rgripper2, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with lgripper2, robot1 is not carrying ball7 with rgripper2, robot1 is not located at room3, robot1's lgripper2 is not available, robot1's rgripper2 is not available, robot2 is carrying ball6 with rgripper2, robot2 is located at room2, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper1, robot2 is not present in room1, robot2 is not present in room3 and robot2's lgripper1 is not free. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial state, the following sequence of actions is executed: robot2 moves from room2 to room3, then from room3, robot2's left gripper (lgripper2) picks up ball1 and its right gripper (rgripper2) picks up ball2, after which robot2 moves back to room2. In room2, robot2's lgripper2 drops ball1 and its rgripper2 drops ball2. Then, robot2 moves to room3, where its lgripper2 picks up ball4 and its rgripper2 picks up ball7. Robot2 then moves back to room2, where it drops ball7 with its rgripper2, and its rgripper2 picks up ball3. Next, robot2 moves to room1, where it drops ball4 with its lgripper2 and picks up ball5 with its lgripper2. In room1, robot2's rgripper2 drops ball3, and its rgripper2 picks up ball6. Finally, robot2 moves to room2, where it drops ball5 with its lgripper2, resulting in the current state. \n\nIn this state, are the following properties true (both with and without negations)? ball1 is in room2, ball1 is not in room1, ball1 is not in room3, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's lgripper1, ball1 is not being carried by robot2's rgripper2, ball2 is not in room1, ball2 is not in room3, ball2 is not being carried by robot1's rgripper1, ball2 is not being carried by robot2's lgripper1, ball2 is not being carried by robot2's rgripper2, ball2 is in room2, ball3 is in room1, ball3 is not in room3, ball3 is not in room2, ball4 is in room1, ball4 is not in room2, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot2's rgripper2, ball4 is not in room3, ball5 is in room2, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not in room1, ball5 is not in room3, ball6 is not in room2, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper1, ball6 is not in room1, ball6 is not in room3, ball7 is in room2, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's rgripper2, ball7 is not in room1, ball7 is not in room3, robot1's lgripper1 is free, robot1's lgripper1 is not carrying ball1, robot1's lgripper1 is not carrying ball2, robot1's lgripper1 is not carrying ball3, robot1's lgripper1 is not carrying ball4, robot1's lgripper1 is not carrying ball6, robot2's lgripper1 is not carrying ball4, robot2's lgripper1 is not carrying ball5, robot1's lgripper2 is not carrying ball2, robot1's lgripper2 is not carrying ball3, robot1's lgripper2 is not carrying ball4, robot1's lgripper2 is not carrying ball5, robot1's lgripper2 is not carrying ball6, robot2's lgripper2 is free, robot2's lgripper2 is not carrying ball2, robot2's lgripper2 is not carrying ball3, robot2's lgripper2 is not carrying ball7, robot1's rgripper1 is free, robot1's rgripper1 is not carrying ball1, robot1's rgripper1 is not carrying ball7, robot2's rgripper1 is not carrying ball1, robot2's rgripper1 is not carrying ball2, robot2's rgripper1 is not carrying ball7, robot2's rgripper1 is not free, robot1's rgripper2 is not carrying ball1, robot1's rgripper2 is not carrying ball2, robot1's rgripper2 is not carrying ball4, robot2's rgripper2 is not carrying ball5, robot2's rgripper2 is not free, robot1 is in room2, robot1 is not in room1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with rgripper2, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with lgripper2, robot1 is not carrying ball7 with rgripper2, robot1 is not in room3, robot1's lgripper2 is not available, robot1's rgripper2 is not available, robot2 is carrying ball6 with rgripper2, robot2 is in room2, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper1, robot2 is not in room1, robot2 is not in room3, and robot2's lgripper1 is not free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has a free lgripper2 and robot2 has a free rgripper2."}
{"question_id": "d0ab0606-3e7c-4278-86ba-dcbdf47148f1", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is located at room2, ball1 is located at room3, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper2, ball1 is present at room1, ball2 is at room1, ball2 is not at room2, ball2 is not at room3, ball2 is not being carried by robot1's lgripper1, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot2's rgripper1, ball3 is not present at room2, ball3 is present at room1, ball4 is at room2, ball4 is being carried by robot1's rgripper2, ball4 is being carried by robot2's lgripper1, ball4 is located at room1, ball4 is not present at room3, ball5 is at room1, ball5 is being carried by robot2's lgripper1, ball5 is not located at room2, ball5 is not present at room3, ball6 is located at room1, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room2, ball6 is present at room3, ball7 is at room1, ball7 is being carried by robot1's lgripper1, ball7 is not at room3, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot2's rgripper2, ball7 is not present at room2, lgripper1 of robot1 is carrying ball1, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball6, lgripper2 of robot1 is carrying ball2, lgripper2 of robot1 is carrying ball3, lgripper2 of robot1 is carrying ball4, lgripper2 of robot1 is carrying ball6, lgripper2 of robot2 is not carrying ball3, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot2 is carrying ball2, rgripper1 of robot2 is carrying ball4, rgripper1 of robot2 is carrying ball7, rgripper1 of robot2 is not free, rgripper2 of robot1 is carrying ball7, rgripper2 of robot1 is not carrying ball5, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is not carrying ball4, robot1 is carrying ball1 with lgripper2, robot1 is carrying ball1 with rgripper2, robot1 is carrying ball3 with rgripper2, robot1 is carrying ball5 with lgripper2, robot1 is carrying ball6 with lgripper1, robot1 is carrying ball7 with rgripper1, robot1 is located at room1, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is present in room2, robot1 is present in room3, robot1's lgripper1 is not free, robot1's lgripper2 is not available, robot1's rgripper1 is available, robot1's rgripper2 is not available, robot2 is at room1, robot2 is carrying ball2 with lgripper2, robot2 is carrying ball2 with rgripper2, robot2 is carrying ball4 with lgripper2, robot2 is carrying ball5 with rgripper1, robot2 is carrying ball5 with rgripper2, robot2 is carrying ball6 with rgripper2, robot2 is carrying ball7 with lgripper1, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball2 with lgripper1, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not located at room2, robot2 is present in room3, robot2's lgripper1 is not available, robot2's lgripper2 is not free and robot2's rgripper2 is not free. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? \n\n- Is ball1 located at room2, or is it located at room3? \n- Is ball1 being carried by robot2's lgripper2 or robot2's rgripper2? \n- Is ball1 present at room1? \n- Is ball2 located at room1, or is it located at room2 or room3? \n- Is ball2 being carried by robot1's lgripper1? \n- Is ball3 located at room3, or is it being carried by robot1's lgripper1 or robot2's rgripper1? \n- Is ball3 present at room2? \n- Is ball3 present at room1? \n- Is ball4 located at room2, or is it being carried by robot1's rgripper2 or robot2's lgripper1? \n- Is ball4 located at room1? \n- Is ball4 present at room3? \n- Is ball5 located at room1, or is it being carried by robot2's lgripper1? \n- Is ball5 located at room2 or room3? \n- Is ball6 located at room1, or is it being carried by robot1's rgripper1? \n- Is ball6 present at room2? \n- Is ball6 present at room3? \n- Is ball7 located at room1, or is it being carried by robot1's lgripper1? \n- Is ball7 located at room3? \n- Is ball7 being carried by robot1's lgripper2 or robot2's rgripper2? \n- Is ball7 present at room2? \n- Is lgripper1 of robot1 carrying ball1? \n- Is lgripper1 of robot2 carrying ball1 or ball6? \n- Is lgripper2 of robot1 carrying ball2, ball3, ball4, or ball6? \n- Is lgripper2 of robot2 carrying ball3? \n- Is rgripper1 of robot1 carrying ball1, ball2, ball3, or ball4? \n- Is rgripper1 of robot2 carrying ball2, ball4, or ball7? \n- Is rgripper1 of robot2 free? \n- Is rgripper2 of robot1 carrying ball7 or ball5 or ball6? \n- Is rgripper2 of robot2 carrying ball4? \n- Is robot1 carrying ball1 with lgripper2 or rgripper2? \n- Is robot1 carrying ball3 with rgripper2? \n- Is robot1 carrying ball5 with lgripper2? \n- Is robot1 carrying ball6 with lgripper1? \n- Is robot1 carrying ball7 with rgripper1? \n- Is robot1 located at room1, or is it present in room2 or room3? \n- Is robot1 carrying ball2 with rgripper2? \n- Is robot1 carrying ball4 with lgripper1? \n- Is robot1 carrying ball5 with lgripper1 or rgripper1? \n- Is robot1's lgripper1 free? \n- Is robot1's lgripper2 available? \n- Is robot1's rgripper1 available? \n- Is robot1's rgripper2 available? \n- Is robot2 located at room1, or is it at room2? \n- Is robot2 carrying ball2 with lgripper2 or rgripper2? \n- Is robot2 carrying ball4 with lgripper2? \n- Is robot2 carrying ball5 with rgripper1 or rgripper2? \n- Is robot2 carrying ball6 with rgripper2? \n- Is robot2 carrying ball7 with lgripper1? \n- Is robot2 carrying ball1 with rgripper1? \n- Is robot2 carrying ball2 with lgripper1? \n- Is robot2 carrying ball3 with lgripper1 or rgripper2? \n- Is robot2 carrying ball5 with lgripper2? \n- Is robot2 carrying ball6 with lgripper2 or rgripper1? \n- Is robot2 carrying ball7 with lgripper2? \n- Is robot2 present in room3? \n- Is robot2's lgripper1 available? \n- Is robot2's lgripper2 free? \n- Is robot2's rgripper2 free?", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "2eb99b38-4e9e-4b92-9c9f-38b2a9bf7398", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7 and from room3, robot2 moves to room2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is being carried by robot1's rgripper1, ball1 is being carried by robot1's rgripper2, ball1 is not at room3, ball1 is not present at room2, ball1 is present at room1, ball2 is being carried by robot1's rgripper2, ball2 is being carried by robot2's lgripper1, ball2 is located at room2, ball2 is not located at room3, ball2 is present at room1, ball3 is at room2, ball3 is being carried by robot1's rgripper1, ball3 is being carried by robot2's rgripper2, ball3 is located at room3, ball3 is not at room1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's rgripper1, ball4 is being carried by robot1's lgripper1, ball4 is not at room2, ball4 is not being carried by robot2's lgripper2, ball4 is present at room1, ball4 is present at room3, ball5 is being carried by robot1's rgripper2, ball5 is being carried by robot2's lgripper1, ball5 is being carried by robot2's lgripper2, ball5 is located at room1, ball5 is not at room2, ball5 is not present at room3, ball6 is being carried by robot2's rgripper2, ball6 is not being carried by robot1's rgripper2, ball6 is not located at room1, ball6 is not located at room3, ball6 is present at room2, ball7 is at room2, ball7 is at room3, ball7 is being carried by robot2's lgripper2, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot2's rgripper1, ball7 is not present at room1, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not free, lgripper1 of robot2 is not carrying ball1, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot2 is carrying ball6, lgripper2 of robot2 is not carrying ball1, lgripper2 of robot2 is not free, rgripper1 of robot1 is carrying ball6, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot2 is carrying ball4, rgripper1 of robot2 is carrying ball5, rgripper1 of robot2 is not carrying ball1, rgripper2 of robot1 is carrying ball3, robot1 is at room2, robot1 is carrying ball4 with lgripper2, robot1 is carrying ball4 with rgripper1, robot1 is carrying ball4 with rgripper2, robot1 is carrying ball6 with lgripper2, robot1 is carrying ball7 with rgripper2, robot1 is located at room3, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room1, robot1's lgripper2 is free, robot1's rgripper1 is free, robot1's rgripper2 is available, robot2 is at room1, robot2 is at room2, robot2 is carrying ball1 with rgripper2, robot2 is carrying ball2 with lgripper2, robot2 is carrying ball2 with rgripper1, robot2 is carrying ball3 with lgripper2, robot2 is carrying ball4 with lgripper1, robot2 is carrying ball7 with rgripper2, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball6 with rgripper1, robot2 is not carrying ball7 with lgripper1, robot2 is not present in room3, robot2's lgripper1 is not available, robot2's rgripper1 is free and robot2's rgripper2 is not available. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial state, the following sequence of actions is executed: robot2 moves from room2 to room3, robot2's left gripper (lgripper2) picks up ball1 in room3, robot2's right gripper (rgripper2) picks up ball2 in room3, robot2 moves from room3 to room2, robot2's lgripper2 drops ball1 in room2, robot2's rgripper2 drops ball2 in room2, robot2 moves from room2 to room3, robot2's lgripper2 picks up ball4 in room3, robot2's rgripper2 picks up ball7 in room3, and robot2 moves from room3 to room2 to reach the current state. In this state, are the following properties (both affirmative and negative) valid? \n\nball1 is being held by robot1's right gripper (rgripper1), \nball1 is being held by robot1's left gripper (lgripper2), \nball1 is not in room3, \nball1 is not in room2, \nball1 is in room1, \nball2 is being held by robot1's right gripper (rgripper2), \nball2 is being held by robot2's left gripper (lgripper1), \nball2 is in room2, \nball2 is not in room3, \nball2 is in room1, \nball3 is in room2, \nball3 is being held by robot1's right gripper (rgripper1), \nball3 is being held by robot2's right gripper (rgripper2), \nball3 is in room3, \nball3 is not in room1, \nball3 is not being held by robot1's left gripper (lgripper1), \nball3 is not being held by robot1's left gripper (lgripper2), \nball3 is not being held by robot2's left gripper (lgripper1), \nball3 is not being held by robot2's right gripper (rgripper1), \nball4 is being held by robot1's left gripper (lgripper1), \nball4 is not in room2, \nball4 is not being held by robot2's left gripper (lgripper2), \nball4 is in room1, \nball4 is in room3, \nball5 is being held by robot1's right gripper (rgripper2), \nball5 is being held by robot2's left gripper (lgripper1), \nball5 is being held by robot2's left gripper (lgripper2), \nball5 is in room1, \nball5 is not in room2, \nball5 is not in room3, \nball6 is being held by robot2's right gripper (rgripper2), \nball6 is not being held by robot1's right gripper (rgripper2), \nball6 is not in room1, \nball6 is not in room3, \nball6 is in room2, \nball7 is in room2, \nball7 is in room3, \nball7 is being held by robot2's left gripper (lgripper2), \nball7 is not being held by robot1's left gripper (lgripper2), \nball7 is not being held by robot2's right gripper (rgripper1), \nball7 is not in room1, \nrobot1's left gripper (lgripper1) is holding ball2, \nrobot1's left gripper (lgripper1) is not holding ball1, \nrobot1's left gripper (lgripper1) is not holding ball5, \nrobot1's left gripper (lgripper1) is not free, \nrobot2's left gripper (lgripper1) is not holding ball1, \nrobot1's right gripper (lgripper2) is not holding ball2, \nrobot1's right gripper (lgripper2) is not holding ball5, \nrobot2's right gripper (lgripper2) is holding ball6, \nrobot2's right gripper (lgripper2) is not holding ball1, \nrobot2's right gripper (lgripper2) is not free, \nrobot1's right gripper (rgripper1) is holding ball6, \nrobot1's right gripper (rgripper1) is not holding ball5, \nrobot2's right gripper (rgripper1) is holding ball4, \nrobot2's right gripper (rgripper1) is holding ball5, \nrobot2's right gripper (rgripper1) is not holding ball1, \nrobot1's right gripper (rgripper2) is holding ball3, \nrobot1 is in room2, \nrobot1 is holding ball4 with left gripper (lgripper2), \nrobot1 is holding ball4 with right gripper (rgripper1), \nrobot1 is holding ball4 with right gripper (rgripper2), \nrobot1 is holding ball6 with left gripper (lgripper2), \nrobot1 is holding ball7 with right gripper (rgripper2), \nrobot1 is in room3, \nrobot1 is not holding ball1 with left gripper (lgripper2), \nrobot1 is not holding ball2 with right gripper (rgripper1), \nrobot1 is not holding ball6 with left gripper (lgripper1), \nrobot1 is not holding ball7 with left gripper (lgripper1), \nrobot1 is not holding ball7 with right gripper (rgripper1), \nrobot1 is not in room1, \nrobot1's left gripper (lgripper2) is free, \nrobot1's right gripper (rgripper1) is free, \nrobot1's right gripper (rgripper2) is available, \nrobot2 is in room1, \nrobot2 is in room2, \nrobot2 is holding ball1 with right gripper (rgripper2), \nrobot2 is holding ball2 with left gripper (lgripper2), \nrobot2 is holding ball2 with right gripper (rgripper1), \nrobot2 is holding ball3 with left gripper (lgripper2), \nrobot2 is holding ball4 with right gripper (rgripper1), \nrobot2 is holding ball7 with right gripper (rgripper2), \nrobot2 is not holding ball2 with right gripper (rgripper2), \nrobot2 is not holding ball4 with right gripper (rgripper2), \nrobot2 is not holding ball5 with right gripper (rgripper2), \nrobot2 is not holding ball6 with left gripper (lgripper1), \nrobot2 is not holding ball6 with right gripper (rgripper1), \nrobot2 is not holding ball7 with right gripper (rgripper1), \nrobot2 is not in room3, \nrobot2's left gripper (lgripper1) is not available, \nrobot2's right gripper (rgripper1) is free, \nrobot2's right gripper (rgripper2) is not available.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "5e380aad-59d0-4639-911b-878112f01444", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves to room2 from room5, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is being carried by robot1's lgripper1, ball2 is at room2, ball3 is at room5, ball4 is located at room1, ball5 is present at room2, ball6 is being carried by robot1's rgripper1, ball7 is located at room3 and robot1 is present in room1. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then robot1's lgripper1 grasps ball1 in room1, and robot1's rgripper1 picks up ball3 in room1. Next, robot1 moves from room1 to room5, where robot1's rgripper1 releases ball3. Robot1 then proceeds from room5 to room2, picks up ball4 with rgripper1 in room2, and moves from room2 back to room1. In room1, robot1's rgripper1 drops ball4 and picks up ball6. This sequence of actions results in the current state. In this state, are the following properties true without involving negations? ball1 is held by robot1's lgripper1, ball2 is in room2, ball3 is in room5, ball4 is in room1, ball5 is in room2, ball6 is held by robot1's rgripper1, ball7 is in room3, and robot1 is in room1. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "0fc94355-eccc-4bc3-bfcf-10f546824f0d", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4, ball4 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball5, robot1 moves from room2 to room5, in room5, robot1's lgripper1 drops ball4, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball3, in room1, robot1's rgripper1 drops ball5, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not at room2, ball1 is not at room4, ball1 is not at room5, ball1 is not located at room1, ball2 is not at room3, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not present at room2, ball2 is not present at room5, ball3 is not at room3, ball3 is not located at room2, ball3 is not present at room1, ball3 is not present at room4, ball4 is not at room4, ball4 is not located at room2, ball4 is not located at room3, ball4 is not present at room1, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room4, ball5 is not present at room2, ball5 is not present at room5, ball6 is not at room5, ball6 is not located at room3, ball6 is not present at room1, ball6 is not present at room2, ball6 is not present at room4, lgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not free, robot1 is not at room3, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not located at room2, robot1 is not located at room4 and robot1 is not present in room1. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1. Next, robot1 moves to room2, where it uses its right gripper (rgripper1) to pick up ball2. Robot1 then proceeds to room3, where it uses lgripper1 to drop ball1. From room3, robot1 moves to room4 and drops ball2 using rgripper1. Subsequently, robot1 moves back to room2, picks up ball4 with lgripper1, and also picks up ball5 with rgripper1. Robot1 then moves to room5, where it drops ball4 using lgripper1. Next, robot1 moves to room1, picks up ball3 with lgripper1, drops ball5 using rgripper1, picks up ball6 with rgripper1, and finally moves to room5, where it drops ball3 using lgripper1 to reach the current state. In this state, are all of the following properties that involve negations valid? ball1 is not in room2, ball1 is not in room4, ball1 is not in room5, ball1 is not in room1, ball2 is not in room3, ball2 is not being held by robot1's rgripper1, ball2 is not in room1, ball2 is not in room2, ball2 is not in room5, ball3 is not in room3, ball3 is not in room2, ball3 is not in room1, ball3 is not in room4, ball4 is not in room4, ball4 is not in room2, ball4 is not in room3, ball4 is not in room1, ball5 is not in room3, ball5 is not being held by robot1's lgripper1, ball5 is not in room4, ball5 is not in room2, ball5 is not in room5, ball6 is not in room5, ball6 is not in room3, ball6 is not in room1, ball6 is not in room2, ball6 is not in room4, robot1's lgripper1 is not holding ball1, robot1's rgripper1 is not holding ball3, robot1's rgripper1 is not empty, robot1 is not in room3, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball5 with lgripper1, robot1 is not holding ball6 with lgripper1, robot1 is not in room2, robot1 is not in room4, and robot1 is not in room1. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "8ef27f42-11fc-4b54-9334-e4753a5219ed", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is at room3, ball1 is located at room1, ball1 is located at room4, ball1 is not present at room2, ball1 is not present at room5, ball2 is located at room4, ball2 is not at room1, ball2 is not present at room5, ball2 is present at room2, ball2 is present at room3, ball3 is at room5, ball3 is located at room2, ball3 is located at room4, ball3 is not being carried by robot1's lgripper1, ball3 is present at room1, ball3 is present at room3, ball4 is located at room1, ball4 is located at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room5, ball4 is present at room2, ball4 is present at room4, ball5 is located at room2, ball5 is located at room5, ball5 is not at room1, ball5 is not at room4, ball5 is present at room3, ball6 is at room2, ball6 is being carried by robot1's rgripper1, ball6 is located at room3, ball6 is located at room4, ball6 is not located at room5, ball6 is not present at room1, ball7 is at room5, ball7 is being carried by robot1's lgripper1, ball7 is not at room1, ball7 is not located at room3, ball7 is not present at room2, ball7 is not present at room4, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is carrying ball4, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball7 with rgripper1, robot1 is located at room5, robot1 is not at room3, robot1 is not at room4, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room1, robot1 is not present in room2 and robot1's lgripper1 is available. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to reach the current state. In this state, are all of the following properties of the state (both with and without negations) valid? Is ball1 located at room3, is ball1 at room1, is ball1 at room4, is ball1 absent from room2, is ball1 absent from room5, is ball2 at room4, is ball2 not at room1, is ball2 not at room5, is ball2 present at room2, is ball2 present at room3, is ball3 at room5, is ball3 at room2, is ball3 at room4, is ball3 not held by robot1's lgripper1, is ball3 at room1, is ball3 at room3, is ball4 at room1, is ball4 at room3, is ball4 not held by robot1's rgripper1, is ball4 not at room5, is ball4 at room2, is ball4 at room4, is ball5 at room2, is ball5 at room5, is ball5 not at room1, is ball5 not at room4, is ball5 at room3, is ball6 at room2, is ball6 held by robot1's rgripper1, is ball6 at room3, is ball6 at room4, is ball6 not at room5, is ball6 not at room1, is ball7 at room5, is ball7 held by robot1's lgripper1, is ball7 not at room1, is ball7 not at room3, is ball7 not at room2, is ball7 not at room4, is ball1 being carried by robot1's lgripper1, is ball4 being carried by robot1's lgripper1, is ball6 not being carried by robot1's lgripper1, is robot1's rgripper1 free, is ball1 not being carried by robot1's rgripper1, is ball2 not being carried by robot1's rgripper1, is robot1 carrying ball2 with lgripper1, is robot1 carrying ball5 with rgripper1, is robot1 carrying ball7 with rgripper1, is robot1 at room5, is robot1 not at room3, is robot1 not at room4, is robot1 not carrying ball3 with rgripper1, is robot1 not carrying ball5 with lgripper1, is robot1 not at room1, is robot1 not in room2, and is robot1's lgripper1 available? Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "bb47f66b-8ece-4511-850e-a80d087f5881", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not located at room1, ball1 is not present at room2, ball1 is not present at room3, ball1 is not present at room4, ball1 is not present at room5, ball2 is not at room3, ball2 is not at room4, ball2 is not located at room1, ball2 is not present at room5, ball3 is not at room1, ball3 is not at room2, ball3 is not at room3, ball3 is not at room4, ball4 is not at room4, ball4 is not located at room3, ball4 is not located at room5, ball4 is not present at room2, ball5 is not at room4, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room1, ball5 is not located at room5, ball5 is not present at room3, ball6 is not at room1, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room3, ball6 is not present at room2, ball6 is not present at room4, ball7 is not at room4, ball7 is not located at room1, ball7 is not present at room2, ball7 is not present at room5, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball7, robot1 is not at room5, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not located at room2, robot1 is not located at room4, robot1 is not present in room3 and robot1's rgripper1 is not available. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 relocates from room4 to room1, robot1 uses lgripper1 to pick up ball1 in room1, then robot1's rgripper1 picks up ball3 in room1, robot1 proceeds to room5 from room1, and in room5, robot1's rgripper1 releases ball3, robot1 then moves from room5 to room2, where rgripper1 of robot1 picks up ball4, robot1 moves from room2 to room1, and in room1, robot1's rgripper1 drops ball4 and then picks up ball6 with rgripper1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not in room1, ball1 is not in room2, ball1 is not in room3, ball1 is not in room4, ball1 is not in room5, ball2 is not in room3, ball2 is not in room4, ball2 is not in room1, ball2 is not in room5, ball3 is not in room1, ball3 is not in room2, ball3 is not in room3, ball3 is not in room4, ball4 is not in room4, ball4 is not in room3, ball4 is not in room5, ball4 is not in room2, ball5 is not in room4, ball5 is not being held by robot1's rgripper1, ball5 is not in room1, ball5 is not in room5, ball5 is not in room3, ball6 is not in room1, ball6 is not in room5, ball6 is not being held by robot1's lgripper1, ball6 is not in room3, ball6 is not in room2, ball6 is not in room4, ball7 is not in room4, ball7 is not in room1, ball7 is not in room2, ball7 is not in room5, robot1's lgripper1 is not holding ball3, robot1's lgripper1 is not holding ball5, robot1's lgripper1 is not holding ball7, robot1's lgripper1 is not empty, robot1's rgripper1 is not holding ball7, robot1 is not in room5, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not in room2, robot1 is not in room4, robot1 is not in room3, and robot1's rgripper1 is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "440e0884-0463-40a4-ba7f-d99c47367875", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not present at room4, ball1 is not present at room5, ball2 is not at room1, ball2 is not at room3, ball2 is not at room5, ball2 is not present at room4, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room2, ball3 is not located at room4, ball3 is not located at room5, ball3 is not present at room3, ball4 is not at room5, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not located at room4, ball4 is not present at room1, ball5 is not at room4, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not located at room3, ball6 is not at room3, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not present at room4, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5, robot1 is not at room3, robot1 is not at room5, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not present in room2 and robot1 is not present in room4. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, are all of the following properties that involve negations valid? The properties include: robot1's rgripper1 is not holding ball1, ball1 is not in room2, ball1 is not in room3, ball1 is not in room4, ball1 is not in room5, ball2 is not in room1, ball2 is not in room3, ball2 is not in room5, ball2 is not in room4, robot1's lgripper1 is not holding ball3, robot1's rgripper1 is not holding ball3, ball3 is not in room2, ball3 is not in room4, ball3 is not in room5, ball3 is not in room3, ball4 is not in room5, robot1's rgripper1 is not holding ball4, ball4 is not in room3, ball4 is not in room4, ball4 is not in room1, ball5 is not in room4, ball5 is not in room5, robot1's lgripper1 is not holding ball5, ball5 is not in room1, ball5 is not in room3, ball6 is not in room3, ball6 is not in room5, robot1's lgripper1 is not holding ball6, ball6 is not in room2, ball6 is not in room4, robot1's rgripper1 is not holding ball2, robot1's rgripper1 is not holding ball5, robot1 is not in room3, robot1 is not in room5, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball6 with rgripper1, robot1 is not in room2, and robot1 is not in room4. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "f473e97d-dde5-4f15-85b8-eee7b40acc1b", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is located at room1, ball1 is not at room4, ball1 is not at room5, ball1 is not located at room2, ball1 is not located at room3, ball2 is not at room1, ball2 is not at room3, ball2 is not at room4, ball2 is not being carried by robot1's rgripper1, ball2 is not present at room5, ball2 is present at room2, ball3 is not at room3, ball3 is not at room4, ball3 is not present at room2, ball3 is not present at room5, ball3 is present at room1, ball4 is at room2, ball4 is not at room1, ball4 is not located at room3, ball4 is not located at room5, ball4 is not present at room4, ball5 is at room2, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room4, ball5 is not located at room5, ball5 is not present at room1, ball6 is located at room1, ball6 is not at room5, ball6 is not located at room3, ball6 is not located at room4, ball6 is not present at room2, ball7 is located at room3, ball7 is not at room5, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room2, ball7 is not located at room4, ball7 is not present at room1, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball6, robot1 is not at room3, robot1 is not at room4, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room2, robot1 is not located at room5 and robot1 is present in room1. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to reach the current state. In this state, are all of the following properties of the state (both with and without negations) valid? ball1 is in room1, ball1 is not in room4, ball1 is not in room5, ball1 is not in room2, ball1 is not in room3, ball2 is not in room1, ball2 is not in room3, ball2 is not in room4, ball2 is not held by robot1's rgripper1, ball2 is not in room5, ball2 is in room2, ball3 is not in room3, ball3 is not in room4, ball3 is not in room2, ball3 is not in room5, ball3 is in room1, ball4 is in room2, ball4 is not in room1, ball4 is not in room3, ball4 is not in room5, ball4 is not in room4, ball5 is in room2, ball5 is not in room3, ball5 is not held by robot1's lgripper1, ball5 is not in room4, ball5 is not in room5, ball5 is not in room1, ball6 is in room1, ball6 is not in room5, ball6 is not in room3, ball6 is not in room4, ball6 is not in room2, ball7 is in room3, ball7 is not in room5, ball7 is not held by robot1's rgripper1, ball7 is not in room2, ball7 is not in room4, ball7 is not in room1, robot1's lgripper1 is available, robot1's lgripper1 is not holding ball3, robot1's rgripper1 is available, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball3, robot1's rgripper1 is not holding ball6, robot1 is not in room3, robot1 is not in room4, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball5 with lgripper1, robot1 is not holding ball6 with lgripper1, robot1 is not holding ball7 with lgripper1, robot1 is not in room2, robot1 is not in room5 and robot1 is in room1. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "bd1a3c91-f8c8-40cb-9f6c-06ca13921f45", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is not at room3, ball1 is not at room5, ball1 is not located at room2, ball1 is not located at room6, ball1 is not present at room4, ball2 is not at room6, ball2 is not located at room1, ball2 is not located at room3, ball2 is not located at room4, ball2 is not located at room5, ball3 is not at room6, ball3 is not located at room3, ball3 is not located at room4, ball3 is not located at room5, ball3 is not present at room2, ball4 is not at room4, ball4 is not at room5, ball4 is not located at room1, ball4 is not present at room3, ball4 is not present at room6, ball5 is not at room1, ball5 is not located at room6, ball5 is not present at room2, ball5 is not present at room4, ball5 is not present at room5, ball6 is not at room2, ball6 is not at room6, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room3, ball6 is not located at room5, ball6 is not present at room4, ball7 is not at room1, ball7 is not at room2, ball7 is not at room3, ball7 is not at room4, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room5, ball7 is not present at room6, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5, robot1 is not at room5, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room3, robot1 is not present in room1, robot1 is not present in room2, robot1 is not present in room6 and robot1's lgripper1 is not free. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 uses lgripper1 to pick up ball7 from room4, resulting in the current state. In this state, are the following properties that involve negations all valid? ball1 is not in room3, ball1 is not in room5, ball1 is not found in room2, ball1 is not found in room6, ball1 is not in room4, ball2 is not in room6, ball2 is not in room1, ball2 is not in room3, ball2 is not in room4, ball2 is not in room5, ball3 is not in room6, ball3 is not in room3, ball3 is not in room4, ball3 is not in room5, ball3 is not in room2, ball4 is not in room4, ball4 is not in room5, ball4 is not in room1, ball4 is not in room3, ball4 is not in room6, ball5 is not in room1, ball5 is not in room6, ball5 is not in room2, ball5 is not in room4, ball5 is not in room5, ball6 is not in room2, ball6 is not in room6, ball6 is not being held by robot1's rgripper1, ball6 is not in room3, ball6 is not in room5, ball6 is not in room4, ball7 is not in room1, ball7 is not in room2, ball7 is not in room3, ball7 is not in room4, ball7 is not being held by robot1's rgripper1, ball7 is not in room5, ball7 is not in room6, robot1's lgripper1 is not holding ball1, robot1's lgripper1 is not holding ball6, robot1's rgripper1 is not holding ball2, robot1's rgripper1 is not holding ball5, robot1 is not in room5, robot1 is not holding ball1 with rgripper1, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball5 with lgripper1, robot1 is not in room3, robot1 is not in room1, robot1 is not in room2, robot1 is not in room6 and robot1's lgripper1 is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "cb93b3f0-403f-4872-afec-aa7ba403ca32", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves from room3 to room4, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? ball1 is located at room3, ball2 is located at room4, ball3 is located at room1, ball4 is being carried by robot1's lgripper1, ball5 is present at room2, ball6 is at room1, robot1 is located at room2 and robot1's rgripper1 is free. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then from room1, robot1's left gripper (lgripper1) grasps ball1, next, robot1 moves from room1 to room2, where robot1's right gripper (rgripper1) picks up ball2, robot1 proceeds from room2 to room3, and in room3, lgripper1 releases ball1, then robot1 moves from room3 to room4, and in room4, rgripper1 drops ball2, subsequently, robot1 moves from room4 back to room2, and finally, robot1's lgripper1 picks up ball4 from room2, resulting in the current state. In this state, are the following properties true without involving negations? ball1 is in room3, ball2 is in room4, ball3 is in room1, ball4 is being held by robot1's lgripper1, ball5 is in room2, ball6 is in room1, robot1 is in room2, and robot1's rgripper1 is available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "ef7e9dcf-73b8-4bc9-9272-05d4ca1cc927", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3 and robot1 moves to room2 from room5 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is at room3, ball1 is located at room5, ball1 is not at room2, ball1 is not located at room1, ball1 is not located at room6, ball1 is present at room4, ball2 is at room4, ball2 is located at room5, ball2 is located at room6, ball2 is not at room3, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not present at room2, ball3 is at room1, ball3 is at room3, ball3 is at room5, ball3 is at room6, ball3 is not at room2, ball3 is present at room4, ball4 is at room4, ball4 is being carried by robot1's rgripper1, ball4 is not at room6, ball4 is not located at room5, ball4 is not present at room1, ball4 is not present at room2, ball4 is present at room3, ball5 is being carried by robot1's lgripper1, ball5 is located at room1, ball5 is located at room2, ball5 is not at room3, ball5 is not located at room5, ball5 is not present at room4, ball5 is not present at room6, ball6 is located at room3, ball6 is not at room1, ball6 is not at room6, ball6 is not located at room2, ball6 is not present at room4, ball6 is not present at room5, ball7 is not at room1, ball7 is not at room4, ball7 is not located at room2, ball7 is not present at room3, ball7 is not present at room5, ball7 is not present at room6, lgripper1 of robot1 is carrying ball7, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is carrying ball2, rgripper1 of robot1 is not free, robot1 is at room2, robot1 is carrying ball1 with rgripper1, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball6 with lgripper1, robot1 is carrying ball6 with rgripper1, robot1 is located at room4, robot1 is located at room6, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room5, robot1 is not present in room1, robot1 is not present in room3 and robot1's lgripper1 is not available. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1, using lgripper1, picks up ball7 from room4, then moves from room4 to room5, where it drops ball7 using lgripper1, then proceeds to room1, picks up ball1 with lgripper1 and ball3 with rgripper1, moves back to room5, drops ball1 with lgripper1 and ball3 with rgripper1, and finally moves to room2 to reach the current state. In this state, are the following properties (including their negations) valid? ball1 is in room3, ball1 is in room5, ball1 is not in room2, ball1 is not in room1, ball1 is not in room6, ball1 is in room4, ball2 is in room4, ball2 is in room5, ball2 is in room6, ball2 is not in room3, ball2 is not held by robot1's lgripper1, ball2 is not in room1, ball2 is not in room2, ball3 is in room1, ball3 is in room3, ball3 is in room5, ball3 is in room6, ball3 is not in room2, ball3 is in room4, ball4 is in room4, ball4 is held by robot1's rgripper1, ball4 is not in room6, ball4 is not in room5, ball4 is not in room1, ball4 is not in room2, ball4 is in room3, ball5 is held by robot1's lgripper1, ball5 is in room1, ball5 is in room2, ball5 is not in room3, ball5 is not in room5, ball5 is not in room4, ball5 is not in room6, ball6 is in room3, ball6 is not in room1, ball6 is not in room6, ball6 is not in room2, ball6 is not in room4, ball6 is not in room5, ball7 is not in room1, ball7 is not in room4, ball7 is not in room2, ball7 is not in room3, ball7 is not in room5, ball7 is not in room6, robot1's lgripper1 is holding ball7, robot1's lgripper1 is not holding ball4, robot1's rgripper1 is holding ball2, robot1's rgripper1 is not free, robot1 is in room2, robot1 is holding ball1 with rgripper1, robot1 is holding ball3 with rgripper1, robot1 is holding ball5 with rgripper1, robot1 is holding ball6 with lgripper1, robot1 is holding ball6 with rgripper1, robot1 is in room4, robot1 is in room6, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball7 with rgripper1, robot1 is not in room5, robot1 is not in room1, robot1 is not in room3, and robot1's lgripper1 is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "b48e3dc4-d8e3-46a7-adc4-4ec9bc316967", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7 and robot2 moves to room2 from room3 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is located at room2, ball1 is not being carried by robot1's rgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not present at room1, ball1 is not present at room3, ball2 is not at room3, ball2 is not being carried by robot1's lgripper2, ball2 is not located at room1, ball2 is present at room2, ball3 is located at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot2's lgripper1, ball3 is not located at room1, ball4 is being carried by robot2's lgripper2, ball4 is not at room1, ball4 is not at room2, ball4 is not at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's rgripper1, ball4 is not being carried by robot2's rgripper2, ball5 is at room1, ball5 is not at room2, ball5 is not being carried by robot1's rgripper2, ball5 is not present at room3, ball6 is located at room1, ball6 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's lgripper1, ball6 is not located at room2, ball6 is not located at room3, ball7 is not at room3, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper2, ball7 is not located at room2, ball7 is not present at room1, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball7, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is not carrying ball7, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball7, rgripper1 of robot2 is not carrying ball2, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not carrying ball6, rgripper1 of robot2 is not carrying ball7, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball3, rgripper2 of robot1 is not free, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not free, robot1 is located at room2, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not present in room1, robot1's lgripper1 is available, robot1's lgripper2 is not available, robot2 is carrying ball7 with rgripper2, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball2 with lgripper1, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper2, robot2 is not located at room3, robot2 is not present in room1, robot2 is present in room2 and robot2's lgripper2 is not free. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, the left gripper of robot2 picks up ball1 in room3, then the right gripper of robot2 picks up ball2 in room3, robot2 moves back to room2 from room3, the left gripper of robot2 drops ball1 in room2, and the right gripper of robot2 drops ball2 in room2. Then, robot2 moves from room2 to room3, picks up ball4 with its left gripper and ball7 with its right gripper in room3, and finally moves to room2 from room3 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is in room2, ball1 is not being held by robot1's right gripper, ball1 is not being held by robot2's left gripper, ball1 is not in room1, ball1 is not in room3, ball2 is not in room3, ball2 is not being held by robot1's left gripper, ball2 is not in room1, ball2 is in room2, ball3 is in room2, ball3 is not in room3, ball3 is not being held by robot1's left gripper, ball3 is not being held by robot2's left gripper, ball3 is not in room1, ball4 is being held by robot2's left gripper, ball4 is not in room1, ball4 is not in room2, ball4 is not in room3, ball4 is not being held by robot1's left gripper, ball4 is not being held by robot2's left gripper, ball4 is not being held by robot2's right gripper, ball4 is not being held by robot2's right gripper, ball5 is in room1, ball5 is not in room2, ball5 is not being held by robot1's right gripper, ball5 is not in room3, ball6 is in room1, ball6 is not being held by robot1's right gripper, ball6 is not being held by robot2's left gripper, ball6 is not in room2, ball6 is not in room3, ball7 is not in room3, ball7 is not being held by robot1's left gripper, ball7 is not being held by robot1's right gripper, ball7 is not in room2, ball7 is not in room1, robot1's left gripper is not holding ball5, robot1's left gripper is not holding ball7, robot2's left gripper is not holding ball1, robot2's left gripper is not holding ball7, robot2's left gripper is not free, robot1's right gripper is not holding ball5, robot1's right gripper is not holding ball6, robot2's right gripper is not holding ball7, robot1's right gripper is free, robot1's right gripper is not holding ball2, robot1's right gripper is not holding ball4, robot1's right gripper is not holding ball5, robot1's right gripper is not holding ball7, robot2's right gripper is not holding ball2, robot2's right gripper is not holding ball5, robot2's right gripper is not holding ball6, robot2's right gripper is not holding ball7, robot2's right gripper is not free, robot1's right gripper is not holding ball2, robot1's right gripper is not holding ball3, robot1's right gripper is not free, robot2's right gripper is not holding ball1, robot2's right gripper is not holding ball2, robot2's right gripper is not free, robot1 is in room2, robot1 is not in room3, robot1 is not holding ball1 with its left gripper, robot1 is not holding ball1 with its right gripper, robot1 is not holding ball1 with its right gripper, robot1 is not holding ball2 with its left gripper, robot1 is not holding ball3 with its right gripper, robot1 is not holding ball3 with its right gripper, robot1 is not holding ball4 with its right gripper, robot1 is not holding ball4 with its right gripper, robot1 is not holding ball6 with its left gripper, robot1 is not holding ball6 with its right gripper, robot1 is not in room1, robot1's left gripper is available, robot1's right gripper is not available, robot2 is holding ball7 with its right gripper, robot2 is not holding ball1 with its left gripper, robot2 is not holding ball2 with its left gripper, robot2 is not holding ball2 with its right gripper, robot2 is not holding ball3 with its left gripper, robot2 is not holding ball3 with its right gripper, robot2 is not holding ball3 with its right gripper, robot2 is not holding ball5 with its left gripper, robot2 is not holding ball5 with its right gripper, robot2 is not holding ball5 with its right gripper, robot2 is not holding ball6 with its left gripper, robot2 is not holding ball6 with its right gripper, robot2 is not in room3, robot2 is not in room1, robot2 is in room2 and robot2's left gripper is not free. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is situated in room3, ball5 is located in room1, ball6 is also in room1, ball7 is situated in room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is located in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "45d6658e-beb7-4273-8916-363a40f5701e", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, in room3, robot1's lgripper1 drops ball1, robot1 moves from room3 to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball4 in room2, rgripper1 of robot1 picks up ball5 in room2, from room2, robot1 moves to room5, lgripper1 of robot1 drops ball4 in room5, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball3 in room1, in room1, robot1's rgripper1 drops ball5, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room1 to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is located at room3, ball1 is located at room5, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room1, ball1 is not present at room2, ball1 is not present at room4, ball2 is at room3, ball2 is being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not located at room2, ball2 is not located at room4, ball2 is present at room5, ball3 is at room2, ball3 is not at room3, ball3 is not present at room5, ball3 is present at room1, ball3 is present at room4, ball4 is located at room1, ball4 is not at room2, ball4 is not at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not present at room5, ball4 is present at room4, ball5 is at room3, ball5 is being carried by robot1's lgripper1, ball5 is located at room5, ball5 is not present at room2, ball5 is present at room1, ball5 is present at room4, ball6 is being carried by robot1's lgripper1, ball6 is not at room1, ball6 is not at room2, ball6 is not at room4, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room5, ball6 is present at room3, lgripper1 of robot1 is carrying ball2, lgripper1 of robot1 is carrying ball3, lgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball5, robot1 is at room5, robot1 is located at room1, robot1 is not at room2, robot1 is not carrying ball1 with lgripper1, robot1 is not present in room3, robot1 is not present in room4, robot1's lgripper1 is not free and robot1's rgripper1 is not available. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, robot1's left gripper (lgripper1) grasps ball1 in room1, then robot1 proceeds to room2 from room1, robot1's right gripper (rgripper1) picks up ball2 in room2, robot1 moves to room3 from room2, in room3, robot1's lgripper1 releases ball1, robot1 then moves from room3 to room4, robot1's rgripper1 drops ball2 in room4, robot1 moves from room4 back to room2, robot1's lgripper1 picks up ball4 in room2, and robot1's rgripper1 picks up ball5 in room2, from room2, robot1 moves to room5, robot1's lgripper1 drops ball4 in room5, from room5, robot1 moves to room1, robot1's lgripper1 picks up ball3 in room1, in room1, robot1's rgripper1 drops ball5, robot1's rgripper1 then picks up ball6 in room1, and finally, robot1 moves from room1 to room5, where robot1's lgripper1 drops ball3, resulting in the current state. In this state, are the following properties (both affirmative and negative) valid? ball1 is in room3, ball1 is in room5, ball1 is not being held by robot1's rgripper1, ball1 is not in room1, ball1 is not in room2, ball1 is not in room4, ball2 is in room3, ball2 is being carried by robot1's rgripper1, ball2 is not in room1, ball2 is not in room2, ball2 is not in room4, ball2 is in room5, ball3 is in room2, ball3 is not in room3, ball3 is not in room5, ball3 is in room1, ball3 is in room4, ball4 is in room1, ball4 is not in room2, ball4 is not in room3, ball4 is not being held by robot1's rgripper1, ball4 is not in room5, ball4 is in room4, ball5 is in room3, ball5 is being carried by robot1's lgripper1, ball5 is in room5, ball5 is not in room2, ball5 is in room1, ball5 is in room4, ball6 is being carried by robot1's lgripper1, ball6 is not in room1, ball6 is not in room2, ball6 is not in room4, ball6 is not being carried by robot1's rgripper1, ball6 is not in room5, ball6 is in room3, robot1's lgripper1 is carrying ball2, robot1's lgripper1 is carrying ball3, robot1's lgripper1 is carrying ball4, robot1's rgripper1 is not carrying ball3, robot1's rgripper1 is not carrying ball5, robot1 is in room5, robot1 is in room1, robot1 is not in room2, robot1 is not carrying ball1 with lgripper1, robot1 is not in room3, robot1 is not in room4, robot1's lgripper1 is occupied and robot1's rgripper1 is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "e6dcf1b0-e584-4a0b-b749-f738b193c66d", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? ball1 is at room5, ball1 is not located at room2, ball1 is not located at room3, ball1 is present at room4, ball2 is being carried by robot1's lgripper1, ball2 is not at room1, ball2 is not at room4, ball2 is not located at room3, ball2 is present at room5, ball3 is located at room2, ball3 is located at room3, ball3 is located at room4, ball3 is present at room5, ball4 is not at room5, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room1, ball4 is present at room3, ball4 is present at room4, ball5 is located at room1, ball5 is not at room5, ball5 is not located at room3, ball5 is not present at room4, ball6 is located at room3, ball6 is not at room2, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room4, ball6 is present at room5, lgripper1 of robot1 is carrying ball3, lgripper1 of robot1 is carrying ball5, lgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball4, robot1 is at room2, robot1 is carrying ball1 with rgripper1, robot1 is carrying ball5 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not present in room4, robot1 is present in room3 and robot1 is present in room5. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, are all of the following properties that involve negations valid? ball1 is in room5, ball1 is not in room2, ball1 is not in room3, ball1 is in room4, ball2 is being held by robot1's left gripper, ball2 is not in room1, ball2 is not in room4, ball2 is not in room3, ball2 is in room5, ball3 is in room2, ball3 is in room3, ball3 is in room4, ball3 is in room5, ball4 is not in room5, ball4 is not being held by robot1's left gripper, ball4 is not in room1, ball4 is in room3, ball4 is in room4, ball5 is in room1, ball5 is not in room5, ball5 is not in room3, ball5 is not in room4, ball6 is in room3, ball6 is not in room2, ball6 is not being held by robot1's left gripper, ball6 is not being held by robot1's right gripper, ball6 is not in room4, ball6 is in room5, robot1's left gripper is holding ball3, robot1's left gripper is holding ball5, robot1's left gripper is not holding ball1, robot1's right gripper is not holding ball2, robot1's right gripper is not holding ball4, robot1 is in room2, robot1 is holding ball1 with its right gripper, robot1 is holding ball5 with its right gripper, robot1 is not holding ball3 with its right gripper, robot1 is not in room4, robot1 is in room3 and robot1 is in room5. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "e39803c3-6914-49f8-a879-5fe3c419fdaf", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? ball1 is not at room2, ball1 is not located at room1, ball1 is not located at room4, ball1 is not present at room5, ball1 is present at room3, ball2 is at room4, ball2 is not at room2, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room3, ball2 is not located at room5, ball2 is not present at room1, ball3 is at room1, ball3 is not at room2, ball3 is not at room4, ball3 is not at room5, ball3 is not present at room3, ball4 is not located at room1, ball4 is not located at room2, ball4 is not located at room3, ball4 is not located at room4, ball4 is not present at room5, ball5 is located at room2, ball5 is not at room4, ball5 is not located at room3, ball5 is not located at room5, ball5 is not present at room1, ball6 is at room1, ball6 is not at room5, ball6 is not located at room3, ball6 is not located at room4, ball6 is not present at room2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, robot1 is carrying ball4 with lgripper1, robot1 is located at room2, robot1 is not at room4, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not present in room1, robot1 is not present in room3, robot1 is not present in room5 and robot1's lgripper1 is not available. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, moves to room2, and uses rgripper1 to pick up ball2 in room2. From room2, robot1 proceeds to room3, where it drops ball1 using lgripper1, then moves to room4 and drops ball2 using rgripper1. Finally, robot1 moves back to room2 and uses lgripper1 to pick up ball4, resulting in the current state. In this state, are the following properties (including their negations) valid? ball1 is not in room2, ball1 is not in room1, ball1 is not in room4, ball1 is not in room5, ball1 is in room3, ball2 is in room4, ball2 is not in room2, ball2 is not being held by robot1's rgripper1, ball2 is not in room3, ball2 is not in room5, ball2 is not in room1, ball3 is in room1, ball3 is not in room2, ball3 is not in room4, ball3 is not in room5, ball3 is not in room3, ball4 is not in room1, ball4 is not in room2, ball4 is not in room3, ball4 is not in room4, ball4 is not in room5, ball5 is in room2, ball5 is not in room4, ball5 is not in room3, ball5 is not in room5, ball5 is not in room1, ball6 is in room1, ball6 is not in room5, ball6 is not in room3, ball6 is not in room4, ball6 is not in room2, robot1's lgripper1 is not holding ball3, robot1's lgripper1 is not holding ball5, robot1's rgripper1 is empty, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball3, robot1's rgripper1 is not holding ball4, robot1's rgripper1 is not holding ball5, robot1 is holding ball4 with lgripper1, robot1 is in room2, robot1 is not in room4, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball2 with lgripper1, robot1 is not holding ball6 with lgripper1, robot1 is not holding ball6 with rgripper1, robot1 is not in room1, robot1 is not in room3, robot1 is not in room5, and robot1's lgripper1 is not available. Respond with True or False.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is positioned in room2, ball6 is situated in room1, the rgripper1 of robot1 is available, robot1 is currently in room4 and robot1's lgripper1 is also available."}
